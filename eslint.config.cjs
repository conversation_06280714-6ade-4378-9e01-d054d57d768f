const { defineConfig } = require('eslint/config');

const globals = require('globals');
const js = require('@eslint/js');

const { FlatCompat } = require('@eslint/eslintrc');

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

module.exports = defineConfig([
  {
    languageOptions: {
      globals: {
        ...globals.node,
      },

      ecmaVersion: 'latest',
      sourceType: 'module',
      parserOptions: {},
    },

    settings: {
      'n': {
        allowModules: ['#app', '#modules', '#scripts', '#templates', '#utils'],
        resolvePaths: [__dirname],
        tryExtensions: ['.js'],
      },

      'import/resolver': {
        node: {
          paths: ['src'],
        },

        alias: {
          map: [
            ['#app', './app.js'],
            ['#modules', './modules'],
            ['#scripts', './scripts'],
            ['#templates', './templates'],
            ['#utils', './utils'],
          ],

          extensions: ['.ts', '.js', '.jsx', '.json'],
        },
      },
    },

    extends: compat.extends('plugin:n/recommended', 'prettier'),

    rules: {
      'consistent-return': 'off',
      'spaced-comment': 'off',
      'no-console': 'warn',
      'guard-for-in': 'off',
      'no-continue': 'off',
      'func-names': 'off',
      'global-require': 'off',
      'object-shorthand': 'off',
      'no-await-in-loop': 'off',
      'import/no-dynamic-require': 'off',
      'import/no-cycle': 'off',
      'import/no-named-as-default-member': 'off',
      'import/extensions': ['off'],
      'import/prefer-default-export': 'off',
      'no-process-exit': 'off',

      'no-use-before-define': [
        'error',
        {
          functions: false,
          classes: true,
        },
      ],

      'no-param-reassign': 'off',
      'no-return-await': 'off',
      'no-underscore-dangle': 'off',
      'no-nested-ternary': 'off',
      'new-cap': 'off',
      'class-methods-use-this': 'off',
      'no-restricted-syntax': 'off',
      'n/no-unsupported-features/es-syntax': 'off',
      'prefer-arrow-callback': 'off',

      'prefer-destructuring': [
        'error',
        {
          object: true,
          array: false,
        },
      ],

      'no-unused-vars': [
        'error',
        {
          argsIgnorePattern: 'req|res|next|val|^_',
          args: 'all',
          caughtErrors: 'all',
          caughtErrorsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          ignoreRestSiblings: true,
        },
      ],
    },
  },
]);
