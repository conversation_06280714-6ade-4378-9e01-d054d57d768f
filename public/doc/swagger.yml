openapi: 3.0.3

info:
  title: AWE Backend API
  version: 1.0.0
  description: |
    ## API for the media library of the Adventist Web Engine.

    ---
  termsOfService: https://hopeplatform.org/terms
  contact:
    {
      name: API Support,
      url: https://hopeplatform.org/support,
      email: <EMAIL>,
    }

servers:
  - url: 'https://api.hopeplatform.org/v1'

tags:
  - name: Channels
    description: All endpoints related to Channels of the Media Library.
  - name: Shows
    description: All endpoints related to Shows of the Media Library.
  - name: Seasons
    description: All endpoints related to Seasons of the Media Library.
  - name: Episodes
    description: All endpoints related to Episodes of the Media Library.
  - name: Persons
    description: All endpoints related to Hosts and Guests of the Media Library.
  - name: Courses
    description: All endpoints related to Courses.
  - name: Course Providers
    description: All endpoints related to Course Providers.
  - name: Course Lessons
    description: All endpoints related to Course Lessons.
  - name: Course Slides
    description: All endpoints related to Course Slides.
  - name: Course Questionnaires
    description: All endpoints related to Course Questionnaires.
  - name: Articles
    description: All endpoints related to Articles.
  - name: Categories
    description: All endpoints related to Categories.
  - name: Images
    description: All endpoints related to Images.
  - name: Documents
    description: All endpoints related to Documents.
  - name: Sites
    description: Enpoints of Sites
  - name: Redirects
    description: All endpoints related to Redirects.

paths:
  # Channels
  '/media-library/channels':
    parameters:
      - $ref: '#/components/parameters/clientToken'
    get:
      tags:
        - Channels
      summary: Get list of channels
      parameters:
        - $ref: '#/components/parameters/fields'
        - $ref: '#/components/parameters/filter'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Channel'
                  count:
                    type: number
                    description: Total number of channels
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Channels
      summary: Create new channel
      requestBody:
        description: Add new channel to the system
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseChannel'
                - required:
                    - title
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Channel'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/channels/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Channels
      summary: Get channel by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Channel'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Channels
      summary: Update channel
      requestBody:
        description: Update a channel of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseChannel'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Channel'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Channels
      summary: Delete channel
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Channel'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/channels/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Channels
      summary: Disable channel
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Channel'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/channels/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Channels
      summary: Enable channel
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Channel'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/channels/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Channels
      summary: Restore channel
      description: Restores a channel that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Channel'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Shows
  '/media-library/channels/{channelId}/shows':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/channelId'
    get:
      tags:
        - Shows
      summary: Get list of shows
      parameters:
        - $ref: '#/components/parameters/fields'
        - $ref: '#/components/parameters/filter'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Show'
                  count:
                    type: number
                    description: Total number of shows
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Shows
      summary: Create new show
      requestBody:
        description: Add new show to the system
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseShow'
                - required:
                    - title
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Show'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/shows/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Shows
      summary: Get show by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Show'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Shows
      summary: Update show
      requestBody:
        description: Update a show of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseShow'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Show'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Shows
      summary: Delete show
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Show'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/shows/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Shows
      summary: Disable show
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Show'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/shows/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Shows
      summary: Enable show
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Show'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/shows/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Shows
      summary: Restore show
      description: Restores a show that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Show'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Seasons
  '/media-library/shows/{showId}/seasons':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/showId'
    get:
      tags:
        - Seasons
      summary: Get list of seasons
      parameters:
        - $ref: '#/components/parameters/fields'
        - $ref: '#/components/parameters/filter'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Season'
                  count:
                    type: number
                    description: Total number of seasons
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Seasons
      summary: Create new season
      requestBody:
        description: Add new season to the system
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseSeason'
                - required:
                    - title
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Season'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/seasons/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Seasons
      summary: Get season by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Season'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Seasons
      summary: Update season
      requestBody:
        description: Update a season of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseSeason'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Season'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Seasons
      summary: Delete season
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Season'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/seasons/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Seasons
      summary: Disable season
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Season'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/seasons/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Seasons
      summary: Enable season
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Season'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/seasons/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Seasons
      summary: Restore season
      description: Restores a season that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Season'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Episodes
  '/media-library/shows/{showId}/episodes':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/showId'
    get:
      tags:
        - Episodes
      summary: Get list of episodes
      parameters:
        - $ref: '#/components/parameters/fields'
        - $ref: '#/components/parameters/filter'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Episode'
                  count:
                    type: number
                    description: Total number of episodes
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Episodes
      summary: Create new episode
      requestBody:
        description: Add new episode to the system
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseEpisode'
                - required:
                    - title
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Episode'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/episodes/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Episodes
      summary: Get episode by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Episode'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Episodes
      summary: Update episode
      requestBody:
        description: Update a episode of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseEpisode'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Episode'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Episodes
      summary: Delete episode
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Episode'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/episodes/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Episodes
      summary: Disable episode
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Episode'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/episodes/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Episodes
      summary: Enable episode
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Episode'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/episodes/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Episodes
      summary: Restore episode
      description: Restores a episode that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Episode'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Persons
  '/media-library/channels/{channelId}/persons':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/channelId'
    get:
      tags:
        - Persons
      summary: Get list of persons
      parameters:
        - $ref: '#/components/parameters/fields'
        - $ref: '#/components/parameters/filter'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Person'
                  count:
                    type: number
                    description: Total number of persons
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Persons
      summary: Create new person
      requestBody:
        description: Add new person to the system
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BasePerson'
                - required:
                    - lastName
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Person'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/persons/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Persons
      summary: Get person by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Person'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Persons
      summary: Update person
      requestBody:
        description: Update a person of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BasePerson'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Person'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Persons
      summary: Delete person
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Person'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/persons/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Persons
      summary: Disable person
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Person'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/persons/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Persons
      summary: Enable person
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Person'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/media-library/persons/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Persons
      summary: Restore person
      description: Restores a person that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Person'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Courses
  '/courses/providers/{providerId}/courses':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/providerId'
    get:
      tags:
        - Courses
      summary: Get list of courses
      parameters:
        - $ref: '#/components/parameters/search'
        - $ref: '#/components/parameters/status'
        - $ref: '#/components/parameters/courseIds'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Course'
                  count:
                    type: number
                    description: Total number of courses
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Courses
      summary: Create new course
      requestBody:
        description: Add new course to the system
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseCourse'
                - required:
                    - title
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Course'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Courses
      summary: Get course by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Course'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Courses
      summary: Update course
      requestBody:
        description: Update a course of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseCourse'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Course'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Courses
      summary: Delete course
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Course'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
      - $ref: '#/components/parameters/provider'
    patch:
      tags:
        - Courses
      summary: Disable course
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Course'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
      - $ref: '#/components/parameters/provider'
    patch:
      tags:
        - Courses
      summary: Enable course
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Course'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Courses
      summary: Restore course
      description: Restores a course that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Course'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Course Providers
  '/courses/providers':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Course Providers
      summary: Get list of course providers
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/CourseProvider'
                  count:
                    type: number
                    description: Total number of course providers
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Course Providers
      summary: Create new provider
      requestBody:
        description: Add new provider to the course
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseCourseProvider'
                - required:
                    - title
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseProvider'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/providers/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Course Providers
      summary: Get provider by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseProvider'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Course Providers
      summary: Update provider
      requestBody:
        description: Update a provider of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseCourseProvider'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseProvider'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Course Providers
      summary: Delete provider
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseProvider'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/providers/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Providers
      summary: Disable provider
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseProvider'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/providers/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Providers
      summary: Enable provider
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseProvider'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/providers/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Providers
      summary: Restore provider
      description: Restores a provider that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseProvider'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Course Lessons
  '/courses/{id}/lessons':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Course Lessons
      summary: Get list of course lessons
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/ListCourseLesson'
                  count:
                    type: number
                    description: Total number of course lessons
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Course Lessons
      summary: Create new lesson
      requestBody:
        description: Add new lesson to the course
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseCourseLesson'
                - required:
                    - title
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseLesson'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/lessons/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Course Lessons
      summary: Get lesson by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseLesson'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Course Lessons
      summary: Update lesson
      requestBody:
        description: Update a lesson of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseCourseLesson'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseLesson'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Course Lessons
      summary: Delete lesson
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseLesson'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/lessons/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Lessons
      summary: Disable lesson
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseLesson'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/lessons/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Lessons
      summary: Enable lesson
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseLesson'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/lessons/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Lessons
      summary: Restore lesson
      description: Restores a lesson that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseLesson'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Course Slides
  '/courses/lessons/{id}/slides':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Course Slides
      summary: Get list of course slides
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/ListCourseSlide'
                  count:
                    type: number
                    description: Total number of course slides
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Course Slides
      summary: Create new slide
      requestBody:
        description: Add new slide to the lesson
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseCourseSlide'
                - required:
                    - title
                    - type
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseSlide'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/slides/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Course Slides
      summary: Get slide by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseSlide'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Course Slides
      summary: Update slide
      requestBody:
        description: Update a slide of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseCourseSlide'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseSlide'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Course Slides
      summary: Delete slide
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseSlide'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/slides/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Slides
      summary: Disable slide
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseSlide'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/slides/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Slides
      summary: Enable slide
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseSlide'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/courses/slides/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Course Slides
      summary: Restore slide
      description: Restores a slide that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseSlide'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Course Questionnaires
  '/courses/questionnaires/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Course Questionnaires
      summary: Get questionnaire by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseQuestionnaire'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Course Questionnaires
      summary: Update questionnaire
      requestBody:
        description: Update a questionnaire of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseCourseQuestionnaire'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/CourseQuestionnaire'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Articles
  '/articles':
    parameters:
      - $ref: '#/components/parameters/clientToken'
    get:
      tags:
        - Articles
      summary: Get list of articles
      parameters:
        - $ref: '#/components/parameters/fields'
        - $ref: '#/components/parameters/filter'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Article'
                  count:
                    type: number
                    description: Total number of articles
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Articles
      summary: Create new article
      requestBody:
        description: Add new article to the system
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseArticle'
                - required:
                    - title
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Article'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/articles/{id}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    get:
      tags:
        - Articles
      summary: Get article by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Article'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Articles
      summary: Update article
      requestBody:
        description: Update a article of the system
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseArticle'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Article'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Articles
      summary: Delete article
      description: "**Note:** This only performs a _soft_ deletion, by setting article's `deleted` flag to `true`."
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Article'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/articles/{id}/publish':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Articles
      summary: Publish article
      requestBody:
        description: Update a article of the system
        content:
          application/json:
            schema:
              type: object
              properties:
                canonicalSite:
                  $ref: '#/components/schemas/ArticleCanonicalSite'
                canonicalSitePage:
                  $ref: '#/components/schemas/ArticleCanonicalSitePage'
                canonicalSiteUrl:
                  $ref: '#/components/schemas/ArticleCanonicalSiteUrl'
                sites:
                  $ref: '#/components/schemas/ArticleSites'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Article'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/articles/{id}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Articles
      summary: Disable article
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Article'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/articles/{id}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Articles
      summary: Enable article
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Article'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/articles/{id}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/id'
    patch:
      tags:
        - Articles
      summary: Restore article
      description: Restores a article that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Article'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/articles/{id}/chech-slug/{siteId}/{slug}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
    get:
      tags:
        - Articles
      summary: Check slug
      parameters:
        - in: path
          name: id
          description: Article's ID
          required: true
          schema:
            type: string
        - in: path
          name: siteId
          description: Site ID
          required: true
          schema:
            type: string
        - in: path
          name: slug
          description: Slug to be checked
          required: true
          schema:
            type: string
      description: Checks if a slug is already being used in a website
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Article'
                  count:
                    type: number
                    description: Number of articles that have this slug
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Categories
  '/categories':
    parameters:
      - $ref: '#/components/parameters/clientToken'
    get:
      tags:
        - Categories
      summary: Get list of categories
      parameters:
        - $ref: '#/components/parameters/fieldsArray'
        - $ref: '#/components/parameters/statuses'
        - name: types
          in: query
          description: 'Category type. Possible values: `global`, `network`, `entity`'
          required: false
          schema:
            type: array
            items:
              type: string
        - $ref: '#/components/parameters/search'
        - name: language
          in: query
          description: Language to use for the search, as IETF language tag. For example `en`, `en-US`.
          required: false
          schema:
            type: string
            default: en
        - name: ids
          in: query
          description: 'Category ids to filter by'
          required: false
          schema:
            type: array
            items:
              type: string
        - $ref: '#/components/parameters/limit25'
        - $ref: '#/components/parameters/skip'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/sort'
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/Category'
                  count:
                    type: number
                    description: Total number of categories
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Images
  '/images/upload':
    parameters:
      - $ref: '#/components/parameters/clientToken'
    post:
      tags:
        - Images
      summary: Upload image
      requestBody:
        description: Upload image
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  format: binary
                  description: 'Supported media formats: `.jpg`, `.jpeg`, `.png`, `.svg`<br />Max file size: `16 MB`'
              required:
                - file
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/imageFile'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/images/upload-remote':
    parameters:
      - $ref: '#/components/parameters/clientToken'
    post:
      tags:
        - Images
      summary: Upload remote image
      requestBody:
        description: Upload remote image
        content:
          application/json:
            schema:
              properties:
                url:
                  type: string
                  description: Image URL, starting with `https://`
              required:
                - url
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/imageFile'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Documents
  '/documents/upload':
    parameters:
      - $ref: '#/components/parameters/clientToken'
    post:
      tags:
        - Documents
      summary: Upload document
      requestBody:
        description: Upload document
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  format: binary
                  description: 'Supported file formats: `.pdf`, `.doc`, `.docx`, `.xls`, `.xlsx`, `.ppt`, `.pptx`, `.zip`, `.rar`, `.7z`<br />Max file size: `10 MB`'
              required:
                - file
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/file'
        403:
          $ref: '#/components/responses/403NotAllowed'

  # Sites
  '/web/sites':
    parameters:
      - $ref: '#/components/parameters/clientToken'
    get:
      tags:
        - Sites
      summary: Get list of sites
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Sites'
        403:
          $ref: '#/components/responses/403NotAllowed'
  # Redirects
  '/web/sites/{siteId}/redirects':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/siteId'
    get:
      tags:
        - Redirects
      summary: Get list of redirects
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Redirect'
        403:
          $ref: '#/components/responses/403NotAllowed'
    post:
      tags:
        - Redirects
      summary: Add a redirect to a site
      requestBody:
        description: Add a redirect to a site
        content:
          application/json:
            schema:
              allOf:
                - $ref: '#/components/schemas/BaseRedirect'
                - required:
                    - source
                    - target
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Redirect'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/web/sites/{siteId}/redirects/{redirectId}':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/siteId'
      - $ref: '#/components/parameters/redirectId'
    get:
      tags:
        - Redirects
      summary: Get redirect by ID
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Redirect'
        403:
          $ref: '#/components/responses/403NotAllowed'
    patch:
      tags:
        - Redirects
      summary: Update redirect
      requestBody:
        description: Update a redirect of the site
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BaseRedirect'

      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Redirect'
        403:
          $ref: '#/components/responses/403NotAllowed'
    delete:
      tags:
        - Redirects
      summary: Delete redirect
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Redirect'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/web/sites/{siteId}/redirects/{redirectId}/disable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/siteId'
    patch:
      tags:
        - Redirects
      summary: Disable redirect
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Redirect'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/web/sites/{siteId}/redirects/{redirectId}/enable':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/siteId'
    patch:
      tags:
        - Redirects
      summary: Enable redirect
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Redirect'
        403:
          $ref: '#/components/responses/403NotAllowed'

  '/web/sites/{siteId}/redirects/{redirectId}/restore':
    parameters:
      - $ref: '#/components/parameters/clientToken'
      - $ref: '#/components/parameters/siteId'
    patch:
      tags:
        - Redirects
      summary: Restore redirect
      description: Restores a redirect that was previously deleted
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                $ref: '#/components/schemas/Redirect'
        403:
          $ref: '#/components/responses/403NotAllowed'

security:
  - BasicAuth: []

components:
  # Security schemes
  securitySchemes:
    BasicAuth:
      type: http
      scheme: Basic
      name: BasicAuth
      description: |
        The request must contain a header field in the form of `Authorization: Basic <credentials>`, where `<credentials>` is the Base64 encoding of email and password joined by a single colon `:`

        Example:
          - Let's say the email is `<EMAIL>` and the password is `Test123`
          - We join the credentials with a single colon: `<EMAIL>:Test123`
          - And then convert them to Base64: `************************************`
          - Finally, the header would be: `Authorization: Basic ************************************`

        Additionally, there must be an `Origin` header field with the backend URL as value

        Example:
          - `Origin: https://manage.hopeplatform.org`

  # Reusable responses
  responses:
    403NotAllowed:
      description: Operation not allowed. Authorization or ClientToken header is missing or invalid.
    404NotFound:
      description: Resource not found.

  # Reusable parameters
  parameters:
    id:
      name: id
      in: path
      description: Resource ID
      required: true
      schema:
        type: string
    channelId:
      name: channelId
      in: path
      description: Channel ID
      required: true
      schema:
        type: string
    showId:
      name: showId
      in: path
      description: Show ID
      required: true
      schema:
        type: string
    siteId:
      name: siteId
      in: path
      description: Site ID
      required: true
      schema:
        type: string
    providerId:
      name: providerId
      in: path
      description: Provider ID
      required: true
      schema:
        type: string
    courseId:
      name: courseId
      in: path
      description: Course ID
      required: true
      schema:
        type: string
    redirectId:
      name: redirectId
      in: path
      description: Redirect ID
      required: true
      schema:
        type: string
    clientToken:
      name: ClientToken
      description: Token provided to allow communication with the API
      required: true
      in: header
      schema:
        type: string
    limit:
      name: limit
      in: query
      description: Quantity limit. Default is 100
      required: false
      schema:
        type: number
    limit25:
      name: limit
      in: query
      description: Quantity limit. Default is 25
      required: false
      schema:
        type: number
    skip:
      name: skip
      in: query
      description: Skip value, for pagination. If `skip` param is set, the `page` param will be ignored. Default is 0
      required: false
      schema:
        type: number
    page:
      name: page
      in: query
      description: Page number, for pagination. Use in combination with `limit`. Default is 1
      required: false
      schema:
        type: number
    sort:
      name: sort
      in: query
      description: Comma-separated list of sort fields. Add a `-` to sort by descending order
      required: false
      schema:
        type: string
    fields:
      name: fields
      in: query
      description: Comma-separated list of fields to return
      required: false
      schema:
        type: string
    fieldsArray:
      name: fields
      in: query
      description: Array of fields to return
      required: false
      schema:
        type: array
        items:
          type: string
    filter:
      name: filter
      in: query
      description: JSON-encoded string to filter results
      required: false
      example: '{"title:":"Hope Channel North America","deleted":false}'
      schema:
        type: string
    search:
      name: search
      in: query
      description: Search query
      required: false
      schema:
        type: string
    status:
      name: status
      in: query
      description: 'Status filter. Possible values: `active`, `disabled`, `deleted`. Default value: `active,disabled`'
      required: false
      schema:
        type: string
    statuses:
      name: statuses
      in: query
      description: 'Status filter. Possible values: `active`, `disabled`, `deleted`'
      required: false
      schema:
        type: array
        items:
          type: string
    provider:
      name: provider
      in: query
      description: Provider ID
      required: false
      schema:
        type: string
    courseIds:
      name: ids
      in: query
      description: Comma-separated list of course IDs
      required: false
      schema:
        type: string

  # Reusable schemas (data models)
  schemas:
    # Fields
    deleted:
      properties:
        deleted:
          type: boolean
          default: false
          description: Flag telling if the record was deleted or not!
    enabled:
      properties:
        enabled:
          type: boolean
          description: Flag telling if the record is enabled or not
    rss:
      type: object
      properties:
        mediaFormat:
          type: string
          description: 'Media formats. Possible values: `mp4-hd`, `mp4-sd`, `mp3`'
        type:
          type: string
          description: Podcast type. `serial` if episodes are intended to be consumed in sequential order, else `episodic`
          default: episodic
        categories:
          type: array
          description: Comma-separated list of categories
        subcategories:
          type: array
          description: Comma-separated list of subcategories
        author:
          type: string
          description: Host or group responsible for creating the podcast
        owner:
          type: string
          description: Podcast owner, typically the name of the channel
        ownerEmail:
          type: string
          description: Email address of owner
        copyright:
          type: string
          description: 'Copyright message. Use `%Y` as placeholder. For example "Copyright %Y. All rights reserved."'
    importIDs:
      type: array
      description: Import IDs
      items:
        type: object
        properties:
          enabled:
            type: boolean
            description: Flag to decide whether the import ID is enabled or not
          type:
            type: string
            description: 'Import type. Example: `typo3`, `mam`, ...'
          accountID:
            type: string
            description: 'Not used yet'
          recordID:
            type: string
            description: Foreign ID
    scheduleIDs:
      type: array
      description: Schedule IDs for schedule import
      items:
        type: object
        properties:
          enabled:
            type: boolean
            description: Flag to decide whether the schedule ID is enabled or not
          scheduleID:
            type: string
            description: ID used for schedule import
    links:
      type: array
      description: Links
      items:
        type: object
        properties:
          enabled:
            type: boolean
            description: Flag to decide whether the link is enabled or not
          title:
            type: string
            description: Link title
          url:
            type: string
            description: Link URL, starting with `https://`
    file:
      type: object
      properties:
        name:
          type: string
          description: Filename with extension
        extension:
          type: string
          description: 'File extension. For example: `.jpg`'
        originalFilename:
          type: string
          description: Original filename without extension
        mime:
          type: string
          description: 'MIME type. For example: `image/jpeg`.'
        size:
          type: number
          description: Size in bytes
        containerId:
          type: string
          description: Entity ID
    imageFile:
      type: object
      description: Image object from CDN
      properties:
        name:
          type: string
          description: Filename with extension
        extension:
          type: string
          description: 'File extension. For example: `.jpg`'
        originalFilename:
          type: string
          description: Original filename without extension
        mime:
          type: string
          description: 'MIME type. For example: `image/jpeg`.'
        size:
          type: number
          description: Size in bytes
        width:
          type: number
          description: Width in pixels
        height:
          type: number
          description: Height in pixels
        blurhash:
          type: string
          description: Calculated BlurHash for the image
        containerId:
          type: string
          description: Entity ID
    mediaLink:
      type: object
      description: MediaLink object
      properties:
        link:
          type: string
          # description: Link URL
        duration:
          type: number
          description: Duration in seconds

    # Data models
    Channel:
      allOf:
        - properties:
            id:
              type: string
              description: Channel ID
            entity:
              type: string
              description: Entity ID the channel belongs to
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseChannel'

    BaseChannel:
      description: Channel structure
      type: object
      properties:
        title:
          type: string
          description: Channel title
        mainLanguage:
          type: string
          description: Main language of the channel as IETF language tag (`en`, `en-US`)
          default: en
        additionalLanguages:
          type: array
          items:
            type: string
          description: Additional supported languages as IETF language tags (`en`, `en-US`)
        contactEmail:
          type: string
          description: Contact email address
        countries:
          type: array
          items:
            type: string
          description: Countries as ISO-3166-1 country codes (`US`, `DE`, `FR`, ...)
        hls:
          type: string
          description: m3u8 URL for HTTP Live streaming (HLS)
        jetstreamId:
          type: string
          description: Jetsteram ID
        playerOrder:
          type: array
          description: Order for player selection
          default: ['youtube', 'jetstream', 'vimeo']
        rss:
          allOf:
            - description: Default values that will be used for all shows belonging to this channel
            - $ref: '#/components/schemas/rss'
        scheduleImport:
          type: object
          description: Schedule import settings
          properties:
            ftpLocation:
              type: string
              description: Folder name (not path) where the files with the schedule XML will be uploaded
            feedbackEmail:
              type: string
              description: Comma-separated list of email addresses to notify upon errors
            enabled:
              type: boolean
              description: Flag to decide whether the schedule import is enabled or not
            includeEntityChannels:
              type: boolean
              description: Flag to decide whether the schedule import should include episodes of other entity channels or not

        collections:
          type: array
          items:
            type: string
          description: IDs of Collection records
        importIDs:
          type: array
          items:
            $ref: '#/components/schemas/importIDs'
        siteUrl:
          type: string
          description: The site URL of the channel
        hideInMap:
          type: boolean
          description: Flag to decide whether the channel will be displayed in the map or not
        mapRegion:
          type: string
          description: 'Global region the channel belongs to. Possible values: `africa`, `america/north-america`, `america/inter-america`, `america/south-america`, `asia`, `europe`, `oceania`'
        mapTitle:
          type: string
          description: Title to be used in the global Hope Channel partners map. If no title is set, the channel title will be used.
        description:
          type: string
          description: Channel description
        mapPoint:
          type: object
          description: Channel location
          properties:
            coordinates:
              type: array
              items:
                type: number
                format: double
              description: Coordinates of channel location. First entry in array is `longitude`, second entry `latitude`
            zoom:
              type: number
              description: Zoom level on map. `1.4` = far, `2.5` = medium, `4` = close
        mapOverrides:
          type: object
          description: Title overrides for all partner channels. Key = "Channel ID", value = "Channel Title Override"

    Show:
      allOf:
        - properties:
            id:
              type: string
              description: Show ID
            channel:
              type: string
              description: Channel ID the show belongs to
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseShow'

    BaseShow:
      description: Show structure
      type: object
      properties:
        title:
          type: string
          description: Show title
        slug:
          type: string
          description: Show slug
        abstract:
          type: string
          description: Show abstract
        body:
          $ref: '#/components/schemas/TiptapContent'
        images:
          type: object
          description: Image objects
          properties:
            default:
              $ref: '#/components/schemas/imageFile'
            banner:
              $ref: '#/components/schemas/imageFile'
            poster:
              $ref: '#/components/schemas/imageFile'
            podcast:
              $ref: '#/components/schemas/imageFile'
        # backgroundImages
        # colors
        language:
          type: string
          description: Show language as IETF language tag. For example `en`, `en-US`
          default: en
        contactEmail:
          type: string
          description: Contact email address
        categories:
          type: array
          description: IDs of Category records
          items:
            type: string
        tags:
          type: array
          description: Tags that describe the show
          items:
            type: string
        episodesDownloadable:
          type: boolean
          description: Flag to decide whether the episodes should be downloadable or not
        hosts:
          type: array
          description: IDs of Person records
          items:
            type: string
        documents:
          type: array
          description: File objects from CDN
          items:
            $ref: '#/components/schemas/file'
        links:
          $ref: '#/components/schemas/links'
        podcasts:
          type: array
          description: Show podcasts
          items:
            type: object
            description: Show podcasts
            properties:
              enabled:
                type: boolean
                description: Flag to decide whether the podcast is enabled or not
              title:
                type: string
                description: Podcast title
              url:
                type: string
                description: Podcast URL, starting with `https://`
        seasons:
          type: array
          description: IDs of Season records. To change the season sorting, change the sort order in the array.
          items:
            type: string
        importIDs:
          $ref: '#/components/schemas/importIDs'
        scheduleIDs:
          $ref: '#/components/schemas/scheduleIDs'
        videoOnDemand:
          type: boolean
          description: Flag to decide whether the show is available as video on demand or not
        videoOnDemandStartsAt:
          type: string
          format: date-time
          description: Start date and time for video on demand (ISO-8601)
        videoOnDemandEndsAt:
          type: string
          format: date-time
          description: End date and time for video on demand (ISO-8601)
        rss:
          allOf:
            - description: RSS feed settings
            - $ref: '#/components/schemas/rss'
            - properties:
                type:
                  type: string
                  description: Podcast type. `serial` if episodes are intended to be consumed in sequential order, else `episodic`
                  default: episodic
                episodeImage:
                  type: boolean
                  description: Flag to decide whether the podcast should display episode images or not
        relatedShows:
          type: array
          description: IDs of Show records
          items:
            type: string
        # videosTitle
        # videoCategories
        # trailer

    Season:
      allOf:
        - properties:
            id:
              type: string
              description: Season ID
            channel:
              type: string
              description: Channel ID the season belongs to
            show:
              type: string
              description: Show ID the season belongs to
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseSeason'

    BaseSeason:
      description: Season structure
      type: object
      properties:
        title:
          type: string
          description: Season title
        slug:
          type: string
          description: Season slug
        number:
          type: number
          description: Season number
        groupName:
          type: string
          description: Season group name
        # documents
        # links

    Episode:
      allOf:
        - properties:
            id:
              type: string
              description: Episode ID
            channel:
              type: string
              description: Channel ID the episode belongs to
            show:
              type: string
              description: Show ID the episode belongs to
            mediaLinks:
              type: object
              description: Media links
              properties:
                youtube:
                  allOf:
                    - $ref: '#/components/schemas/mediaLink'
                    - properties:
                        link:
                          type: string
                          description: YouTube ID
                        title:
                          type: string
                          description: Title received by the YouTube Data API
                        thumbnails:
                          type: object
                          description: Thumbnails received by the YouTube Data API
                        aspectRatio:
                          type: string
                          description: 'Aspect ratio. For example: `16/9`.'
                jetstream:
                  allOf:
                    - $ref: '#/components/schemas/mediaLink'
                    - properties:
                        aspectRatio:
                          type: string
                          description: 'Aspect ratio. For example: `16/9`.'
                        link:
                          type: string
                          description: Jetstream ID
                        renditions:
                          type: array
                          description: Renditions received by the Jetstream API
                          items:
                            type: object
                            properties:
                              type:
                                type: string
                                description: 'Rendition types. Possible types: `mp3-128kb`, `mp4-360p`, `mp4-720p`, `hls`, `bif`'
                              url:
                                type: string
                                description: Rendition URL
                              bitrate:
                                type: number
                                description: Bitrate
                              width:
                                type: number
                                description: Width in pixels
                              height:
                                type: number
                                description: Height in pixels
                              filesize:
                                type: string # TODO: change to number
                                description: Size in bytes
                        subtitles:
                          type: array
                          description: Subtitles received by the Jetstream API
                          items:
                            type: object
                vimeo:
                  allOf:
                    - $ref: '#/components/schemas/mediaLink'
                    - properties:
                        link:
                          type: string
                          description: Vimeo ID
                        title:
                          type: string
                          description: Title received by the Vimeo Data API
                        thumbnails:
                          type: object
                          description: Thumbnails received by the Vimeo Data API
                        aspectRatio:
                          type: string
                          description: 'Aspect ratio. For example: `16/9`.'
                mp4-hd:
                  allOf:
                    - $ref: '#/components/schemas/mediaLink'
                    - properties:
                        fileSize:
                          type: number
                          description: Size in bytes
                        aspectRatio:
                          type: string
                          description: 'Aspect ratio. For example: `16/9`.'
                mp4-sd:
                  allOf:
                    - $ref: '#/components/schemas/mediaLink'
                    - properties:
                        fileSize:
                          type: number
                          description: Size in bytes
                        aspectRatio:
                          type: string
                          description: 'Aspect ratio. For example: `16/9`.'
                mp3:
                  allOf:
                    - $ref: '#/components/schemas/mediaLink'
                    - properties:
                        fileSize:
                          type: number
                          description: Size in bytes
                bif:
                  allOf:
                    - $ref: '#/components/schemas/mediaLink'
                    - properties:
                        fileSize:
                          type: number
                          description: Size in bytes
                hls:
                  allOf:
                    - $ref: '#/components/schemas/mediaLink'
                    - properties:
                        fileSize:
                          type: number
                          description: Size in bytes
            plays:
              type: number
              description: How often the episode was played
              default: 0
            downloads:
              type: number
              description: How often the episode was downloaded
              default: 0
            rating:
              type: number
              description: Episode rating
              default: 0
            duration:
              type: number
              description: Duration in seconds
              default: 0

        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseEpisode'

    BaseEpisode:
      description: Episode structure
      type: object
      properties:
        season:
          type: string
          description: Season ID the episode belongs to
        title:
          type: string
          description: Episode title
        slug:
          type: string
          description: Episode slug
        # number
        abstract:
          type: string
          description: Episode abstract
        body:
          $ref: '#/components/schemas/TiptapContent'
        image:
          $ref: '#/components/schemas/imageFile'
        language:
          type: string
          description: Episode language as IETF language tag. For example `en`, `en-US`
          default: en
        categories:
          type: array
          description: IDs of Category records
          items:
            type: string
        tags:
          type: array
          description: Tags that describe the episode
          items:
            type: string
        downloadable:
          type: boolean
          description: Flag to decide whether the episode should be downloadable or not
        hosts:
          type: array
          description: IDs of Person records
          items:
            type: string
        guests:
          type: array
          description: IDs of Person records
          items:
            type: string
        documents:
          type: array
          description: File objects from CDN
          items:
            $ref: '#/components/schemas/file'
        links:
          $ref: '#/components/schemas/links'
        mediaLinks:
          type: object
          description: Media links
          properties:
            youtube:
              description: YouTube
              properties:
                link:
                  type: string
                  description: YouTube Link or ID
            jetstream:
              description: Jetstream
              properties:
                link:
                  type: string
                  description: Jetstream ID
            vimeo:
              description: Vimeo
              properties:
                link:
                  type: string
                  description: Vimeo Link or ID
            mp4-hd:
              description: MP4 HD download link
              properties:
                link:
                  type: string
                  description: URL to MP4 file, 720p (recommended) or 1080p
            mp4-sd:
              description: MP4 SD download link
              properties:
                link:
                  type: string
                  description: URL to MP4 file, 360p (recommended) or 480p
            mp3:
              description: MP3 download link
              properties:
                link:
                  type: string
                  description: URL to MP3 file, 128kbs (recommended)
            hls:
              description: HTTP Live Stream URL (for apps)
              properties:
                link:
                  type: string
                  description: URL to HTTP Live Stream (HLS) file
        subtitles:
          type: object
          description: Subtitles
          properties:
            language:
              type: string
              description: Language code. For example `en`
            label:
              type: string
              description: Label
            url:
              type: string
              description: Subtitle URL
        importIDs:
          $ref: '#/components/schemas/importIDs'
        scheduleIDs:
          $ref: '#/components/schemas/scheduleIDs'
        firstAirDate:
          type: string
          format: date-time
          description: First air date on TV (ISO-8601)
        videoOnDemand:
          type: boolean
          description: Flag to decide whether the episode is available as video on demand or not
        videoOnDemandStartsAt:
          type: string
          format: date-time
          description: Start date and time for video on demand (ISO-8601)
        videoOnDemandEndsAt:
          type: string
          format: date-time
          description: End date and time for video on demand (ISO-8601)
        # videosTitle

    Person:
      allOf:
        - properties:
            id:
              type: string
              description: Person ID
            channel:
              type: string
              description: Channel ID the season belongs to
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BasePerson'

    BasePerson:
      description: Person structure
      type: object
      properties:
        gender:
          type: string
          description: 'Gender. Possible values: `male`, `female`, ``'
        slug:
          type: string
          description: Person slug
        prefix:
          type: string
          description: Prefix
        firstName:
          type: string
          description: First name
        middleName:
          type: string
          description: Middle name
        lastName:
          type: string
          description: Last name
        suffix:
          type: string
          description: Suffix
        avatar:
          $ref: '#/components/schemas/imageFile'
        image:
          $ref: '#/components/schemas/imageFile'
        body:
          $ref: '#/components/schemas/TiptapContent'
        # TODO: email
        roles:
          type: array
          description: 'Roles. Possible values: `host`, `guest`'
          items:
            type: string
        # TODO: importIDs

    Category:
      allOf:
        - properties:
            id:
              type: string
              description: Category ID
            entity:
              type: string
              description: Entity ID the category belongs to. `null` for global categories.
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseCategory'

    BaseCategory:
      description: Category structure
      type: object
      properties:
        title:
          type: object
          description: Category title in multiple languages
          properties:
            en:
              type: string
              description: English title
            fr:
              type: string
              description: French title
            es:
              type: string
              description: Spanish title
            de:
              type: string
              description: German title
            no:
              type: string
              description: Norwegian title
            ja:
              type: string
              description: Japanese title
            ar:
              type: string
              description: Arabic title

    Course:
      allOf:
        - properties:
            id:
              type: string
              description: Course ID
            provider:
              type: string
              description: Provider ID the course belongs to
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseCourse'

    BaseCourse:
      description: Course structure
      type: object
      properties:
        title:
          type: string
          description: Course title
        slug:
          type: string
          description: Course slug
          default: auto-generated from course title
        abstract:
          type: string
          description: Course abstract
        body:
          $ref: '#/components/schemas/TiptapContent'
        images:
          type: object
          description: Image objects
          properties:
            default:
              $ref: '#/components/schemas/imageFile'
            banner:
              $ref: '#/components/schemas/imageFile'
            poster:
              $ref: '#/components/schemas/imageFile'
        language:
          type: string
          description: Course language as IETF language tag. For example `en`
          default: Provider's main language
        correspondenceCourse:
          type: boolean
          description: Flag to decide whether the course is a correspondence course or not
          default: false
        onlineCourse:
          type: boolean
          description: Flag to decide whether the course is an online course or not
          default: false
        autonomousCourse:
          type: boolean
          description: Flag to decide whether the course is an autonomous course or not (online courses only)
          default: true
        supervisedCourse:
          type: boolean
          description: Flag to decide whether the course is a supervised course or not (online courses only)
          default: true
        progression:
          type: string
          description: 'Course progression. `linear` if the course should be taken in a linear order, `free` if the course can be taken in any order'
          default: linear
        lessons:
          type: array
          description: IDs of Lesson records. To change the lesson sorting, change the sort order in the array
          items:
            type: string
        provider:
          type: string
          description: ID of the provider the course belongs to (auto-generated)
        entity:
          type: string
          description: ID of the entity the course belongs to (auto-generated)

    CourseProvider:
      allOf:
        - properties:
            id:
              type: string
              description: Provider ID
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseCourseProvider'

    BaseCourseProvider:
      description: Provider structure
      type: object
      properties:
        title:
          type: string
          description: Provider title
        slug:
          type: string
          description: Provider slug
          default: auto-generated from provider title
        contactEmail:
          type: string
          description: Contact email address
        language:
          type: string
          description: Course language as IETF language tag. For example `en`
          default: Provider's main language
        entity:
          type: string
          description: ID of the entity the course belongs to (auto-generated)

    CourseLesson:
      allOf:
        - properties:
            id:
              type: string
              description: Lesson ID
            course:
              type: string
              description: Course ID the course belongs to
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseCourseLesson'

    BaseCourseLesson:
      description: Lesson structure
      type: object
      properties:
        title:
          type: string
          description: Lesson title
        slug:
          type: string
          description: Lesson slug
          default: auto-generated from lesson title
        content:
          $ref: '#/components/schemas/CraftJsContent'
        preview:
          type: boolean
          description: Flag to decide whether the lesson can be previewed or not
          default: false
        slides:
          type: array
          description: IDs of Slide records. To change the slide sorting, change the sort order in the array
          items:
            type: string
        course:
          type: string
          description: ID of the course the lesson belongs to (auto-generated)
        provider:
          type: string
          description: ID of the provider the lesson belongs to (auto-generated)

    ListCourseLesson:
      description: Lesson structure
      type: object
      allOf:
        - properties:
            id:
              type: string
              description: Lesson ID
            title:
              type: string
              description: Lesson title
            slides:
              type: array
              description: IDs of Slide records
              items:
                type: string
        - $ref: '#/components/schemas/enabled'

    CourseSlide:
      allOf:
        - properties:
            id:
              type: string
              description: Slide ID
            course:
              type: string
              description: Slide ID the lesson belongs to
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseCourseSlide'

    BaseCourseSlide:
      description: Slide structure
      type: object
      properties:
        title:
          type: string
          description: Slide title
        slug:
          type: string
          description: Slide slug
          default: auto-generated from slide title
        type:
          type: string
          description: 'Slide type. `content` if the slide contains plain content, `questionnaire` if the slide is a questionnaire'
        content:
          $ref: '#/components/schemas/CraftJsContent'
        questionnaire:
          type: string
          description: ID of the questionnaire of the slide (if the slide has the type `questionnaire`)
        preview:
          type: boolean
          description: Flag to decide whether the slide can be previewed or not
          default: false
        lesson:
          type: string
          description: ID of the lesson the slide belongs to (auto-generated)
        course:
          type: string
          description: ID of the course the lesson belongs to (auto-generated)
        provider:
          type: string
          description: ID of the provider the lesson belongs to (auto-generated)

    ListCourseSlide:
      description: Slide structure
      type: object
      allOf:
        - properties:
            id:
              type: string
              description: Slide ID
            title:
              type: string
              description: Slide title
        - $ref: '#/components/schemas/enabled'

    CourseQuestionnaire:
      allOf:
        - properties:
            id:
              type: string
              description: Questionnaire ID
        - $ref: '#/components/schemas/BaseCourseQuestionnaire'
        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - required:
            - questions

    BaseCourseQuestionnaire:
      description: Questionnaire structure
      type: object
      properties:
        questions:
          type: array
          description: JSON-encoded questions
          items:
            $ref: '#/components/schemas/CourseQuestion'
        # format:
        #   type: string
        #   description: 'Questionnaire format. Possible values: `compact`, `sequential`, `conversation`'
        #   default: compact
        questionNumbering:
          type: boolean
          description: Flag to decide whether the questions should be numbered or not
          default: true
        requiresReview:
          type: boolean
          description: Flag to decide whether the questionnaire requires review or not
          default: false
        showConfirmationMessage:
          type: boolean
          description: Flag to decide whether a confirmation message should be shown when a student submits the questionnaire
          default: true
        slide:
          type: string
          description: ID of the slide the questionnaire belongs to (auto-generated)
        lesson:
          type: string
          description: ID of the lesson the questionnaire belongs to (auto-generated)
        course:
          type: string
          description: ID of the course the questionnaire belongs to (auto-generated)
        provider:
          type: string
          description: ID of the provider the questionnaire belongs to (auto-generated)

    CourseQuestion:
      description: Question structure
      type: object
      properties:
        text:
          type: string
          description: 'Question text (when `richText`: short description of the content, only being displayed in the backend)'
        type:
          type: string
          description: 'Question type. Possible values: `openAnswer`, `multipleOpenAnswers`, `multipleChoice`, `singleChoice`, `scale`, `richText`, `sectionTitle`'
        config:
          oneOf:
            - title: openAnswer
              description: Configuration for openAnswer questions
              type: object
              properties:
                required:
                  type: boolean
                  description: Flag to decide whether the question is required or not
                rows:
                  type: number
                  description: Number of rows for the text area. Value between 1 and 15
                  default: 5
                feedback:
                  type: string
                  description: Feedback message shown after the questionnaire is submitted
            - title: multipleOpenAnswers
              description: Configuration for multipleOpenAnswers questions
              type: object
              properties:
                textBoxes:
                  type: array
                  description: Array of text boxes
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 'Unique option ID (24 characters: `A-Z`, `a-z`, `0-9`)'
                      text:
                        type: string
                        description: Question text
                      required:
                        type: boolean
                        description: Flag to decide whether the question is required or not
                      rows:
                        type: number
                        description: Number of rows for the text area. Value between 1 and 15
                        default: 5
                      feedback:
                        type: string
                        description: Feedback message shown after the questionnaire is submitted
                      enabled:
                        type: boolean
                        description: Flag to decide whether the text box is enabled or not
                    required:
                      - id
                      - text
              required:
                - textBoxes
            - title: multipleChoice
              description: Configuration for multipleChoice questions
              type: object
              properties:
                options:
                  type: array
                  description: Array of options
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 'Unique option ID (24 characters: `A-Z`, `a-z`, `0-9`)'
                      label:
                        type: string
                        description: Option label
                      solution:
                        type: string
                        description: 'Solution. Possible values: `neutral`, `correct`, `incorrect`'
                        default: neutral
                      neutralAnswerFeedback:
                        type: string
                        description: Feedback message shown after the questionnaire is submitted (neutral answers only)
                      wrongAnswerFeedback:
                        type: string
                        description: Feedback message shown if a wrong answer was submitted (not neutral answers only)
                      enabled:
                        type: boolean
                        description: Flag to decide whether the text box is enabled or not
                    required:
                      - id
                      - label
                      - solution
                layout:
                  type: string
                  description: 'Layout. Possible values: `vertical`, `horizontal`'
                  default: vertical
                variant:
                  type: string
                  description: 'Option variant. Possible values: `chip`, `default`'
                  default: chip
              required:
                - options
            - title: singleChoice
              description: Configuration for singleChoice questions
              type: object
              properties:
                options:
                  type: array
                  description: Array of options
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 'Unique option ID (24 characters: `A-Z`, `a-z`, `0-9`)'
                      label:
                        type: string
                        description: Option label
                      solution:
                        type: string
                        description: 'Solution. Possible values: `neutral`, `correct`, `incorrect`'
                        default: neutral
                      enabled:
                        type: boolean
                        description: Flag to decide whether the text box is enabled or not
                    required:
                      - id
                      - label
                      - solution
                layout:
                  type: string
                  description: 'Layout. Possible values: `vertical`, `horizontal`'
                  default: vertical
                neutralAnswerFeedback:
                  type: string
                  description: Feedback message shown after the questionnaire is submitted (neutral answers only)
                wrongAnswerFeedback:
                  type: string
                  description: Feedback message shown if a wrong answer was submitted (not neutral answers only)
              required:
                - options
            - title: scale
              description: Configuration for scale questions
              type: object
              properties:
                legend:
                  type: string
                  description: 'Legend. Example: `[Bad] [OK] [Good]`'
                scale:
                  type: string
                  description: 'Scale. Example: `[1] [2] [3] [4] [5] [6] [7] [8] [9] [10]`'
                feedback:
                  type: string
                  description: Feedback message shown after the questionnaire is submitted
              required:
                - scale
            - title: richText
              description: JSON-encoded content
              type: object
              properties:
                content:
                  $ref: '#/components/schemas/TiptapContent'
                  description: Content
              required:
                - scale
            - title: sectionTitle
              description: Configuration for sectionTitle
              type: object
              properties:
                as:
                  type: string
                  description: 'Title format. Possible values: `h1`, `h2`, `h3`'
                  default: h2
        enabled:
          type: boolean
          description: Flag to decide whether the text box is enabled or not
      required:
        - text
        - type
        - config

    Article:
      allOf:
        - properties:
            id:
              type: string
              description: Article ID
            entity:
              type: string
              description: Entity ID where the article belongs to

        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseArticle'
        - $ref: '#/components/schemas/ArticleSites'
        - $ref: '#/components/schemas/ArticleCanonicalSite'

    BaseArticle:
      type: object
      description: Article structure
      properties:
        title:
          type: string
          description: Article's title
        subtitle:
          type: string
          description: Article's subtitle
        abstract:
          type: string
          description: Article's abstract
        body:
          $ref: '#/components/schemas/TiptapContent'
        image:
          type: object
          properties:
            file:
              $ref: '#/components/schemas/imageFile'
            caption:
              type: string
              description: Image caption
        location:
          type: string
          description: Article's geographical location
        author:
          type: string
          description: Article's author
        authorEmail:
          type: string
          description: Article's author's email
        language:
          type: string
          description: Article's language (like `en`, `es`, `de`, etc.)
        related:
          type: array
          description: IDs of Article records
          items:
            type: string
        categories:
          type: array
          description: IDs of Category records
          items:
            type: string
        tags:
          type: array
          description: Article's tags
          items:
            type: string

    ArticleCanonicalSite:
      properties:
        canonicalSite:
          deprecated: true
          type: string
          description: Article's canonical site ID (the "source" website for the article)

    ArticleCanonicalSitePage:
      properties:
        canonicalSitePage:
          type: string
          description: Article's canonical sites page ID (the "source" page for the article)

    ArticleCanonicalSiteUrl:
      properties:
        canonicalSiteUrl:
          type: string
          description: Article's canonical URL (the "source" URL for the article)

    ArticleSites:
      properties:
        sites:
          type: object
          description: Mapping of sites where the article is being published
          properties:
            '[siteId]':
              type: object
              description: Publication settings for a specific site. _Note:_ Replace `[siteId]` key with website's ID .
              properties:
                enabled:
                  type: boolean
                  description: Deifnes if article publishing is enabled.
                slug:
                  type: string
                  description: Article's slug specific for this website.
                startsAt:
                  type: string
                  format: date-time
                  description: When the article will be start being published (it will be used also as "publication date").
                endsAt:
                  type: string
                  format: date-time
                  description: When the article will be end being published.
                flags:
                  type: object
                  description: Defines under which site's flags this article is going to be published (optional).
                  properties:
                    '[flagId]':
                      type: boolean
                      description: When `true`, the article is going to be publishe under this flag. _Note:_ Replace `[flagId]` key with the ID of site's flag.

    Redirect:
      allOf:
        - properties:
            id:
              type: string
              description: ID of the redirect.
            site:
              type: object
              description: Object that contains data related to the site.

        - $ref: '#/components/schemas/deleted'
        - $ref: '#/components/schemas/enabled'
        - $ref: '#/components/schemas/BaseRedirect'

    BaseRedirect:
      description: Redirect structure
      type: object
      properties:
        source:
          type: string
          description: URL from where you want to redirect.
        target:
          type: string
          description: URL to where you want to redirect. In order to match several URLs you can use asterisks (*) to create patterns in the 'Source' field. Then you can use $1, $2, $3 and so on to replace the corrsponding matching asterisks in the 'Target' field.
        permanent:
          type: boolean
          description: A permanent redirect indicates that the resource requested has been definitively moved to the target URL. If this is not set a temporary redirect will be used.

    PatchRedirect:
      allOf:
        - properties:
            id:
              type: string
              description: ID of the redirect.
        - $ref: '#/components/schemas/BaseRedirect'

    Sites:
      description: Get a list of sites
      type: object
      properties:
        id:
          type: string
          description: ID of the site.
        domain:
          type: string
          description: Site's main domain. Does not include http:// nor https://
        name:
          type: string
          description: Name of the site.
        description:
          type: string
          description: Description of the site.
        entity:
          type: string
          description: ID of the entity the site belongs to.

    TiptapContent:
      description: 'JSON representation of rich text content from the Tiptap editor'
      type: object
      properties:
        type:
          type: string
          description: \"doc\"
        content:
          type: array
          description: Use any of the following node types
          items:
            anyOf:
              - title: heading
                description: Heading node
                type: object
                properties:
                  type:
                    type: string
                    description: \"heading\"
                  attrs:
                    type: object
                    description: Heading attributes
                    properties:
                      level:
                        type: number
                        description: 'Heading level. Possible values: `1`, `2`, `3`'
                  content:
                    type: array
                    description: Heading content
                    items:
                      $ref: '#/components/schemas/TiptapTextNode'
                required:
                  - type
                  - attrs
                  - content
              - title: paragraph
                $ref: '#/components/schemas/TiptapParagraph'
              - title: bulletList
                description: Bullet list node
                type: object
                properties:
                  type:
                    type: string
                    description: \"bulletList\"
                  content:
                    type: array
                    description: Node content
                    items:
                      $ref: '#/components/schemas/TiptapListItem'
                required:
                  - type
                  - content
              - title: orderedList
                description: Bullet list node
                type: object
                properties:
                  type:
                    type: string
                    description: \"bulletList\"
                  attrs:
                    type: object
                    description: Node attributes
                    properties:
                      start:
                        type: number
                        description: '1'
                  content:
                    type: array
                    description: Node content
                    items:
                      $ref: '#/components/schemas/TiptapListItem'
                required:
                  - type
              - title: blockquote
                description: Blockquote node
                type: object
                properties:
                  type:
                    type: string
                    description: \"blockquote\"
                  content:
                    type: array
                    description: Use as first item a `paragraph` for the quote, and as second item a `smallText` for the source/author
                    items:
                      oneOf:
                        - $ref: '#/components/schemas/TiptapParagraph'
                        - $ref: '#/components/schemas/TiptapSmallText'
                required:
                  - type
                  - content
              - title: bibleVerse
                description: Bible verse node
                type: object
                properties:
                  type:
                    type: string
                    description: \"bibleVerse\"
                  attrs:
                    type: object
                    description: Bible verse attributes
                    properties:
                      passage:
                        type: string
                        description: Bible passage (e.g. \"John 3:16\")
                      text:
                        type: string
                        description: Bible text (e.g. \"For God so loved the world...\")
                      bible:
                        type: string
                        description: Bible version (e.g. \"NIV\")
                    required:
                      - passage
                      - text
                required:
                  - type
                  - attrs
              - title: image
                description: Image node
                type: object
                properties:
                  type:
                    type: string
                    description: \"image\"
                  attrs:
                    type: object
                    description: Image attributes
                    properties:
                      file:
                        $ref: '#/components/schemas/imageFile'
                      caption:
                        type: string
                        description: Image caption
                      alt:
                        type: string
                        description: Image alt text
                      copyright:
                        type: string
                        description: Image copyright
                      url:
                        type: string
                        description: Image URL
                      align:
                        type: string
                        description: 'Image alignment. Possible values: `""`, `left`, `right`, `center-medium`'
                      transparent:
                        type: boolean
                        description: Flag to decide whether the image has some transparent background or not (PNG images)
                      clickToEnlarge:
                        type: boolean
                        description: Flag to decide whether the image can be clicked to enlarge or not
                    required:
                      - file
                required:
                  - type
                  - attrs
              - title: media
                description: Media node
                type: object
                properties:
                  type:
                    type: string
                    description: \"media\"
                  attrs:
                    type: object
                    description: Media attributes
                    properties:
                      type:
                        type: string
                        description: 'Media type. Possible values: `audio`, `video`'
                      attrs:
                        type: object
                        description: Media attributes
                        properties:
                          type:
                            type: string
                            description: \"video\"
                          provider:
                            type: string
                            description: 'Media provider. Possible values: `youtube`, `vimeo`, `soundcloud`'
                          id:
                            type: string
                            description: 'Media ID of selected provider'
                          caption:
                            type: string
                            description: Media caption
                          copyright:
                            type: string
                            description: Media copyright
                        required:
                          - type
                          - provider
                          - id
              - title: smallText
                $ref: '#/components/schemas/TiptapSmallText'
      required:
        - type
        - content

    TiptapTextNode:
      title: text
      description: Text node from the Tiptap editor
      type: object
      properties:
        type:
          type: string
          description: \"text\"
        text:
          type: string
          description: 'Text content'
        marks:
          type: array
          description: Text marks
          items:
            type: object
            description: 'Text mark'
            properties:
              type:
                type: string
                description: 'Text mark type. Possible values: `bold`, `italic`, `subscript`, `superscript`, `strike`, `code`, `link`'
              attrs:
                type: object
                description: 'Link attributes (only for type `link`)'
                properties:
                  type:
                    type: string
                    description: 'Link type. Possible values: `external`, `email`, `phone`'
                  href:
                    type: string
                    description: 'Link URL'
                  target:
                    type: string
                    description: 'Link target. Possible values: `_blank`, `_self`, `_parent`, `_top`'
                required:
                  - type
                  - attrs
            required:
              - type
      required:
        - type
        - text

    TiptapListItem:
      title: listItem
      description: List item from the Tiptap editor
      type: object
      properties:
        type:
          type: string
          description: \"listItem\"
        content:
          type: array
          description: Node content
          items:
            $ref: '#/components/schemas/TiptapParagraph'
          minItems: 1
          maxItems: 1
      required:
        - type
        - content

    TiptapParagraph:
      title: paragraph
      description: Paragraph node
      type: object
      properties:
        type:
          type: string
          description: \"paragraph\"
        content:
          type: array
          description: Node content
          items:
            $ref: '#/components/schemas/TiptapTextNode'
      required:
        - type
        - content

    TiptapSmallText:
      title: smallText
      description: SmallText node
      type: object
      properties:
        type:
          type: string
          description: \"smallText\"
        content:
          type: array
          description: SmallText content
          items:
            $ref: '#/components/schemas/TiptapTextNode'
      required:
        - type
        - content

    CraftJsContent:
      title: Craft.js content
      description: 'JSON representation of content from the Craft.js editor'
      type: object
      properties:
        ROOT:
          type: object
          description: 'Root node'
          properties:
            type:
              type: object
              description: 'Node type'
              properties:
                resolvedName:
                  type: string
                  enum: ['Root']
                  description: \"Root\"
            isCanvas:
              type: boolean
              enum: [true]
            props:
              type: object
              description: 'Empty object'
              enum: [{}]
            displayName:
              type: string
              description: \"Root\"
            custom:
              type: object
              description: 'Empty object'
              enum: [{}]
            hidden:
              type: boolean
              enum: [false]
            nodes:
              type: array
              description: 'Array with just one ID (ID of the second property). Choose for the ID a random string (e.g. `d42CAEUR32`), then name the second property with the same ID.'
              items:
                type: string
                description: 'Node ID (same ID as second property)'
                enum: [e.g. "d42CAEUR32"]
              minItems: 1
              maxItems: 1
            linkedNodes:
              type: object
              description: 'Empty object'
              enum: [{}]
          required:
            - type
            - content
        d42CAEUR32:
          oneOf:
            - $ref: '#/components/schemas/CraftJsRichTextBlock'

    CraftJsRichTextBlock:
      title: Rich Text
      description: 'RichText node'
      type: object
      properties:
        type:
          type: object
          description: 'Node type'
          properties:
            resolvedName:
              type: string
              description: \"RichText\"
        isCanvas:
          type: boolean
          enum: [false]
        props:
          $ref: '#/components/schemas/TiptapContent'
        displayName:
          type: string
          description: \"RichText\"
        custom:
          type: object
          description: 'Custom properties'
          properties:
            type:
              type: string
              description: \"content\"
        parent:
          type: string
          description: \"ROOT\"
        hidden:
          type: boolean
          enum: [false]
        nodes:
          type: array
          description: 'Empty array'
          items: {}
          minItems: 0
          maxItems: 0
        linkedNodes:
          type: object
          description: 'Empty object'
          properties: {} # Always empty object
          additionalProperties: false
      required:
        - type
        - content
