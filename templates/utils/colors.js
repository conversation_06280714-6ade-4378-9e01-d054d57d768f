import _ from 'lodash';

// Default colors (<PERSON>)
export const defaultColors = {
  gray: {
    50: '#fefefe',
    100: '#f7fafc',
    200: '#edf2f7',
    300: '#e2e8f0',
    400: '#cbd5e0',
    500: '#a0aec0',
    600: '#718096',
    700: '#4a5568',
    800: '#2d3748',
    900: '#1a202c',
  },
  primary: {
    50: '#f3f6fa',
    100: '#ebf8ff',
    200: '#bee3f8',
    300: '#90cdf4',
    400: '#63b3ed',
    500: '#0b4ca3',
    600: '#3182ce',
    700: '#2b6cb0',
    800: '#2c5282',
    900: '#2a4365',
  },
  secondary: {
    50: '#fefdf5',
    100: '#fefbeb',
    200: '#fefcbf',
    300: '#faf089',
    400: '#f6e05e',
    500: '#f2d434',
    600: '#d69e2e',
    700: '#b7791f',
    800: '#975a16',
    900: '#744210',
  },
};

/**
 * Cleans the provided string
 * @param {String} string String to clean
 * @returns {String} Cleaned string
 */
function cleanString(string) {
  return string.trim().replace(/['"]/gm, '');
}

/**
 * Sets the colors variables for the provided variant in the provided tailwind config with the provided colors
 * @param {Object} tailwind Tailwind config
 * @param {String} variant Variant to set the colors for
 * @param {String} colors Colors to set
 */
export function setColorsVariables(tailwind = {}, variant = '', colors = '') {
  // If no tailwind config is provided, or no variant is provided, return the tailwind config
  if (_.isEmpty(tailwind) || !variant) {
    return tailwind;
  }

  // If no colors are provided, use the default ones
  if (_.isEmpty(colors)) {
    tailwind.theme.extend.colors[variant] = defaultColors[variant];
    return tailwind;
  }

  const themeColors = colors.replace(/(\r\n|\n|\r|{|})/gm, '').split(',');

  themeColors.forEach((themeColor) => {
    const [key, color] = themeColor.split(':');

    if (key && color) {
      tailwind.theme.extend.colors[variant] = {
        ...tailwind.theme.extend.colors[variant],
        [cleanString(key)]: cleanString(color),
      };
    }
  });

  return tailwind;
}
