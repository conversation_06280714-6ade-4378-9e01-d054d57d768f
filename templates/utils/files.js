import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the path to the current folder based on current file
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get the path to the styles and templates folders
const stylesPath = path.join(__dirname, '../styles');
const templatesPath = path.join(__dirname, '../templates');

/**
 * Read a file from a folder
 * @param {String} folderPath
 * @param {String} fileName
 * @returns {String}
 */
function readFile(folderPath, fileName) {
  const filePath = path.join(folderPath, fileName);
  return fs.readFileSync(filePath, 'utf8');
}

/**
 * Get CSS styles from a file as a string
 * @param {String} name - name of the file (without extension, expects .css)
 * @returns {String} - CSS styles as a string
 */
export function getStyles(name) {
  return readFile(stylesPath, `${name}.css`);
}

/**
 * Get HTML template from a file as a string
 * @param {String} name - name of the file (without extension, expects .html)
 * @returns {String} - HTML template as a string
 */
export function getTemplate(name) {
  return readFile(templatesPath, `${name}.html`);
}
