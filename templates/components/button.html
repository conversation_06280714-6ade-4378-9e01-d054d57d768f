<script props>
  module.exports = {
    ...props,
  };
</script>

<table>
  <tr>
    <th class="bg-primary-500 rounded" style="mso-padding-alt: 16px 24px">
      <a
        href="{{buttonURL}}"
        class="block font-semibold text-white text-base text-center leading-normal py-3 px-5 no-underline"
      >
        {{buttonText}}
      </a>
    </th>
  </tr>
</table>

<div class="leading-6">&nbsp;</div>
<div class="text-sm text-gray-700">
  <p class="m-0">{{buttonAlt}}</p>
  <p class="m-0 mt-1 leading-4">
    <a href="{{buttonURL}}" class="text-primary-500">{{buttonURL}}</a>
  </p>
</div>
