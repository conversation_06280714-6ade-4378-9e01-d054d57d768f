<x-layout>
  <p class="m-0 font-medium text-xl">{{title}}</p>
  <div class="leading-6">&nbsp;</div>

  <p class="m-0">{{subtitle}}</p>
  <div class="leading-6">&nbsp;</div>

  <if condition="showMessage">
    <div class="rounded-lg p-12 pb-8 bg-gray-200 text-darkgray">
      <if condition="messageSubject">
        <p class="m-0 mb-4 font-semibold">{{messageSubject}}</p>
      </if>
      <each loop="item, index in messageText">
        <if condition="item !== ''">
          <p class="m-0 mb-4">{{item}}</p>
        </if>
      </each>
    </div>
    <div class="leading-6">&nbsp;</div>
  </if>

  <x-button
    props='{"buttonURL": "{{linkURL}}", "buttonText": "{{linkText}}", "buttonAlt": "{{linkNotWorking}}"}'
  ></x-button>
  <div class="leading-6">&nbsp;</div>
</x-layout>
