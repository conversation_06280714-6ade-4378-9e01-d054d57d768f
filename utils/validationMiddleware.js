import { errorCodes, generateError } from './appError.js';
import { logError } from './logger.js';

/**
 * Validate request's body/params against the schema
 * @param {Object} schema - Joi schema
 * @param {String} validationProperty - property to validate against ("body", "query" or "params")
 * @returns {Function} - middleware function
 */
export function validate(schema, validationProperty = 'body') {
  return (req, res, next) => {
    // This is necessary to avoid modifying the original request object,
    // because some properties, like query, are immutable
    const objectToValidate = { ...req[validationProperty] };
    const { error, value } = schema.validate(objectToValidate);
    if (error) {
      logError('Validation Error', error.details);
      throw generateError(
        'Validation Error',
        errorCodes.VALIDATION_ERROR,
        422,
        error.details
      );
    } else {
      // Replace the original request body with the validated value
      if (validationProperty === 'body') {
        req.body = value;
      }
      next();
    }
  };
}
