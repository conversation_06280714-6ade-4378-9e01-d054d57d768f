import { createRequire } from 'module';
import Logger from './logger.js';

const require = createRequire(import.meta.url);

const en = require('../locales/en.json');

/**
 * Returns the translation specified
 *
 * @param {String} [language] Language to use (i.e. 'en'). If not specified, the default is used.
 * @param {String} key Key of the translation.
 * @param {Object} [values] Dynamic values to be inserted in the translation
 * @returns {String} Translation
 */
export const t = (language = 'en', key = '', values = {}) => {
  let locale;
  try {
    locale = require(`../locales/${language}.json`);
  } catch {
    Logger.warning(`Locale ${language} not found.`);
  }

  // Try to use required language, and default to english
  const translations = locale || en;

  let text = translations[key] || en[key] || key;

  // Replace the placeholders in the translation with the values passed
  Object.keys(values).forEach((v) => {
    text = text.replaceAll(`{{${v}}}`, values[v]);
  });

  return text;
};

export default { t };
