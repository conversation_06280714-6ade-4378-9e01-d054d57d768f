export class AppError extends Error {
  constructor(message, code, statusCode, validationErrors) {
    super(message);

    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = true;
    this.validationErrors = validationErrors;
  }
}

// TODO: Ideally, this should be registered per module, but for now, we will use global error codes until we have module registration available
export const errorCodes = {
  'CONFIGURATION_ERROR': 'CONFIGURATION_ERROR',
  'VALIDATION_ERROR': 'VALIDATION_ERROR',
  'INTERNAL_SERVER_ERROR': 'INTERNAL_SERVER_ERROR',
  'ENTITY_NOT_ALLOWED': 'ENTITY_NOT_ALLOWED',
  'USER_NOT_LOGGED_IN': 'USER_NOT_LOGGED_IN',
  'NOT_ALLOWED': 'NOT_ALLOWED',
  'NOT_PERMITTED': 'NOT_PERMITTED',
  'NOT_IMPLEMENTED': 'NOT_IMPLEMENTED',
  'ACCOUNT_DISABLED_OR_DELETED': 'ACCOUNT_DISABLED_OR_DELETED',
  'AUTH_FAILED': 'AUTH_FAILED',
  'LOGIN_BLOCKED': 'LOGIN_BLOCKED',
  '2FA_TOKEN_REQUIRED': '2FA_TOKEN_REQUIRED',
  'PARAMS_REQUIRED': 'PARAMS_REQUIRED',
  'EMAIL_NOT_VALID': 'EMAIL_NOT_VALID',
  'TOKEN_NOT_VALID': 'TOKEN_NOT_VALID',
  '2FA_NOT_VALID': '2FA_NOT_VALID',
  'FILE_DOWNLOAD_ERROR': 'FILE_DOWNLOAD_ERROR',
  'FILE_UPLOAD_ERROR': 'FILE_UPLOAD_ERROR',
  'FILE_TOO_BIG': 'FILE_TOO_BIG',
  'FILE_TYPE_NOT_ALLOWED': 'FILE_TYPE_NOT_ALLOWED',
  'VIDEO_NOT_FOUND': 'VIDEO_NOT_FOUND',
  'AUDIO_NOT_FOUND': 'AUDIO_NOT_FOUND',
  'TASK_ERROR': 'TASK_ERROR',
  'ALREADY_EXISTS': 'ALREADY_EXISTS',
  'NOT_FOUND': 'NOT_FOUND',
  'ANALYTICS_ERROR': 'ANALYTICS_ERROR',
  'NO_BACKEND_URL': 'NO_BACKEND_URL',
  'USER_CREATION_ERROR': 'USER_CREATION_ERROR',
  'USER_ALREADY_EXISTS': 'USER_ALREADY_EXISTS',
  'CAST_ERROR': 'CAST_ERROR',
  'DUPLICATE_ADVISOR': 'DUPLICATE_ADVISOR',
  'BACKEND_USER_NOT_FOUND': 'BACKEND_USER_NOT_FOUND',
  'INVALID_BACKEND_USER': 'INVALID_BACKEND_USER',
  'MAILJET_SENDER_NOT_FOUND': 'MAILJET_SENDER_NOT_FOUND',
};

export const generateError = (message, code, statusCode, validationErrors) =>
  new AppError(message, code, statusCode, validationErrors);

export const generateMissingParamsError = (parameterList = []) => {
  const errors = parameterList.reduce((acc, param) => {
    acc[param] = {
      type: 'REQUIRED',
      value: null,
    };
    return acc;
  }, {});

  return generateError(
    'Validation Error',
    errorCodes.VALIDATION_ERROR,
    422,
    errors
  );
};

export const errors = {
  // Internal error (500)
  internal_error: () =>
    generateError(
      'Internal Server Error',
      errorCodes.INTERNAL_SERVER_ERROR,
      500
    ),
  not_implemented: () =>
    generateError('Not implemented.', errorCodes.NOT_IMPLEMENTED, 501),

  // Unauthorized access errors (403)
  entity_not_allowed: () =>
    generateError(
      'Your account has no access to this entity.',
      errorCodes.ENTITY_NOT_ALLOWED,
      403
    ),
  entity_not_found: () =>
    generateError('Entity not found.', errorCodes.ENTITY_NOT_FOUND, 403),
  not_logged_in: () =>
    generateError('User not logged in.', errorCodes.USER_NOT_LOGGED_IN, 403),
  not_allowed: () =>
    generateError('Operation not allowed.', errorCodes.NOT_ALLOWED, 403),
  not_permitted: (module = '', permissions = [], recordId = '') =>
    generateError(
      `Your account has no permission to perform this operation. 
      - Module: "${module}"
      - Permissions: "${permissions.join('", "')}"
      ${recordId ? `- Record ID: ${recordId}` : ''}`,
      errorCodes.NOT_PERMITTED,
      403
    ),
  account_disabled: () =>
    generateError(
      'Your account is disabled or deleted.',
      errorCodes.ACCOUNT_DISABLED_OR_DELETED,
      403
    ),

  // Authentication fail errors (401)
  auth_failed: () =>
    generateError('Password or token not valid.', errorCodes.AUTH_FAILED, 401),
  login_blocked: () =>
    generateError(
      'Your login is momentarily blocked.',
      errorCodes.LOGIN_BLOCKED,
      401
    ),
  twofa_token_required: () =>
    generateError(
      'This user must authenticate with the 2FA token.',
      errorCodes['2FA_TOKEN_REQUIRED'],
      401
    ),

  // Bad request errors (400)
  params: (missing = []) =>
    generateError(
      `Please provide all the parameters required. ${
        missing.length > 0
          ? `Missing ${missing.map((x) => `'${x}'`).join(', ')}`
          : ''
      }`,
      errorCodes.PARAMS_REQUIRED,
      400
    ),
  configuration_error: (message = '') =>
    generateError(
      message ||
        'A configuration error occured. Please check that you have the correct settings for this operation before proceeding.',
      errorCodes.CONFIGURATION_ERROR,
      400
    ),
  email_not_valid: () =>
    generateError('The email is not valid.', errorCodes.EMAIL_NOT_VALID, 400),
  token_not_valid: () =>
    generateError('Token invalid or expired!', errorCodes.TOKEN_NOT_VALID, 400),
  twofa_not_valid: () =>
    generateError(
      '2FA information is not valid.',
      errorCodes['2FA_NOT_VALID'],
      400
    ),
  file_download_error: () =>
    generateError(
      "An error ocurred when trying to download the file. Make sure it's a valid URL and the file is not bigger than the maximum size allowed.",
      errorCodes.FILE_DOWNLOAD_ERROR,
      400
    ),
  file_upload_error: () =>
    generateError(
      "An error ocurred when receiving the file. Make sure it's a valid file and it's not bigger than the maximum size allowed.",
      errorCodes.FILE_UPLOAD_ERROR,
      400
    ),
  file_too_big: () =>
    generateError(
      'File size exceeds the maximum allowed.',
      errorCodes.FILE_TOO_BIG,
      400
    ),

  video_not_found: () =>
    generateError('The video was not found', errorCodes.VIDEO_NOT_FOUND, 404),

  audio_not_found: () =>
    generateError('The audio was not found', errorCodes.AUDIO_NOT_FOUND, 404),

  task_error: (message) =>
    generateError(
      message || 'An error ocurred when running the task.',
      errorCodes.TASK_ERROR,
      400
    ),

  already_exists: () =>
    generateError(
      'A record with the same key already exists.',
      errorCodes.ALREADY_EXISTS,
      409
    ),

  // Not found error (404)
  not_found: (name = 'Resource', id = '') =>
    generateError(`${name} not found.`, errorCodes.NOT_FOUND, 404, { id }),

  // Analytics error
  analytics_error: (message) =>
    generateError(
      message || 'An error ocurred on the analytics server.',
      errorCodes.ANALYTICS_ERROR,
      400
    ),
};
