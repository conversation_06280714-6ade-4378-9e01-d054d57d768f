import { getClientIp } from '@supercharge/request-ip/dist/index.js';

import { toLogDate } from '#utils/dates.js';

// Coloring of different methods for better visualization
const methods = {
  GET: '\x1b[32mGET\x1b[0m', // Green
  POST: '\x1b[38;5;208mPOST\x1b[0m', // Orange
  PATCH: '\x1b[38;5;220mPATCH\x1b[0m', // Yellow
  DELETE: '\x1b[31mDELETE\x1b[0m', // Red
};

export default function logResponse(tokens, req, res) {
  return [
    `${toLogDate(new Date())}`,
    getClientIp(req),
    `${tokens['response-time'](req, res)} ms |`,
    res.statusCode,
    methods[req.method] || req.method,
    decodeURIComponent(req.originalUrl),
  ].join(' ');
}
