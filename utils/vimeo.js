import { Vimeo } from '@vimeo/vimeo';

import { logError } from '#utils/logger.js';
import { isArray } from '#utils/types.js';

let vimeoClient = new Vimeo(
  process.env.VIMEO_CLIENT_ID,
  process.env.VIMEO_CLIENT_SECRET,
  process.env.VIMEO_ACCESS_TOKEN
);

/**
 * Promisified Vimeo client request.
 * @param {string|RequestOptions} url The Vimeo API URL to request.
 * @return {Promise<Object>} The response from the Vimeo API.
 */
function promisifiedVimeoClient(url) {
  return new Promise((resolve, reject) => {
    vimeoClient.request(url, (error, result) =>
      error ? reject(error) : resolve(result)
    );
  });
}

/**
 * Gets the Vimeo ID from a URL.
 *
 *  Works with following urls:
 *  - http://vimeo.com/25451551
 *  - https://vimeo.com/25451551
 *  - https://vimeo.com/25451551?app_id=122963
 *  - https://www.vimeo.com/25451551
 *  - http://player.vimeo.com/video/25451551
 *  - https://player.vimeo.com/video/25451551&jjk
 *  - https://vimeo.com/manage/videos/25451551
 *  - https://vimeo.com/manage/videos/25451551/privacy
 *  - //player.vimeo.com/video/25451551
 *  - 25451551
 *
 * @param {string} url The Vimeo URL.
 * @return {string|null} The Vimeo ID or null if not found.
 */
function getVimeoId(url) {
  if (!url) return null;
  const regExp =
    /(?:www\.|player\.)?vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/(?:[^/]*)\/videos\/|album\/(?:\d+)\/video\/|manage\/videos\/|video\/|)(\d+)(?:[a-zA-Z0-9_-]+)?/;
  const match = url.match(regExp);
  return match && match.length > 1 ? match[1] : url;
}

/**
 * Gets a Vimeo video by its ID.
 * @param {string} value The Vimeo ID or URL.
 * @return {Promise<Object|null>}
 */
async function getVideo(value) {
  if (!value) return;
  const vimeoId = getVimeoId(value);

  try {
    return await promisifiedVimeoClient({
      path: `/videos/${vimeoId}`,
      query: {
        fields: 'link,name,duration,pictures,privacy,files,status,stats',
      },
    });
  } catch (error) {
    logError(`Error getting vimeo video (${vimeoId})`, error);
  }
  return null;
}

/**
 * Gets a media link for a Vimeo video.
 * @param {Object} vimeoVideo The Vimeo video object.
 * @return {Object|null} The media link object or null if not found.
 */
async function getMediaLink(vimeoVideo) {
  if (!vimeoVideo) return null;

  const { link, name, duration } = vimeoVideo;

  return {
    link: getVimeoId(link),
    aspectRatio: '16:9',
    duration,
    title: name,
  };
}

/**
 * Gets a specific video file from a Vimeo video object.
 * @param {Object} vimeoVideo The Vimeo video object.
 * @param {string} quality The quality of the video (e.g., 'hd', 'sd', 'hls').
 * @param {string} rendition The rendition of the video (e.g., '720p', '360p', 'adaptive').
 * @return {Object|null} The video file object or null if not found.
 */
function getVideoFile(vimeoVideo, quality, rendition) {
  if (!vimeoVideo || !isArray(vimeoVideo.files)) return null;
  return vimeoVideo.files.find(
    (f) => f.quality === quality && f.rendition === rendition
  );
}

/**
 * Initializes the Vimeo client with custom credentials.
 * @param {Object} channel The channel configuration.
 * @param {Object} channel.vimeo The Vimeo configuration.
 * @param {string} channel.vimeo.clientID The Vimeo client ID.
 * @param {string} channel.vimeo.clientSecret The Vimeo client secret.
 * @param {string} channel.vimeo.accessToken The Vimeo access token.
 * @return {void}
 */
function initClient(channel) {
  const { vimeo } = channel || {};
  const { clientID, clientSecret, accessToken } = vimeo || {};
  vimeoClient = new Vimeo(
    clientID || process.env.VIMEO_CLIENT_ID,
    clientSecret || process.env.VIMEO_CLIENT_SECRET,
    accessToken || process.env.VIMEO_ACCESS_TOKEN
  );
}

export default {
  getVimeoId,
  getVideo,
  getMediaLink,
  getVideoFile,
  initClient,
};
