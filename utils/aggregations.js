/* eslint-disable no-console */
import _ from 'lodash';

export function getSearchField(fields, fieldName = 'searchField') {
  return {
    [fieldName]: {
      $concat: fields.map((field) => ({ $ifNull: [`$${field}`, ''] })),
    },
  };
}

export function getSearchFilter(value, fieldName = 'searchField') {
  if (!value) return null;
  return {
    [fieldName]: {
      $regex: value,
      $options: 'i',
    },
  };
}

export function getDateFilter(dateFrom, dateTo, field = 'createdAt') {
  if (dateFrom) {
    dateFrom = new Date(dateFrom);
    dateFrom.setHours(0, 0, 0, 0);
  }
  if (dateTo) {
    dateTo = new Date(dateTo);
    dateTo.setHours(23, 59, 59, 999);
  }

  if (dateFrom && dateTo) {
    return {
      [field]: {
        $gte: dateFrom,
        $lte: dateTo,
      },
    };
  }

  if (dateFrom) {
    return {
      [field]: {
        $gte: dateFrom,
      },
    };
  }

  if (dateTo) {
    return {
      [field]: {
        $lte: dateTo,
      },
    };
  }
  return {};
}

/**
 * Get the aggregation pipeline for the list of entities
 *
 */
export async function getCount(aggregation) {
  const model = aggregation._model;
  const result = await model.aggregate(aggregation.pipeline()).count('count');
  return result[0]?.count || 0;
}

export async function explainAggregation(aggregation, consoleLog = true) {
  const model = aggregation._model;
  const result = await model
    .aggregate(aggregation.pipeline())
    .explain('executionStats');
  if (consoleLog) {
    console.log('====================================================');
    console.log(result?.stages);
    console.log('====================================================');
  }
  return result?.stages;
}

/**
 * Get the projection for the aggregation pipeline
 * @param {Array} fields The fields to project
 * @returns {Object} The projection object
 */
export function getProjection(fields) {
  if (_.isEmpty(fields)) return null;
  return fields.reduce((acc, field) => {
    acc[field] = 1;
    return acc;
  }, {});
}

// Values to check for each type
const typeCheckValueMap = {
  string: '',
  object: {},
  number: 0,
};

/**
 * Get the translation done field for the aggregation pipeline
 * @param {Array} fields The fields to check
 * @returns {Object} The aggregation condition
 */
export function getTranslationDoneField(fields = []) {
  if (_.isEmpty(fields)) return {};

  return {
    $cond: {
      if: {
        $and: fields.map((field) => {
          const chechValue = typeCheckValueMap[field.type];
          return {
            $ne: [{ $ifNull: [`$${field.name}`, chechValue] }, chechValue], // Check if field is empty
          };
        }),
      },
      then: true,
      else: false,
    },
  };
}
