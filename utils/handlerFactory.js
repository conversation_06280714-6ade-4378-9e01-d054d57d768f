import APIFeatures from '#utils/apiFeatures.js';
import { errors } from '#utils/appError.js';

import { isValidObjectId } from './api/mongoose/id.js';

/**
 * Returns a list of records
 * @param {Object} Model The data model/schema
 * @param {Object} req The request object
 * @param {Object} options Allows to override or extend query's defaults
 * @param {Object} options.filter Extra filters
 * @param {Boolean} options.filterByEntity Defines if it should get results for the current entity
 * @param {Object[]|String[]} options.populate Relations to populate
 * @param {String[]} options.sort Sorting order
 * @param {String[]} options.fields Returns only the specified fields
 * @returns {Promise<{ count: number, items: array }>} The response object
 */
export const getAll = async (Model, req, options = {}) => {
  const opts = {
    filter: {},
    filterByEntity: false,
    populate: [],
    sort: [],
    fields: [],
    ...(options || {}),
  };

  const _filter = {
    ...(opts.filterByEntity ? { entity: req.entity._id } : {}),
    ...(opts.filter || {}),
  };

  const features = new APIFeatures(Model.find(_filter), req.query)
    .filter()
    .populate(opts.populate)
    .collation({ locale: 'en' })
    .sort(opts.sort)
    .limitFields(opts.fields)
    .pagination();

  const featuresCount = new APIFeatures(
    Model.find(_filter),
    req.query
  ).filter();

  // Execute queries
  const items = await features.query;
  const count = await featuresCount.query.countDocuments();

  // Return values
  return { count, items };
};

/**
 * Returns a single record
 * @param {Object} Model The data model/schema
 * @param {Object} req The request object
 * @param {Object} options Allows to override or extend query's defaults
 * @param {String} options.id To get an specific record by id
 * @param {String} options.paramId Request parameter used to get record's id
 * @param {Object} options.filter Extra filters
 * @param {Boolean} options.filterByEntity Defines if it should get results for the current entity
 * @param {Object[]|String[]} options.populate Relations to populate
 * @param {String[]} options.fields Returns only the specified fields
 * @returns {Promise{Object}} The response object
 */
export const getOne = async (Model, req, options = {}) => {
  const opts = {
    id: undefined,
    paramId: 'id',
    filter: {},
    filterByEntity: false,
    populate: [],
    fields: [],
    ...(options || {}),
  };

  if (!opts.paramId) throw errors.params(['paramId']);

  const _id = opts.id ?? req.params[opts.paramId];
  const validId = isValidObjectId(_id);

  if (!validId) throw errors.not_found();

  let query = Model.findOne({
    _id,
    ...(opts.filterByEntity ? { entity: req.entity._id } : {}),
    ...(opts.filter || {}),
  });

  opts.populate.forEach((pop) => {
    query = query.populate(pop);
  });

  query = query.select(
    Array.isArray(opts.fields) && opts.fields.length > 0
      ? opts.fields.join(' ')
      : typeof opts.fields === 'string'
        ? opts.fields.replace(/,/g, ' ')
        : '-__v'
  );

  const doc = await query;

  if (!doc) throw errors.not_found(Model, _id);

  return doc;
};

/**
 * Updates a single record
 * @param {Object} Model The data model/schema
 * @param {Object} req The request object
 * @param {Object} options Allows to override or extend query's defaults
 * @param {Object} options.filter Extra filters
 * @param {Boolean} options.filterByEntity Defines if it should get results for the current entity
 * @param {String} options.paramId Request parameter used to get record's id
 * @param {Object[]|String[]} options.populate Relations to populate
 * @returns {Promise{Object}} The response object
 */
export const updateOne = async (Model, req, options = {}) => {
  const opts = {
    paramId: 'id',
    filter: {},
    filterByEntity: false,
    populate: [],
    ...(options || {}),
  };

  const _id = req.params[opts.paramId];

  let query = Model.findOneAndUpdate(
    {
      _id,
      ...(opts.filterByEntity ? { entity: req.entity._id } : {}),
      ...(opts.filter || {}),
    },
    req.body,
    {
      new: true,
      runValidators: true,
    }
  );

  opts.populate.forEach((pop) => {
    query = query.populate(pop);
  });

  const doc = await query;

  if (!doc) throw errors.not_found(Model, _id);

  return doc;
};

/**
 * Sets a single record as deleted
 * @param {Object} Model The data model/schema
 * @param {Object} req The request object
 * @param {Object} options Allows to override or extend query's defaults
 * @param {Object} options.filter Extra filters
 * @param {Boolean} options.filterByEntity Defines if it should get results for the current entity
 * @param {String} options.paramId Request parameter used to get record's id
 * @returns {Promise{Object}} The response object
 */
export const deleteOne = async (Model, req, options = {}) => {
  req.body = { deleted: true };

  return await updateOne(Model, req, options);
};

/**
 * Sets a single record as NOT deleted
 * @param {Object} Model The data model/schema
 * @param {Object} req The request object
 * @param {Object} options Allows to override or extend query's defaults
 * @param {Object} options.filter Extra filters
 * @param {Boolean} options.filterByEntity Defines if it should get results for the current entity
 * @param {String} options.paramId Request parameter used to get record's id
 * @returns {Promise{Object}} The response object
 */
export const restoreOne = async (Model, req, options = {}) => {
  req.body = { deleted: false };

  return await updateOne(Model, req, options);
};

/**
 * Sets a single record as NOT enabled
 * @param {Object} Model The data model/schema
 * @param {Object} req The request object
 * @param {Object} options Allows to override or extend query's defaults
 * @param {Object} options.filter Extra filters
 * @param {Boolean} options.filterByEntity Defines if it should get results for the current entity
 * @param {String} options.paramId Request parameter used to get record's id
 * @returns {Promise{Object}}
 */
export const disableOne = async (Model, req, options = {}) => {
  req.body = { enabled: false };

  return await updateOne(Model, req, options);
};

/**
 * Sets a single record as enabled
 * @param {Object} Model The data model/schema
 * @param {Object} req The request object
 * @param {Object} options Allows to override or extend query's defaults
 * @param {Object} options.filter Extra filters
 * @param {Boolean} options.filterByEntity Defines if it should get results for the current entity
 * @param {String} options.paramId Request parameter used to get record's id
 * @returns {Promise{Object}}
 */
export const enableOne = async (Model, req, options = {}) => {
  req.body = { enabled: true };

  return await updateOne(Model, req, options);
};

export default {
  getAll,
  getOne,
  updateOne,
  deleteOne,
  restoreOne,
  disableOne,
  enableOne,
};
