import _ from 'lodash';
import { encode } from 'blurhash';
import sharp from 'sharp';

import Logger from '#utils/logger.js';

// Image manipulation
export const getImageSizeData = async (imagePath) =>
  await sharp(imagePath).metadata();

export const cropImage = async (imagePath, targetPath, width, height, x, y) => {
  const buff = await sharp(imagePath)
    .extract({ width, height, left: x, top: y })
    .toBuffer();

  return await sharp(buff).toFile(targetPath);
};

export const resizeImage = async (
  imagePath,
  targetPath,
  width,
  height,
  fit = 'inside'
) => {
  const buff = await sharp(imagePath)
    .resize(width, height, { fit: fit || 'inside' })
    .toBuffer();

  return await sharp(buff).toFile(targetPath);
};

export const getCropValues = (crop, imageWidth, imageHeight) => {
  let x = _.toInteger(crop.x);
  let y = _.toInteger(crop.y);
  let cropWidth = _.toInteger(crop.width);
  let cropHeight = _.toInteger(crop.height);

  // Make sure the crop values are valid
  x = x > imageWidth || x < 0 ? 0 : x;
  y = y > imageHeight || y < 0 ? 0 : y;

  const maxWidth = imageWidth - x;
  if (cropWidth > maxWidth || cropWidth < 10) {
    cropWidth = maxWidth;
  }

  const maxHeight = imageHeight - y;
  if (cropHeight > maxHeight || cropHeight < 10) {
    cropHeight = maxHeight;
  }

  return { x, y, cropWidth, cropHeight };
};

export const encodeImageToBlurhash = (imagePath) =>
  new Promise((resolve) => {
    sharp(imagePath)
      .resize(300)
      .raw()
      .ensureAlpha()
      .toBuffer((err, buffer, info) => {
        if (err) {
          Logger.error('Error generating blurhash!', err);
          return resolve(null);
        }

        const { width, height } = info;
        const blurhash = encode(
          new Uint8ClampedArray(buffer),
          width,
          height,
          4,
          3
        );

        resolve(blurhash);
      });
  });

// File Types

// Images
const jpeg = ['image/jpg', 'image/jpeg'];
const png = 'image/png';
const gif = 'image/gif';
const svg = 'image/svg+xml';
const tiff = 'image/tiff';
const webp = 'image/webp';

// Files
const xml = ['application/xml', 'text/xml'];
const json = ['application/json', 'application/ld+json'];
const zip = 'application/zip';
const rar = 'application/vnd.rar';
const sevenZ = 'application/x-7z-compressed';

// Audio
const mp3 = 'audio/mpeg';
const m4a = 'audio/mp4';

// Video
const mp4 = 'video/mp4';
const mov = 'video/quicktime';
const avi = 'video/x-msvideo';

// Documents
const pdf = 'application/pdf';
const ai = 'application/pdf';
const epub = 'application/epub+zip';

// Fonts
const woff = 'font/woff';
const woff2 = 'font/woff2';
const ttf = 'font/ttf';
const otf = 'font/otf';

// Office
const doc = 'application/msword';
const xls = 'application/vnd.ms-excel';
const ppt = 'application/vnd.ms-powerpoint';

const officePrefix = `application/vnd.openxmlformats-officedocument.`;
const docx = `${officePrefix}wordprocessingml.document`;
const xlsx = `${officePrefix}spreadsheetml.sheet`;
const pptx = `${officePrefix}presentationml.presentation`;

// iWork
const applePrefix = `application/vnd.apple`;
const pages = `${applePrefix}.pages`;
const numbers = `${applePrefix}.numbers`;
const keynote = `${applePrefix}.keynote`;

export const fileTypes = {
  // Images
  jpg: [...jpeg],
  png: [png],
  gif: [gif],
  svg: [svg],
  tiff: [tiff],
  webp: [webp],
  anyImage: [...jpeg, png, gif, svg, tiff, webp],

  // Files
  zip: [zip],
  xml: [...xml],
  json: [...json],
  anyCompressed: [zip, sevenZ, rar],

  // Audio
  mp3: [mp3],
  m4a: [m4a],
  anyAudio: [mp3, m4a],

  // Video
  mp4: [mp4],
  mov: [mov],
  avi: [avi],
  anyVideo: [mp4, mov, avi],

  // Documents
  pdf: [pdf],
  ai: [ai],
  doc: [doc, docx],
  docx: [docx],
  pages: [pages],
  anyDocument: [pdf, doc, docx, pages, ai, epub],

  // Stylesheets
  xls: [xls, xlsx],
  xlsx: [xlsx],
  numbers: [numbers],
  anySpreadsheet: [xls, xlsx, numbers],

  // Presentations
  ppt: [ppt, pptx],
  pptx: [pptx],
  keynote: [keynote],
  anyPresentation: [ppt, pptx, keynote],

  // Fonts
  woff: [woff],
  woff2: [woff2],
  ttf: [ttf],
  otf: [otf],
  anyFont: [woff, woff2, ttf, otf],
};

export default {
  getImageSizeData,
  cropImage,
  resizeImage,
  getCropValues,
  encodeImageToBlurhash,
  fileTypes,
};
