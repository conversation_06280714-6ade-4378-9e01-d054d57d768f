const formatKeys = {
  p: 'prefix',
  f: 'firstName',
  m: 'middleName',
  l: 'lastName',
  s: 'suffix',
};

/**
 * Get the full name of a person, based on the format provided
 * @param {Object} person The person object
 * @param {String} format The format of the full name
 * @returns {String} The full name of the person
 *
 * @example
 * const person = { firstName: 'John', lastName: 'Doe', suffix: 'Jr' };
 * const format = '%f %l'; // Pick only the first name and last name
 * const fullName = getFullName(person, format); // '<PERSON>'
 */
export default function getFullName(person = {}, format = '%p %f %m %l %s') {
  if (typeof person !== 'object' || typeof format !== 'string') return '';

  // If the person has a full name, return it as is
  if (person?.fullName) return person.fullName;

  let output = format;

  for (const key of Object.keys(formatKeys)) {
    output = output.replace(`%${key}`, person?.[formatKeys[key]] || '');
  }

  return output.replace(/\s+/g, ' ').trim();
}
