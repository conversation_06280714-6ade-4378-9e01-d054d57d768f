import { htmlToText } from 'html-to-text';
import nodemailer from 'nodemailer';

import {
  getEmailColors,
  getEmailLogo,
} from '#modules/users/utils/ancestors.js';
import renderEmail from '#templates/utils/renderEmail.js';

import Logger, { logError } from '#utils/logger.js';
import io from './socket.js';
import { getImageUrl } from './images.js';

const nodeEnv = process.env.NODE_ENV || 'development';
const emailTest = process.env.EMAIL_TEST || false;
const devEmail = process.env.DEV_EMAIL || '<EMAIL>';

export async function sendEmail({
  to = '',
  subject = '',
  templateName = '',
  templateValues = {},
  language = 'en',
  entity = {},
  site = {},
}) {
  if (!to) return;

  // Get logo and colors (from entity or ancestors)
  const entityEmailConfigLogo = await getEmail<PERSON>ogo(entity);
  const entityEmailConfigColors = await getEmailColors(entity);

  // Set email configuration (from site or entity)
  const emailConfig = {
    from: site?.email?.from || entity?.config?.email?.from,
    fromName: (site?.email?.from ? site.title : entity?.name) || 'AWE',
    colors: site?.email?.appearance?.colors || entityEmailConfigColors,
    logo:
      site?.email?.appearance?.logo ||
      site?.design?.header?.logo ||
      entityEmailConfigLogo ||
      entity?.logo,
    footer: {
      emailFooter: (
        site?.email?.appearance?.footerText ||
        entity?.config?.email?.appearance?.footerText ||
        ''
      ).split('\n'),
      email: site?.email?.from || entity?.config?.email?.from || '',
      phone: site ? '' : entity?.phone || '',
    },
  };

  // Get email configuration
  const host = process.env.EMAIL_HOST || 'localhost';
  const port = process.env.EMAIL_PORT || 587;
  const user = process.env.EMAIL_USER || 'user';
  const pass = process.env.EMAIL_PASS || 'pass';

  // Render html
  renderEmail({
    language,
    template: templateName,
    colors: emailConfig.colors || {},
    values: {
      subject,
      entityInfo: emailConfig.footer,
      logoURL: getImageUrl(emailConfig.logo) || process.env.DEFAULT_LOGO_URL,
      ...templateValues,
    },
  })
    .then((html) => {
      // Send email using the html rendered
      const transporter = nodemailer.createTransport({
        host,
        port,
        auth: { user, pass },
      });

      transporter
        .sendMail({
          from: emailConfig.from
            ? `${emailConfig.fromName} <${emailConfig.from}>`
            : process.env.EMAIL_ADDRESS,
          to,
          subject,
          html,
          text: htmlToText(html),
        })
        .catch((e) => {
          Logger.error('Could not send email!', e);
        });
    })
    .catch((err) => {
      Logger.error(
        'Could not render template!',
        { template: templateName },
        err
      );
    });
}

export const notifyAdmins = ({
  subject = '',
  templateName = '',
  templateValues = {},
  entity = {},
}) => {
  if (nodeEnv === 'production' || emailTest) {
    if (templateName === 'requestError') {
      logError(`Error sending email notification to ${devEmail}...`, {
        subject,
        templateName,
        templateValues,
      });
    }

    sendEmail({
      language: 'en',
      entity,
      to: devEmail,
      subject,
      templateName,
      templateValues,
    });
  }
};

export const sendEvent = ({ to = '', event = '', data = null } = {}) => {
  if (to === 'all') {
    io.of('/notifications').emit(event, data);
  } else {
    io.of('/notifications').to(to).emit(event, data);
  }
};

/**
 * Sends a notification to a onesignal user.
 *
 *
 */
export async function sendPushNotification({
  site = {},
  to = [], // Array of webUser ids
  headings = {},
  contents = {}, // Format should be { en: 'All languages message' } unless more than one language is supported
  targetUrl = '', // TODO: Use this to open a specific page when the notification is clicked
}) {
  const { onesignalAppId, onesignalRestApiKey } = site.mobileApp || {};
  if (onesignalAppId && onesignalRestApiKey && to.length > 0) {
    try {
      const url = 'https://api.onesignal.com/notifications';
      const options = {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Authorization': `Basic ${onesignalRestApiKey}`,
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          headings,
          contents,
          include_aliases: {
            external_id: to,
          },
          target_channel: 'push',
          app_id: onesignalAppId,
          url: targetUrl,
        }),
      };

      const response = await fetch(url, options);
      const json = await response.json();
      if (json.errors) {
        Logger.error('errors:', json.errors);
      }
    } catch (error) {
      Logger.error(`Error when trying to send push notification: ${error}`);
    }
  }
}

export default {
  sendEmail,
  sendEvent,
  notifyAdmins,
  sendPushNotification,
};
