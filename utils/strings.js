import _ from 'lodash';
import { PASSWORD_REGEX } from './constants.js';

const numbersChars = '0123456789';
const specialChars = '.,/?!@#$%^&*()_-+=[]{}"|<>~';
const lowerChars = 'abcdefghijklmnopqrstuvwxyz';
const upperChars = lowerChars.toUpperCase();
const passwordChars = [numbersChars, lowerChars, upperChars, specialChars].join(
  ''
);
const noSpecialChars = [numbersChars, lowerChars, upperChars].join('');

/**
 * Converts a string intro a valid slug
 *
 * @param {String} string The string to convert in a slug
 * @param {Number} [maxLength=120] - Maximum slug string lenght
 */
export const slugify = (string, maxLength = 120) => {
  const slug = _.kebabCase(
    string
      .toString()

      // Replace special characters
      .replace('Ä', 'Ae')
      .replace('Ö', 'Oe')
      .replace('Ü', 'Ue')
      .replace('ä', 'ae')
      .replace('ö', 'oe')
      .replace('ü', 'ue')
      .replace('ß', 'ss')
      .normalize('NFKC')
      .replace(/&|\./g, '-')
  )
    .replace(/--+/g, '-') // Replace multiple - with single -
    .replace(/^-+/, '') // Trim - from start of text
    .replace(/-+$/, '') // Trim - from end of text
    .replace(/  +/g, ' ') // Replace multiple spaces with single space
    .replace(/undefined/g, '')

    .trim();

  const maxIndex = slug.indexOf('-', maxLength);

  return maxIndex > 0 ? slug.substring(0, maxIndex) : slug;
};

export const randomString = ({
  chars = noSpecialChars,
  length = null,
  min = 8,
  max = 16,
} = {}) => {
  let string = '';

  if (!length) {
    const maximal = Math.floor(Math.random() * max) + 1;
    length = maximal < min ? min : maximal;
  }

  for (let i = 0; i < length; i += 1) {
    const rnum = Math.floor(Math.random() * chars.length);
    string += chars.substring(rnum, rnum + 1);
  }

  return string;
};

export const randomPassword = ({ min = 10, max = 20, attempt = 1 } = {}) => {
  const password = randomString({ chars: passwordChars, min, max });

  // to prevent an infinite loop...
  if (attempt > 50) return null;

  return password.match(PASSWORD_REGEX)
    ? password
    : randomPassword({ min, max, attempt: attempt + 1 });
};

export const uniquifySlug = (slug = '') =>
  `${slug}-${randomString({ chars: numbersChars, length: 6 })}`;

export const escapeSpecialChars = (string = '') =>
  string.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');

/**
 * Converts an array of strings into a single string. Values are trimmed and empty strings or nullish values are removed. *
 * @param {Array} [array=[]]  - The array of strings *
 * @param {String} [separator=', '] - The separator to use
 * @returns {String}
 */
export function stringArrayToString(array = [], separator = ', ') {
  return array
    .filter(Boolean)
    .map((value) => value.toString().trim())
    .join(separator);
}

export default {
  slugify,
  randomPassword,
  randomString,
  uniquifySlug,
  escapeSpecialChars,
  stringArrayToString,
};
