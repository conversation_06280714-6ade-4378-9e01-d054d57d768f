class APIFeatures {
  constructor(query, queryString) {
    this.query = query;
    this.queryString = queryString;
  }

  filter() {
    const queryObj = JSON.parse(this.queryString.filter || '{}');

    this.query = this.query.find(queryObj);

    return this;
  }

  collation(collation = {}) {
    this.query = this.query.collation(collation);

    return this;
  }

  sort(fields = []) {
    this.query = this.query.sort(
      `${
        this.queryString.sort
          ? this.queryString.sort.replace(/,/g, ' ')
          : Array.isArray(fields) && fields.length > 0
            ? fields.join(' ')
            : '-createdAt'
      } _id`
    );

    return this;
  }

  limitFields(fields = []) {
    this.query = this.query.select(
      Array.isArray(fields) && fields.length > 0
        ? fields.join(' ')
        : this.queryString.fields
          ? this.queryString.fields.replace(/,/g, ' ')
          : '-__v'
    );

    return this;
  }

  pagination() {
    const page = this.queryString.page * 1 || 1;
    const limit = this.queryString.limit * 1 || 100;
    const skip = this.queryString.skip
      ? this.queryString.skip * 1
      : (page - 1) * limit;

    this.query = this.query.limit(limit).skip(skip);

    return this;
  }

  populate(fields = []) {
    fields.forEach((field) => {
      this.query = this.query.populate(field);
    });

    return this;
  }
}

export default APIFeatures;
