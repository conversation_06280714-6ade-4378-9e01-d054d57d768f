import { DateTime } from 'luxon';
import { logError } from '#utils/logger.js';

/**
 * @typedef {Object} DateObject An object representing a date
 * @property {number} year - Year (0-9999)
 * @property {number} month - Month (0-11)
 * @property {number} date - Day (1-31)
 * @property {number} [hour] - Hour (0-23)
 * @property {number} [minute] - Minute (0-59)
 * @property {number} [second] - Second (0-59)
 * @property {number} [millisecond] - Millisecond (0-999)
 * @property {string} [zone] - Timezone (e.g. 'UTC', 'Europe/Berlin')
 */

/**
 * Converts a date to a Luxon's DateTime object
 *
 * The input can be:
 *   - a DateTime object: returned without modification
 *   - a Date object    : Converted to DateTime
 *   - an array         : Interpreted as [year,month,day]. NOTE: month is 0-11.
 *   - a number         : Interpreted as number of milliseconds since 1 Jan 1970 (a timestamp)
 *   - a string         : Any format supported by the javascript engine, like "YYYY/MM/DD", "MM/DD/YYYY", "Jan 31 2009" etc.
 *   - an object        : Interpreted as an object with year, month and date attributes.  **NOTE** month is 0-11.
 *
 * @param {DateTime|Date|Array|Number|String|DateObject} date Date to convert
 * @returns {DateTime|NaN} Luxon's DateTime object
 */
export function convert(date) {
  return date.constructor === DateTime // When already a Luxon DateTime object
    ? date // return as is
    : date.constructor === Date // When a date object
      ? DateTime.fromMillis(date.valueOf()) // ... convert to milliseconds and then to DateTime
      : date.constructor === Array // When an array i.e. [2020, 1, 2]
        ? DateTime.utc(
            date[0],
            date[1],
            date[2],
            date[3],
            date[4],
            date[5],
            date[6]
          ).setZone(date[7] || 'UTC') // ... convert using UTC, // with year, month, day, hour, minute, second, millisecond and optional zone attributes
        : date.constructor === Number // When a number, i.e. 1234567890
          ? DateTime.fromMillis(date) // ... convert from milliseconds
          : date.constructor === String // When a string, i.e. "2014-06-13T12:34:56.789"
            ? DateTime.fromISO(date) // ... convert from ISO format
            : typeof date === 'object' // When an object, i.e. {year: 2020, month: 1, day: 2}
              ? DateTime.utc(
                  date.year,
                  date.month,
                  date.date,
                  date.hour,
                  date.minute,
                  date.second,
                  date.millisecond
                ).setZone(date.zone || 'UTC')
              : NaN; // And retun NaN if invalid
}

/**
 * Checks if a date is valid
 * @param {*} date - Date to check
 * @returns `boolean` - True if the date is valid, false otherwise
 */
export function isValidDate(date) {
  if (date) {
    try {
      return convert(date).isValid;
    } catch (error) {
      logError('Error trying to convert date:', date, 'Error:', error);
      return false;
    }
  } else {
    return false;
  }
}

export const startOfDay = function (date, zone) {
  return convert(date, zone).startOf('day');
};

export const endOfDay = function (date, zone) {
  return convert(date, zone).endOf('day');
};

/**
 * Provides the first day of the month for the current date
 * @returns `DateTime` - Start of the month for the current date
 */
export function firstDayOfCurrentMonth() {
  return DateTime.utc().startOf('month');
}

/**
 * Localizes a date based on provided locale
 * @param {*} date - Date to format
 * @param {*} locale - Locale to use
 * @returns `string` - Localized date string
 */
export function toLocaleStringFull(date, locale = 'en-US') {
  return convert(date).setLocale(locale).toLocaleString(DateTime.DATE_MED);
}

/**
 * Converts a date to a string in the format YYYY-MM-DD HH:MM:SS
 * @param {*} date - Date to convert
 * @param {*} locale - Locale to use
 * @returns `string` - Date in the format YYYY-MM-DD HH:MM:SS
 */
export function toLocaleStringFullWithSeconds(date, locale = 'en-US') {
  return convert(date)
    .setLocale(locale)
    .toLocaleString(DateTime.DATETIME_FULL_WITH_SECONDS);
}

/**
 * Converts a date to a string in the format YYYY-MM-DD
 * @param {*} date - Date to convert
 * @returns `string` - Date in the format YYYY-MM-DD
 */
export function toLocaleStringShort(date, locale = 'en-US') {
  return convert(date).setLocale(locale).toLocaleString(DateTime.DATE_SHORT);
}

/**
 * Converts a date to a string to be used in logs
 * @param {*} date
 * @param {String} locale
 * @returns `string` - Date in the format YYYY-MM-DD HH:MM:SS
 */
export function toLogDate(date, locale = 'de-DE') {
  // Setting the locale so it shows time in 24h format
  date = convert(date).setLocale(locale);

  return `${date.toISODate()} ${date.toLocaleString(
    DateTime.TIME_WITH_SECONDS
  )}`;
}

/**
 *  Compare two dates (could be of any type supported by the convert function above)
 * @param {*} a - Date A
 * @param {*} b - Date B
 * @returns `number` - -1 if a < b, 0 if a = b, 1 if a > b, NaN if a or b is an illegal date
 */
export function compare(a, b) {
  a = convert(a).valueOf();
  b = convert(b).valueOf();

  return Number.isFinite(a) && Number.isFinite(b) ? (a > b) - (a < b) : NaN;
}

/**
 * Compare two dates with a minimum threshold in miliseconds:
 * @param {*} start - Start date
 * @param {*} end- End date
 * @param {*} minDiff- Minimum difference in miliseconds
 * @returns `boolean | NaN` - if difference between start and end is equal or bigger than minDiff: `true`, if difference between start and end is less than minDiff: `false`, and if one or more of the dates is illegal: `NaN`.
 */
export function haveMinDiff(start, end, minDiff) {
  const a = convert(start).valueOf();
  const b = convert(end).valueOf();

  // NOTE: The code inside isFinite does an assignment (=).
  return Number.isFinite(a) && Number.isFinite(b) ? b - a >= minDiff : NaN;
}

/**
 * Checks if date in d is between dates in start and end.
 * @param {*} date - Date to check
 * @param {*} start - Start date
 * @param {*} end - End date
 * @returns `boolean | NaN` - Returns if `date` is between start and end (inclusive): `true`, if `date` is before start or after end: `false`, and if one or more of the dates are illegal: `NaN`.
 */
export function inRange(date, start, end) {
  date = convert(date).valueOf();
  start = convert(start).valueOf();
  end = convert(end).valueOf();

  return Number.isFinite(date) && Number.isFinite(start) && Number.isFinite(end)
    ? start < date && date < end
    : NaN;
}

/**
 * Convert a date to unix timestamp
 * @param {*} date - Date to convert
 * @returns `number` - Unix timestamp
 */
export function toUnixDate(date) {
  return Math.floor(new Date(date).getTime() / 1000);
}

/**
 * Check if two dates are the same day
 * @param {Date} dateA
 * @param {Date} dateB
 * @returns `boolean` - True if the dates are the same day, false otherwise
 */
export function isSameDay(dateA, dateB) {
  if (!(DateTime.isDateTime(dateA) || DateTime.isDateTime(dateB))) return false;

  return (
    dateA.hasSame(dateB, 'year') &&
    dateA.hasSame(dateB, 'month') &&
    dateA.hasSame(dateB, 'day')
  );
}

/**
 * Get ISO date string from duration
 * @param {Object} duration - Duration object (e.g. { weeks: 2 }, { months: 1, days: 5 }, etc.)
 * @returns {string|undefined} - ISO date string
 * @example
 * getRelativeISODate({ weeks: 1 }); // '2024-02-01T00:00:00Z'
 */
export function getRelativeISODate(duration) {
  if (!duration) return undefined;
  return DateTime.now().minus(duration).toISODate({ format: 'extended' });
}

/**
 * Convert a date string to a MongoDB date object.
 * If the date is not provided, the current date will be used.
 * @param {String} date The date string to convert
 * @returns {Object} The MongoDB date object
 */
export function toMongoDateObject(date) {
  return {
    $date: date || DateTime.local().toISO(), // If the date is not provided, use the current date
  };
}

export default {
  compare,
  convert,
  firstDayOfCurrentMonth,
  getRelativeISODate,
  haveMinDiff,
  inRange,
  isSameDay,
  isValidDate,
  toLocaleStringFull,
  toLocaleStringFullWithSeconds,
  toLocaleStringShort,
  toLogDate,
  toUnixDate,
};
