import isEmpty from 'lodash/isEmpty.js';

/**
 * @typedef {object} SearchFieldRegex Search field regex object
 * @property {String} $regex The regex string to search for
 * @property {'i'} $options The regex options
 */

/**
 * @typedef {object} SearchFilter Search filter object with $or operator
 * @property {Object.<string,SearchFieldRegex>} $or The $or operator with search fields
 */

/**
 * Search filter helper for mongoose queries
 * @param {Object} params - The search parameters
 * @param {String} params.search - The search string
 * @param {String[]} params.fields - The fields to search in
 * @returns {SearchFilter} The search filter object with $or operator
 */
export default function getSearchFilters({ search = '', fields = [] } = {}) {
  if (!search || isEmpty(fields)) return {};

  return {
    $or: fields.map((field) => ({
      [field]: {
        $regex: search, // TODO: check for special characters
        $options: 'i',
      },
    })),
  };
}
