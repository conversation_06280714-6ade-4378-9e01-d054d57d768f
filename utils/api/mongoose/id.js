import Logger from '#utils/logger.js';
import mongoose from 'mongoose';

/**
 * Check if a string is a valid ObjectId.
 * @param {String} id - String to check if it is a valid ObjectId.
 * @returns {Boolean} - True if the string is a valid ObjectId, false otherwise.
 */

export function isValidObjectId(id) {
  return mongoose.Types.ObjectId.isValid(id); // TODO: migrate to mongoose.isValidObjectId() when MongoDB v5 is available in Scalingo
}

/**
 * Convert string to ObjectId.
 * @param {String} id - String to convert to ObjectId.
 * @param {Boolean} silent - If true, suppress error logging for invalid ObjectId.
 * @returns {ObjectId} - Converted ObjectId.
 */
export function toObjectId(id, silent = false) {
  if (!isValidObjectId(id)) {
    if (!silent) {
      Logger.error(`Invalid ObjectId: ${id}`);
    }
    return null;
  }

  return new mongoose.Types.ObjectId(id);
}

/**
 * Get only valid ObjectIds from an array of strings and convert them to ObjectIds.
 * @param {string[]} ids Array of strings to convert to ObjectIds.
 * @returns {ObjectId[]} Filtered array of converted ObjectIds.
 */
export function toObjectIds(ids = []) {
  // If the input is not an array or is an empty array, return an empty array
  if (!Array.isArray(ids) || ids.length === 0) {
    return [];
  }

  // Filter out invalid ObjectIds
  return ids.filter((id) => isValidObjectId(id)).map((id) => toObjectId(id));
}

/**
 * Convert string to a MongoDB ObjectId in JSON format
 * @param {String?} id - The string id (optional, if not provided, a new ObjectId will be generated)
 * @returns {Object} - The ObjectId object
 */
export function toJsonObjectId(id) {
  if (!id) {
    id = newMongoDBUUid();
  }

  return { $oid: id };
}

/**
 * Generates a valid MongoDB BSON UUID string using mongoose
 * @returns {String} - The generated UUID string
 */
export function newMongoDBUUid() {
  return new mongoose.Types.ObjectId().toString();
}
