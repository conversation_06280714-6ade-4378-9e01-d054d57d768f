import isEmpty from 'lodash/isEmpty.js';

/**
 * @typedef {Array<'active'|'disabled'| 'deleted'>} StatusesOptions
 */

/**
 * @typedef {object} ActiveStatusFilter
 * @property {true} enabled Enabled status filter with true value
 * @property {false} deleted Deleted status filter with false value
 */

/**
 * @type {ActiveStatusFilter} Active status filter with true enabled and false deleted values
 */
const activeStatusFilter = { enabled: true, deleted: false };

/**
 * @typedef {object} DisabledStatusFilter
 * @property {false} enabled Enabled status filter with false value
 * @property {false} deleted Deleted status filter with false value
 */

/**
 * @type {DisabledStatusFilter} Disabled status filter with false enabled and deleted values
 */
const disabledStatusFilter = { enabled: false, deleted: false };

/**
 * @typedef {object} DeletedStatusFilter
 * @property {true} deleted Deleted status filter with true value
 */

/**
 * @type {DeletedStatusFilter} Deleted status filter with true deleted value
 */
const deletedStatusFilter = { deleted: true };

/**
 * @typedef {object} NonDeletedStatusFilter
 * @property {false} deleted Deleted status filter with false value
 */
/**
 * @type {NonDeletedStatusFilter} Non-deleted status filter with false deleted value
 */
const nonDeletedStatusFilter = { deleted: false };

/**
 * Custom status filter with $or operator
 * @typedef {object} CustomStatusFilter
 * @property {Array<ActiveStatusFilter|DisabledStatusFilter|DeletedStatusFilter>} $or Custom status filter with $or operator
 */

/**
 * Status filter for mongoose queries
 * @typedef {NonDeletedStatusFilter|CustomStatusFilter} StatusFilter
 */

/**
 * Status filter helper for mongoose queries based on the statuses array provided
 *
 * @param {StatusesOptions} statuses Array of statuses
 * @returns {StatusFilter} Status filter object for a mongoose query
 */
export default function getStatusFilter(statuses = []) {
  // if no statuses provided, return non-deleted status filter (enabled and not deleted)
  if (isEmpty(statuses)) {
    return nonDeletedStatusFilter;
  }

  /** @type {Array<ActiveStatusFilter|DisabledStatusFilter|DeletedStatusFilter>} */
  const statusFilter = [];

  if (statuses.includes('active')) {
    statusFilter.push(activeStatusFilter);
  }
  if (statuses.includes('disabled')) {
    statusFilter.push(disabledStatusFilter);
  }
  if (statuses.includes('deleted')) {
    statusFilter.push(deletedStatusFilter);
  }

  return { $or: statusFilter };
}
