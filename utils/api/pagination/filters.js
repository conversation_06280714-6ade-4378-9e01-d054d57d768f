/**
 * Validates a parameter to be a number greater than or equal to a minimum value
 * @param {any} param The parameter to validate
 * @param {Number} defaultValue The default value to return when the parameter is invalid
 * @param {Function} condition The condition to check the parameter value against
 * @returns {Number} The validated parameter value
 */
function validateNumber(param, defaultValue, condition = () => false) {
  const value = parseInt(param, 10);
  return Number.isNaN(value) || condition(value) ? defaultValue : value;
}

/**
 * @typedef {object} PaginationFilter
 * @property {Number} limit The number of records to return
 * @property {Number} skip The number of records to skip
 */

/**
 * Creates filters for pagination
 * @param {Object} query The query to apply pagination to
 * @param {Object} paramsThe pagination parameters (`limit`, `page` and `skip`)
 * @param {Number} params.defaultLimit The number of records to return as a fallback value for limit (defaults to `10`)
 * @param {Number} params.limit The number of records to return (defaults to `defaultLimit`)
 * @param {Number} params.maxLimit The maximum limit allowed (defaults to `1000`)
 * @param {Number} params.page The page number to return (defaults to `1`)
 * @param {Number} params.skip The number of records to skip (defaults to `0` or `(page - 1) * limit`). This overrides `page`
 * @param {Number} params.maxSkip The maximum number of records to skip
 * @returns {PaginationFilter} The pagination filters
 */
export default function getPaginationFilters(
  params = {
    defaultLimit: 10,
    limit: 10,
    page: 1,
    skip: 0,
    maxLimit: 1000,
  }
) {
  const defaultLimit = validateNumber(params.defaultLimit, 10); // Limit defaults to 10
  const maxLimit = validateNumber(params.maxLimit, 1000); // Limit defaults to 10

  // Check if params are numbers, and set default values when they aren't
  let limit = validateNumber(params.limit, defaultLimit, (value) => value < 0); // Limit defaults to 10 and always greater than 0
  if (limit > maxLimit) {
    limit = maxLimit;
  }

  const page = validateNumber(params.page, 1, (value) => value <= 0); // Page defaults to 1

  const maxSkip = validateNumber(params.maxSkip, null); // Limit defaults to 10
  let skip = validateNumber(
    params.skip,
    (page - 1) * limit, // If skip is not a number, calculate the default from page and limit
    (value) => value <= 0 // Skip must be greater than 0
  );
  if (maxSkip && skip > maxSkip) {
    skip = maxSkip;
  }

  // Apply pagination to query
  return { limit, skip };
}
