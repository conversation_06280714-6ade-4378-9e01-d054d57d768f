import { expect, test, describe } from 'vitest';
import getPaginationFilters from './filters';

describe('getPaginationFilters', () => {
  test('should return the correct limit filter with varying limit input', () => {
    // No params
    expect(getPaginationFilters()).toEqual({ limit: 10, skip: 0 });

    // All (default) params
    expect(
      getPaginationFilters({
        defaultLimit: 10,
        limit: 10,
        page: 1,
        skip: 0,
        maxLimit: 1000,
      })
    ).toEqual({ limit: 10, skip: 0 });

    // Variations of limit
    expect(
      getPaginationFilters({
        defaultLimit: 20,
      })
    ).toEqual({ limit: 20, skip: 0 });
    expect(
      getPaginationFilters({
        limit: 20,
      })
    ).toEqual({ limit: 20, skip: 0 });
    expect(
      getPaginationFilters({
        limit: 'foo',
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        maxLimit: 50,
        limit: 51,
      })
    ).toEqual({ limit: 50, skip: 0 });
    expect(
      getPaginationFilters({
        limit: -1,
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        limit: null,
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        limit: 0,
      })
    ).toEqual({ limit: 0, skip: 0 });
  });

  test('should return the correct skip filter with varying skip input', () => {
    // Numeric skip values
    expect(
      getPaginationFilters({
        skip: 0,
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        skip: -1,
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        skip: 10,
      })
    ).toEqual({ limit: 10, skip: 10 });
    expect(
      getPaginationFilters({
        skip: 10,
        maxSkip: 9,
      })
    ).toEqual({ limit: 10, skip: 9 });

    // Incorrect or string skip values
    expect(
      getPaginationFilters({
        skip: '10',
      })
    ).toEqual({ limit: 10, skip: 10 });
    expect(
      getPaginationFilters({
        skip: 'foo',
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        skip: 10,
        maxSkip: 'foo',
      })
    ).toEqual({ limit: 10, skip: 10 });
    expect(
      getPaginationFilters({
        skip: null,
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        skip: new Date(),
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        skip: [1, 2], // ParseInt will return 1
      })
    ).toEqual({ limit: 10, skip: 1 });
  });

  test('should return the correct skip filter with varying page input', () => {
    // Numeric page values
    expect(
      getPaginationFilters({
        page: 1,
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        page: -1,
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        page: 2,
      })
    ).toEqual({ limit: 10, skip: 10 });
    expect(
      getPaginationFilters({
        limit: 20,
        page: 2,
      })
    ).toEqual({ limit: 20, skip: 20 });
    expect(
      getPaginationFilters({
        limit: 0,
        page: 2,
      })
    ).toEqual({ limit: 0, skip: 0 });

    // Incorrect or string page values
    expect(
      getPaginationFilters({
        page: '1',
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        page: null,
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        page: new Date(),
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        page: [1, 2],
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        limit: '20',
        page: '2',
      })
    ).toEqual({ limit: 20, skip: 20 });
    expect(
      getPaginationFilters({
        page: 'two',
      })
    ).toEqual({ limit: 10, skip: 0 });
    expect(
      getPaginationFilters({
        limit: 'twenty',
        page: 'two',
      })
    ).toEqual({ limit: 10, skip: 0 });
  });

  test('should return the correct skip filter with varying skip, limit and page combination input', () => {
    // Numeric skip values
    expect(
      getPaginationFilters({
        skip: 0,
        limit: 100,
        page: 1,
      })
    ).toEqual({ limit: 100, skip: 0 });
    expect(
      getPaginationFilters({
        skip: null,
        limit: 100,
        page: 2,
      })
    ).toEqual({ limit: 100, skip: 100 });
    expect(
      getPaginationFilters({
        skip: 0,
        limit: 100,
        page: 2,
      })
    ).toEqual({ limit: 100, skip: 100 });
    expect(
      getPaginationFilters({
        skip: 10,
        limit: 0,
        page: 2,
      })
    ).toEqual({ limit: 0, skip: 10 });
    expect(
      getPaginationFilters({
        skip: 'foo',
        limit: 100,
        page: 'bar',
      })
    ).toEqual({ limit: 100, skip: 0 });
    expect(
      getPaginationFilters({
        skip: 'foo',
        limit: 'bar',
        page: 'baz',
      })
    ).toEqual({ limit: 10, skip: 0 });
  });
});
