import { isEmpty } from '#utils/arrays.js';

/**
 * @typedef {object} DefaultSortFilter Default sort filter object with createdAt as key and 1 as value
 * @property {1} createdAt Sort by createdAt in ascending order
 */

/**
 * @typedef {Object.<string, 1 | -1>} CustomSortFilter Custom sort filter object with field names as keys and values 1 for ascending and -1 for descending
 */

/**
 * Sort filter for mongoose queries
 * @typedef {DefaultSortFilter|CustomSortFilter} SortFilter
 */

/**
 * Creates a sort filter based on the sort string and fields array
 * @param {Object} params - The sort parameters
 * @param {String} params.sort - The sort string
 * @param {String[]} params.defaultFields - The default fields to sort by
 * @returns {SortFilter} The sort filter object
 */
export default function getSortFilter({
  sort = '',
  order = 'asc',
  defaultFields = [],
} = {}) {
  // Get fields from sort string (i.e. 'field1,field2,-_id' => ['field1', 'field2', '-_id'])
  const sortFields = sort ? sort.replace(/,/g, ' ').split(' ') : [];

  // If no fields in sort string, use default fields, or empty array if none
  const fields = sortFields.length ? sortFields : defaultFields || [];

  // If no fields,
  if (isEmpty(fields)) {
    //  Always sort by createdAt in ascending order
    return { createdAt: 1 };
  }

  // Otherwise, return and object with fields as keys and values 1 for ascending  and -1 for descending
  return fields.reduce((acc, field) => {
    // Check if field starts with '-'
    const startsWithDash = field.startsWith('-');

    // Check if order is descending
    const isDescending = startsWithDash || order === 'desc';

    // Remove '-' from field name if starts with one
    const fieldName = startsWithDash ? field.slice(1) : field;

    // Add field to accumulator object with 1 or -1
    acc[fieldName] = isDescending ? -1 : 1;

    return acc;
  }, {});
}
