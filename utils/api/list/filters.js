import getPaginationFilter from '../pagination/filters.js';
import getSortFilter from '../sort/filters.js';
import getSearchFilter from '../search/filters.js';
import getStatusFilter from '../statuses/filters.js';

/**
 * @typedef {object} ListFiltersOutput
 * @property {import('../statuses/filters.js').StatusFilter} statuses Statuses filter
 * @property {import('../pagination/filters.js').PaginationFilter} pagination Pagination filter
 * @property {import('../sort/filters.js').SortFilter} sort Sort filter
 * @property {import('../search/filters.js').SearchFilter} search Search filter
 */

/**
 * Generic filters for requests with pagination, sort, search and status filters
 * @param {Object} param The generic parameters
 * @param {import('../statuses/filters.js').StatusesOptions} param.statuses Array of statuses
 * @param {Number} param.limit Number of items per page
 * @param {Number} param.page Page number
 * @param {Number} param.skip Number of items to skip in the query
 * @param {String} param.sort Field to sort by (default: 'title')
 * @param {'asc'|'desc'} param.order Sort order of the results for the sort field (default: 'asc')
 * @param {Array<string>} param.sortFields Default sort fields
 * @param {String} param.search Search string
 * @param {Array<string>} param.searchFields Fields to search for the search string
 * @returns {ListFiltersOutput} The filters object with statuses, pagination, sort and search filters
 */
export default function getListFilters({
  statuses = [],
  limit = 10,
  page = 1,
  skip = 0,
  sort = 'title',
  order = 'asc',
  sortFields = [],
  search = '',
  searchFields = [],
} = {}) {
  // get statuses filter based on statuses array
  const statusesFilter = getStatusFilter(statuses);

  // get pagination filter based on limit, page and skip
  const paginationFilter = getPaginationFilter({
    limit,
    page,
    skip,
  });

  // get sort filter based on sort string and sortFields array
  const sortFilter = getSortFilter({
    sort,
    order,
    defaultFields: sortFields,
  });

  // get search filter based on search string and searchFields array
  const searchFilter = getSearchFilter({
    search,
    fields: searchFields,
  });

  return {
    statuses: statusesFilter,
    pagination: paginationFilter,
    sort: sortFilter,
    search: searchFilter,
  };
}
