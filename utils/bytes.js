/*!
 * Based on: https://github.com/visionmedia/bytes.js
 */

// Regular expressions to match the value and the unit
const formatThousandsRegExp = /\B(?=(\d{3})+(?!\d))/g;
const formatDecimalsRegExp = /(?:\.0*|(\.[^0]+)0+)$/;

// Regular expression to parse the value and the unit
const parseRegExp = /^((-|\+)?(\d+(?:\.\d+)?)) *(kb|mb|gb|tb|pb)$/i;

/**
 * @typedef {'B'|'KB'|'MB'|'GB'|'TB'|'PB'} ByteUnit The byte unit to use (in upper case, e.g. 'KB')
 * @typedef {' ByteUnitSeparator
 */

/**
 * Byte units.
 */
const map = {
  b: 1,
  kb: 1024,
  mb: 1024 ** 2,
  gb: 1024 ** 3,
  tb: 1024 ** 4,
  pb: 1024 ** 5,
};

/**
 * Format the given value in bytes into a string.
 * If the value is negative, it is kept as such. If it is a float,
 * it is rounded.
 *
 * @param {Number} value The value in bytes
 * @param {Object} [options] The formatting options
 * @param {Number} [options.decimalPlaces=2]  The number of decimal places to include
 * @param {Number} [options.fixedDecimals=false] Whether to always include the specified number of decimal places
 * @param {String} [options.thousandsSeparator] The character to use as a thousands separator
 * @param {ByteUnit} [options.uni=] The unit to use (B, KB, MB, GB, TB, PB)
 * @param {String} [options.unitSeparator] The character to use as a separator
 *
 * @returns {String|Null}
 *
 * @example
 * formatBytes(1024); // => '1 KB'
 */
export function formatBytes(value, options) {
  // If the value is not a number, return null
  if (!Number.isFinite(value)) {
    return null;
  }

  const mag = Math.abs(value);
  const thousandsSeparator = (options && options.thousandsSeparator) || '';
  const unitSeparator = (options && options.unitSeparator) || ' ';
  const decimalPlaces =
    options && options.decimalPlaces !== undefined ? options.decimalPlaces : 2;
  const fixedDecimals = Boolean(options && options.fixedDecimals);
  let unit = (options && options.unit) || '';

  if (!unit || !map[unit.toLowerCase()]) {
    if (mag >= map.pb) {
      unit = 'PB';
    } else if (mag >= map.tb) {
      unit = 'TB';
    } else if (mag >= map.gb) {
      unit = 'GB';
    } else if (mag >= map.mb) {
      unit = 'MB';
    } else if (mag >= map.kb) {
      unit = 'KB';
    } else {
      unit = 'B';
    }
  }

  const val = value / map[unit.toLowerCase()];
  let str = val.toFixed(decimalPlaces);

  if (!fixedDecimals) {
    str = str.replace(formatDecimalsRegExp, '$1');
  }

  if (thousandsSeparator) {
    str = str.replace(formatThousandsRegExp, thousandsSeparator);
  }

  return str + unitSeparator + unit;
}

/**
 * Parse the string value into an integer in bytes.
 * If no unit is given, it is assumed the value is in bytes.
 * @param {Number|String} val The value to parse
 * @returns {Number|Null} The parsed value in bytes
 * @example
 * parseBytes('1 KB'); // => 1024
 */
export function parseBytes(val) {
  // If the value is already a number, return it
  if (typeof val === 'number' && !Number.isNaN(val)) {
    return val;
  }

  // If the value is not a string, return null
  if (typeof val !== 'string') {
    return null;
  }

  // Test if the string passed is valid
  const results = parseRegExp.exec(val);
  let floatValue;
  let unit = 'b';

  if (!results) {
    // Nothing could be extracted from the given string
    floatValue = parseInt(val, 10);
    unit = 'b';
  } else {
    // Retrieve the value and the unit
    floatValue = parseFloat(results[1]);
    unit = results[4].toLowerCase();
  }

  return Math.floor(map[unit] * floatValue);
}
