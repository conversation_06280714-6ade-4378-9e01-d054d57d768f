import { expect, test } from 'vitest';

import { stringArrayToString } from './strings.js';

test('stringArrayToString', () => {
  expect(stringArrayToString(['a', 'b', 'c'], ', ')).toEqual('a, b, c');
  expect(stringArrayToString(['a', 'b', 'c'], '\n')).toEqual('a\nb\nc');
  expect(stringArrayToString(['a', 'b ', ' c'], '')).toEqual('abc');
  expect(stringArrayToString([], ', ')).toEqual('');
  expect(stringArrayToString([null, 0, false, undefined], ', ')).toEqual('');
  expect(stringArrayToString()).toEqual('');
});
