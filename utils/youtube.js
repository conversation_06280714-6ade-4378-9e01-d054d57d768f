import { google } from 'googleapis';

import Logger from '#utils/logger.js';

const youtube = google.youtube({
  version: 'v3',
  auth: process.env.YOUTUBE_API_KEY,
});

/**
 * Gets the YouTube ID from a URL.
 *
 *  Works with following urls:
 *  - https://www.youtube.com/watch?v=xSe3O2E2sDI?t=67
 *  - https://www.youtube.com/watch?v=xSe3O2E2sDI
 *  - https://youtu.be/xSe3O2E2sDI?t=67
 *  - https://youtu.be/xSe3O2E2sDI
 *  - https://www.youtube.com/watch?v=xSe3O2E2sDI
 *  - https://www.youtube.com/embed/xSe3O2E2sDI
 *  - www.youtube.com/embed/xSe3O2E2sDI
 *  - //www.youtube.com/watch?v=xSe3O2E2sDI
 *  - xSe3O2E2sDI
 */
export const getYouTubeId = (url) => {
  if (!url) return null;
  const regExp =
    /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??(v=)?([^#&?]*).*/;
  const match = url.match(regExp);
  return match && match[8]?.length === 11 ? match[8] : url;
};

/**
 * Parses the ISO 8601 duration format of the YouTube Data API to seconds.
 *
 * Works with following values:
 * - PT1H
 * - PT23M
 * - PT45S
 * - PT1H23M
 * - PT1H45S
 * - PT23M45S
 * - PT1H23M45S
 */
function youtubeDurationToSeconds(duration) {
  const regex = /PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/;
  const match = regex.exec(duration);
  const hours = parseInt(match[1] || 0, 10);
  const minutes = parseInt(match[2] || 0, 10);
  const seconds = parseInt(match[3] || 0, 10);
  return hours * 60 * 60 + minutes * 60 + seconds;
}

export const getVideo = async (value) => {
  if (!value) return;
  const youtubeId = getYouTubeId(value);
  try {
    const result = await youtube.videos.list({
      id: youtubeId,
      part: 'snippet,contentDetails,statistics',
    });
    const { data, status } = result;
    if (status === 200 && data.items.length > 0) {
      return data.items[0];
    }
  } catch (error) {
    Logger.error(`Error getting youtube video (${youtubeId})`, error);
  }
  return null;
};

export const getMediaLink = async (value) => {
  if (!value) return null;
  const youtubeId = getYouTubeId(value);
  try {
    const result = await youtube.videos.list({
      id: youtubeId,
      part: 'snippet,contentDetails',
    });
    const { data, status } = result;
    if (status === 200 && data.items.length > 0) {
      const { contentDetails, id, snippet } = data.items[0];
      const { duration } = contentDetails;
      const { title, thumbnails } = snippet;
      return {
        link: id,
        aspectRatio: '16:9',
        duration: youtubeDurationToSeconds(duration),
        title: title,
        thumbnails: thumbnails,
      };
    }
  } catch (error) {
    Logger.error(`Error getting youtube video (${youtubeId})`, error);
    return null;
  }
  return null;
};

export default {
  getYouTubeId,
  getMediaLink,
  getVideo,
};
