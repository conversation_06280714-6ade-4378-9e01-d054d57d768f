import { DateTime } from 'luxon';
import { expect, test } from 'vitest';

import { convert } from './dates.js';

const commonFormat = "yyyy-MM-dd'T'HH:mm:ss";

test('convert date', () => {
  // Test various date formats and ensure they convert correctly

  // - JavaScript Date object
  const jsDate = new Date('2023-10-01T12:00:00.000+02:00');
  expect(convert(jsDate).toFormat(commonFormat)).toEqual('2023-10-01T12:00:00');

  // - ISO date strings
  const dateString = '2023-10-01T12:00:00.000+02:00';
  expect(convert(dateString).toFormat(commonFormat)).toEqual(
    '2023-10-01T12:00:00'
  );

  // - Unix timestamps
  // Note: Luxon expects milliseconds, so we multiply seconds by 1000
  const unixTimestamp = 1696154400 * 1000; // Corresponds to '2023-10-01T12:00:00+02:00'
  expect(convert(unixTimestamp).toFormat(commonFormat)).toEqual(
    '2023-10-01T12:00:00'
  );

  // - DateTime objects from Luxon
  const luxonDateTime = DateTime.fromISO('2023-10-01T12:00:00+02:00');
  expect(convert(luxonDateTime).toFormat(commonFormat)).toEqual(
    '2023-10-01T12:00:00'
  );

  // - String with date only
  const date = '2023-10-01';
  expect(convert(date).toFormat(commonFormat)).toEqual('2023-10-01T00:00:00');

  // - String with date and time
  const dateTime = '2023-10-01T12:00:00';
  expect(convert(dateTime).toFormat(commonFormat)).toEqual(
    '2023-10-01T12:00:00'
  );

  // - String with date, time, and timezone
  const dateTimeWithZone = '2023-10-01T12:00:00+02:00';
  expect(convert(dateTimeWithZone).toFormat(commonFormat)).toEqual(
    '2023-10-01T12:00:00'
  );

  // - Object with date components
  const objectDate = {
    year: 2023,
    month: 10,
    day: 1,
    hour: 12,
    minute: 0,
    second: 0,
    zone: 'Europe/Berlin',
  };
  expect(convert(objectDate).toFormat(commonFormat)).toEqual(
    '2023-10-01T14:00:00' // NOTE: 2 hours difference from UTC to Berlin time in October
  );

  // - Array with date components [year, month, day, hour, minute, second, millisecond, zone]
  const arrayDate = [2023, 10, 1, 12, 0, 0, 0, 'Europe/Berlin'];
  expect(convert(arrayDate).toFormat(commonFormat)).toEqual(
    '2023-10-01T14:00:00' // NOTE: 2 hours difference from UTC to Berlin time in October is expected
  );
});
