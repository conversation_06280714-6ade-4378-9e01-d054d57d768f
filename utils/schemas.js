import mongoose from 'mongoose';

export const FieldSchemaOption = new mongoose.Schema({
  label: { type: String, required: true },
  value: { type: String, required: true },
});

export const FieldSchema = new mongoose.Schema({
  name: { type: String, required: true },
  label: { type: String, required: true },
  type: { type: String, required: true },
  typeOption: { type: String },
  options: { type: [FieldSchemaOption] },
  placeholder: { type: String },
  help: { type: String },
  required: { type: Boolean },
  unique: { type: Boolean },
  size: { type: mongoose.SchemaTypes.Mixed },
  colSpan: { type: mongoose.SchemaTypes.Mixed },
});

export const FormSchema = new mongoose.Schema({
  columns: { type: mongoose.SchemaTypes.Mixed },
  fields: { type: [FieldSchema] },
});
