import { stringArrayToString } from '#utils/strings.js';

/**
 * Get searchable person name
 * @param {Object} person - The person object
 * @param {String} person.prefix - The prefix of the person
 * @param {String} person.firstName - The first name of the person
 * @param {String} person.middleName - The middle name of the person
 * @param {String} person.lastName - The last name of the person
 * @param {String} person.suffix - The suffix of the person
 * @returns {String}
 */
export function getSearchablePersonName(person) {
  const { prefix, firstName, middleName, lastName, suffix } = person || {};

  return stringArrayToString(
    [prefix, firstName, middleName, lastName, suffix],
    ' '
  );
}
