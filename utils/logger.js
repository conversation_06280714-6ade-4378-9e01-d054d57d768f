import { toLogDate } from '#utils/dates.js';

/**
 * The colors for the different message types
 * @type {Object}
 * @property {String} success The color for success messages
 * @property {String} warning The color for warning messages
 * @property {String} info The color for info messages
 * @property {String} error The color for error messages
 */
const consoleColors = {
  reset: '\x1b[0m', // Reset color
  error: '\x1b[31m', // Red
  success: '\x1b[32m', // Green
  warning: '\x1b[33m', // Yellow/Orange
  info: '\x1b[36m', // Cyanx
  debug: '\x1b[35m', // Magenta
};

/**
 * Format a message for logging
 * @param {Object} params The parameters object
 * @param {Array<string>} params.messages The messages to log
 * @param {String} params.type The type of message
 * @param {Boolean} [params.showDate=true] Whether to show the
 * @returns {void}
 * @example
 * formatMessage({
 *  type: 'info',
 *  messages: ['This is a message'],
 * });
 * // 2024-02-01 01:23:45 [info] This is a message
 */
function formatMessage({ messages = [], type = 'info', showDate = true }) {
  const output = [];

  if (showDate) {
    output.push(`${toLogDate(new Date())}`);
  }

  // Add type prefix (with color)
  const color = consoleColors[type] || '';
  output.push(`${color}${type}\t${consoleColors.reset}`);

  // Add message(s)
  output.push(...messages);

  // Log the message based on the type
  switch (type) {
    case 'error':
      console.error(...output); // eslint-disable-line no-console
      break;
    case 'warning':
      console.warn(...output); // eslint-disable-line no-console
      break;
    default:
      console.log(...output); // eslint-disable-line no-console
      break;
  }
}

/**
 * Logs a success message
 * @param {Array<string>} messages The messages to log
 * @returns {void}
 */
export function logSuccess(...messages) {
  formatMessage({ messages, type: 'success' });
}

/**
 * Logs a warning message
 * @param {Array<string>} messages The messages to log
 * @returns {void}
 */
export function logWarning(...messages) {
  formatMessage({ messages, type: 'warning' });
}

/**
 * Logs an info message
 * @param {Array<string>} messages The messages to log
 * @returns {void}
 */
export function logInfo(...messages) {
  formatMessage({ messages, type: 'info' });
}

/**
 * Logs an error message
 * @param {Array<string>} messages The messages to log
 * @returns {void}
 */
export function logError(...messages) {
  formatMessage({ messages, type: 'error' });
}

/**
 * Logs a debug message
 * @param {Array<string>} messages The messages to log
 * @returns {void}
 */
export function logDebug(...messages) {
  formatMessage({ messages, type: 'debug' });
}

/**
 * The Logger class
 * @deprecated Use the individual `log*` functions instead
 * @class Logger
 * @example
 * Logger.success('This is a success message');
 * Logger.warning('This is a warning message');
 * Logger.info('This is an info message');
 * Logger.error('This is an error message');
 */
export default class Logger {
  /**
   * Logs a success message
   * @deprecated Use `logSuccess` instead
   * @param {Array<string>} args The messages to log
   * @returns {void}
   */
  static success(...args) {
    logSuccess(...args);
  }

  /**
   * Logs a warning message
   * @deprecated Use `logWarning` instead
   * @param {Array<string>} args The messages to log
   * @returns {void}
   */
  static warning(...args) {
    logWarning(...args);
  }

  /**
   * Logs an info message
   * @deprecated Use `logInfo` instead
   * @param {Array<string>} args The messages to log
   * @returns {void}
   */
  static info(...args) {
    logInfo(...args);
  }

  /**
   * Logs an error message
   * @deprecated Use `logError` instead
   * @param {Array<string>} args The messages to log
   * @returns {void}
   */
  static error(...args) {
    logError(...args);
  }
}
