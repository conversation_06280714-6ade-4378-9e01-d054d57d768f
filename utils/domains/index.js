import { getProvider } from './helpers/utils.js';

export async function addDomain({ providerName, ...rest }) {
  // If no provider is found, use the default provider set in the environment variables.
  const provider = getProvider(providerName);

  if (provider?.addDomain) {
    return await provider.addDomain({ ...rest, provider });
  }

  return false; // Return false to indicate that it wasn't possible to add the domain.
}

export async function removeDomain({ providerName, ...rest }) {
  const provider = getProvider(providerName);

  if (provider?.removeDomain) {
    return await provider.removeDomain({ ...rest, provider });
  }

  return false;
}

export default {
  addDomain,
  removeDomain,
};
