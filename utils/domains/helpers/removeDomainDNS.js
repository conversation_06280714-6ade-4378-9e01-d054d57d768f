import ky from 'ky';

import Logger from '#utils/logger.js';
import { notifyAdmins } from '#utils/notifier.js';

const { DNS_PROVIDER, DNS_PROVIDER_TOKEN } = process.env;

export async function removeDomainFromDNS({
  domain,
  region,
  zoneIdentifier,
  subdomain,
}) {
  const dnsHeaders = { Authorization: `Bearer ${DNS_PROVIDER_TOKEN}` };
  const dnsBaseUrl = `https://api.cloudflare.com/client/v4/zones/${zoneIdentifier}/dns_records`;
  const queryParameters = '?tag=added_by_zafir'; // This tag is used to identify and remove the record should the need arise. // TODO: Rename this to AWE

  try {
    const data = await ky
      .get(`${dnsBaseUrl}${queryParameters}`, {
        headers: dnsHeaders,
      })
      .json();

    // Attempt to add domain only if it's not already added
    const matchingSubdomain = data.result.find(
      (r) => r.name.split('.')[0] === subdomain
    );
    if (matchingSubdomain) {
      Logger.info('Attempting to remove domain from DNS provider:', domain);
      const response = await ky.delete(
        `${dnsBaseUrl}/${matchingSubdomain.id}`,
        {
          headers: dnsHeaders,
        }
      );

      if (response.status === 200) {
        Logger.success('Domain removed from DNS provider:', domain);
      }
    } else {
      Logger.info('Domain already removed from DNS provider:', domain);
    }
  } catch (error) {
    notifyAdmins({
      subject: 'Error removing domain from DNS provider',
      templateName: 'domainRegistration',
      templateValues: {
        domain,
        region,
        isError: true,
        error: JSON.stringify(error.response?.data || error),
      },
    });
    Logger.error(
      `Couldn't remove DNS record for ${domain} in ${DNS_PROVIDER}:`,
      error.response || error
    );
  }
}
