import { addDomain } from '../helpers/addDomain.js';
import { removeDomain } from '../helpers/removeDomain.js';

const { VERCEL_TEAM_ID, VERCEL_API_TOKEN } = process.env;

export const vercel = {
  name: 'Vercel',
  baseUrl: 'https://api.vercel.com/v8/projects/{{design}}-{{region}}/domains',
  getDeletePath: (domain) => `${domain}`,
  params: `?teamId=${VERCEL_TEAM_ID}`,
  defaultRegion: 'eu-central',
  supportsRedirects: true,
  getBearerToken: async () => VERCEL_API_TOKEN,
  addDomain: async (config) => await addDomain(config),
  removeDomain: async (config) => await removeDomain(config),
};
