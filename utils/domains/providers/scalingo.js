import ky from 'ky';
import { addDomain } from '../helpers/addDomain.js';
import { removeDomain } from '../helpers/removeDomain.js';

const { SCALINGO_API_TOKEN } = process.env;

export const scalingo = {
  name: '<PERSON>aling<PERSON>',
  baseUrl:
    'https://api.{{region}}.scalingo.com/v1/apps/{{design}}-osc-fr1/domains',
  getDeletePath: (domain, data) => {
    const { id: domainId } = data.domains.find((d) => d.name === domain);
    return `${domainId}`;
  },
  defaultRegion: 'osc-fr1', // Note: This is the default region for Scalingo, and the only one, it is equivalent to eu-central.
  getBearerToken: async () => {
    const credentials = Buffer.from(`:${SCALINGO_API_TOKEN}`).toString(
      'base64'
    );
    const data = await ky
      .post('https://auth.scalingo.com/v1/tokens/exchange', {
        body: '',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      })
      .json();
    const { token } = data;

    return token;
  },
  addDomain: async (config) => await addDomain(config),
  removeDomain: async (config) => await removeDomain(config),
};
