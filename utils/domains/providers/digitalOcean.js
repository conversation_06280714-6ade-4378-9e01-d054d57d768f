import Entity from '#modules/entities/models/Entity.js';
import Logger from '#utils/logger.js';
import { notifyAdmins } from '#utils/notifier.js';
import ky from 'ky';

const { DIGITALOCEAN_API_TOKEN } = process.env;

export const digitalOcean = {
  name: 'DigitalOcean',
  addDomain: async (config) => await addToDigitalOcean(config),
  removeDomain: async (config) => await removeFromDigitalOcean(config),
};

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${DIGITALOCEAN_API_TOKEN}`,
};

async function addToDigitalOcean({ domain, design, region, appName }) {
  if (!domain)
    throw new Error('No domain provided to remove from DigitalOcean app');

  if (domain.startsWith('copy-of')) {
    Logger.info('Not adding domain because it starts with "copy-of"');
    return false;
  }

  if (domain.includes('localhost')) {
    Logger.info('Not adding domain because is a local dev domain');
    return false;
  }
  // We now check if the domain is not already in any entity.
  // This is to prevent adding the same domain to multiple entities.
  const duplicateDomain = await Entity.findOne({
    'config.backendDomains': { $in: [{ domain }] },
  });

  if (duplicateDomain) {
    throw new Error('Domain already in use by another entity');
  }

  // If appName is not provided and both design and region are not provided, throw an error
  if (!appName && (!design || !region))
    throw new Error('No appName or design and region provided');

  // For digitalOcean we need to get the app spec, insert the domain, and then put it back.
  try {
    // If appName is provided use that, otherwise use design and region
    const data = await getDigitalOceanApp({ appName, design, region });

    const digitalOceanDomain = {
      domain,
      type: 'ALIAS',
      wildcard: false,
    };

    const appSpec = data.app.spec;
    if (!appSpec)
      throw new Error('No app spec found for this appName in DigitalOcean.');

    // We need to take the domains of spec, add the new domain if it's not already there, and then put it back
    const domains = appSpec.domains.map((d) => d.domain); //Each domain object has a domain property
    if (domains.includes(domain)) {
      throw new Error('Domain already added to this DigitalOcean app');
    }

    appSpec.domains.push(digitalOceanDomain);

    const payload = {
      spec: appSpec,
    };

    const response = await ky.put(
      `https://api.digitalocean.com/v2/apps/${data.app.id}`,
      {
        json: payload,
        headers,
      }
    );

    if (response.status !== 200) {
      const errorData = await response.json();
      Logger.error(errorData);
      throw new Error(`Error adding domain to DigitalOcean ${errorData}`);
    }

    notifyAdmins({
      subject: 'Successfully added domain to DigitalOcean',
      templateName: 'domainRegistration',
      templateValues: {
        domain,
        region: appName || region,
        isError: false,
      },
    });

    Logger.success(
      'Domain added:',
      domain,
      'to DigitalOcean app:',
      appName || `${design}-${region}`
    );

    return true; // Return true if domain was added
  } catch (error) {
    notifyAdmins({
      subject: 'Error adding domain to DigitalOcean',
      templateName: 'domainRegistration',
      templateValues: {
        domain,
        region: appName || region,
        isError: true,
        error: JSON.stringify(error.response?.data || error),
      },
    });

    Logger.error(
      `Couldn't add domain ${domain} to ${
        appName || `region ${region}`
      } in Digital Ocean:`,
      error.response || error
    );
  }
}

async function removeFromDigitalOcean({ domain, design, region, appName }) {
  if (!domain)
    throw new Error('No domain provided to remove from DigitalOcean app');

  if (domain.includes('localhost')) {
    Logger.info('Not removing domain because is a local dev domain');
    return false;
  }

  // If appName is not provided and both design and region are not provided, throw an error
  if (!appName && (!design || !region))
    throw new Error('No appName or design and region provided');

  try {
    const data = await getDigitalOceanApp({ appName, design, region });

    const appSpec = data.app.spec;
    if (!appSpec)
      throw new Error('No app spec found for this appName in DigitalOcean.');

    const domains = appSpec.domains.map((d) => d.domain); //Each domain object has a domain property
    if (!domains.includes(domain)) {
      throw new Error('Domain not found in this DigitalOcean app');
    }

    // Remove the domain from the app spec
    appSpec.domains = appSpec.domains.filter((d) => d.domain !== domain);

    const payload = {
      spec: appSpec,
    };

    // Put the app spec back
    const response = await ky.put(
      `https://api.digitalocean.com/v2/apps/${data.app.id}`,
      {
        json: payload,
        headers,
      }
    );

    if (response.status !== 200) {
      const errorData = await response.json();
      Logger.error(errorData);
      throw new Error(`Error removing domain from DigitalOcean ${errorData}`);
    }

    notifyAdmins({
      subject: 'Successfully removed domain from DigitalOcean',
      templateName: 'domainRegistration',
      templateValues: {
        domain,
        region: appName || region,
        isError: false,
      },
    });

    Logger.success(
      'Domain removed:',
      domain,
      'from DigitalOcean app:',
      appName || `${design}-${region}`
    );

    return true; // Return true if domain was removed
  } catch (error) {
    // If the domain is not found in the app spec, log an error and return false

    notifyAdmins({
      subject: 'Error removing domain from DigitalOcean',
      templateName: 'domainRegistration',
      templateValues: {
        domain,
        region: appName || region,
        isError: true,
        error: JSON.stringify(error.response?.data || error),
      },
    });

    Logger.error(
      `Couldn't remove domain ${domain} from ${
        appName || `${design}-${region}`
      } in Digital Ocean:`,
      error.response || error
    );

    return false;
  }
}

async function getDigitalOceanApp({ appName, design, region }) {
  const data = await ky
    .get(
      `https://api.digitalocean.com/v2/apps/?name=${appName || `${design}-${region}`}`,
      {
        headers,
      }
    )
    .json();

  return data;
}
