import _ from 'lodash';

/**
 * @deprecated This is not being used anywhere in the codebase. // TODO: Remove this function!
 *
 * Returns the string parsed as an object or false.
 *
 * @param {(string|object)} jsonString A stringified json or an object
 * @returns {(object|false)} Parsed object or false
 */
export function tryParseJSON(jsonString) {
  try {
    const o = JSON.parse(jsonString);

    // Handle non-exception-throwing cases:
    // Neither JSON.parse(false) or JSON.parse(1234) throw errors, hence the type-checking,
    // but... JSON.parse(null) returns null, and typeof null === "object",
    // so we must check for that, too. Thankfully, null is falsey, so this suffices:
    if (o && typeof o === 'object') return o;
  } catch {
    if (typeof jsonString === 'object') return jsonString;
  }

  return false;
}

/**
 * @deprecated This is not being used anywhere in the codebase. // TODO: Remove this function!
 *
 * Returns the data as an object.
 * If it's an array, returns it as an object with the values as keys.
 * If it's not an object, returns an empty object.
 *
 * @param {(array|object)} data Array or object
 * @returns {Object} The parsed object
 */
export function parseToObject(data) {
  if (_.isObject(data)) {
    return _.isArray(data)
      ? Object.assign({}, ...data.map((k) => ({ [k]: {} })))
      : data;
  }

  return {};
}

/**
 * @deprecated This is not being used anywhere in the codebase. Use lodash.pick instead, // TODO: Remove this function!
 *
 * Returns a new object with the allowed fields.
 * @param {Object} obj Original object
 * @param  {...string} allowedFields List of allowed fields to filter
 * @returns {Object} New object with allowed fields
 * @example
 * const obj = { name: 'John', age: 30, city: 'New York' }
 * const newObj = filterObj(obj, 'name', 'city')
 * // newObj = { name: 'John', city: 'New York' }
 */
export function filterObj(obj, ...allowedFields) {
  const newObj = {};

  Object.keys(obj).forEach((el) => {
    if (allowedFields.includes(el)) newObj[el] = obj[el];
  });

  return newObj;
}

// Regex to extract the language part of a locale (en-US -> en)
const langRegex = /^([a-zA-Z]*)/;

/**
 * Extracts the language part of a locale.
 * @param {String} locale Locale (en-US) or language (en)
 * @param {String[]} [supportedLanguages] List of supported languages
 * @returns {String} Language extracted from the locale
 * @example
 * extractLanguage('en-US') // 'en'
 */
export function extractLanguage(locale, supportedLanguages) {
  if (!locale) return '';

  const language = locale.match(langRegex).pop() || 'en';

  if (!supportedLanguages) return language;

  const supportedLanguage = supportedLanguages.includes(language)
    ? language
    : 'en';

  return supportedLanguage;
}

/**
 * Returns the language for the user.
 * If no preferences or entity are provided, a default value is returned.
 * @param {Object} [preferences] User preferences object
 * @param {Object} [entity] Entity
 * @returns {String} Language for the user
 */
export function getLanguage(preferences, entity, supportedLanguages) {
  // If user has a language preference set, return it
  if (preferences) {
    if (preferences.language) return extractLanguage(preferences.language);
    if (preferences.localeUI) return extractLanguage(preferences.localeUI);
    if (preferences.locale) return extractLanguage(preferences.locale);
  }

  // If entity has a language set, return it (all entities have a language set)
  if (entity.language)
    return extractLanguage(entity.language, supportedLanguages);

  return 'en';
}

/**
 * Decodes an encoded URL
 * @param {String} url URL to decode
 * @returns {String} Decoded URL
 */
export function decodeUrl(url) {
  // Check if the URL is encoded or not
  return decodeURI(url) === decodeURIComponent(url)
    ? url
    : decodeURIComponent(url);
}

/**
 * Check if the two IDs given are equal
 * @param {string|ObjectId} id1 First ID to compare
 * @param {string|ObjectId} id2 Second ID to compare
 * @returns {Boolean} True if the IDs are equal, false otherwise
 */
export function areEqualIDs(id1, id2) {
  try {
    return id1.toString() === id2.toString();
  } catch {
    return false;
  }
}

/**
 * Returns a promise that resolves after the indicated number of seconds
 * @param {Number} s Number of seconds to wait
 * @returns Promise<void>
 * @example
 * // This will print 'This will be printed 2 seconds later' after 2 seconds
 * await delay(2)
 * console.log('This will be printed 2 seconds later')
 */
export function delay(seconds = 1) {
  return new Promise((resolve) => {
    setTimeout(() => resolve(), seconds * 1000); // Convert seconds to milliseconds
  });
}

export default {
  tryParseJSON,
  parseToObject,
  filterObj,
  extractLanguage,
  getLanguage,
  decodeUrl,
  areEqualIDs,
  delay,
};
