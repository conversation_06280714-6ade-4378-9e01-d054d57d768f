import { t } from '#utils/translator.js';

const isEmptyValue = (value) =>
  value === null || value === undefined || Number.isNaN(value);

const serializeValue = (value) => {
  if (isEmptyValue(value)) return '';
  value = `${value}`;
  if (value.includes(',') || value.includes('\n') || value.includes('"'))
    return `"${value.replace(/"/g, '""').replace(/\n/g, '\\n')}"`;
  return value;
};

/**
 * Returns CSV content of the given data
 * @param {Object} data
 * @returns {String} CSV string
 */
export function buildCsvContent(data, language = 'en') {
  // Build header row
  const headers = `${Object.keys(data[0])
    .map((column) => serializeValue(column))
    .map((column) => t(language, column))
    .join(',')}\n`;

  // Build content rows
  const rows = data
    .map((row) =>
      Object.values(row)
        .map((value) => serializeValue(value))
        .join(',')
    )
    .join('\n');

  // Return CSV content
  return `${headers}${rows}`;
}

export function buildCsvRow(data) {
  const csvRow = Object.values(data)
    .map((value) => serializeValue(value))
    .join(',');
  return `${csvRow}\n`;
}

export default {
  buildCsvContent,
};
