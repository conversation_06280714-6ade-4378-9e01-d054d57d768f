import { isArray } from '#utils/types.js';

export const getIds = function (docs = []) {
  return docs.map((d) => d._id);
};

/**
 * Checks if an arrayay is empty
 *
 * @param {Array} array
 *
 * @return Boolean
 */
export function isEmpty(array) {
  return isArray(array) && array.length === 0;
}

/**
 * Checks if an array is not empty
 *
 * @param {Array} array
 *
 * @return Boolean
 */
export function isNotEmpty(array) {
  return !isEmpty(array);
}

/**
 * Returns the mode of an array (the)
 * @param {Array} array
 * @returns {any} mode of the array
 */
export function mode(array) {
  return array
    .sort(
      (a, b) =>
        array.filter((v) => v === a).length -
        array.filter((v) => v === b).length
    )
    .pop();
}

export default {
  getIds,
  isEmpty,
  isNotEmpty,
  mode,
};
