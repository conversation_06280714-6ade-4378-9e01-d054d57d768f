import mongoose from 'mongoose';

export default (schemaDefinition = {}, schemaOptions = {}) => {
  const schema = new mongoose.Schema(
    {
      deleted: {
        type: Boolean,
        default: false,
      },
      enabled: {
        type: Boolean,
        default: true,
      },
      ...schemaDefinition,
    },
    {
      timestamps: true,
      toJSON: { virtuals: true },
      toObject: { virtuals: true },
      minimize: false,
      ...schemaOptions,
    }
  );

  schema.index({ deleted: 1 });
  schema.index({ enabled: 1 });
  schema.index({ createdAt: 1 });
  schema.index({ updatedAt: 1 });

  return schema;
};
