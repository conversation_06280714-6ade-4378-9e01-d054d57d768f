import { SourceMapConsumer } from 'source-map-js';
import { UAParser } from 'ua-parser-js';

import factory from '#utils/handlerFactory.js';
import { notifyAdmins } from '#utils/notifier.js';

import Client from '../models/Client.js';
import clientServices from '../services/clientServices.js';

export const createClient = async (req, res) => {
  const client = await clientServices.createClient(req.body);

  res.status(201).json(client);
};

export const updateClient = async (req, res) => {
  const updatedClient = await clientServices.updateClient(
    req.params.id,
    req.body
  );

  res.status(200).json(updatedClient);
};

export const resetToken = async (req, res) => {
  const client = await clientServices.resetToken(req.params.id);

  res.status(200).json(client);
};

export const getVersion = async (req, res) => {
  const version = await clientServices.getVersion(req.get('ClientToken'));

  res.status(200).json({ version });
};

export const updateVersion = async (req, res) => {
  const version = await clientServices.updateVersion({
    sysToken: req.get('HopeSysToken'),
    clientToken: req.get('ClientToken'),
  });

  res.status(200).json({ version });
};

async function getSourceMapFromUri(uri, sourceMaps) {
  // Checks if the source map has already been fetched
  if (sourceMaps[uri] !== undefined) {
    return sourceMaps[uri];
  }

  let map;

  try {
    const currentScriptContent = await (await fetch(uri)).text();

    const mapUriMatch = /\/\/# sourceMappingURL=(.*)/.exec(
      currentScriptContent
    );

    if (!mapUriMatch || Array.isArray(mapUriMatch) === false) {
      return null;
    }

    const mapUri = mapUriMatch[1];

    //In the dev environment, the mapUri is not a path to a file, but rather a base64 encoded string of the file contents
    if (mapUri.startsWith('data:')) {
      map = JSON.parse(atob(mapUri?.split(',')[1]));
    } else {
      map = await (await fetch(new URL(mapUri, uri).href)).json();
    }

    sourceMaps[uri] = map;
  } catch (error) {
    console.error(error); // eslint-disable-line no-console
    return null;
  }

  return map;
}

async function mapStackTrace(stack) {
  if (stack === null || stack === undefined) return;

  const sourceMaps = {};
  const stackLines = stack.split('\n');
  const mappedStack = [];

  for (let line of stackLines) {
    line = `${line}`.trim();

    if (line === '') continue;

    //Only map the stack trace if it is a valid stack trace line (contains a url and line number)
    const match = /(.*)(http(s)?:\/\/.*):(\d+):(\d+)/.exec(line);

    // If the line does not match the valid stack trace line pattern, we just push the line as is
    if (!match) {
      mappedStack.push([line]);
      continue;
    }

    //We save the original line so we can get the function name
    const originalLine = line?.trim().split(' ');
    const uri = match[2];
    const sourceMapFromUri = await getSourceMapFromUri(uri, sourceMaps);

    // If the source map is not found, we just push the line as is
    if (sourceMapFromUri === null) {
      mappedStack.push([line]);
      continue;
    }

    const consumer = new SourceMapConsumer(sourceMapFromUri);

    try {
      const originalPosition = consumer.originalPositionFor({
        line: parseInt(match[4], 10) || 0,
        column: parseInt(match[5], 10) || 0,
      });

      if (
        originalPosition.source == null ||
        originalPosition.line == null ||
        originalPosition.column == null
      ) {
        mappedStack.push([line]);
        continue;
      }

      let functionName;

      if (originalPosition.name !== null) {
        functionName = originalPosition.name;
      } else if (originalLine?.length >= 3) {
        functionName = originalLine[1];
      } else {
        functionName = 'No name';
      }

      mappedStack.push([
        `${functionName}`,
        `${originalPosition.source.replace('../../', '')}:${
          originalPosition.line
        }:${originalPosition.column + 1}`,
      ]);
    } catch {
      mappedStack.push([line]);
      continue;
    }
  }

  return mappedStack;
}

export const reportError = async (req, res) => {
  const client = await Client.findOne({ token: req.get('ClientToken') });

  const { error, errorInfo, jsStack, url } = req.body;

  mapStackTrace(jsStack).then((stack) => {
    notifyAdmins({
      subject: `Error on client: ${req.entity?.name}`,
      templateName: 'clientError',
      templateValues: {
        clientName: client.name,
        userName: req.user?.name,
        userEmail: req.user?.email,
        useragent: UAParser(req.get('user-agent')),
        error,
        errorInfo,
        stack,
        url,
      },
    });
  });

  res.status(200).json({ status: 'OK' });
};

export const getAllClients = async (req, res) => {
  const { items, count } = await factory.getAll(Client, req);

  res.status(200).json({
    items: items.map((client) => {
      // Removes the privateToken from the response
      const { privateToken, ...rest } = client.toObject();
      return rest;
    }),
    count,
  });
};

export const getClient = async (req, res) => {
  const data = await factory.getOne(Client, req);

  const client = data.toObject();
  // Don't return the encrypted private token
  delete client.privateToken;

  res.status(200).json(client);
};

export const deleteClient = async (req, res) => {
  const data = await factory.deleteOne(Client, req);

  const client = data.toObject();
  // Don't return the encrypted private token
  delete client.privateToken;

  res.status(200).json(client);
};

export const restoreClient = async (req, res) => {
  const data = await factory.restoreOne(Client, req);

  const client = data.toObject();
  // Don't return the encrypted private token
  delete client.privateToken;

  res.status(200).json(client);
};

export const disableClient = async (req, res) => {
  const data = await factory.disableOne(Client, req);

  const client = data.toObject();
  // Don't return the encrypted private token
  delete client.privateToken;

  res.status(200).json(client);
};

export const enableClient = async (req, res) => {
  const data = await factory.enableOne(Client, req);

  const client = data.toObject();
  // Don't return the encrypted private token
  delete client.privateToken;

  res.status(200).json(client);
};

export default {
  createClient,
  updateClient,
  resetToken,
  getVersion,
  updateVersion,
  reportError,
  getAllClients,
  getClient,
  deleteClient,
  restoreClient,
  disableClient,
  enableClient,
};
