import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import clientController from './controllers/clientController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

router.get('/version', clientController.getVersion);
router.patch('/update-version', clientController.updateVersion);
router.post('/report-error', clientController.reportError);

// Require user for all routes after this middleware
router.use(protect);

// Allow only admins to all routes after this middleware
router.use(restrictTo());

router
  .route('/')
  .get(clientController.getAllClients)
  .post(
    logRequest({ module: 'clients', action: 'CREATE_CLIENT' }),
    clientController.createClient
  );

router
  .route('/:id')
  .get(clientController.getClient)
  .patch(
    logRequest({ module: 'clients', action: 'UPDATE_CLIENT' }),
    clientController.updateClient
  )
  .delete(
    logRequest({ module: 'clients', action: 'DELETE_CLIENT' }),
    clientController.deleteClient
  );

router
  .route('/:id/reset-token')
  .patch(
    logRequest({ module: 'clients', action: 'RESET_CLIENT_TOKEN' }),
    clientController.resetToken
  );

router
  .route('/:id/restore')
  .patch(
    logRequest({ module: 'clients', action: 'RESTORE_CLIENT' }),
    clientController.restoreClient
  );

router
  .route('/:id/disable')
  .patch(
    logRequest({ module: 'clients', action: 'DISABLE_CLIENT' }),
    clientController.disableClient
  );

router
  .route('/:id/enable')
  .patch(
    logRequest({ module: 'clients', action: 'ENABLE_CLIENT' }),
    clientController.enableClient
  );

export default router;
