import { errors } from '#utils/appError.js';
import { IP_REGEX } from '#utils/constants.js';
import { encrypt } from '#utils/encryption.js';
import { sendEvent } from '#utils/notifier.js';
import { randomString } from '#utils/strings.js';

import Client from '../models/Client.js';

const SYS_TOKEN = process.env.SYS_TOKEN || 'SYS-TOKEN';

// Recursive function to generate a unique token among all clients
const generateUniqueToken = async () => {
  let token = randomString({ length: 32 });
  const count = await Client.countDocuments({ token });
  if (count > 0) {
    token = await generateUniqueToken();
  }

  return token;
};

async function createClient(clientBody) {
  // Make sure it has a unique token
  clientBody.token = await generateUniqueToken();

  // Allow only valid IPs
  if (clientBody.allowedIPs) {
    clientBody.allowedIPs = clientBody.allowedIPs.filter((ip) =>
      IP_REGEX.test(ip)
    );
  }

  if (clientBody.privateToken) {
    // Encrypt the private token
    clientBody.privateToken = encrypt(clientBody.privateToken);
  }

  return await Client.create(clientBody);
}

async function updateClient(clientId, clientBody) {
  // Prevent modifying the token
  delete clientBody.token;

  // Allow only valid IPs
  if (clientBody.allowedIPs) {
    clientBody.allowedIPs = clientBody.allowedIPs.filter((ip) =>
      IP_REGEX.test(ip)
    );
  }

  if (clientBody.privateToken) {
    // Encrypt the private token
    clientBody.privateToken = encrypt(clientBody.privateToken);
  }

  // In this case, client is the the document as it was BEFORE update was applied
  const client = await Client.findByIdAndUpdate(clientId, clientBody, {
    runValidators: true,
  });

  if (!client) throw errors.not_found('Client', clientId);

  if (clientBody.version && client.version !== clientBody.version) {
    // Trigger version update event
    sendEvent({
      to: 'all',
      event: 'CLIENT_VERSION_UPDATE',
      data: { token: client.token },
    });
  }

  const updatedClient = await Client.findById(client._id).lean();

  // Don't return the encrypted private token once updated
  delete updatedClient.privateToken;

  return updatedClient;
}

async function resetToken(clientId) {
  const client = await Client.findOne(clientId);

  // Generate a new and unique token
  client.token = await generateUniqueToken();

  await client.save();

  const updatedClient = client.toObject();

  // Don't return the encrypted private token once updated
  delete updatedClient.privateToken;

  return updatedClient;
}

async function getVersion(token) {
  const client = await Client.findOne({ token });

  if (!client) {
    throw errors.not_found('Client');
  }

  return client.version || '0';
}

async function updateVersion({ sysToken, clientToken }) {
  if (!sysToken || sysToken !== SYS_TOKEN) {
    throw errors.not_allowed();
  }

  // TODO: The version will be hardcoded for now
  const client = await Client.findOneAndUpdate(
    { token: clientToken },
    { version: `1.0.0-${Date.now()}` },
    {
      new: true,
      runValidators: true,
    }
  );

  sendEvent({
    to: 'all',
    event: 'CLIENT_VERSION_UPDATE',
    data: { token: client.token },
  });

  return client.version || '0';
}

export default {
  createClient,
  updateClient,
  resetToken,
  getVersion,
  updateVersion,
};
