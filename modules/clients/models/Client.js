import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const clientSchema = SchemaFactory({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  token: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  privateToken: {
    type: mongoose.SchemaTypes.Mixed,
    required: false,
  },
  version: {
    type: String,
    trim: true,
    default: '1.0.0',
  },
  allowedIPs: {
    type: [String],
    default: [],
  },
  entity: {
    type: mongoose.Schema.ObjectId,
    ref: 'Entity',
  },
});

export default mongoose.model('Client', clientSchema);
