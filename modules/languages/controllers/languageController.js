import languageServices from '../services/languageServices.js';

export const getLanguages = async (req, res) => {
  const { query } = req;
  const { statuses, limit, page, sort, search, locales, exceptions } = query;

  const data = await languageServices.getLanguages({
    statuses,
    limit,
    page,
    sort,
    search,
    locales,
    exceptions,
  });

  res.status(200).json(data);
};

export const createLanguage = async (req, res) => {
  const language = await languageServices.createLanguage(req.body);

  res.status(200).json(language);
};

export const getLanguage = async (req, res) => {
  const language = await languageServices.getLanguageById(req.params.id);

  res.status(200).json(language);
};

export const getLanguageByLocale = async (req, res) => {
  const language = await languageServices.getLanguageByLocale(
    req.params.locale
  );

  res.status(200).json(language);
};

export const updateLanguage = async (req, res) => {
  const updatedLanguage = await languageServices.updateLanguage({
    languageId: req.params.id,
    data: req.body,
  });

  res.status(200).json(updatedLanguage);
};

export const disableLanguage = async (req, res) => {
  const updatedLanguage = await languageServices.disableLanguage({
    languageId: req.params.id,
  });

  res.status(200).json(updatedLanguage);
};

export const enableLanguage = async (req, res) => {
  const updatedLanguage = await languageServices.enableLanguage({
    languageId: req.params.id,
  });

  res.status(200).json(updatedLanguage);
};

export const deleteLanguage = async (req, res) => {
  await languageServices.deleteLanguage(req.params.id);

  res.status(204).json({});
};

export default {
  getLanguages,
  createLanguage,
  getLanguage,
  getLanguageByLocale,
  updateLanguage,
  disableLanguage,
  enableLanguage,
  deleteLanguage,
};
