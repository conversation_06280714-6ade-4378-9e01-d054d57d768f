import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const languageSchema = SchemaFactory({
  name: {
    type: mongoose.SchemaTypes.Mixed,
    default: { en: '' },
    set: function (value) {
      // This is a mongoose setter to trim all string values inside the object
      const trimmedValue = {};

      // If it's a string, just trim it
      if (typeof value === 'string') {
        return value.trim();
      }

      for (const key in value) {
        if (typeof value[key] === 'string') {
          trimmedValue[key] = value[key].trim();
        } else {
          trimmedValue[key] = value[key];
        }
      }

      return trimmedValue;
    },
  },
  nativeName: {
    type: String,
    trim: true,
  },
  locale: {
    type: String,
    required: true,
    trim: true,
  },
});

languageSchema.index({ name: 1 });
languageSchema.index({ nativeName: 1 });
languageSchema.index({ locale: 1 });

export default mongoose.model('Language', languageSchema);
