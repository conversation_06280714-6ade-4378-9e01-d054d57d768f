import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const paymentMethodSchema = SchemaFactory({
  name: {
    type: String,
  },

  /*
   * The type of payment method (e.g. 'stripe', 'paypal')
   */
  type: {
    type: String,
    required: true,
  },

  /*
   * Payment method settings (e.g. API keys, currency, etc)
   */
  config: {
    type: mongoose.SchemaTypes.Mixed,
  },

  /*
   * The entity to which this configuration belongs
   */
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
});

export default mongoose.model('PaymentMethod', paymentMethodSchema);
