import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const transactionSchema = SchemaFactory({
  status: {
    type: String,
    required: true,
  },
  referenceCode: {
    type: String,
    required: true,
  },
  isTest: {
    type: Boolean,
    default: false,
  },
  amount: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    required: true,
  },
  paymentService: {
    type: String,
    required: true,
  },
  paymentExternalId: {
    type: String,
    required: true,
  },
  receiptEmail: {
    type: String,
  },
  paymentMethod: {
    type: mongoose.Types.ObjectId,
    ref: 'PaymentMethod',
  },
  // Saved to
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },
  // Page and site that the transaction was made on
  page: {
    type: mongoose.Types.ObjectId,
    ref: 'Page',
  },
  // Block where the labels used to send the email are. (Holds also reference to payment method)
  block: {
    type: String,
  },
  email: {
    type: String,
  },
  name: {
    type: String,
  },
  phone: {
    type: String,
  },
  emailSent: {
    type: Boolean,
    default: false,
  },
  attempts: {
    type: Number,
    default: 0,
  },
  giftAid: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
});

export default mongoose.model('Transaction', transactionSchema);
