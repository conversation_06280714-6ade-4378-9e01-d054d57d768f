import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const projectSchema = SchemaFactory({
  code: {
    type: 'string',
    required: true,
  },
  title: {
    type: 'string',
    required: true,
  },
  abstract: {
    type: 'object',
  },
  image: {
    type: 'object',
  },
  isFeatured: {
    type: 'boolean',
    default: false,
  },
  churchRequired: {
    type: 'boolean',
    default: false,
  },

  /**
   * The entity to which this project belongs
   */
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
});

export default mongoose.model('Project', projectSchema);
