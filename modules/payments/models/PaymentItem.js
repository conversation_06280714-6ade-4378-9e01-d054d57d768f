import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

// This is equivalent to MyOffering's Donation model
const paymentItemSchema = SchemaFactory({
  amount: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    required: true,
  },
  // Modified when a webhook is received with the success of the payment
  succeeded: {
    type: Boolean,
    default: false,
  },
  succeededAt: {
    type: Date,
  },

  /**
   * Payment as donation, or a payment for a product.
   */
  type: {
    type: String,
    required: true,
  },

  /**
   * Transaction of the payment item
   */
  transaction: {
    type: mongoose.Types.ObjectId,
    ref: 'Transaction',
  },

  /**
   * Project the payment item belongs to, if of type 'donation'
   */
  project: {
    type: mongoose.Types.ObjectId,
    ref: 'Project',
  },

  /**
   * Church that a donation is made to for a specific project
   */
  church: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },
});

export default mongoose.model('PaymentItem', paymentItemSchema);
