import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

// Model tied to a user that has made a payment
const payerSchema = SchemaFactory({
  /**
   * The payer's external ID in other systems (Stripe customer ID, etc.). U
   */
  stripeCustomerId: {
    type: String,
    required: true,
  },

  /**
   * The entity to which this payer belongs
   */
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
});

export default mongoose.model('Payer', payerSchema);
