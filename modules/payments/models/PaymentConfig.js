import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const paymentConfigSchema = SchemaFactory({
  /*
   * Gift aid/custom settings
   */
  customField: {
    enabled: {
      type: Boolean,
      default: false,
    },
    message: {
      type: String,
    },
  },

  /*
   * The entity to which this configuration belongs
   */
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
});

export default mongoose.model('PaymentConfig', paymentConfigSchema);
