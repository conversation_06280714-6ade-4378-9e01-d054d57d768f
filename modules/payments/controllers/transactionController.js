import {
  getTransactionsList,
  getTransaction,
} from '../services/transactionServices.js';

export const getTransactionsAction = async (req, res) => {
  const { query, entity } = req;
  const { limit, page, sort, search, filter, type } = query;

  const { initialDate, lastDate } = JSON.parse(filter) || {};

  if (
    !req.user.isAdmin &&
    !req.user.hasPermission({ module: 'transactions', permission: 'read' })
  ) {
    res.status(200).json({ count: 0, items: [] });
    return;
  }
  const data = await getTransactionsList({
    entityId: entity._id,
    limit,
    fields: [],
    page,
    sort,
    search,
    initialDate,
    lastDate,
    type,
  });

  res.status(200).json(data);
};

export const getTransactionAction = async (req, res) => {
  const { entity, params } = req;
  const { id } = params || {};

  const { transaction, error } = await getTransaction({ id, entity });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(transaction);
};
