import factory from '#utils/handlerFactory.js';
import {
  getPaymentMethods as getPaymentMethodsServices,
  getPublicPaymentMethods as getPublicPaymentMethodsServices,
} from '../services/paymentMethodServices.js';
import PaymentMethod from '../models/PaymentMethod.js';

export const getPaymentMethods = async (req, res) => {
  const { entity } = req;

  const { data, error } = await getPaymentMethodsServices({
    entity,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const createPaymentMethod = async (req, res) => {
  const data = await PaymentMethod.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getPaymentMethod = async (req, res) => {
  const data = await factory.getOne(PaymentMethod, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const updatePaymentMethod = async (req, res) => {
  // Merge the prev config with the new one, if a config field doesn't exist in the new one, it will keep the previous value (because of secret key and webhook secret)
  const prevConfig = await PaymentMethod.findOne({
    _id: req.body.id,
  });

  const newBody = {
    ...req.body,
    config: {
      ...prevConfig?.config,
      ...req.body.config,
    },
  };

  const data = await PaymentMethod.findByIdAndUpdate(
    req.body.id,
    { ...newBody },
    {
      runValidators: true,
      new: true,
    }
  );

  res.status(200).json(data);
};

export const disablePaymentMethod = async (req, res) => {
  const data = await factory.disableOne(PaymentMethod, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enablePaymentMethod = async (req, res) => {
  const data = await factory.enableOne(PaymentMethod, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deletePaymentMethod = async (req, res) => {
  const publication = await factory.getOne(PaymentMethod, req, {
    filterByEntity: true,
  });

  await PaymentMethod.deleteOne({ _id: publication._id });

  res.status(204).json({});
};

export const getPublicPaymentMethods = async (req, res) => {
  const { data, error } = await getPublicPaymentMethodsServices({
    entity: req.entity._id,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export default {
  getPaymentMethods,
  createPaymentMethod,
  getPaymentMethod,
  updatePaymentMethod,
  disablePaymentMethod,
  enablePaymentMethod,
  deletePaymentMethod,
  getPublicPaymentMethods,
};
