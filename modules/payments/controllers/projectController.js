import Logger from '#utils/logger.js';

import projectServices from '../services/projectServices.js';

export const getProjects = async (req, res) => {
  const { entity, query } = req;
  const { fields, limit, page, search, sort, order, statuses, types } = query;

  const { items, count, error } = await projectServices.getProjects({
    entity,
    fields,
    limit,
    page,
    search,
    sort,
    order,
    statuses,
    types,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json({ items, count });
};

export const createProject = async (req, res) => {
  const { body, entity } = req;
  const { code, title, abstract, isFeatured, churchRequired } = body;

  const { project, error } = await projectServices.createProject({
    entity,
    code,
    title,
    abstract,
    isFeatured,
    churchRequired,
  });

  if (error) {
    Logger.error(error);
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(project);
};

export const getProject = async (req, res) => {
  const { entity, params } = req;
  const { id } = params || {};

  const { project, error } = await projectServices.getProject({ id, entity });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(project);
};

export const updateProject = async (req, res) => {
  const { body, entity, params } = req;
  const { id } = params || {};

  const { code, title, isFeatured, churchRequired, image } = body;

  const { project, error } = await projectServices.updateProject({
    id,
    entity,
    code,
    title,
    isFeatured,
    churchRequired,
    image,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(project);
};

export const disableProject = async (req, res) => {
  const { entity, params } = req;
  const { id } = params;

  const { project, error } = await projectServices.toggleProject({
    id,
    entity,
    enabled: false,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(project);
};

export const enableProject = async (req, res) => {
  const { entity, params } = req;
  const { id } = params;

  const { project, error } = await projectServices.toggleProject({
    id,
    entity,
    enabled: true,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(project);
};

export const deleteProject = async (req, res) => {
  const { entity, params } = req;
  const { id } = params;

  const { project, error } = await projectServices.deleteProject({
    id,
    entity,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(project);
};

export default {
  getProjects,
  createProject,
  getProject,
  updateProject,
  disableProject,
  enableProject,
  deleteProject,
};
