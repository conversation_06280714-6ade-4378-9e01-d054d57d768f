import Logger from '#utils/logger.js';

import {
  getPaymentConfig as getPaymentConfigService,
  setPaymentConfig as setPaymentConfigService,
} from '../services/paymentConfigService.js';

export const getPaymentConfig = async (req, res) => {
  const { entity } = req;

  const { data, error } = await getPaymentConfigService({
    entity,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const setPaymentConfig = async (req, res) => {
  const { body, entity, user } = req;
  const { network, ...config } = body;

  // Update publications config
  const { data, error } = await setPaymentConfigService({
    entity,
    network,
    user,
    config,
  });

  if (error) {
    Logger.error(error);
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export default {
  getPaymentConfig,
  setPaymentConfig,
};
