# Information about payments

1. Brief explanation of how payments work in the system

- The payment system should be able to handle multiple payment providers in the future. The first method to be added is stripe, which supports many payment method types such as card, SEPA direct debit, etc.

How it works with stripe: The frontend block sends the payment amount to the API, the API then starts a transaction in the database and creates a stripe payment intent (PI), and returns the client secret of this 'PI' to the frontend. This secret is used to display the stripe payment "Elements" which are the card input fields for example. The frontend will confirm the payment directly to stripe. The stripe webhook will then send a payment intent succeeded event to the API, which will then update the payment status in the Transaction document.
Once the transaction has succeeded an email is sent regarding this transaction.

That is the basic flow of the payment system. Other results of payments are to be handled in the webhook, such as failed payments, canceled, etc. These will be set as the status of the transaction in the database.

2. Emails to be sent to the user

An email is sent to the user on payment succeded of a payment intent for stripe.

The email will contain the following information:

- A thank you message

- Reference code of the payment

- The amount paid

- The date of the payment

- Destinations (Donation projects)

- Gift aid id if enabled and provided will be included along a disclaimer that the user can claim gift aid on the donation. The name of the destinatary of the Gift aid along with contact information will also be included.

The email address will only be sent to stripe in the case of a recurring subscription. They will be able to then access the customer portal to manage their subscription on the url that is provided in the email sent from the first payment. This means that for recurring payments to be available the stripe customer portal url has to be loaded in the payment method settings.

3. Features and their conditions

- Payments: A valid payment method selected on the block.

- Gift aid: A valid gift aid id settings in the payment method.

- Recurring payments: A user account will be needed to manage the subscription for the recurring payment.

4. Tests: _Things to consider testing_

- The principal flow of the payment system, from the block to the on success webhook and email sent.

- Try a card that fails after 3 minutes

- Try a card that asks for 3D secure

- Try a card that is declined

- Try other payment methods that succeed and redirect

- Try other payment methods that fail and redirect

# Set up for stripe

1. Log in to Stripe's dashboard and make sure that, on the top left, you have selected the corresponding Stripe account for the Union.

2. Go to the [API keys section](https://dashboard.stripe.com/apikeys).

3. Copy the `Publishable key` and the `Secret key`.

4. Go to the [Webhooks section](https://dashboard.stripe.com/webhooks).

5. Add a new endpoint. In the URL, enter `https://frontend-api.hopeplatform.org/v1/payments/stripe-webhook/{{paymentMethodId}}`, replacing `{{paymentMethodId}}` with the corresponding ID shown in the backend. This is located in the payment method's webhook secret help text.

6. Select the following events from **Payment Intent** section:

- `payment_intent.canceled`

- `payment_intent.payment_failed`

- `payment_intent.processing`

- `payment_intent.succeeded`

7. Once the endpoint is created, it will give you a `Signing secret`, which usually starts with `whsec_`. Copy it.

8. Go to the [Account details](https://dashboard.stripe.com/settings/account) page, and copy the account's ID.

9. Log in to `Zafir` backend, go to the edit page of the corresponding payment configuration and paste all these values in a stripe payment method.

# Development notes

1. Testing webhooks:

To test the webhooks, you can use the [Stripe CLI](https://stripe.com/docs/stripe-cli). Login with this command: `stripe login`

Then you can listen in the terminal for the events with this command: `stripe listen --forward-to localhost:3998/v1/payments/stripe-webhook/{{paymentMethodID}}` replacing `{{paymentMethodID}}` with the corresponding ID of the Entity in the database.

2. Cart block:

The selected donations are saved on local storage. Once the button to start the payment method is pressed, only those donations that have a _valid amount_ will be passed to the api to start the transaction and receive the payment intent secret.
The block uses stripe payment elements, and a couple of our own inputs.

Valid amount: (more than 0, but it could be changed to be minimum value for that currency and payment method)

---

# Payment module functionality

The payment module supports currently only Stripe. How we handle the payment flow is described below.

Summary: The payment flow is handled by the frontend. The frontend sends a request to the FE API to create a payment intent. The FE API creates a payment intent with the amount and currency provided by the frontend. The FE API sends the payment intent client secret back to the frontend. This client secret is used by the stripe library to render its form in an iframe.

Once the user pays with a card or other method, the stripe library in the frontend is used to confirm it.

Apart from the card form/bank payment method form, we obtain the email from the user and send it to the backend to be used in the email sent as confirmation of payment.

### How does the system connect to Stripe?

The system needs to have certain configurations to connect to Stripe. The configurations are stored in the payment method. The configurations are:

- **Stripe Secret key**: Used to create the payment intent with a set price (amount). API only key that doesn't reach the frontend.

- **Stripe Publishable(Public) key**: Used in the frontend. A stripe object (from the stripe library) is intialized with this key, which is used to create the payment form and confirm payments. This stripe object can verify the payment intent client secret sent, which is created in the API with the other key.

- **Stripe Webhook Secret settings**: Includes the webhook information to put in stripe, and the results webhook secret we store. This secret is used to verify the webhooks sent by stripe to our FE API. If a payment was succesful a webhook is received by the FE API, which then updates the transaction and sends the email to the user and recipient. _(Note that this requires the email to have been saved, which is always sent before the confirmation in stripe is done by the frontend. If the email is not saved correctly, the frontend will not continue with the payment.)_

### How is a payment created?

Once the user has filled in amounts in the select amounts step, and the total is more than the minimum and less than the maximum amount, the user can proceed to the payment step. Once that the payment step is loaded, the frontend sends a request to the FE API to create a payment intent. The payment intent is created with the amount provided by the frontend. The FE API sends the payment intent client secret back to the frontend and the amount to later compare.
