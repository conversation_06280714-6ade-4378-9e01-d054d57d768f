import PaymentMethod from '../models/PaymentMethod.js';

export async function getPaymentMethods({ entity }) {
  const methods = await PaymentMethod.find({ entity }).select({
    'config.secretKey': 0,
    'config.webhookSecret': 0,
  });

  return { data: methods };
}

export async function getPublicPaymentMethods({ entity }) {
  const methods = await PaymentMethod.find({
    entity,
    enabled: true,
    deleted: false,
  }).select({
    'config.giftAidEnabled': 1,
    'config.currencyMinLimit': 1,
    'config.currency': 1,
    'name': 1,
    'type': 1,
    'id': 1,
    '_id': 1,
  });

  return { data: methods };
}

export default {
  getPaymentMethods,
  getPublicPaymentMethods,
};
