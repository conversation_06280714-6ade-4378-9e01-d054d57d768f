import isEmpty from 'lodash/isEmpty.js';

import getCollationFilter from '#utils/api/collation/filter.js';
import getListFilters from '#utils/api/list/filters.js';
import Project from '../models/Project.js';

/**
 * Get projects
 * @param {Object} params - The parameters
 * @param {Object} params.entity - The entity object
 * @param {Array} params.statuses - The project statuses
 * @param {Number} params.limit - The number of projects to return
 * @param {Number} params.page - The page number
 * @param {Number} params.skip - The number of projects to skip
 * @param {String} params.sort - The field to sort by
 * @param {Number} params.order - The sort order
 * @param {String} params.search - The search string
 * @param {Array} params.types - The project types
 * @param {String} params.language - The language code
 * @returns {Promise<object>} - The projects and count (or an error)
 */
export async function getProjects({
  entity,
  statuses,
  limit,
  page,
  skip,
  sort,
  order,
  search,
  types,
  language,
}) {
  const filters = getListFilters({
    statuses,
    limit,
    page,
    skip,
    sort,
    order,
    sortFields: ['title', 'code', 'createdAt', 'updatedAt'],
    search,
    searchFields: ['title', 'code'],
  });

  // Base match filters
  const matchFilters = {
    entity: entity._id,
    $and: [filters.statuses],
  };

  // Search by title or code
  if (!isEmpty(filters.search)) {
    matchFilters.$and.push(filters.search);
  }

  // Search by types in the types list
  if (types?.length) {
    matchFilters.type = { $in: types };
  }

  // Get collation filters
  const collationFilters = getCollationFilter({ language });

  // Get all projects for the entity
  const itemsAggregate = Project.aggregate()
    .match(matchFilters)
    .sort(filters.sort)
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit) // NOTE: limit must be after skip!
    .collation(collationFilters)
    .addFields({ id: '$_id' }); // Include alias of _id field as id

  // Count the number of projects
  const countAggregate = Project.aggregate().match(matchFilters).count('count');

  try {
    // Execute the queries in parallel
    const [items, count] = await Promise.all([itemsAggregate, countAggregate]);

    return { items, count: count.length ? count[0].count : 0 };
  } catch (error) {
    return { error };
  }
}

/**
 * Get a project by id
 * @param {Object} params - The parameters
 * @param {String} params.id - The project id
 * @param {Object} params.entity - The entity object
 * @returns {Promise<object>} - The project object (or an error)
 */
export async function getProject({ id, entity }) {
  if (!id) {
    return { error: 'getProject: Missing project id' };
  }

  if (!entity) {
    return { error: 'getProject: Missing entity' };
  }

  try {
    const project = await Project.findById(id) // Find project by id
      .where({ entity: entity._id }); // Ensure project belongs to entity
    return { project };
  } catch (error) {
    return { error };
  }
}

/**
 * Create a project
 * @param {Object} params - The parameters
 * @param {Object} params.entity - The entity object
 * @param {String} params.code - The project code
 * @param {String} params.title - The project title
 * @param {Object} params.abstract - The project abstract
 * @param {Boolean} params.isFeatured - The project is featured
 * @param {Boolean} params.churchRequired - The project requires a church
 * @returns {Promise<object>} - The project object (or an error)
 */
export async function createProject({
  entity,
  code,
  title,
  abstract,
  isFeatured,
  churchRequired,
}) {
  if (!entity) {
    return { error: 'createProject: Missing entity' };
  }

  try {
    const project = await Project.create({
      code,
      title,
      abstract,
      isFeatured,
      churchRequired,
      entity: entity._id,
    });

    return { project };
  } catch (error) {
    return { error };
  }
}

/**
 * Update a project
 * @param {Object} params - The parameters
 * @param {Object} params.entity - The entity object
 * @param {Object} params.id - The project id
 * @param {String} params.code - The project code
 * @param {String} params.title - The project title
 * @param {Object} params.image - The project image
 * @param {Boolean} params.isFeatured - The project is featured
 * @param {Boolean} params.churchRequired - The project requires a church
 * @returns {Promise<object>} - The project object (or an error)
 */
export async function updateProject({
  entity,
  id,
  code,
  title,
  isFeatured,
  image,
  churchRequired,
}) {
  if (!entity) {
    return { error: 'updateProject: Missing entity' };
  }

  if (!id) {
    return { error: 'updateProject: Missing project id' };
  }

  try {
    const project = await Project.findByIdAndUpdate(
      id,
      {
        code,
        title,
        isFeatured,
        churchRequired,
        entity: entity._id,
        image,
      },
      { new: true }
    );

    return { project };
  } catch (error) {
    return { error };
  }
}

/**
 * Toggle a project
 * @param {Object} params - The parameters
 * @param {Object} params.entity - The entity object
 * @param {Object} params.id - The project id to toggle
 * @returns {Promise<object>} - The project object (or an error)
 */
export async function toggleProject({ entity, id, enabled }) {
  if (!entity) {
    return { error: 'toggleProject: Missing entity' };
  }

  if (!id) {
    return { error: 'toggleProject: Missing project id' };
  }

  try {
    const project = await Project.findOneAndUpdate(
      {
        _id: id,
        entity: entity._id,
      },
      { enabled },
      { new: true }
    );

    return { project };
  } catch (error) {
    return { error };
  }
}

/**
 * Delete a project
 * @param {Object} params - The parameters
 * @param {Object} params.entity - The entity object
 * @param {Object} params.id - The project id to delete
 * @returns {Promise<object>} - The project object (or an error)
 */
export async function deleteProject({ entity, id }) {
  if (!entity) {
    return { error: 'deleteProject: Missing entity' };
  }

  if (!id) {
    return { error: 'deleteProject: Missing project id' };
  }

  try {
    const project = await Project.findOneAndUpdate(
      {
        _id: id,
        entity: entity._id,
      },
      { deleted: true } // Set deleted to true
    );

    return { project };
  } catch (error) {
    return { error };
  }
}

export default {
  getProjects,
  getProject,
  createProject,
  updateProject,
  toggleProject,
  deleteProject,
};
