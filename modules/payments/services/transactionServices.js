import getListFilters from '#utils/api/list/filters.js';
import mongoose from 'mongoose';
import Transaction from '../models/Transaction.js';

export async function getTransactionsList({
  entityId,
  search = '',
  sort,
  limit = 10,
  page = 1,
  skip = 0,
  initialDate = `${new Date().getFullYear()} '-01-01'`,
  lastDate = new Date(),
} = {}) {
  const filters =
    getListFilters({
      search,
      sort: `${sort},-startDate`,
      limit,
      page,
      skip,
      sortFields: ['email', 'name', 'updatedAt', 'amount'],
      searchFields: ['email', 'name', 'amount', 'referenceCode'],
    }) || {};

  const lowerCutoff = new Date(initialDate);
  const now = new Date();
  const upperCutoff = new Date(lastDate) > now ? now : new Date(lastDate);

  // We build the pipeline for the aggregation query
  const pipeline = [
    // Filter the transaction by entityId, status succeded and where the succeded date is between the initialDate and lastDate
    {
      $match: {
        entity: entityId,
        status: 'succeeded',
        updatedAt: {
          $gte: lowerCutoff,
          $lte: upperCutoff,
        },
      },
    },
  ];
  const countPromise = Transaction.aggregate(pipeline);
  const count = await countPromise;

  const items = await Transaction.aggregate(pipeline)
    .match({
      ...filters.search,
    })
    .sort(filters.sort)
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit);

  return { items, count: count.length };
}

export async function getTransaction({ id }) {
  const transaction = await Transaction.aggregate([
    {
      $match: {
        _id: new mongoose.Types.ObjectId(id),
      },
    },
    // Also include all payment items that are related to the transaction
    {
      $lookup: {
        from: 'paymentitems',
        localField: '_id',
        foreignField: 'transaction',
        as: 'paymentItems',
      },
    },
    {
      $unwind: {
        path: '$paymentItems',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'paymentItems.project',
        foreignField: '_id',
        as: 'paymentItems.project',
      },
    },
    {
      $unwind: {
        path: '$paymentItems.project',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $group: {
        _id: '$_id',
        transaction: { $first: '$$ROOT' },
        paymentItems: { $push: '$paymentItems' },
      },
    },
    {
      $replaceRoot: {
        newRoot: {
          $mergeObjects: ['$transaction', { paymentItems: '$paymentItems' }],
        },
      },
    },
  ]).then((result) => {
    if (result.length > 0) {
      return result[0];
    }
    return null;
  });

  if (!transaction) {
    return {
      error: 'Transaction not found',
    };
  }

  return { transaction };
}
