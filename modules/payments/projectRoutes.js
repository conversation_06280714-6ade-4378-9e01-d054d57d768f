import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import listSchema from '#utils/api/list/schema.js';
import { validate } from '#utils/validationMiddleware.js';

import projectController from './controllers/projectController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(validate(listSchema, 'query'), projectController.getProjects)
  .post(
    restrictTo({
      module: 'projects',
      permissions: ['create'],
    }),
    logRequest({
      module: 'payments',
      action: 'CREATE_PROJECT',
    }),
    projectController.createProject
  );

router
  .route('/:id')
  .get(projectController.getProject)
  .patch(
    restrictTo({
      module: 'projects',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({
      module: 'payments',
      action: 'UPDATE_PROJECT',
    }),
    projectController.updateProject
  )
  .delete(
    restrictTo({
      module: 'projects',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({
      module: 'payments',
      action: 'DELETE_PROJECT',
    }),
    projectController.deleteProject
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'projects',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'payments',
    action: 'DISABLE_PROJECT',
  }),
  projectController.disableProject
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'projects',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'payments',
    action: 'ENABLE_PROJECT',
  }),
  projectController.enableProject
);

export default router;
