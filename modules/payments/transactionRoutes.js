import express from 'express';

import {
  authorizeRequest,
  protect,
} from '#modules/users/controllers/authController.js';

import listSchema from '#utils/api/list/schema.js';
import { validate } from '#utils/validationMiddleware.js';
import {
  getTransactionAction,
  getTransactionsAction,
} from './controllers/transactionController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router.route('/').get(validate(listSchema, 'query'), getTransactionsAction);

router.route('/:id').get(getTransactionAction);

export default router;
