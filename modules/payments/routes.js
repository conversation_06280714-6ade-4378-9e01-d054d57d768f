import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { validate } from '#utils/validationMiddleware.js';
import paymentController from './controllers/paymentController.js';
import { paymentMethodSchema } from './validations/paymentMethodSchema.js';
import paymentMethodController from './controllers/paymentMethodController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/config')
  .get(paymentController.getPaymentConfig)
  .patch(
    restrictTo({ module: 'payment-methods', permissions: ['update'] }),
    validate(paymentMethodSchema, 'query'),
    logRequest({
      module: 'payments',
      action: 'UPDATE_PAYMENT_CONFIG',
    }),
    paymentController.setPaymentConfig
  );

router
  .route('/public/payment-methods')
  .get(paymentMethodController.getPublicPaymentMethods);

router
  .route('/payment-methods')
  .get(
    restrictTo({
      module: 'payment-methods',
      permissions: ['read'],
    }),
    paymentMethodController.getPaymentMethods
  )
  .post(
    restrictTo({ module: 'payment-methods', permissions: ['create'] }),
    logRequest({
      module: 'payments',
      action: 'CREATE_PAYMENT_METHOD',
    }),
    paymentMethodController.createPaymentMethod
  );

router
  .route('/payment-methods/:id')
  .get(paymentMethodController.getPaymentMethod)
  .patch(
    restrictTo({ module: 'payment-methods', permissions: ['update'] }),
    logRequest({
      module: 'payments',
      action: 'UPDATE_PAYMENT_METHOD',
    }),
    paymentMethodController.updatePaymentMethod
  )
  .delete(
    restrictTo({ module: 'payment-methods', permissions: ['delete'] }),
    logRequest({
      module: 'payments',
      action: 'DELETE_PAYMENT_METHOD',
    }),
    paymentMethodController.deletePaymentMethod
  );

export default router;
