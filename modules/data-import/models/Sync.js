import mongoose from 'mongoose';

const syncSchema = new mongoose.Schema(
  {
    lastSync: Date,
    baseURL: {
      type: String,
      required: true,
    },
    secret: {
      type: String,
      required: true,
    },
    entity: {
      type: mongoose.Types.ObjectId,
      ref: 'Entity',
      required: true,
    },
    site: {
      type: mongoose.Types.ObjectId,
      ref: 'Site',
      required: true,
    },
    settings: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
    articles: {
      type: Boolean,
      default: false,
    },
    articlePage: {
      type: mongoose.Types.ObjectId,
      ref: 'Page',
    },
    /**
     * To assign TYPO3 page IDs to AWE site flags
     * Key: Site flag
     * Value: TYPO3 page ID
     */
    articleFlagMappings: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
    mediaLibrary: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    minimize: false,
  }
);

syncSchema.index({ lastSync: 1 });
syncSchema.index({ entity: 1, site: 1 }, { unique: true });

export default mongoose.model('Sync', syncSchema);
