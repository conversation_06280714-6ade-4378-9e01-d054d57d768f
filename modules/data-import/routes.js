import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import syncController from './controllers/syncController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

// Allow only admins to all routes after this middleware
router.use(restrictTo());

router
  .route('/syncs')
  .get(syncController.getAllSyncs)
  .post(
    logRequest({ module: 'data-import', action: 'CREATE_SYNC' }),
    syncController.createSync
  );

router
  .route('/syncs/:id')
  .get(syncController.getSync)
  .patch(
    logRequest({ module: 'data-import', action: 'UPDATE_SYNC' }),
    syncController.updateSync
  )
  .delete(
    logRequest({ module: 'data-import', action: 'DELETE_SYNC' }),
    syncController.deleteSync
  );

router.post(
  '/syncs/:id/run',
  logRequest({ module: 'data-import', action: 'RUN_SYNC' }),
  syncController.runSync
);

export default router;
