import factory from '#utils/handlerFactory.js';
import typo3Import from '#scripts/import/typo3-import.js';

import Sync from '../models/Sync.js';

export const createSync = async (req, res) => {
  const sync = await Sync.create({
    ...req.body,
  });

  res.status(200).json(sync);
};

export const getAllSyncs = async (req, res) => {
  const sync = await factory.getAll(Sync, req, {
    populate: [
      { path: 'entity', select: 'name' },
      { path: 'site', select: 'domain name flags' },
    ],
  });

  res.status(200).json(sync);
};

export const getSync = async (req, res) => {
  const sync = await factory.getOne(Sync, req, {
    populate: [
      { path: 'entity', select: 'name' },
      { path: 'site', select: 'domain name flags' },
    ],
  });

  res.status(200).json(sync);
};

export const updateSync = async (req, res) => {
  const sync = await factory.updateOne(Sync, req);

  res.status(200).json(sync);
};

export const deleteSync = async (req, res) => {
  const sync = await factory.getOne(Sync, req);

  await Sync.deleteOne({ _id: sync._id });

  res.status(204).json({});
};

export const runSync = async (req, res) => {
  const sync = await factory.getOne(Sync, req, {
    populate: ['entity', 'site'],
  });

  typo3Import({ sync });

  res.status(204).json({});
};

export default {
  createSync,
  getAllSyncs,
  getSync,
  runSync,
  updateSync,
  deleteSync,
};
