import featureFlagService from '../services/featureFlagService.js';

const getFeatureFlagKeys = async (req, res) => {
  const featureFlags = featureFlagService.getFeatureFlagKeys();

  res.status(200).json(featureFlags);
};

const getAllFeatureFlags = async (req, res) => {
  const { items, count } = await featureFlagService.getFeatureFlags(req.query);

  res.status(200).json({
    items,
    count,
  });
};

const createFeatureFlag = async (req, res) => {
  const featureFlag = await featureFlagService.createFeatureFlag(req.body);

  res.status(201).json(featureFlag);
};

const getFeatureFlag = async (req, res) => {
  const featureFlag = await featureFlagService.getFeatureFlag(
    req.params.featureFlagId
  );

  res.status(200).json(featureFlag);
};

const updateFeatureFlag = async (req, res) => {
  const featureFlag = await featureFlagService.updateFeatureFlag(
    req.params.featureFlagId,
    req.body
  );

  res.status(200).json(featureFlag);
};

const deleteFeatureFlag = async (req, res) => {
  const featureFlag = await featureFlagService.deleteFeatureFlag(
    req.params.featureFlagId
  );

  res.status(200).json(featureFlag);
};

const restoreFeatureFlag = async (req, res) => {
  const featureFlag = await featureFlagService.restoreFeatureFlag(
    req.params.featureFlagId
  );

  res.status(200).json(featureFlag);
};

const disableFeatureFlag = async (req, res) => {
  const featureFlag = await featureFlagService.disableFeatureFlag(
    req.params.featureFlagId
  );

  res.status(200).json(featureFlag);
};

const enableFeatureFlag = async (req, res) => {
  const featureFlag = await featureFlagService.enableFeatureFlag(
    req.params.featureFlagId
  );

  res.status(200).json(featureFlag);
};

export default {
  getFeatureFlagKeys,
  getAllFeatureFlags,
  createFeatureFlag,
  getFeatureFlag,
  updateFeatureFlag,
  deleteFeatureFlag,
  restoreFeatureFlag,
  disableFeatureFlag,
  enableFeatureFlag,
};
