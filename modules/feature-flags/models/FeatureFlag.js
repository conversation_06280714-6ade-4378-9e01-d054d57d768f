import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';
import { uniquifySlug } from '#utils/strings.js';

const featureFlagSchema = SchemaFactory(
  {
    key: {
      type: String,
      trim: true,
    },
    title: {
      type: String,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    releaseStrategies: {
      type: [String],
      enum: ['network', 'entity', 'site', 'design'],
      default: [],
    },
    designs: {
      type: [String],
      default: [],
    },
    sites: {
      type: [mongoose.Types.ObjectId],
      ref: 'Site',
      default: [],
    },
    entities: {
      type: [mongoose.Types.ObjectId],
      ref: 'Entity',
      default: [],
    },
    networks: {
      type: [mongoose.Types.ObjectId],
      ref: 'Network',
      default: [],
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
featureFlagSchema.index(
  { key: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);
featureFlagSchema.index({ title: 1 });

// Statics
featureFlagSchema.statics.getAvailableKey = async function (
  key,
  featureFlagId = null
) {
  const query = { key, deleted: false };

  if (featureFlagId) {
    query._id = { $ne: featureFlagId };
  }

  const existingFeatureFlag = await this.findOne(query);

  return existingFeatureFlag ? uniquifySlug(key) : key;
};

export default mongoose.model('FeatureFlag', featureFlagSchema);
