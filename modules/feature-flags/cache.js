// A cache for feature toggles. This is initialised at server startup and
// used to avoid making multiple requests to the DB
let featureFlagCache = {};

function getCache() {
  return featureFlagCache;
}

function getCacheItem(key) {
  return featureFlagCache[key];
}

function setCache(cache = {}) {
  featureFlagCache = cache;
}

function setCacheItem(key, value) {
  featureFlagCache[key] = value;
}

export default {
  getCache,
  getCacheItem,
  setCache,
  setCacheItem,
};
