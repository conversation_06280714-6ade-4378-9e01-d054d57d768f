import Joi from 'joi';

export const createOrUpdateFeatureFlagSchema = Joi.object().keys({
  key: Joi.string().required(),
  title: Joi.string().required(),
  description: Joi.string().allow('', null),
  releaseStrategies: Joi.array()
    .items(Joi.string().allow('network', 'entity', 'site', 'design', null))
    .optional()
    .unique(),
  designs: Joi.array().items(Joi.string()).optional().unique(),
  sites: Joi.array().items(Joi.string()).optional().unique(),
  entities: Joi.array().items(Joi.string()).optional().unique(),
  networks: Joi.array().items(Joi.string()).optional().unique(),
});
