import siteServices from '#modules/web/services/siteServices.js';
import { errors } from '#utils/appError.js';

import featureFlagService from '../services/featureFlagService.js';

export const isRouteEnabled =
  (key, { strategyOverride = {} } = {}) =>
  async (req, res, next) => {
    try {
      const { site } = req.params?.siteId
        ? await siteServices.getSiteById(req.params.siteId)
        : {};

      const isEnabled = featureFlagService.isFeatureEnabled(key, {
        entity: req.entity,
        site,
        strategyOverride,
      });

      if (!isEnabled) {
        next(errors.not_found());
      }
      next();
    } catch (error) {
      next(error);
    }
  };
