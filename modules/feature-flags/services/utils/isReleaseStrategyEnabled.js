import { isSet } from '#utils/types.js';

function isFeatureEnabledForDesign({
  featureFlag,
  site,
  strategyOverride = {},
}) {
  const { designs = [] } = featureFlag;

  // Strategy can be overridden. Useful for cases where site is not known
  if (isSet(strategyOverride.design)) {
    return strategyOverride.design;
  }

  // Release strategy cannot be determined without a site
  if (!site) {
    return false;
  }

  return designs.includes(site?.design?.name);
}

function isFeatureEnabledForSite({ featureFlag, site, strategyOverride = {} }) {
  const { sites = [] } = featureFlag;

  // Strategy can be overridden. Useful for cases where site is not known
  if (isSet(strategyOverride.site)) {
    return strategyOverride.site;
  }

  // Release strategy cannot be determined without a site
  if (!site) {
    return false;
  }

  return sites.includes(site._id?.toString());
}

function isFeatureEnabledForEntity({
  entity,
  featureFlag,
  strategyOverride = {},
}) {
  const { entities = [] } = featureFlag;

  // Strategy can be overridden. Useful for cases where entity is not known
  if (isSet(strategyOverride.entity)) {
    return strategyOverride.entity;
  }

  // Release strategy cannot be determined without an entity
  if (!entity) {
    return false;
  }

  return entities.includes(entity._id?.toString());
}

const strategies = {
  design: isFeatureEnabledForDesign,
  entity: isFeatureEnabledForEntity,
  site: isFeatureEnabledForSite,
};

export function isReleaseStrategyEnabled({
  entity,
  featureFlag,
  site,
  strategyOverride,
}) {
  return function (strategy) {
    return strategies[strategy]({
      entity,
      featureFlag,
      site,
      strategyOverride,
    });
  };
}
