export default function getQuestionnairePermissions(providerId, req) {
  const userIsAdvisor = !!req.advisor;
  return {
    canReadQuestionnaires: req.user.hasPermission({
      module: 'courses-questionnaires',
      permission: 'read',
      recordModule: 'courses-providers',
      recordId: providerId,
    }),
    canReadAllQuestionnaires: req.user.hasPermission({
      module: 'courses-questionnaires',
      permission: 'readAll',
      recordModule: 'courses-providers',
      recordId: providerId,
    }),
    canUpdateQuestionnaires:
      userIsAdvisor &&
      req.user.hasPermission({
        module: 'courses-questionnaires',
        permission: 'update',
        recordModule: 'courses-providers',
        recordId: providerId,
      }),
    canUpdateAllQuestionnaires:
      userIsAdvisor &&
      req.user.hasPermission({
        module: 'courses-questionnaires',
        permission: 'updateAll',
        recordModule: 'courses-providers',
        recordId: providerId,
      }),
    canDeleteQuestionnaires:
      userIsAdvisor &&
      req.user.hasPermission({
        module: 'courses-questionnaires',
        permission: 'delete',
        recordModule: 'courses-providers',
        recordId: providerId,
      }),
    canDeleteAllQuestionnaires:
      userIsAdvisor &&
      req.user.hasPermission({
        module: 'courses-questionnaires',
        permission: 'deleteAll',
        recordModule: 'courses-providers',
        recordId: providerId,
      }),
  };
}
