import mongoose from 'mongoose';

import { getSearch } from '#utils/request.js';

import Student from '../models/Student.js';

export default async function getStudentIds(req) {
  let studentIds = [];
  if (req.query.search) {
    const { searchField, searchFilter } = getSearch(req, [
      'firstName',
      'lastName',
      'username',
    ]);
    const studentsAggregation = Student.aggregate();
    if (searchField) studentsAggregation.addFields(searchField);
    const students = await studentsAggregation
      .match({
        deleted: false,
        enabled: true,
        ...searchFilter,
      })
      .project({ _id: 1 });
    studentIds = students.map(
      (student) => new mongoose.Types.ObjectId(student._id)
    );
  }
  return studentIds;
}
