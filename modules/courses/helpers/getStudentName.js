export default function getStudentName({ student, online }) {
  const studentName = {
    firstName: '',
    lastName: '',
    username: '',
  };

  // Online profile
  if (online) {
    studentName.username = student.profile.username || '';
    studentName.firstName = student.profile.firstName || '';
    studentName.lastName = student.profile.lastName || '';
  }

  // Correspondence profile
  else {
    studentName.username = student.profile.username || '';
    studentName.firstName =
      student.firstName || student.profile.firstName || '';
    studentName.lastName = student.lastName || student.profile.lastName || '';
  }

  return studentName;
}
