import { getParam } from '#utils/request.js';

import { getAdvisor } from '../services/advisorService.js';

export const loadAdvisor = async (req, res, next) => {
  const userId = req.user.impersonateUser ?? req.user._id;
  const providerId = getParam(req, 'providerId');
  req.advisor = req.user
    ? await getAdvisor({
        backendUserId: userId,
        providerId,
        fields: ['_id', 'firstName', 'lastName'],
      })
    : null;

  next();
};
