import mongoose from 'mongoose';
import { errorCodes, generateError } from '#utils/appError.js';
import User from '#modules/users/models/User.js';
import Advisor from '../models/Advisor.js';

export const validateNewAdvisor = async (req, res, next) => {
  const { providerId } = req.params;

  const { backendUser, email, firstName, lastName } = req.body;

  if (backendUser) {
    if (!mongoose.Types.ObjectId.isValid(backendUser)) {
      throw generateError('Invalid user', errorCodes.INVALID_BACKEND_USER, 400);
    }

    const user = await User.findById(backendUser);
    if (!user) {
      throw generateError(
        'Backend user not found',
        errorCodes.BACKEND_USER_NOT_FOUND,
        404
      );
    }
  }

  const advisor = await Advisor.findOne({
    provider: new mongoose.Types.ObjectId(providerId),
    email,
    firstName,
    lastName,
    backendUser,
    deleted: false,
  });

  if (advisor) {
    throw generateError(
      'Advisor already exists',
      errorCodes.DUPLICATE_ADVISOR,
      400
    );
  }

  next();
};
