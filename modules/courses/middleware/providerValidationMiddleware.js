import mongoose from 'mongoose';
import { errors } from '#utils/appError.js';
import Provider from '../models/Provider.js';

export const validateProvider = async (req, res, next) => {
  const { providerId } = req.params;
  const entityId = req.entity._id;

  if (!mongoose.Types.ObjectId.isValid(providerId)) {
    throw errors.not_found('CourseProvider', providerId);
  }

  const provider = await Provider.findById(providerId)
    .select('_id')
    .where({
      entity: new mongoose.Types.ObjectId(entityId),
      deleted: false,
      enabled: true,
    });

  if (!provider) {
    throw errors.not_found('CourseProvider', providerId);
  }

  next();
};
