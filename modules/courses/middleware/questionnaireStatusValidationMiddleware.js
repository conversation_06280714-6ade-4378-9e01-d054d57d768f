import questionnaireStatusService from '../services/questionnaireStatusService.js';

export const validateQuestionnaireStatus = async (req, res, next) => {
  const { questionnaireStatusId } = req.params;

  // ERROR: Missing params (bad request)
  if (!questionnaireStatusId)
    return res.status(400).json({ error: 'ERROR_MISSING_PARAMS' });

  // ERROR: User not authenticated
  if (!req.user) return res.status(401).json({ error: 'ERROR_LOGIN_REQUIRED' });

  // ERROR: Advisor not found
  if (!req.advisor)
    return res.status(404).json({ error: 'ERROR_ADVISOR_NOT_FOUND' });

  // Get questionnaire status
  const questionnaireStatus =
    await questionnaireStatusService.getQuestionnaireStatus({
      questionnaireStatusId,
      fields: ['_id'],
    });

  // ERROR: Questionnaire status not found
  if (!questionnaireStatus)
    return res.status(404).json('ERROR_QUESTIONNAIRE_STATUS_MISSING');

  next();
};
