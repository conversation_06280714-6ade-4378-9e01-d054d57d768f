/* eslint-disable no-use-before-define */
/* eslint-disable no-console */
import _ from 'lodash';
import { Text } from 'slate';

import { isArray, isFunction } from '#utils/types.js';
import { importImage, getImageUrl } from './images.js';

const CRAFTJS_RICHTEXT_NODE = '759msudxZ_';

const markMappings = {
  bold: { type: 'bold' },
  italic: { type: 'italic' },
  subscript: { type: 'subscript' },
  superscript: { type: 'superscript' },
};

const nodeMappings = {
  'text': async ({ text, ...rest }) => {
    if (!text) return null;

    const node = {
      type: 'text',
      marks: [],
      text,
    };

    // Add text format (bold, italic, subscript, superscript)
    node.marks = getTextNodeMarks(rest).map((mark) => markMappings[mark]);

    // Remove property if empty
    if (_.isEmpty(node.marks)) delete node.marks;

    return node;
  },
  'heading-one': async ({ children }, entity, options) => ({
    type: 'heading',
    attrs: { level: 1 },
    content: await serializeChildren(children, entity, options),
  }),
  'heading-two': async ({ children }, entity, options) => ({
    type: 'heading',
    attrs: { level: 2 },
    content: await serializeChildren(children, entity, options),
  }),
  'heading-three': async ({ children }, entity, options) => ({
    type: 'heading',
    attrs: { level: 3 },
    content: await serializeChildren(children, entity, options),
  }),
  'paragraph': async ({ children }, entity, options) => {
    const content = await serializeChildren(children, entity, options);
    return {
      type: 'paragraph',
      ...(content && { content }),
    };
  },
  'unordered-list': async ({ children }, entity, options) => ({
    type: 'bulletList',
    content: await serializeChildren(children, entity, options),
  }),
  'ordered-list': async ({ children }, entity, options) => ({
    type: 'orderedList',
    attrs: { start: 1 },
    content: await serializeChildren(children, entity, options),
  }),
  'list-item': async ({ children }, entity, options) => ({
    type: 'listItem',
    content: [
      {
        type: 'paragraph',
        content: await serializeChildren(children, entity, options),
      },
    ],
  }),
  'quote': async ({ children }, entity, options) => ({
    type: 'blockquote',
    content: [
      {
        type: 'paragraph',
        content: await serializeChildren(children, entity, options),
      },
    ],
  }),
  'image': async (
    { caption, copyright, alt, clickEnlarge, alignment, image },
    entity,
    { existingContent } = {}
  ) => {
    // Prepare empty image
    let imageNode = {
      type: 'image',
      attrs: {
        caption,
        copyright,
        alt,
        file: {
          containerId: '', // '602bd46f36432128d40d5e81',
          extension: '', // '.jpg',
          name: '', // '8e41643297997886.jpg',
          originalFilename: '', // 'photo-1475938476802-32a7e851dad1',
          size: 0, // 216635,
          mime: '', // 'image/jpeg',
          width: 0, // 1950,
          height: 0, // 1302,
          blurhash: '', // 'LAEfWv}Q0001LgMd#5vy9FXn%M%2',
        },
        url: '',
        priority: '',
        align: '',
        clickToEnlarge: clickEnlarge,
      },
    };

    let previouslyDownloadedImageFound = false;
    if (existingContent) {
      const existingImageNode = existingContent.reduce((acc, node) => {
        if (acc) return acc;
        // Check images that are direct children
        if (
          node.type === 'image' &&
          node.attrs.file.size === image?.size &&
          node.attrs.file.width === image?.width &&
          node.attrs.file.height === image?.height
        ) {
          acc = node;
        }
        // Check images that are nested in paragraphs
        else if (node.type === 'paragraph') {
          const existingImageChildNode = node.content.find(
            (n) =>
              n.type === 'image' &&
              n.attrs.file.size === image?.size &&
              n.attrs.file.width === image?.width &&
              n.attrs.file.height === image?.height
          );
          if (existingImageChildNode) {
            acc = existingImageChildNode;
          }
        }
        return acc;
      }, null);
      if (existingImageNode) {
        // TEMPORARY FIX
        // ------------------------------------------------------------------------
        // Fix image alignment
        if (['left', 'right', 'center-medium'].includes(alignment)) {
          existingImageNode.attrs.align = alignment;
        }
        // Fix original filename
        existingImageNode.attrs.file.originalFilename = image.originalFilename
          ? image.originalFilename
          : existingImageNode.attrs.file.originalFilename;
        // ------------------------------------------------------------------------

        imageNode = existingImageNode;
        previouslyDownloadedImageFound = true;
        console.log(' --->>> IMAGE FOUND <<<---');
      }
    }

    // Download image (if not found)
    if (!previouslyDownloadedImageFound) {
      const imageUrl = getImageUrl({ image });
      if (imageUrl) {
        console.log('--->>> DOWNLOADING IMAGE <<<---');
        const imageFile = await importImage(
          imageUrl,
          entity,
          () => image.originalFilename
        );
        if (imageFile) {
          imageNode = {
            type: 'image',
            attrs: {
              caption,
              copyright,
              alt,
              file: imageFile,
              url: '',
              priority: '',
              align: ['left', 'right', 'center-medium'].includes(alignment)
                ? alignment
                : '',
              clickToEnlarge: clickEnlarge,
            },
          };
        }
      }
    }

    return imageNode;
  },
  // eslint-disable-next-line no-unused-vars
  'video': async ({ id, provider, caption, copyright }, entity, options) => {
    const providers = {
      youtube: 'youtube',
      vimeo: 'vimeo',
      jetstream: null,
    };
    if (!providers[provider]) {
      console.log(
        `No mapping found for node type 'video' and provider '${provider}'`
      );
      return null;
    }
    return {
      type: 'media',
      attrs: {
        caption,
        copyright,
        id,
        provider: providers[provider],
        startAt: null,
        endAt: null,
        priority: '',
        type: 'video',
      },
    };
  },
  'link': async ({ href, children }) => {
    // Different behaviour between slate and tiptap:
    // --> slate: one node with many children
    // --> tiptap: one node per child
    const nodes = await children?.reduce(async (accPromise, node) => {
      const acc = await accPromise;
      const textMapping = nodeMappings.text;
      const mapping = await textMapping(node);
      if (!mapping) return await acc; // Ignore empty nodes
      if (_.isEmpty(mapping.marks)) mapping.marks = [];
      mapping.marks.push({
        type: 'link',
        attrs: {
          href,
          type: 'external',
          target: '_blank',
        },
      });
      acc.push(mapping);
      return await acc;
    }, []);

    // Note: returns array, not object
    return nodes || [];
  },
  // eslint-disable-next-line no-unused-vars
  'bible-verse': async ({ passage, verses, translation }, entity, options) => ({
    type: 'bibleVerse',
    attrs: {
      passage,
      text: verses,
      bible: translation || '',
    },
  }),
  'footnote': async ({ children }, entity, options) => ({
    type: 'footnote',
    content: await serializeChildren(children, entity, options),
  }),
};

function getTextNodeMarks(node) {
  return Object.keys(node).filter(
    (mark) => node[mark] === true && markMappings[mark]
  );
}

function isEmptySlateDoc(value) {
  if (!value) return true;
  return (
    isArray(value) &&
    value.length === 1 &&
    value[0].type === 'paragraph' &&
    isArray(value[0].children) &&
    value[0].children.length === 1 &&
    value[0].children[0].text === ''
  );
}
async function serialize(node, entity, options = {}) {
  const type = !Text.isText(node) ? node.type : 'text';
  const mapping = nodeMappings[type];
  if (!mapping) {
    console.log(`No mapping found for node type '${type}'`);
    return null;
  }
  return isFunction(mapping) ? await mapping(node, entity, options) : mapping;
}

const serializeChildren = async (children, entity, options) =>
  await children?.reduce(async (accPromise, node) => {
    let acc = await accPromise;
    const _node = await serialize(node, entity, options);
    acc = _node ? (isArray(_node) ? [...acc, ..._node] : [...acc, _node]) : acc;
    return await acc;
    // return _node
    //   ? isArray(_node)
    //     ? [...acc, ..._node]
    //     : [...acc, _node]
    //   : acc;
  }, []);

/**
 * Transforms a Slate document to a Tiptap document
 * @param {Object} content - Slate document as JSON object
 * @param {Object} entity - AWE entity
 * @param {Object} options - Optional settings
 * @returns {Array} - Tiptap document as JSON object
 */
export default async function transform(content, entity, options = {}) {
  if (isEmptySlateDoc(content)) return null;
  return await content?.reduce(async (accPromise, node) => {
    const acc = await accPromise;
    const mapping = await serialize(node, entity, options);
    if (mapping) acc.push(mapping);
    return await acc;
  }, []);
}

/**
 * Gets RichText content from a CraftJS page
 * @param {String} craftJSContent - Craft.js page content as JSON string
 * @returns {Object} - RichText content as JSON object
 */
export function getRTEContent(craftJSContent) {
  if (!craftJSContent) return null;
  return JSON.parse(craftJSContent)?.[CRAFTJS_RICHTEXT_NODE].props.doc.content;
}

/**
 * Gets a CraftJS page with RichText content
 * @param {String} rteContent - RichText content string (Tiptap)
 * @returns {Object} - CraftJS page as JSON object
 */
export function getCraftJSPage(rteContent) {
  return {
    ROOT: {
      type: {
        resolvedName: 'Root',
      },
      isCanvas: true,
      props: {},
      displayName: 'Root',
      custom: {},
      hidden: false,
      nodes: [CRAFTJS_RICHTEXT_NODE],
      linkedNodes: {},
    },
    [CRAFTJS_RICHTEXT_NODE]: {
      type: {
        resolvedName: 'RichText',
      },
      isCanvas: false,
      props: {
        doc: {
          type: 'doc',
          content: isArray(rteContent) ? rteContent : [],
        },
      },
      displayName: 'RichText',
      custom: {
        type: 'content',
      },
      parent: 'ROOT',
      hidden: false,
      nodes: [],
      linkedNodes: {},
    },
  };
}
