import Logger from '#utils/logger.js';
import Questionnaire from '../../../models/Questionnaire.js';

import transform from '../utils/transform.js';

async function importQuestionnaire({
  questionnaire,
  slide,
  lesson,
  course,
  entity,
  content,
  providerId,
}) {
  let questionnaireId;

  Logger.info(`      - Questionnaire`);

  try {
    let existingQuestionnaire = await Questionnaire.findOne({
      // provider: providerId,
      course: course,
      lesson: lesson,
      slide: slide,
      _id: questionnaire.id,
    });

    const { id, isEnabled, createdAt, requiresRevision, notifyAdvisor } =
      questionnaire;

    const noQuestionNumbering = [
      '1. Après la chute, quoi faire ?',
      '1. <PERSON><PERSON><PERSON> le rédempteur',
      '1. La nature de la Loi',
      '1. Le livre de Dieu',
      '2. <PERSON><PERSON>, pourquoi Jésus ?',
      '2. Les origines',
      '2. Sauvé du péché',
      '3. Jésus nous rend justes (<PERSON><PERSON><PERSON> le justicier)',
      '3. Le péché',
      "3. Vers l'issue de secours.",
      '4. Une question de foi',
      'ARCHÉOLOGIE EN PAYS BIBLIQUES',
    ];

    const baseQuestionnaire = {
      slide,
      lesson,
      course,
      provider: providerId,
      enabled: isEnabled,
      deleted: false,
      requiresReview: requiresRevision,
      questionNumbering: !noQuestionNumbering.includes(course.title),
      notifyAdvisor,
      format: 'compact',
    };

    // Add questions
    if (questionnaire.questions) {
      const questions = [];
      const _content = await transform(content, entity);
      if (_content?.length) {
        // Find the first paragraph and use it as the title
        const contentTitle = _content.reduce((acc, node) => {
          if (acc === '' && node.type === 'paragraph') {
            const textNode = node.content?.find((child) => child.text);
            acc = textNode?.text?.split('\n')[0];
            return acc;
          }
          return acc;
        }, '');

        // Add the content as the first question
        questions.push({
          enabled: true,
          text: contentTitle || 'Intro',
          type: 'richText',
          config: {
            content: {
              type: 'doc',
              content: _content,
            },
          },
        });
      }

      // Add the questionnaire questions
      baseQuestionnaire.questions = questions.concat(
        await Promise.all(
          questionnaire.questions
            .filter((question) => !question.isDeleted)
            .sort((a, b) => (a.position - b.position ? 1 : -1))
            .map(async (question) => {
              const { text } = question;
              let { config, type } = question;

              switch (type) {
                case 'openAnswer':
                  config.rows = 5;
                  break;

                case 'essay':
                  type = 'openAnswer';
                  config.rows = 15;
                  break;

                case 'multipleOpenAnswers':
                  config.textBoxes =
                    config.textBoxes
                      ?.filter((textBox) => !textBox.isDeleted)
                      .map((textBox) => ({
                        id: textBox.id,
                        enabled: textBox.isEnabled,
                        text: textBox.label,
                        config: {
                          required: textBox.required,
                          rows: 5,
                        },
                      })) || [];
                  break;

                case 'multipleChoice':
                  type = config.singleOption
                    ? 'singleChoice'
                    : 'multipleChoice';
                  config.variant = config.singleOption ? 'default' : 'chip';
                  delete config.singleOption;
                  config.layout = 'vertical';
                  config.options =
                    config.options
                      ?.filter((option) => !option.isDeleted)
                      .map((option) => ({
                        id: option.id,
                        enabled: option.isEnabled,
                        label: option.label,
                        solution:
                          option.solution === 'wrong'
                            ? 'incorrect'
                            : option.solution === 'correct'
                              ? 'correct'
                              : 'neutral',
                      })) || [];
                  break;

                case 'fillInTheBlank':
                  config.hint = config.hint || '';
                  break;

                case 'sectionTitle':
                  config = {
                    as: 'h2',
                  };
                  break;

                case 'textAndImage':
                  type = 'richText';
                  config.content = {
                    type: 'doc',
                    content: await transform(config.content, entity),
                  };
                  break;

                case 'scale':
                default:
                  type = type;
              }

              return {
                _id: question.id,
                enabled: isEnabled,
                text,
                type,
                config,
              };
            })
        )
      );
    }

    // Update existing questionnaire
    if (existingQuestionnaire) {
      await existingQuestionnaire.updateOne(baseQuestionnaire);

      // Set questionnaire id
      questionnaireId = existingQuestionnaire._id;
    }

    // Create new questionnaire
    else {
      existingQuestionnaire = await Questionnaire.create({
        ...baseQuestionnaire,
        _id: id,
        createdAt,
      });

      // Set questionnaire id
      questionnaireId = existingQuestionnaire._id;
    }

    // Update slide requiresReview
    if (baseQuestionnaire.requiresReview !== slide.requiresReview) {
      slide.requiresReview = baseQuestionnaire.requiresReview;
      await slide.save();
    }
  } catch (error) {
    Logger.error(
      `      Error when trying to import questionnaire "${questionnaire.id}"`,
      error
    );
  }

  return questionnaireId;
}

export default importQuestionnaire;
