import Logger from '#utils/logger.js';

import WebUser from '#modules/web-auth/models/WebUser.js';
import Site from '#modules/web/models/Site.js';
import WebUserCollection from '#modules/web-auth/models/WebUserCollection.js';
import { areEqualIDs } from '#utils/helpers.js';

import Country from '../../../models/Country.js';
import Student from '../../../models/Student.js';
import Provider from '../../../models/Provider.js';

import importCourseStatuses from './courseStatuses.js';
import importStudentAdvisorMappings from './studentAdvisorMappings.js';
import importMessages from './messages.js';

async function importStudents(entity, providerId, ky, testUsername, options) {
  const results = {
    imported: 0,
    updated: 0,
  };

  try {
    Logger.info('Importing students');

    const provider = await Provider.findById(providerId);
    const site = await Site.findById(provider?.site);
    const webUserCollection = await WebUserCollection.findById(
      site?.auth?.userCollection
    );

    if (
      options.webUsers &&
      webUserCollection?.userProfile?.profileNameField !== 'username'
    ) {
      Logger.error('No web user collection found');
      return 'No web user collection found';
    }

    const courseCountries = await Country.find(
      {},
      {
        _id: 1,
        countryCode: 1,
      }
    );

    const data = await ky
      .get(`api/students`, {
        searchParams: {
          filter: JSON.stringify({
            fields: ['id'],
            where: {
              isDeleted: false,
              emailVerified: true,
              ...(testUsername && { username: testUsername }),
            },
          }),
        },
      })
      .json();
    const numberOfRecords = data.length;

    if (numberOfRecords === 0) {
      Logger.info('No students found to import');
      return;
    }

    const filter = {
      where: {
        isDeleted: false,
        emailVerified: true,
        ...(testUsername && { username: testUsername }),
      },
      order: 'createdAt ASC',
      include: [
        'roles',
        {
          relation: 'courseStatuses',
          scope: {
            include: [
              {
                relation: 'course',
                scope: { where: { isDeleted: false } },
                fields: ['isDeleted'],
              },
              {
                relation: 'questionnaireStatuses',
                scope: {
                  include: [
                    {
                      relation: 'answers',
                      scope: {
                        include: [
                          'question', // TODO: only return fields 'config' and 'type'
                          // {
                          //   relation: 'question',
                          //   scope: {
                          //     where: { isDeleted: false },
                          //     fields: ['config', 'type'],
                          //   },
                          // },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          relation: 'advisor',
          scope: {
            where: { isDeleted: false },
            fields: ['id', 'firstName', 'lastName', 'email'],
          },
        },
        {
          relation: 'advisors',
          scope: {
            where: { isDeleted: false },
            fields: ['id', 'firstName', 'lastName', 'email'],
          },
        },
      ],
    };

    const limit = 10;
    let skip = 0;

    while (skip < numberOfRecords) {
      const students = await ky
        .get('api/students', {
          searchParams: {
            filter: JSON.stringify({
              ...filter,
              limit,
              skip,
            }),
          },
        })
        .json();

      for (const student of students) {
        const result = await importStudent(
          student,
          entity,
          providerId,
          ky,
          courseCountries,
          webUserCollection,
          options
        );
        switch (result.status) {
          case 'new':
            results.imported += 1;
            break;
          case 'updated':
            results.updated += 1;
            break;
          default:
            break;
        }
      }

      // Increase the offset
      skip += limit;
    }

    Logger.info(`${results.imported} students imported`);
    Logger.info(`${results.updated} students updated`);
  } catch (error) {
    Logger.error('Error when trying to import student:', error);
  }

  return results;
}

async function importStudent(
  student,
  entity,
  providerId,
  ky,
  courseCountries,
  webUserCollection,
  options
) {
  const result = {
    status: null,
    record: null,
    error: null,
  };

  Logger.info(`- ${student.username}`);

  try {
    let existingStudent = await Student.findOne({
      _id: student.id,
    });

    const {
      id,
      username,
      isEnabled,
      createdAt,
      firstName,
      lastName,
      email,
      gender,
      birthday, // 1996-7-23
      // avatar --> never used
      // notes --> never used
      street,
      postalCode,
      city,
      countryId,
      regionId,
      phone,
      preferences,
      advisorId,
      staticAdvisor,
      // lastLoginAt --> add to web user?
      // lastLoginFrom --> add to web user?
      advisors,
      courseStatuses,
    } = student;

    const countryCode = student.countryCode?.toUpperCase() || '';

    const baseStudent = {
      entity,
      provider: providerId,
      enabled: isEnabled,
      deleted: false,
      gender,
      username,
      firstName,
      lastName,
      birthday: birthday ? new Date(birthday) : null,
      address: {
        ...(street && { street }),
        ...(city && { city }),
        zip: postalCode || '',
        country: countryCode,
      },
      profile: {
        username,
        email,
        firstName,
        lastName,
        birthday: birthday ? new Date(birthday) : null,
        address: {
          ...(street && { street }),
          ...(city && { city }),
          zip: postalCode || '',
          country: countryCode,
        },
      },
      email,
      phone,
      courseCountry:
        countryId ||
        courseCountries.find((c) => c.countryCode === countryCode)?._id ||
        null,
      courseCountryRegion: regionId || null,
      advisor: advisorId,
      staticAdvisor,
      preferences,
      importID: {
        type: 'meine.hopekurse.de',
        recordID: student.id,
      },
      importIDs: [
        {
          type: 'meine.hopekurse.de',
          recordID: student.id,
        },
      ],
    };

    // Update existing student
    if (existingStudent) {
      await existingStudent.updateOne(baseStudent);

      // Set result
      result.status = 'updated';
      result.record = existingStudent;
    }

    // Create new student
    else {
      // Create slug
      const slug = username;

      existingStudent = await Student.create({
        ...baseStudent,
        _id: id,
        slug,
        createdAt,
      });

      // Set result
      result.status = 'new';
      result.record = existingStudent;
    }

    if (existingStudent) {
      if (options.webUsers) {
        await importWebUser(student, existingStudent, webUserCollection);
      }

      if (options.courseStatuses) {
        await importCourseStatuses(courseStatuses, providerId);
      }

      if (options.messages) {
        const advisorIds = await importStudentAdvisorMappings(
          existingStudent,
          advisors,
          providerId
        );
        await importMessages(existingStudent, advisorIds, providerId, ky);
      }
    }
  } catch (error) {
    Logger.error(
      `Error when trying to import student "${student.username}"`,
      error
    );
    result.error = error;
  }

  return result;
}

async function importWebUser(student, existingStudent, webUserCollection) {
  let existingWebUser = await WebUser.findOne({
    'userCollection': webUserCollection,
    'profile.username': student.username,
  });

  const formFields = webUserCollection.userProfile.form.fields;

  // Create new web user
  if (!existingWebUser) {
    Logger.info(`  - Creating new web user`);
    existingWebUser = await WebUser.create({
      email: student.email,
      emailVerified: new Date(),
      userCollection: webUserCollection,
      profile: {
        username: student.username,
        ...(formFields.find((f) => f.name === 'eventAdvertisement') && {
          eventAdvertisement: student.preferences.eventAdvertisement,
        }),
        ...(formFields.find((f) => f.name === 'newsletter') && {
          newsletter: student.preferences.newsletter,
        }),
      },
    });
  }

  // Add web user to
  if (existingWebUser && !areEqualIDs(existingWebUser._id, student.user)) {
    await existingStudent.updateOne({ webUser: existingWebUser._id });
  }
}

export default importStudents;
