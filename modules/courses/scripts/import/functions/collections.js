import Logger from '#utils/logger.js';
import { slugify } from '#utils/strings.js';

import Collection from '../../../models/Collection.js';
import Course from '../../../models/Course.js';

async function importCollections(
  sourceEntityId,
  providerId,
  ky,
  testCollection
) {
  const results = {
    imported: 0,
    updated: 0,
  };

  try {
    Logger.info('Importing collections');

    const data = await ky
      .get(`api/entities/${sourceEntityId}/categories`, {
        searchParams: {
          filter: JSON.stringify({
            fields: ['id'],
            where: {
              isDeleted: false,
              ...(testCollection && { name: testCollection }),
            },
          }),
        },
      })
      .json();
    const numberOfRecords = data.length;

    if (numberOfRecords === 0) {
      Logger.info('No collections found to import');
      return;
    }

    const filter = {
      where: {
        isDeleted: false,
        ...(testCollection && { name: testCollection }),
      },
      order: 'position ASC',
      include: [
        {
          relation: 'courses',
          scope: {
            where: { isDeleted: false },
            fields: ['id', 'name'],
          },
        },
      ],
    };

    const collections = await ky
      .get(`api/entities/${sourceEntityId}/categories`, {
        searchParams: {
          filter: JSON.stringify(filter),
        },
      })
      .json();

    for (const collection of collections) {
      const result = await importCollection(collection, providerId);
      switch (result.status) {
        case 'new':
          results.imported += 1;
          break;
        case 'updated':
          results.updated += 1;
          break;
        default:
          break;
      }
    }

    Logger.info(`${results.imported} collections imported`);
    Logger.info(`${results.updated} collections updated`);
  } catch (error) {
    Logger.error('Error when trying to import collections:', error);
  }

  return results;
}

async function importCollection(collection, providerId) {
  const result = {
    status: null,
    record: null,
    error: null,
  };

  Logger.info(`- ${collection.name}`);

  try {
    const existingCollection = await Collection.findOne({
      _id: collection.id,
    });

    const { id, name, isEnabled, createdAt, position, courses } = collection;

    const baseCollection = {
      provider: providerId,
      enabled: isEnabled,
      deleted: false,
      position,
      title: name,
      courses: [],
    };

    // Add courses
    const existingCourses = await Course.find(
      {
        _id: { $in: courses.map((c) => c.id) },
        deleted: false,
      },
      { id: 1 }
    );
    const existingCourseIds = existingCourses.map((c) => c.id);
    courses.forEach((course) => {
      if (existingCourseIds.includes(course.id)) {
        baseCollection.courses.push(course.id);
      }
    });

    // Update existing collection
    if (existingCollection) {
      await existingCollection.updateOne(baseCollection);

      // Set result
      result.status = 'updated';
      result.record = existingCollection;
    }

    // Create new collection
    else {
      // Ensure collection has a valid slug within the provider
      const slug = await Collection.getAvailableSlug(slugify(name), providerId);

      const newCollection = await Collection.create({
        ...baseCollection,
        _id: id,
        slug,
        createdAt,
      });

      // Set result
      result.status = 'new';
      result.record = newCollection;
    }
  } catch (error) {
    Logger.error(
      `Error when trying to import collection "${collection.name}"`,
      error
    );
    result.error = error;
  }

  return result;
}

export default importCollections;
