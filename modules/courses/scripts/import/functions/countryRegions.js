import _ from 'lodash';

import Logger from '#utils/logger.js';

import CountryRegion from '../../../models/CountryRegion.js';

async function importCountryRegions(country, scopes, providerId) {
  if (_.isEmpty(scopes)) return;

  for (const scope of scopes) {
    await importCountryRegion(country, scope, providerId);
  }
}

async function importCountryRegion(country, scope, providerId) {
  try {
    let existingCountryRegion = await CountryRegion.findOne({
      _id: scope.id,
    });

    const { id, type, config, createdAt, isEnabled, advisorId } = scope;

    const baseCountryRegion = {
      provider: providerId,
      country: country._id, // TODO: countryCode??
      enabled: isEnabled,
      type,
      config,
      advisor: advisorId,
    };

    // Update existing region
    if (existingCountryRegion) {
      await existingCountryRegion.updateOne(baseCountryRegion);
      Logger.info(`  - region updated`);
    }

    // Create new region
    else {
      existingCountryRegion = await CountryRegion.create({
        ...baseCountryRegion,
        _id: id,
        createdAt,
      });
      Logger.info(`  - region imported`);
    }
  } catch (error) {
    Logger.error(
      `    Error when trying to import region for country "${country.countryCode}"`,
      error
    );
  }
}

export default importCountryRegions;
