import _ from 'lodash';

import Logger from '#utils/logger.js';
import Lesson from '../../../models/Lesson.js';

import importSlides from './slides.js';

import transform, {
  getCraftJSPage,
  getRTEContent,
} from '../utils/transform.js';

async function importLessons(course, entity, providerId, ky, options) {
  const lessons = await loadLessons(course, ky);
  if (_.isEmpty(lessons)) return [];

  const lessonIds = [];
  for (const lesson of lessons) {
    const lessonId = await importLesson(
      lesson,
      course,
      entity,
      providerId,
      options
    );
    if (lessonId) lessonIds.push(lessonId);
  }
  return lessonIds;
}

async function importLesson(lesson, course, entity, providerId, options) {
  let lessonId;

  Logger.info(`  - ${lesson.title}`);

  try {
    let existingLesson = await Lesson.findOne({
      course: course,
      _id: lesson.id,
    });

    const {
      id,
      isEnabled,
      createdAt,
      position,
      slug,
      title,
      content,
      // courseId,
      slides,
    } = lesson;

    const baseLesson = {
      provider: providerId,
      course,
      enabled: isEnabled,
      deleted: false,
      position,
      title,
      slug,
    };

    // Transform content from slate to tiptap
    if (options.content) {
      const _content = await transform(content, entity, {
        existingContent: existingLesson
          ? getRTEContent(existingLesson.content)
          : null,
      });
      if (_content) {
        baseLesson.content = JSON.stringify(getCraftJSPage(_content));
      }
    }

    // Update existing lesson
    if (existingLesson) {
      await existingLesson.updateOne(baseLesson);

      // Set lesson id
      lessonId = existingLesson._id;
    }

    // Create new lesson
    else {
      existingLesson = await Lesson.create({
        ...baseLesson,
        _id: id,
        slug,
        createdAt,
      });

      // Set lesson id
      lessonId = existingLesson._id;
    }

    if (existingLesson && options.slides) {
      const slideIds = await importSlides(
        slides,
        existingLesson,
        course,
        entity,
        providerId,
        options
      );
      if (!_.isEmpty(slideIds)) {
        existingLesson.slides = slideIds;
        existingLesson.save();
      }
    }
  } catch (error) {
    Logger.error(
      `    Error when trying to import lesson "${lesson.title}"`,
      error
    );
  }

  return lessonId;
}

async function loadLessons(course, ky) {
  const lessons = await ky
    .get(`api/courses/${course.id}/lessons`, {
      searchParams: {
        filter: JSON.stringify({
          where: { isDeleted: false },
          order: 'position ASC',
          include: [
            {
              relation: 'slides',
              scope: {
                where: { isDeleted: false },
                order: 'position ASC',
                include: [
                  {
                    relation: 'questionnaire',
                    scope: {
                      where: { isDeleted: false },
                      include: [
                        {
                          relation: 'questions',
                          scope: {
                            where: { isDeleted: false },
                            order: 'position ASC',
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            },
          ],
        }),
      },
    })
    .json();

  return lessons;
}

export default importLessons;
