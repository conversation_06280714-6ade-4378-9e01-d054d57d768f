import Logger from '#utils/logger.js';

import Country from '../../../models/Country.js';

import importCountryRegions from './countryRegions.js';

async function importCountries(sourceEntityId, providerId, ky) {
  const results = {
    imported: 0,
    updated: 0,
  };

  try {
    Logger.info('Importing countries');

    const data = await ky
      .get(`api/entities/${sourceEntityId}/countries`, {
        searchParams: {
          filter: JSON.stringify({
            fields: ['id'],
            where: {
              isDeleted: false,
            },
          }),
        },
      })
      .json();
    const numberOfRecords = data.length;

    if (numberOfRecords === 0) {
      Logger.info('No countries found to import');
      return;
    }

    const filter = {
      where: { isDeleted: false },
      order: 'createdAt ASC',
      include: [
        {
          relation: 'scopes',
          scope: {
            where: { isDeleted: false },
          },
        },
      ],
    };

    const limit = 10;
    let skip = 0;

    while (skip < numberOfRecords) {
      const countries = await ky
        .get(`api/entities/${sourceEntityId}/countries`, {
          searchParams: {
            filter: JSON.stringify({
              ...filter,
              limit,
              skip,
            }),
          },
        })
        .json();

      for (const country of countries) {
        const result = await importCountry(country, providerId);
        switch (result.status) {
          case 'new':
            results.imported += 1;
            break;
          case 'updated':
            results.updated += 1;
            break;
          default:
            break;
        }
      }

      // Increase the offset
      skip += limit;
    }

    Logger.info(`${results.imported} countries imported`);
    Logger.info(`${results.updated} countries updated`);
  } catch (error) {
    Logger.error('Error when trying to import country:', error);
  }

  return results;
}

async function importCountry(country, providerId) {
  const result = {
    status: null,
    record: null,
    error: null,
  };

  Logger.info(`- ${country.countryCode}`);

  try {
    let existingCountry = await Country.findOne({
      _id: country.id,
    });

    const {
      id,
      isEnabled,
      createdAt,
      countryCode,
      scopeType,
      scopeTitleSingular,
      scopeTitlePlural,
      advisorId,
      scopes,
    } = country;

    const baseCountry = {
      provider: providerId,
      enabled: isEnabled,
      deleted: false,
      countryCode: countryCode.toUpperCase(),
      regionType: scopeType,
      regionTitleSingular: scopeTitleSingular,
      regionTitlePlural: scopeTitlePlural,
      advisor: advisorId,
    };

    // Update existing country
    if (existingCountry) {
      await existingCountry.updateOne(baseCountry);

      // Set result
      result.status = 'updated';
      result.record = existingCountry;
    }

    // Create new country
    else {
      existingCountry = await Country.create({
        ...baseCountry,
        _id: id,
        createdAt,
      });

      // Set result
      result.status = 'new';
      result.record = existingCountry;
    }

    if (existingCountry) {
      await importCountryRegions(existingCountry, scopes, providerId);
    }
  } catch (error) {
    Logger.error(
      `Error when trying to import country "${country.countryCode}"`,
      error
    );
    result.error = error;
  }

  return result;
}

export default importCountries;
