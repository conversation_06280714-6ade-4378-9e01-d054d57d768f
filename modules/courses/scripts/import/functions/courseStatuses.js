import _ from 'lodash';

import Logger from '#utils/logger.js';
import { areEqualIDs } from '#utils/helpers.js';

import Course from '../../../models/Course.js';
import CourseStatus from '../../../models/CourseStatus.js';

import importQuestionnaireStatuses from './questionnaireStatuses.js';

async function importCourseStatuses(courseStatuses, providerId) {
  if (_.isEmpty(courseStatuses)) return [];

  const courseStatusIds = [];
  for (const courseStatus of courseStatuses) {
    const courseStatusId = await importCourseStatus(courseStatus, providerId);
    if (courseStatusId) courseStatusIds.push(courseStatusId);
  }
  return courseStatusIds;
}

async function importCourseStatus(courseStatus, providerId) {
  let courseStatusId = null;

  // Skip deleted courses
  if (!courseStatus.course || courseStatus.course.isDeleted) return;

  try {
    let existingCourseStatus = await CourseStatus.findOne({
      _id: courseStatus.id,
    });

    const {
      id,
      createdAt,
      updatedAt,
      studentId,
      courseId,
      finishedAt,
      slidesRead,
      reachedLessonId,
      activeLessonId,
      activeSlideId,
      questionnaireStatuses,
    } = courseStatus;

    // Get lessons and slides of course
    const { lessons, title } = await Course.findById(courseId)
      .select({ id: 1, lessons: 1, title: 1 })
      .populate({
        path: 'lessons',
        match: { deleted: false, enabled: true },
        select: { id: 1, slides: 1 },
        populate: {
          path: 'slides',
          match: { deleted: false, enabled: true },
          select: { id: 1, type: 1, questionnaire: 1 },
          populate: {
            path: 'questionnaire',
            select: { id: 1, questions: 1, requiresReview: 1 },
          },
        },
      });

    // Set started slides
    const startedSlideIds = slidesRead;

    // Set completed slides
    const completedSlideIds = startedSlideIds.filter((slideId) => {
      // Get slide type and questionnaire
      const { type, questionnaire } = lessons.reduce((acc, lesson) => {
        const slide = lesson.slides.find((s) => areEqualIDs(s.id, slideId));
        if (slide) acc = slide;
        return acc;
      }, {});

      // If slide is plain content, it is completed
      if (type === 'content') return true;

      // If slide is questionnaire
      if (type === 'questionnaire' && questionnaire) {
        const { submittedAt, correctedAt } =
          questionnaireStatuses.find((questionnaireStatus) => {
            const { questionnaireId } = questionnaireStatus;
            return areEqualIDs(questionnaireId, questionnaire.id);
          }) || {};

        // Slide is completed, if it was submitted (and corrected if required)
        return questionnaire.requiresReview
          ? submittedAt && correctedAt
          : submittedAt;
      }
      return false;
    });

    // Set started lessons (all lessons until reached lesson)
    const lessonIds = lessons.map((lesson) => lesson.id);
    const startedLessonIds = lessonIds.slice(
      0,
      lessonIds.indexOf(reachedLessonId) + 1
    );

    // Set completed lessons
    const completedLessonIds = startedLessonIds.filter((lessonId) => {
      const lesson = lessons.find((l) => areEqualIDs(l.id, lessonId));
      if (lesson) {
        // Get all lesson slides
        const lessonSlideIds = lesson.slides.map((slide) => slide.id);

        // Check if all slides are completed
        return lessonSlideIds.every((slideId) =>
          completedSlideIds.includes(slideId)
        );
      }
      return false;
    });

    // Set reached lesson
    const _reachedLessonId =
      startedLessonIds.length === completedLessonIds.length
        ? // next lesson (or last lesson) if all started lessons are completed
          lessonIds[startedLessonIds.length] || // next lesson
          lessonIds[startedLessonIds.length - 1] // last lesson
        : // last started lesson
          lessonIds[startedLessonIds.length - 1];

    const baseCourseStatus = {
      student: studentId,
      course: courseId,
      startedAt: createdAt,
      updatedAt,
      createdAt,
      finishedAt,
      activeLesson: activeLessonId,
      activeSlide: activeSlideId,

      startedSlides: startedSlideIds,
      completedSlides: completedSlideIds,
      startedLessons: startedLessonIds,
      completedLessons: completedLessonIds,
      reachedLesson: _reachedLessonId, // before: reachedLessonId

      reachedLesson_deprecated: reachedLessonId, // deprecated
      slidesRead: null, // deprecated - replaced by startedSlides (slidesRead)

      mode: 'supervised',
      advisor: null, // will be set later
      provider: providerId,
    };

    // Update existing course status
    if (existingCourseStatus) {
      await existingCourseStatus.updateOne(baseCourseStatus, {
        timestamps: false,
      });
      Logger.info(`  - course status updated (${title})`);
    }

    // Create new course status
    else {
      existingCourseStatus = await CourseStatus.create({
        ...baseCourseStatus,
        _id: id,
      });

      // Set timestamps (not possible in create...)
      await existingCourseStatus.updateOne(
        { updatedAt, createdAt },
        { timestamps: false }
      );
      Logger.info(`  - course status imported (${title})`);
    }

    // Set course status id
    courseStatusId = existingCourseStatus._id;

    if (existingCourseStatus) {
      // Import questionnaire statuses
      const importedQuestionnaireStatuses = await importQuestionnaireStatuses(
        questionnaireStatuses,
        courseId,
        providerId
      );

      // Set advisor of last advisor who corrected a questionnaire
      const lastAdvisorId = importedQuestionnaireStatuses
        .reverse()
        .find(({ advisorId }) => advisorId)?.advisorId;
      if (
        lastAdvisorId &&
        !areEqualIDs(lastAdvisorId, existingCourseStatus.advisor)
      ) {
        await existingCourseStatus.updateOne(
          { advisor: lastAdvisorId },
          { timestamps: false }
        );
      }
    }
  } catch (error) {
    Logger.error('Error when trying to import course status:', error);
  }

  return courseStatusId;
}

export default importCourseStatuses;
