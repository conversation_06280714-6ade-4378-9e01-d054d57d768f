import _ from 'lodash';

import Logger from '#utils/logger.js';

import QuestionnaireStatus from '../../../models/QuestionnaireStatus.js';

async function importQuestionnaireStatuses(
  questionnaireStatuses,
  courseId,
  providerId
) {
  if (_.isEmpty(questionnaireStatuses)) return [];

  const importedQuestionnaireStatuses = [];
  for (const questionnaireStatus of questionnaireStatuses) {
    const result = await importQuestionnaireStatus(
      questionnaireStatus,
      courseId,
      providerId
    );
    if (!result.error) {
      importedQuestionnaireStatuses.push(result);
    }
  }
  return importedQuestionnaireStatuses;
}

async function importQuestionnaireStatus(
  questionnaireStatus,
  courseId,
  providerId
) {
  const result = {
    questionnaireStatusId: null,
    advisorId: null,
  };

  try {
    let existingQuestionnaireStatus = await QuestionnaireStatus.findOne({
      _id: questionnaireStatus.id,
    });

    const {
      id,
      updatedAt,
      createdAt,
      savedAt,
      submittedAt,
      correctedAt,
      studentId,
      advisorId,
      questionnaireId,
      courseStatusId,
      answers,
    } = questionnaireStatus;

    const baseQuestionnaireStatus = {
      student: studentId,
      advisor: advisorId,
      course: courseId,
      questionnaire: questionnaireId,
      courseStatus: courseStatusId,
      updatedAt,
      savedAt,
      reviewRequested: true,
      submittedAt,
      correctedAt,
      advisorNotesRead: !!correctedAt,
      provider: providerId,
    };

    // Add answers
    if (answers) {
      const { _answers, _advisorNotes } =
        answers?.reduce(
          (acc, answer) => {
            const { advisorNotes, questionId, question, value } = answer;
            if (!question || question.isDeleted) return acc;

            const { type, config } = question;

            let answerValue;
            let advisorNotesValue;
            switch (type) {
              case 'openAnswer':
              case 'essay':
                answerValue = value.text;
                advisorNotesValue = advisorNotes;

                break;
              case 'multipleOpenAnswers':
                answerValue = value.textBoxes;
                advisorNotesValue = advisorNotes?.textBoxes; // TODO: || {}
                break;
              case 'scale':
                answerValue = value.choice;
                advisorNotesValue = advisorNotes;
                break;
              case 'multipleChoice':
                answerValue = config.singleOption
                  ? value.choice
                  : value?.options;
                advisorNotesValue = config.singleOption
                  ? advisorNotes
                  : advisorNotes?.options;
                break;
              case 'fillInTheBlank':
                answerValue = value;
                advisorNotesValue = advisorNotes;
                break;
            }

            // Set answer
            acc._answers[questionId] = answerValue;

            // Set advisor notes
            acc._advisorNotes[questionId] = advisorNotesValue;
            return acc;
          },
          {
            _answers: {},
            _advisorNotes: {},
          }
        ) || {};

      baseQuestionnaireStatus.answers = _answers;
      baseQuestionnaireStatus.advisorNotes = _advisorNotes;

      /*
      baseQuestionnaireStatus.answers = answers?.reduce((acc, answer) => {
        const { advisorNotes, questionId, question, value } = answer;
        if (!question || question.isDeleted) return acc;

        const { type, config } = question;

        let newValue;
        switch (type) {
          case 'openAnswer':
          case 'essay':
            newValue = value.text;
            break;
          case 'multipleOpenAnswers':
            newValue = value.textBoxes;
            break;
          case 'scale':
            newValue = value.choice;
            break;
          case 'multipleChoice':
            newValue = config.singleOption ? value.choice : value.options;
            break;
          case 'fillInTheBlank':
            newValue = value;
            break;
        }
        acc[questionId] = {
          id: questionId,
          type,
          value: newValue,
          // advisorNotes, // TODO: move this to baseQuestionnaireStatus.advisorNotes
        };
        return acc;
      }, {});
      */
    }

    // Update existing questionnaire status
    if (existingQuestionnaireStatus) {
      await existingQuestionnaireStatus.updateOne(baseQuestionnaireStatus, {
        timestamps: false,
      });
      Logger.info(`    - questionnaire status updated`);
    }

    // Create new questionnaire status
    else {
      existingQuestionnaireStatus = await QuestionnaireStatus.create({
        ...baseQuestionnaireStatus,
        _id: id,
      });

      // Set timestamps (not possible in create...)
      await existingQuestionnaireStatus.updateOne(
        { updatedAt, createdAt },
        { timestamps: false }
      );
      Logger.info(`    - questionnaire status imported`);
    }

    // Set result
    result.questionnaireStatusId = existingQuestionnaireStatus._id;
    result.advisorId = advisorId;
  } catch (error) {
    Logger.error('Error when trying to import questionnaire status:', error);
    result.error = 'Error when trying to import questionnaire status';
  }

  return result;
}

export default importQuestionnaireStatuses;
