import _ from 'lodash';

import Logger from '#utils/logger.js';
import Slide from '../../../models/Slide.js';
import transform, {
  getCraftJSPage,
  getRTEContent,
} from '../utils/transform.js';

import importQuestionnaire from './questionnaires.js';

async function importSlides(
  slides,
  lesson,
  course,
  entity,
  providerId,
  options
) {
  if (_.isEmpty(slides)) return [];

  const slideIds = [];
  for (const slide of slides) {
    const slideId = await importSlide(
      slide,
      lesson,
      course,
      entity,
      providerId,
      options
    );
    if (slideId) slideIds.push(slideId);
  }
  return slideIds;
}

async function importSlide(slide, lesson, course, entity, providerId, options) {
  let slideId;

  Logger.info(`    - ${slide.title}`);

  try {
    let existingSlide = await Slide.findOne({
      course: course,
      lesson: lesson,
      _id: slide.id,
    });

    const {
      id,
      isEnabled,
      createdAt,
      position,
      slug,
      title,
      content,
      type,
      questionnaire,
    } = slide;

    const baseSlide = {
      provider: providerId,
      type,
      lesson,
      course,
      enabled: isEnabled,
      deleted: false,
      position,
      title,
      slug,
    };

    // Transform content from slate to tiptap
    if (options.content) {
      if (type === 'content') {
        const _content = await transform(content, entity, {
          existingContent: existingSlide
            ? getRTEContent(existingSlide.content)
            : null,
        });
        if (_content) {
          baseSlide.content = JSON.stringify(getCraftJSPage(_content));
        }
      }
    }

    // Fix for Hope Bible France: They only used slides of type questionnaire, even to display only rich text content. Doing so, students
    // would need to "submit" a slide (questionnaire) without questions. The advisors on their side would need to correct
    // a lot of questionnaires without answers.
    if (type === 'questionnaire' && questionnaire.questions.length === 0) {
      baseSlide.type = 'content';
      if (options.content) {
        const _content = await transform(content, entity, {
          existingContent: existingSlide
            ? getRTEContent(existingSlide.content)
            : null,
        });
        if (_content) {
          baseSlide.content = JSON.stringify(getCraftJSPage(_content));
        }
      }
    }

    // Update existing slide
    if (existingSlide) {
      await existingSlide.updateOne(baseSlide);

      // Set slide id
      slideId = existingSlide._id;
    }

    // Create new slide
    else {
      existingSlide = await Slide.create({
        ...baseSlide,
        _id: id,
        slug,
        createdAt,
      });

      // Set slide id
      slideId = existingSlide._id;
    }

    if (
      existingSlide &&
      existingSlide.type === 'questionnaire' &&
      questionnaire &&
      options.questionnaires
    ) {
      const questionnaireId = await importQuestionnaire({
        questionnaire,
        slide: existingSlide,
        content,
        lesson,
        course,
        entity,
        providerId,
      });
      if (questionnaireId) {
        existingSlide.questionnaire = questionnaireId;
        existingSlide.save();
      }
    }
  } catch (error) {
    Logger.error(
      `      Error when trying to import slide "${slide.title}"`,
      error
    );
  }

  return slideId;
}

export default importSlides;
