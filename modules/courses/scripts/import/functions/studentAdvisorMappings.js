import _ from 'lodash';

import Logger from '#utils/logger.js';
// TODO: Is this import needed?
// eslint-disable-next-line no-unused-vars
import Advisor from '../../../models/Advisor.js';
import StudentAdvisor from '../../../models/StudentAdvisor.js';
// TODO: Is this import needed?
// import Student from '../../../models/Student.js';

async function importStudentAdvisorMappings(student, advisors, providerId) {
  // Delete existing mappings
  await deleteStudentAdvisorMappings(student);

  // Return if no advisors
  if (_.isEmpty(advisors)) return [];

  const advisorIds = [];
  for (const advisor of advisors) {
    const advisorId = await importStudentAdvisorMapping(
      student,
      advisor,
      providerId
    );
    if (advisorId) advisorIds.push(advisorId);
  }
  return advisorIds;
}

async function importStudentAdvisorMapping(student, advisor, providerId) {
  let advisorId = null;

  try {
    let existingMapping = await StudentAdvisor.findOne({
      advisor: advisor.id,
      student: student._id,
      // provider: providerId,
    });

    const baseMapping = {
      advisor: advisor.id,
      student: student._id,
      provider: providerId,
    };

    // Update existing mapping
    if (existingMapping) {
      await existingMapping.updateOne(baseMapping);
      Logger.info(`  - advisor mapping updated`);

      // Set advisor id
      advisorId = advisor.id;
    }

    // Create new mapping
    else {
      existingMapping = await StudentAdvisor.create({
        ...baseMapping,
      });
      Logger.info(`  - advisor mapping imported`);

      // Set advisor id
      advisorId = advisor.id;
    }
  } catch (error) {
    Logger.error(
      `    Error when trying to import mapping for student "${student.username} / ${advisor.firstName} ${advisor.lastName}"`,
      error
    );
  }

  return advisorId;
}

async function deleteStudentAdvisorMappings(student) {
  await StudentAdvisor.deleteMany({
    student: student._id,
  });
}

export default importStudentAdvisorMappings;
