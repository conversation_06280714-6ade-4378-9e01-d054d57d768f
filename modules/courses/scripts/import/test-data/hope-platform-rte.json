{"ROOT": {"type": {"resolvedName": "Root"}, "isCanvas": true, "props": {}, "displayName": "Root", "custom": {}, "hidden": false, "nodes": ["759msudxZ_"], "linkedNodes": {}}, "759msudxZ_": {"type": {"resolvedName": "RichText"}, "isCanvas": false, "props": {"doc": {"type": "doc", "content": [{"type": "heading", "attrs": {"level": 1}, "content": [{"type": "text", "text": "<PERSON>a"}, {"type": "text", "marks": [{"type": "link", "attrs": {"href": "https://test.com", "type": "external", "target": "_blank"}}], "text": "din"}, {"type": "text", "text": "g "}, {"type": "text", "marks": [{"type": "bold"}], "text": "One"}, {"type": "text", "text": " 1"}]}, {"type": "heading", "attrs": {"level": 2}, "content": [{"type": "text", "text": "Heading 2"}]}, {"type": "heading", "attrs": {"level": 3}, "content": [{"type": "text", "text": "Heading 3"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Normal paragraph with "}, {"type": "text", "marks": [{"type": "bold"}], "text": "bold"}, {"type": "text", "text": " and "}, {"type": "text", "marks": [{"type": "italic"}], "text": "italic"}, {"type": "text", "text": " text, and a "}, {"type": "text", "marks": [{"type": "bold"}], "text": "nicely "}, {"type": "text", "marks": [{"type": "bold"}, {"type": "italic"}], "text": "combined"}, {"type": "text", "marks": [{"type": "bold"}], "text": " text2 with "}, {"type": "text", "marks": [{"type": "link", "attrs": {"href": "https://admin.hopekurse.de", "type": "external", "target": "_blank"}}, {"type": "bold"}], "text": "li"}, {"type": "text", "marks": [{"type": "link", "attrs": {"href": "https://admin.hopekurse.de", "type": "external", "target": "_blank"}}, {"type": "bold"}, {"type": "italic"}], "text": "n"}, {"type": "text", "marks": [{"type": "link", "attrs": {"href": "https://admin.hopekurse.de", "type": "external", "target": "_blank"}}, {"type": "bold"}], "text": "k"}, {"type": "text", "text": ", and with a single "}, {"type": "text", "marks": [{"type": "link", "attrs": {"href": "https://meine.hopekurse.de", "type": "external", "target": "_blank"}}], "text": "link"}, {"type": "text", "text": "."}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Another text with subscript and superscript."}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Unordered list:"}]}, {"type": "bulletList", "content": [{"type": "listItem", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Item A"}, {"type": "hardBreak"}, {"type": "text", "text": "Item A"}]}]}, {"type": "listItem", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "<PERSON><PERSON>"}]}]}, {"type": "listItem", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Item C"}]}]}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Ordered list:"}]}, {"type": "orderedList", "attrs": {"start": 1}, "content": [{"type": "listItem", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Item A"}]}]}, {"type": "listItem", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "<PERSON><PERSON>"}]}]}, {"type": "listItem", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Item C"}]}]}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Here comes a quote:"}]}, {"type": "blockquote", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "If you can’t fly, then run, If you can’t run, then walk, If you can’t walk, then crawl, but whatever you do, you have to keep moving forward. <PERSON>"}]}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Here comes a bible verse:"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "NOT SUPPORTED YET"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Here comes an image:"}]}, {"type": "image", "attrs": {"caption": "Do what is great", "copyright": "Photo by <PERSON> | Unsplash", "file": {"containerId": "602bd46f36432128d40d5e81", "extension": ".jpeg", "name": "ynp1682412778170.jpeg", "originalFilename": "photo-1503437313881-503a91226402", "size": 144961, "mime": "image/jpeg", "width": 2064, "height": 1161, "blurhash": "LC7ntMx]4TD$.TogI9RPE1NF%2xv"}, "url": "https://unsplash.com/de/fotos/5Xwaj9gaR0g", "priority": "", "clickToEnlarge": true}}, {"type": "paragraph", "content": [{"type": "text", "text": "Here comes a YouTube video:"}]}, {"type": "media", "attrs": {"caption": "The Breathtaking Beauty of Nature", "copyright": "Eredus | YouTube", "id": "IUN664s7N-c", "provider": "youtube", "startAt": null, "endAt": null, "priority": "", "type": "video"}}, {"type": "paragraph", "content": [{"type": "text", "text": "Here comes a Vimeo video:"}]}, {"type": "media", "attrs": {"caption": "WRITE YOUR LINE - <PERSON>", "copyright": "PVS Company | Vimeo", "id": "*********", "provider": "vimeo", "startAt": null, "endAt": null, "priority": "", "type": "video"}}, {"type": "paragraph", "content": [{"type": "text", "text": "Here comes a Jetstream video:"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "NOT SUPPORTED YET"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Here comes a footnote:"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "NOT SUPPORTED YET"}]}]}}, "displayName": "RichText", "custom": {"type": "content"}, "parent": "ROOT", "hidden": false, "nodes": [], "linkedNodes": {}}}