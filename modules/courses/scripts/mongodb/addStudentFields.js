const db = {};
const ObjectId = (id) => id;

// ---------------------------------------- //
//    Scripts to be run in mongodb shell    //
// ---------------------------------------- //

// Add profile field
db.coursestudents.updateMany(
  {
    // entity: ObjectId("602bd46f36432128d40d5e81"), // Hope Software
    // provider: ObjectId("653b8fdddfb6c893bc36dc92"), // Hope Kurse
    // _id: ObjectId('5fa92c40b43bf93d0dff9bbb'), // Test user
  },
  [
    {
      $set: {
        profile: {
          username: '$username',
          firstName: '$firstName',
          lastName: '$lastName',
          email: '$email',
          birthday: '$birthday',
          address: '$address',
        },
      },
    },
  ]
);

// <PERSON><PERSON><PERSON> to create an importIDs field with the content of the importID field.
// If the importID field is missing, the importIDs field will not be created.
db.coursestudents.updateMany(
  {
    // entity: ObjectId("602bd46f36432128d40d5e81"), // Hope Software
    // provider: ObjectId("653b8fdddfb6c893bc36dc92"), // Hope Kurse
    _id: ObjectId('5fa92c40b43bf93d0dff9bbb'), // Test user
  },
  [
    {
      $set: {
        importIDs: {
          $cond: {
            if: {
              $and: [
                // Check if importID exists (not null or missing)
                { $ifNull: ['$importID', false] },
                // Check if importID is NOT already in importIDs
                {
                  $not: { $in: ['$importID', { $ifNull: ['$importIDs', []] }] },
                },
              ],
            },
            then: {
              $concatArrays: [
                { $ifNull: ['$importIDs', []] }, // Be sure to keep existing importIDs
                ['$importID'], // Add importID to the array
              ],
            },
            else: '$importIDs', // Keep field as it was (field will not be created if it was missing before)
          },
        },
      },
    },
  ]
);

// Delete student fields
db.coursestudents.updateMany(
  {
    // entity: ObjectId("602bd46f36432128d40d5e81"), // Hope Software
    // provider: ObjectId("653b8fdddfb6c893bc36dc92"), // Hope Kurse
    _id: ObjectId('5fa92c40b43bf93d0dff9bbb'), // Test user
  },
  [
    {
      $unset: ['username', 'importID'],
    },
  ]
);
