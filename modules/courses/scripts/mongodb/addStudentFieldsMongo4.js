const db = {};
const ObjectId = (id) => id;

// ---------------------------------------- //
//    Scripts to be run in mongodb shell    //
// ---------------------------------------- //

// Add profile field
db.coursestudents
  .find({
    // entity: ObjectId("602bd46f36432128d40d5e81"), // Hope Software
    // provider: ObjectId("653b8fdddfb6c893bc36dc92"), // Hope Kurse
    // _id: ObjectId('5f7ee273310fae700f386cda'), // olivierguy
  })
  .forEach((student) => {
    const profile = {
      username: student.username,
      firstName: student.firstName,
      lastName: student.lastName,
      email: student.email,
      birthday: student.birthday,
      address: student.address,
    };

    db.coursestudents.updateOne({ _id: student._id }, { $set: { profile } });
  });

// Script to create an importIDs field with the content of the importID field.
// If the importID field is missing, the importIDs field will not be created.
db.coursestudents
  .find({
    // entity: ObjectId("602bd46f36432128d40d5e81"), // Hope Software
    // provider: ObjectId("653b8fdddfb6c893bc36dc92"), // Hope Kurse
    // _id: ObjectId('5f7ee273310fae700f386cda'), // olivierguy
  })
  .forEach((student) => {
    const importIDs = student.importID ? [student.importID] : [];
    db.coursestudents.updateOne({ _id: student._id }, { $set: { importIDs } });
  });

// Delete student fields
db.coursestudents
  .find({
    // entity: ObjectId("602bd46f36432128d40d5e81"), // Hope Software
    // provider: ObjectId("653b8fdddfb6c893bc36dc92"), // Hope Kurse
    _id: ObjectId('5f7ee273310fae700f386cda'), // olivierguy
  })
  .forEach((student) => {
    db.coursestudents.updateOne(
      { _id: student._id },
      { $unset: { importID: '' } }
    );
  });
