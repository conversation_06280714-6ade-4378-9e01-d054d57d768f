# FileMaker Import API Documentation

## API

**Endpoint:**

`http://localhost:3999/v1/courses/students/filemaker-import`

**Required params:**

- `entity`: The entity ID
- `provider`: The provider ID
- `advisor`: The advisor ID (the owner of the file that will be imported)
- `file`: The XML file that was exported by FileMaker and that is located in the `data` folder

**Optional params:**

- `filter`: You can filter by any field in the XML file (e.g. "Konfession: Adventist")

**Testing params:**

- `analyze`: Comma-separated list of fields you want to analyze before importing the data (e.g. EGebdat, Konfession, Kontakt, Anrede, Personenkreise, Titel, ESEX, EFS, DSVO Veranstaltungen, DSVO Newsletter Spenden, DSVO SDH, Ausland)
- `dry`: Run the script in dry mode (nothing will be written to the DB)
- `onlineStudents`: If passed, online students will be imported/synchronized, else only correspondence students will be imported

## Prerequisites

- Export files from FileMaker and copy them to the `data` folder
- Run migration script for Student data (addStudentFields.js). Must be run in mongodb shell

## Analyze data

1. Run script with `analyze` param, then check mappings and create eventually data in DB

- countries (Countries in DB)
- denominations (Provider settings)
- studentSources (Provider settings)
- maritalStatus (Student.js)

2. Run script with `dry`, then check the log file

a. Add usernames mappings to `usernames.js`

## Settings

Check the `settings.js` file:

- onlineStudents: Choose whether the online students should be imported
- courses: Choose whether the courses should be imported

## Run script

1. Run script in dry mode first
2. Run script
3. Check log file, and update diffs manually in AWE
