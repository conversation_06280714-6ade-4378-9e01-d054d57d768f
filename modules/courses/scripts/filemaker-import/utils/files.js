import fs from 'fs';

const rootPath = 'modules/courses/scripts/filemaker-import/data';

export function fileExists(fileName) {
  const filePath = `${rootPath}/${fileName}`;
  return fs.existsSync(filePath);
}

export function getReadStream(fileName) {
  const filePath = `${rootPath}/${fileName}`;
  const readStream = fs.createReadStream(filePath, { encoding: 'utf-8' });
  return readStream;
}

export function getWriteStream(fileName) {
  const filePath = `${rootPath}/${fileName}`;
  const writeStream = fs.createWriteStream(filePath, { flags: 'w' }); // 'w' for write/overwrite
  return writeStream;
}
