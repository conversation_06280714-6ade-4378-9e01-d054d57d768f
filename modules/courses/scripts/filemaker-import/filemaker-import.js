import Logger from '#utils/logger.js';

import loadMetaData from './helpers/loadMetaData.js';
import importStudent from './functions/student.js';

import { fileExists, getReadStream, getWriteStream } from './utils/files.js';
import getXmlParser from './utils/getXmlParser.js';

const importData = async function ({
  entityId,
  providerId,
  advisorId,
  fileName,
  filter,
  analyzeFields,
  dryMode,
  onlineStudents,
}) {
  Logger.info('Starting filemaker import');

  const results = analyzeFields
    ? {}
    : {
        online: {
          updated: 0,
          missing: 0,
          diff: 0,
        },
        correspondence: {
          new: 0,
          updated: 0,
        },
        errors: [],
        debug: [],
        diffs: [],
      };

  // Return if file does not exist
  if (!fileExists(fileName)) {
    return { status: 404, error: 'File not found' };
  }

  // Load meta data
  const metaData = await loadMetaData(fileName);

  // Init XML parser and read stream
  const parser = getXmlParser();
  const readStream = getReadStream(fileName);
  const writeStream = getWriteStream(fileName.replace('.xml', '.csv'));

  // Loop through the XML data
  let buffer = '';
  for await (const chunk of readStream) {
    buffer += chunk;

    while (true) {
      // Check if row is complete
      const rowStart = buffer.indexOf('<ROW');
      const rowEnd = buffer.indexOf('</ROW>');

      // Exit loop if no complete row exists
      if (rowStart === -1 || rowEnd === -1) break;

      // Extract and parse a single ROW
      const xmlRow = buffer.substring(rowStart, rowEnd + '</ROW>'.length);

      // Parse the XML into JSON
      const jsonData = parser.parse(xmlRow);

      // Enrich the JSON data with the meta data
      const filemakerData = jsonData.ROW.COL.reduce((acc, col, index) => {
        const fieldName = metaData[index]['@_NAME'];
        const fieldValue = col.DATA;
        acc[fieldName] = fieldValue;
        return acc;
      }, {});

      // Analyze data
      if (analyzeFields) {
        const fields = analyzeFields.split(',');
        fields.forEach((field) => {
          field = field.trim();
          const value = filemakerData[field];

          if (value) {
            // Initialize field
            if (!results[field]) results[field] = {};

            // Initialize value
            if (!results[field][value]) results[field][value] = 0;

            // Increase count
            results[field][value] += 1;

            // Sort fields by count
            results[field] = Object.fromEntries(
              Object.entries(results[field]).sort(([, a], [, b]) => b - a)
            );
          }
        });
      }

      // Import students
      else {
        let processStudent = true;

        // Check if a filter applies
        if (filter) {
          const [filterKey, filterValue] = filter
            .split(':')
            .map((s) => s.trim());
          processStudent = filemakerData[filterKey] === filterValue;
        }

        if (processStudent) {
          // Import student
          const result = await importStudent({
            filemakerData,
            entityId,
            providerId,
            advisorId,
            writeStream,
            fileName,
            dryMode,
            onlineStudents,
          });

          if (result) {
            // Update stats (type: online|correspondence, status:new|updated|missing)
            results[result.type][result.status] += 1;

            // Add debug values to results
            if (result.debug) {
              results.debug.push(result.debug);
            }

            // Add errors to results
            if (result.error) {
              results.errors.push(result.error);
            }

            // Add diffs to results
            if (result.diff) {
              results.diffs.push(result.diff);
            }
          }
        }
      }

      // Keep the remaining buffer
      buffer = buffer.substring(rowEnd + '</ROW>'.length);
    }
  }

  // Write log file
  const logStream = getWriteStream(fileName.replace('.xml', '.log'));
  logStream.write(JSON.stringify(results, null, 2));
  logStream.end();

  // Close csv stream
  writeStream.end();

  Logger.info('Done');

  return results;
};

export default {
  importData,
};
