import { getStringValue } from './filemakerData.js';

import countries from '../mappings/countries.js';
import courses from '../mappings/courses.js';
import denominations from '../mappings/denominations.js';
import gender from '../mappings/gender.js';
import maritalStatus from '../mappings/maritalStatus.js';
import salutations from '../mappings/salutations.js';
import studentSources from '../mappings/studentSources.js';

export function getCountry(value) {
  return countries[value];
}

export function getDenomination(value) {
  return denominations[value];
}

export function getSalutation(value) {
  return salutations[value];
}

export function getGender(value) {
  return gender[getStringValue(value)];
}

export function getMaritalStatus(value) {
  return maritalStatus[value];
}

export function getStudentSource(value) {
  return studentSources[value];
}

export function getCourseMappings() {
  return courses;
}
