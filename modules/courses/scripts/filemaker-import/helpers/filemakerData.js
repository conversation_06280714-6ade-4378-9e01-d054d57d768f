import { isNumber } from '#utils/types.js';
import { DateTime } from 'luxon';

/*
 * Remove null characters from a string and trim it
 * @param {String} str - The string to clean
 * @returns {String} - The cleaned string
 */
export function getStringValue(str) {
  return isNumber(str) ? str.toString() : str?.replace(/\x00/g, '').trim();
}

/*
 * Convert a date string to a date object
 * @param {String} dateString - The date string to convert
 * @returns {Date} - The date object
 */
export function getDateValue(dateString) {
  if (!dateString) return null;
  if (isNumber(dateString)) dateString = dateString.toString();
  try {
    const date = DateTime.fromFormat(dateString, 'dd.MM.yyyy');
    return date.isValid ? date.toISO() : null;
  } catch {
    return null;
  }
}

/*
 * Convert a string to a boolean
 * @param {String} str - The string to convert
 * @returns {Boolean} - The boolean value
 */
export function getBooleanValue(str) {
  str = getStringValue(str);
  const trueValues = ['x', 'X', '1', 'v']; // TODO IVANA
  return trueValues.includes(str);
}
