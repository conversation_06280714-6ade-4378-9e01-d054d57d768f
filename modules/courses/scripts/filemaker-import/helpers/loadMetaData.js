import { once } from 'events';

import Logger from '#utils/logger.js';

import getXmlParser from '../utils/getXmlParser.js';
import { getReadStream } from '../utils/files.js';

async function loadMetaData(fileName) {
  let metaData = null;
  try {
    Logger.info('Loading meta data');

    const parser = getXmlParser();
    const readStream = getReadStream(fileName);

    let buffer = '';
    readStream.on('data', (chunk) => {
      buffer += chunk;

      // Look for metadata in the buffer
      const metaDataStart = buffer.indexOf('<METADATA>');
      const metaDataEnd = buffer.indexOf('</METADATA>');

      if (metaDataStart !== -1 && metaDataEnd !== -1) {
        // Extract metadata XML
        const metaDataXML = buffer.substring(
          metaDataStart,
          metaDataEnd + '</METADATA>'.length
        );

        // Parse metadata XML into JSON
        metaData = parser.parse(metaDataXML).METADATA.FIELD;

        // Stop reading the stream
        readStream.destroy();
      }
    });

    // Wait for the stream to close
    await once(readStream, 'close');
  } catch (error) {
    Logger.error('Error when trying to load meta data:', error);
  }
  return metaData;
}

export default loadMetaData;
