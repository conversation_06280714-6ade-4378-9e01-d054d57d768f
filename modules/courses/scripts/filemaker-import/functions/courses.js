import { areEqualIDs } from '#utils/helpers.js';
import Logger from '#utils/logger.js';

import { getDateValue, getStringValue } from '../helpers/filemakerData.js';
import { getCourseMappings } from '../helpers/filemakerMappings.js';

import CourseStatus from '../../../models/CourseStatus.js';
import LessonStatus from '../../../models/LessonStatus.js';

async function importCorrespondenceCourses({
  studentId,
  filemakerData,
  providerId,
  advisorId,
  isOnlineStudent,
  dryMode,
}) {
  const results = {
    errors: [],
    count: 0,
  };

  // Get courses the student is enrolled in
  const studentOnlineCourses = await CourseStatus.find({
    provider: providerId,
    student: studentId,
    deleted: false,
    mode: { $in: ['supervised', 'autonomous'] },
  });

  const courseMappings = getCourseMappings();
  for await (const courseMapping of courseMappings) {
    const {
      id: courseId,
      courseAvailableSince,
      lessonMappings,
    } = courseMapping;

    // Check if course was started
    const firstLessonKey = Object.keys(lessonMappings)[0]; // wort01, vat01, tru01, etc.
    const firstLessonDate = getDateValue(filemakerData[firstLessonKey]); // 23.01.2024
    const filemakerCourseStarted = !!firstLessonDate; // here we still don't know if it's a correspondence course

    // Filter out courses that where not available yet
    const courseAvailableSinceDate = getDateValue(courseAvailableSince);
    const courseAvailable =
      !courseAvailableSinceDate || firstLessonDate >= courseAvailableSinceDate;

    // Check if course should be imported
    // - online student: import only if there is no online course (and if course was started when active)
    // - correspondence student: import all courses (if course was started when active)
    const doImport = courseAvailable
      ? isOnlineStudent
        ? !studentOnlineCourses.find((courseStatus) =>
            areEqualIDs(courseStatus.course, courseId)
          )
        : true
      : false;

    if (filemakerCourseStarted && doImport) {
      const result = await importCourse({
        filemakerData,
        courseMapping,
        providerId,
        studentId,
        advisorId,
        dryMode,
      });

      if (!result.error) {
        results.count += 1;
      } else {
        results.errors.push(result.error);
      }
    }
  }

  return results;
}

async function importCourse({
  filemakerData,
  courseMapping,
  providerId,
  studentId,
  advisorId,
  dryMode,
}) {
  const result = {
    error: null,
    success: true,
  };

  const {
    id: courseId,
    title,
    lessonMappings,
    courseFinishedKey,
    courseNotesKey,
  } = courseMapping;

  try {
    const firstLessonKey = Object.keys(lessonMappings)[0]; // wort01, vat01, tru01, etc.
    const firstLessonDate = getDateValue(filemakerData[firstLessonKey]); // 23.01.2024
    const courseFinishedDate = getDateValue(filemakerData[courseFinishedKey]); // 23.01.2024

    const existingCourseStatus = await CourseStatus.findOne({
      course: courseId,
      student: studentId,
      mode: 'correspondence',
      deleted: false,
    });

    const existingLessonStatuses = await CourseStatus.find({
      provider: providerId,
      student: studentId,
      course: courseId,
      deleted: false,
    });

    const baseCourseStatus = {
      provider: providerId,
      student: studentId,
      course: courseId,
      mode: 'correspondence',
      startedAt: firstLessonDate,
      finishedAt: courseFinishedDate,
      startedLessons: [],
      completedLessons: [],
      notes: getStringValue(filemakerData[courseNotesKey]),
    };

    const courseStatus = !dryMode
      ? existingCourseStatus
        ? await existingCourseStatus.updateOne(baseCourseStatus)
        : await CourseStatus.create({
            ...baseCourseStatus,
            createdAt: new Date(),
            advisor: advisorId,
          })
      : {};

    // Loop over lessons and update lesson status
    const lessonKeys = Object.keys(lessonMappings);

    const totalLessons = lessonKeys.length;
    let lessonCount = 0;
    for await (const lessonKey of lessonKeys) {
      lessonCount += 1;
      const lessonId = lessonMappings[lessonKey];
      const lessonDate = getDateValue(filemakerData[lessonKey]);
      if (lessonDate) {
        const baseLessonStatus = {
          provider: providerId,
          student: studentId,
          course: courseId,
          lesson: lessonId,
          courseStatus: courseStatus._id,
          submittedAt: lessonDate,
          correctedAt: null,
        };

        // Get next lesson date
        const currentLessonIndex = lessonKeys.indexOf(lessonKey);
        const nextLessonKey = lessonKeys[currentLessonIndex + 1];
        const nextLessonDate = getDateValue(filemakerData[nextLessonKey]);

        // Set correctedAt
        const lastLesson = lessonCount === totalLessons;
        if (nextLessonDate || lastLesson) {
          baseLessonStatus.correctedAt = lastLesson
            ? courseFinishedDate
            : nextLessonDate;
          baseLessonStatus.advisor = advisorId;
          baseLessonStatus.advisorNotes = null; // TODO: Are there advisor notes in the filemaker data?

          // Add lesson to completed lessons (will be saved further down)
          if (!dryMode && !courseStatus.completedLessons.includes(lessonId)) {
            // TODO: mongoose.Types.ObjectId???
            courseStatus.completedLessons.push(lessonId);
          }
        }

        // Check if lesson status exists
        const existingLessonStatus = existingLessonStatuses.find(
          (lessonStatus) => areEqualIDs(lessonStatus.lesson, lessonId)
        );

        // Create or update lesson status
        if (!dryMode) {
          if (existingLessonStatus) {
            await existingLessonStatus.updateOne(baseLessonStatus);
          } else {
            await LessonStatus.create({
              ...baseLessonStatus,
              createdAt: baseLessonStatus.submittedAt,
            });
          }
        }

        // Add lesson to started lessons
        if (!dryMode && !courseStatus.startedLessons.includes(lessonId)) {
          // TODO: mongoose.Types.ObjectId???
          courseStatus.startedLessons.push(lessonId);
        }

        // Set last activity
        courseStatus.lastActivity = lessonDate;
      }
    }

    // Update course status with lesson data
    if (!dryMode) {
      await courseStatus.updateOne({
        startedLessons: courseStatus.startedLessons,
        completedLessons: courseStatus.completedLessons,
        lastActivity: courseStatus.lastActivity,
      });
    }
  } catch (error) {
    const errorMessage = `Error when trying to import course "${title}"`;
    Logger.error(errorMessage, error);
    result.error = errorMessage;
  }

  return result;
}

export default importCorrespondenceCourses;
