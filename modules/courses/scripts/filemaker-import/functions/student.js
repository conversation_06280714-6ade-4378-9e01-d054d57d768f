import { buildCsvRow } from '#utils/csv.js';
import Logger from '#utils/logger.js';

import settings from '../settings.js';

import importCorrespondenceCourses from './courses.js';

import {
  getBooleanValue,
  getDateValue,
  getStringValue,
} from '../helpers/filemakerData.js';
import {
  getCountry,
  getDenomination,
  getGender,
  getMaritalStatus,
  getSalutation,
  getStudentSource,
} from '../helpers/filemakerMappings.js';

import Student from '../../../models/Student.js';
import usernames from '../mappings/usernames.js';

async function importStudent({
  filemakerData,
  entityId,
  providerId,
  advisorId,
  writeStream,
  fileName,
  dryMode,
  onlineStudents,
}) {
  const result = {
    status: null,
    record: null,
    error: null,
    diff: null,
  };

  // Get username
  const username = getUsername(filemakerData);

  // Get student name
  const fullName = getStringValue(filemakerData['TNameGanz']);
  const studentName = `${fullName}${username ? ` (${username})` : ''}`;

  Logger.info(`- ${studentName}`);

  try {
    const isOnlineStudent = !!username;
    result.type = isOnlineStudent ? 'online' : 'correspondence';

    // Get existing student
    const existingStudent = await getExistingOnlineStudent({
      filemakerData,
      entityId,
      providerId,
    });

    const filemakerStudent = getStudentData({
      result,
      filemakerData,
      entityId,
      providerId,
    });

    let diff;

    // DEBUG: Output filemaker data for specific user
    // if (username?.startsWith('angela')) {
    //   result.debug = filemakerData;
    // }

    // DEBUG: Output filemaker data for specific user
    // if (filemakerData['TNameGanz'] === 'Monika  Böhm') {
    //   result.debug = filemakerData;
    // }

    // Online student (skip if onlineStudents flag is not set)
    if (isOnlineStudent && onlineStudents) {
      if (!onlineStudents) return;

      Logger.info(`  - online student`);

      // Return error if student was not found in AWE
      if (!existingStudent) {
        Logger.error(`  - missing`);
        result.status = 'missing';
        result.error = `ONLINE STUDENT NOT FOUND: ${studentName}`;
        const csvData = getCsvData({
          filemakerData,
          filemakerStudent,
          existingStudent,
          error: true,
        });
        writeStream.write(csvData);
        return result;
      }

      Logger.info(`  - update`);

      // Check for differences
      diff = getDiff(filemakerStudent, existingStudent);
      if (diff) {
        Logger.warning(`  - diff:`, diff);
        result.diff = {
          studentName: studentName,
          diff,
        };
      }

      // Update existing AWE student
      if (!dryMode && !diff) {
        await existingStudent.updateOne({
          filemakerStudent,
          preferences: {
            ...existingStudent.preferences,
            ...filemakerStudent.preferences,
          },
        });
      }

      const coursesImportResults = await importCorrespondenceCourses({
        studentId: existingStudent._id,
        isOnlineStudent,
        filemakerData,
        providerId,
        advisorId,
        dryMode,
      });
      if (coursesImportResults.errors?.length) {
        result.error = coursesImportResults.errors;
      }

      // Set result
      result.status = diff ? 'diff' : 'updated';
    }

    // Correspondence student
    if (!isOnlineStudent) {
      Logger.info(`  - correspondence`);
      Logger.info(`  - new`);

      const newStudent = !dryMode
        ? await Student.create({
            ...filemakerStudent,
            advisor: advisorId,
            createdAt:
              getDateValue(filemakerData['Aktivierungsdatum']) || new Date(),
            importIDs: [
              {
                type: 'filemaker',
                recordName: getStringValue(filemakerData['TNameGanz']),
                fileName,
                filemakerData,
                // TODO: Save FileMaker ID instead of student name
                // recordID: getStringValue(filemakerData['???']),
              },
            ],
          })
        : {};

      if (settings.courses) {
        const coursesImportResults = await importCorrespondenceCourses({
          studentId: newStudent._id,
          isOnlineStudent,
          filemakerData,
          providerId,
          advisorId,
          dryMode,
        });
        if (coursesImportResults.errors?.length) {
          result.error = coursesImportResults.errors;
        }
      }

      // Set result
      result.status = 'new';
    }

    // Write CSV data to file
    const csvData = getCsvData({
      filemakerData,
      filemakerStudent,
      existingStudent,
      diff,
    });
    writeStream.write(csvData);
  } catch (error) {
    Logger.error(`Error when trying to import student "${studentName}"`, error);
    result.error = error;
  }

  return result;
}

function getStudentData({ filemakerData, entityId, providerId, result }) {
  const baseStudent = {
    entity: entityId,
    provider: providerId,
    enabled: true,
    deleted: false,
    gender: getGender(filemakerData['ESEX']),
    title: getStringValue(filemakerData['Titel']),
    firstName: getStringValue(filemakerData['Vorname']),
    email: getStringValue(filemakerData['EmailAdresse']),
    phone: getStringValue(filemakerData['Telefon']),
    preferences: {
      eventAdvertisement: getBooleanValue(
        filemakerData['DSVO Veranstaltungen']
      ),
      newsletter: getBooleanValue(filemakerData['DSVO Newsletter Spenden']),
      terms: getBooleanValue(filemakerData['DSVO SDH']),
    },
  };

  // Set last name (if it's not equal to username)
  const lastName = getStringValue(filemakerData['Nachname']);
  const username = getUsername(filemakerData);
  if (lastName !== username) {
    baseStudent.lastName = lastName;
  }

  // Set salution
  const salutation = getSalutation(filemakerData['Anrede']);
  if (salutation) {
    baseStudent.salutation = salutation;
  }

  // Set marital status
  const maritalStatusValue = getStringValue(filemakerData['EFS']);
  if (maritalStatusValue) {
    const maritalStatus = getMaritalStatus(maritalStatusValue);
    if (maritalStatus) {
      baseStudent.maritalStatus = maritalStatus;
    }

    // Set marital status notes
    else {
      baseStudent.maritalStatusNotes = maritalStatusValue;
    }
  }

  // Set birthday
  const birthdayValue = getStringValue(filemakerData['EGebdat']);
  if (birthdayValue) {
    const birthday = getDateValue(birthdayValue);
    if (birthday) {
      baseStudent.birthday = birthday;
    }

    // Set birthday notes
    else {
      baseStudent.birthdayNotes = birthdayValue;

      // DEBUG: Output birthday errors
      // const fullName = getStringValue(filemakerData['TNameGanz']);
      // const studentName = `${fullName}${username ? ` (${username})` : ''}`;
      // result.debug = `${studentName}: ${birthdayString}`;
    }
  }

  // Set address
  const careOf = getStringValue(filemakerData['bei']);
  const street = getStringValue(filemakerData['Strasse']);
  const zipcode = getStringValue(filemakerData['PLZ']);
  const city = getStringValue(filemakerData['Wohnort']);
  const country = getStringValue(filemakerData['Ausland']);
  if (street || zipcode || city || country || careOf) {
    baseStudent.address = {
      careOf: careOf || '',
      street: street || '',
      zip: zipcode || '',
      city: city || '',
      country: getCountry(country) || 'DE',
    };
  }

  // Set public notes
  let publicNotes = getStringValue(filemakerData['TAchtung']);
  if (publicNotes) {
    // Remove username from notes
    publicNotes = username
      ? publicNotes.replace(`@${username}`, '')
      : publicNotes;

    // Remove leading line breaks
    publicNotes = publicNotes.replace(/^\n+/, '');

    // Remove trailing line breaks
    publicNotes = publicNotes.replace(/\n+$/, '');

    if (publicNotes) {
      baseStudent.notes = publicNotes;
    }
  }

  // TODO: Set private notes
  // privateNotes = getStringValue(filemakerData['Bemerkungen']);

  // Add source
  const source = getStudentSource(filemakerData['Kontakt']);
  if (source) baseStudent.source = source;

  // Add denomination
  const denomination = getDenomination(filemakerData['Konfession']);
  if (denomination) baseStudent.denomination = denomination;

  return baseStudent;
}

function getUsername(filemakerData) {
  const publicNotes = getStringValue(filemakerData['TAchtung']) || '';
  const firstItem = publicNotes.split('\n')[0];
  const username = firstItem.startsWith('@') ? firstItem.split('@')[1] : '';
  return getStringValue(username);
}

async function getExistingOnlineStudent({
  filemakerData,
  entityId,
  providerId,
}) {
  let username = getUsername(filemakerData);

  // Correct username if necessary (username was wrong in FileMaker)
  if (usernames[username]) username = usernames[username];

  const existingStudent = username
    ? await Student.findOne({
        entity: entityId,
        provider: providerId,
        // TODO: 'profile.username': username,
        username: username,
      }).lean()
    : null;

  return existingStudent ? JSON.parse(JSON.stringify(existingStudent)) : null;
}

function getDiff(filemakerStudent, student) {
  const diff = {};

  Object.keys(filemakerStudent)
    .filter((key) => !['notes'].includes(key))
    .forEach((key) => {
      let filemakerValue = filemakerStudent[key];
      let aweValue = student[key];

      // trim value if string
      if (typeof filemakerValue === 'string') {
        filemakerValue = filemakerValue.trim();
      }
      if (typeof aweValue === 'string') {
        aweValue = aweValue.trim();
      }

      // Compare address fields
      if (key === 'address') {
        ['street', 'zip', 'city']

          // Check if both values are set
          .filter(
            (addressKey) =>
              filemakerValue?.[addressKey] && aweValue?.[addressKey]
          )

          // Compare values
          .forEach((addressKey) => {
            const filemakerAddressValue =
              filemakerValue?.[addressKey]?.trim() || '';
            const aweAddressValue = aweValue?.[addressKey]?.trim() || '';
            if (filemakerAddressValue !== aweAddressValue) {
              diff[addressKey] = {
                filemaker: filemakerAddressValue,
                awe: aweAddressValue,
              };
            }
          });
      }

      // Compare preferences fields
      else if (key === 'preferences') {
        ['eventAdvertisement', 'newsletter'].forEach((prefKey) => {
          const filemakerPrefValue = !!filemakerValue?.[prefKey];
          const awePrefValue = !!aweValue?.[prefKey];
          if (filemakerPrefValue !== awePrefValue) {
            diff[prefKey] = {
              filemaker: filemakerPrefValue,
              awe: awePrefValue,
            };
          }
        });
      }

      // Compare other fields
      else if (filemakerValue && aweValue && filemakerValue !== aweValue) {
        diff[key] = {
          filemaker: filemakerStudent[key],
          awe: student[key],
        };
      }
    });

  return Object.keys(diff).length ? diff : null;
}

function getCsvData({
  filemakerData,
  filemakerStudent,
  existingStudent,
  diff,
}) {
  const username = getUsername(filemakerData);
  const csvData = buildCsvRow({
    status: username && !existingStudent ? 'ERROR' : diff ? 'DIFF' : '',
    firstName: filemakerStudent.firstName,
    lastName: filemakerStudent.lastName,
    username: username,
    AWE: existingStudent ? `"${existingStudent._id}"` : '-',
    diff: diff ? JSON.stringify(diff) : '',
  });
  return csvData;
}

export default importStudent;
