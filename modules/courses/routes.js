import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import collectionController from './controllers/collectionController.js';
import countryController from './controllers/countryController.js';
import regionsController from './controllers/regionsController.js';
import courseController from './controllers/courseController.js';
import courseStatisticsController from './controllers/courseStatisticsController.js';
import lessonController from './controllers/lessonController.js';
import lessonStatusController from './controllers/lessonStatusController.js';
import slideController from './controllers/slideController.js';
import providerController from './controllers/providerController.js';
import questionnaireController from './controllers/questionnaireController.js';
import courseStatusController from './controllers/courseStatusController.js';
import questionnaireStatusController from './controllers/questionnaireStatusController.js';
import studentController from './controllers/studentController.js';
import fileMakerImportController from './controllers/fileMakerImportController.js';
import chatController from './controllers/chatController.js';
import advisorController from './controllers/advisorController.js';
import advisorStatisticsController from './controllers/advisorStatisticsController.js';
import { validateProvider } from './middleware/providerValidationMiddleware.js';
import { validateNewAdvisor } from './middleware/newAdvisorValidationMiddleware.js';
import { loadAdvisor } from './middleware/loadAdvisorMiddleware.js';
import { validateQuestionnaireStatus } from './middleware/questionnaireStatusValidationMiddleware.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

//#region Providers
// -----------------------------------------------
router
  .route('/providers')
  .get(providerController.getAllProviders)
  .post(
    restrictTo({
      module: 'courses-providers',
      permissions: ['create'],
    }),
    logRequest({ module: 'courses', action: 'CREATE_PROVIDER' }),
    providerController.createProvider
  );

router
  .route('/providers/:providerId')
  .get(providerController.getProvider)
  .patch(
    restrictTo({
      module: 'courses-providers',
      permissions: ['update'],
      paramId: 'providerId',
    }),
    logRequest({ module: 'courses', action: 'UPDATE_PROVIDER' }),
    providerController.updateProvider
  )
  .delete(
    restrictTo({
      module: 'courses-providers',
      permissions: ['delete'],
      paramId: 'providerId',
    }),
    logRequest({ module: 'courses', action: 'DELETE_PROVIDER' }),
    providerController.deleteProvider
  );

router
  .route('/providers/:providerId/partners')
  .get(providerController.getPartnerProviders);

router.route('/providers/:providerId/restore').patch(
  restrictTo({
    module: 'courses-providers',
    permissions: ['delete'],
    paramId: 'providerId',
  }),
  logRequest({ module: 'courses', action: 'RESTORE_PROVIDER' }),
  providerController.restoreProvider
);

router.route('/providers/:providerId/disable').patch(
  restrictTo({
    module: 'courses-providers',
    permissions: ['update'],
    paramId: 'providerId',
  }),
  logRequest({ module: 'courses', action: 'DISABLE_PROVIDER' }),
  providerController.disableProvider
);

router.route('/providers/:providerId/enable').patch(
  restrictTo({
    module: 'courses-providers',
    permissions: ['update'],
    paramId: 'providerId',
  }),
  logRequest({ module: 'courses', action: 'ENABLE_PROVIDER' }),
  providerController.enableProvider
);
//#endregion

//#region Lessons
// -----------------------------------------------
router
  .route('/:courseId/lessons')
  .get(lessonController.getAllLessons)
  .post(
    restrictTo({
      module: 'courses',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'CREATE_LESSON' }),
    lessonController.createLesson
  );

router
  .route('/lessons/:lessonId')
  .get(lessonController.getLesson)
  .patch(
    restrictTo({
      module: 'courses',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'UPDATE_LESSON' }),
    lessonController.updateLesson
  )
  .delete(
    restrictTo({
      module: 'courses',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'DELETE_LESSON' }),
    lessonController.deleteLesson
  );

router.route('/lessons/:lessonId/restore').patch(
  restrictTo({
    module: 'courses',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'RESTORE_LESSON' }),
  lessonController.restoreLesson
);

router.route('/lessons/:lessonId/disable').patch(
  restrictTo({
    module: 'courses',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'DISABLE_LESSON' }),
  lessonController.disableLesson
);

router.route('/lessons/:lessonId/enable').patch(
  restrictTo({
    module: 'courses',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'ENABLE_LESSON' }),
  lessonController.enableLesson
);
//#endregion

//#region Slides
// -----------------------------------------------
router
  .route('/lessons/:lessonId/slides')
  .get(slideController.getAllSlides)
  .post(
    restrictTo({
      module: 'courses',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'CREATE_SLIDE' }),
    slideController.createSlide
  );

router
  .route('/slides/:slideId')
  .get(slideController.getSlide)
  .patch(
    restrictTo({
      module: 'courses',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'UPDATE_SLIDE' }),
    slideController.updateSlide
  )
  .delete(
    restrictTo({
      module: 'courses',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'DELETE_SLIDE' }),
    slideController.deleteSlide
  );

router.route('/slides/:slideId/restore').patch(
  restrictTo({
    module: 'courses',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'RESTORE_SLIDE' }),
  slideController.restoreSlide
);

router.route('/slides/:slideId/disable').patch(
  restrictTo({
    module: 'courses',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'DISABLE_SLIDE' }),
  slideController.disableSlide
);

router.route('/slides/:slideId/enable').patch(
  restrictTo({
    module: 'courses',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'ENABLE_SLIDE' }),
  slideController.enableSlide
);
//#endregion

//#region Questionnaires
// -----------------------------------------------
router
  .route('/questionnaires/:questionnaireId')
  .get(questionnaireController.getQuestionnaire)
  .patch(
    restrictTo({
      module: 'courses',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'UPDATE_QUESTIONNAIRE' }),
    questionnaireController.updateQuestionnaire
  );
//#endregion

//#region Collections
// -----------------------------------------------
router
  .route('/providers/:providerId/collections')
  .get(validateProvider, collectionController.getAllCollections)
  .post(
    restrictTo({
      module: 'courses-collections',
      permissions: ['create'],
    }),
    validateProvider,
    logRequest({ module: 'courses', action: 'CREATE_COLLECTION' }),
    collectionController.createCollection
  );

router
  .route('/collections/:collectionId')
  .get(collectionController.getCollection)
  .patch(
    restrictTo({
      module: 'courses-collections',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'UPDATE_COLLECTION' }),
    collectionController.updateCollection
  )
  .delete(
    restrictTo({
      module: 'courses-collections',
      permissions: ['delete'],
    }),
    logRequest({ module: 'courses', action: 'DELETE_COLLECTION' }),
    collectionController.deleteCollection
  );

router.route('/collections/:collectionId/restore').patch(
  restrictTo({
    module: 'courses-collections',
    permissions: ['delete'],
  }),
  logRequest({ module: 'courses', action: 'RESTORE_COLLECTION' }),
  collectionController.restoreCollection
);

router.route('/collections/:collectionId/disable').patch(
  restrictTo({
    module: 'courses-collections',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'DISABLE_COLLECTION' }),
  collectionController.disableCollection
);

router.route('/collections/:collectionId/enable').patch(
  restrictTo({
    module: 'courses-collections',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'ENABLE_COLLECTION' }),
  collectionController.enableCollection
);
//#endregion

//#region Statistics
// -----------------------------------------------
router
  .route('/providers/:providerId/statistics/providers/stats')
  .get(validateProvider, providerController.getProviderStats);

router
  .route('/providers/:providerId/statistics/students/registrations')
  .get(validateProvider, studentController.getRegistrationStats);

router
  .route('/providers/:providerId/statistics/courses/enrolments')
  .get(validateProvider, courseStatisticsController.getCourseEnrolmentStats);

router
  .route('/providers/:providerId/statistics/courses/completions')
  .get(validateProvider, courseStatisticsController.getCourseCompletionStats);

router
  .route('/providers/:providerId/statistics/courses/activity')
  .get(validateProvider, courseStatisticsController.getCourseActivityStats);

router
  .route('/providers/:providerId/statistics/advisors/students')
  .get(validateProvider, advisorStatisticsController.getAdvisorStudents);
//#endregion

//#region Countries
// -----------------------------------------------
router
  .route('/providers/:providerId/countries')
  .get(validateProvider, countryController.getAllCountries)
  .post(
    restrictTo({
      module: 'courses-countries',
      permissions: ['create'],
    }),
    validateProvider,
    logRequest({ module: 'courses', action: 'CREATE_COUNTRY' }),
    countryController.createCountry
  );

router
  .route('/countries/:countryId')
  .get(countryController.getCountry)
  .patch(
    restrictTo({
      module: 'courses-countries',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'UPDATE_COUNTRY' }),
    countryController.updateCountry
  )
  .delete(
    restrictTo({
      module: 'courses-countries',
      permissions: ['delete'],
    }),
    logRequest({ module: 'courses', action: 'DELETE_COUNTRY' }),
    countryController.deleteCountry
  );

router.route('/countries/:countryId/restore').patch(
  restrictTo({
    module: 'courses-countries',
    permissions: ['delete'],
  }),
  logRequest({ module: 'courses', action: 'RESTORE_COUNTRY' }),
  countryController.restoreCountry
);

router.route('/countries/:countryId/disable').patch(
  restrictTo({
    module: 'courses-countries',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'DISABLE_COUNTRY' }),
  countryController.disableCountry
);

router.route('/countries/:countryId/enable').patch(
  restrictTo({
    module: 'courses-countries',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'ENABLE_COUNTRY' }),
  countryController.enableCountry
);
//#endregion

//#region Regions
// -----------------------------------------------
router
  .route('/countries/:countryId/regions')
  .get(regionsController.getAllRegions)
  .post(
    restrictTo({
      module: 'courses-countries',
      permissions: ['create'],
    }),
    logRequest({ module: 'courses', action: 'CREATE_REGION' }),
    regionsController.createRegion
  );

router
  .route('/regions/:regionId')
  .get(regionsController.getRegion)
  .patch(
    restrictTo({
      module: 'courses-countries',
      permissions: ['update'],
    }),
    logRequest({ module: 'regions', action: 'UPDATE_REGION' }),
    regionsController.updateRegion
  )
  .delete(
    restrictTo({
      module: 'courses-countries',
      permissions: ['update'],
    }),
    logRequest({ module: 'regions', action: 'DELETE_REGION' }),
    regionsController.deleteRegion
  );

router.route('/regions/:regionId/restore').patch(
  restrictTo({
    module: 'courses-countries',
    permissions: ['update'],
  }),
  logRequest({ module: 'regions', action: 'RESTORE_REGION' }),
  regionsController.restoreRegion
);

router.route('/regions/:regionId/disable').patch(
  restrictTo({
    module: 'courses-countries',
    permissions: ['update'],
  }),
  logRequest({ module: 'regions', action: 'DISABLE_REGION' }),
  regionsController.disableRegion
);

router.route('/regions/:regionId/enable').patch(
  restrictTo({
    module: 'courses-countries',
    permissions: ['update'],
  }),
  logRequest({ module: 'regions', action: 'ENABLE_REGION' }),
  regionsController.enableRegion
);
//#endregion

//#region Course Statuses
// -----------------------------------------------
router
  .route('/providers/:providerId/course-statuses')
  .get(
    validateProvider,
    loadAdvisor,
    courseStatusController.getAllCourseStatuses
  );

router
  .route('/providers/:providerId/course-statuses/:courseStatusId')
  .get(validateProvider, loadAdvisor, courseStatusController.getCourseStatus)
  .patch(
    validateProvider,
    restrictTo({
      module: 'courses-questionnaires',
      permissions: ['update', 'updateAll'],
    }),
    logRequest({ module: 'courses', action: 'UPDATE_COURSE_STATUS' }),
    courseStatusController.updateCourseStatus
  );

router
  .route(
    '/providers/:providerId/course-statuses/:courseStatusId/lesson-statuses'
  )
  .post(
    validateProvider,
    restrictTo({
      module: 'courses-questionnaires',
      permissions: ['update'],
    }),
    loadAdvisor,
    logRequest({ module: 'courses', action: 'CREATE_LESSON_STATUS' }),
    lessonStatusController.createLessonStatus
  );

router.route('/providers/:providerId/lesson-statuses/:lessonStatusId').patch(
  validateProvider,
  restrictTo({
    module: 'courses-questionnaires',
    permissions: ['update'],
  }),
  loadAdvisor,
  logRequest({ module: 'courses', action: 'UPDATE_LESSON_STATUS' }),
  lessonStatusController.updateLessonStatus
);

router
  .route(
    '/providers/:providerId/course-statuses/:courseStatusId/questionnaires'
  )
  .get(
    validateProvider,
    loadAdvisor,
    courseStatusController.getCourseStatusQuestionnaires
  );

router
  .route(
    '/providers/:providerId/course-statuses/:courseStatusId/questionnaires-old'
  )
  .get(
    validateProvider,
    loadAdvisor,
    courseStatusController.getCourseStatusQuestionnairesOld
  );
//#endregion

//#region Questionnaire Statuses
// -----------------------------------------------
router
  .route('/providers/:providerId/questionnaire-statuses')
  .get(
    validateProvider,
    loadAdvisor,
    questionnaireStatusController.getOpenQuestionnaireStatuses
  );

router
  .route('/providers/:providerId/questionnaire-statuses/archive')
  .get(
    validateProvider,
    loadAdvisor,
    questionnaireStatusController.getArchivedQuestionnaireStatuses
  );

router
  .route('/providers/:providerId/questionnaire-statuses/:questionnaireStatusId')
  .get(
    validateProvider,
    loadAdvisor,
    questionnaireStatusController.getQuestionnaireStatus
  );

router
  .route(
    '/providers/:providerId/questionnaire-statuses/:questionnaireStatusId/save-advisor-notes'
  )
  .patch(
    validateProvider,
    restrictTo({
      module: 'courses-questionnaires',
      permissions: ['update'],
    }),
    loadAdvisor,
    validateQuestionnaireStatus,
    logRequest({
      module: 'courses',
      action: 'SAVE_QUESTIONNAIRE_STATUS_ADVISOR_NOTES',
    }),
    questionnaireStatusController.saveAdvisorNotes
  );

router
  .route(
    '/providers/:providerId/questionnaire-statuses/:questionnaireStatusId/submit-advisor-notes'
  )
  .patch(
    validateProvider,
    restrictTo({
      module: 'courses-questionnaires',
      permissions: ['update'],
    }),
    loadAdvisor,
    validateQuestionnaireStatus,
    logRequest({
      module: 'courses',
      action: 'SUBMIT_QUESTIONNAIRE_STATUS_ADVISOR_NOTES',
    }),
    questionnaireStatusController.submitAdvisorNotes
  );
//#endregion

//#region Students
// -----------------------------------------------
router
  .route('/providers/:providerId/students')
  .get(validateProvider, loadAdvisor, studentController.getAllStudents)
  .post(
    restrictTo({
      module: 'courses-students',
      permissions: ['create'],
    }),
    validateProvider,
    logRequest({ module: 'courses', action: 'CREATE_STUDENT' }),
    studentController.createStudent
  );

router.get(
  '/providers/:providerId/students/exist',
  validateProvider,
  studentController.studentExists
);

router
  .route('/students/filemaker-import')
  .get(fileMakerImportController.importData);

router
  .route('/providers/:providerId/students/:studentId')
  .get(validateProvider, loadAdvisor, studentController.getStudent)
  .patch(
    validateProvider,
    restrictTo({
      module: 'courses-students',
      permissions: ['update', 'updateAll'],
    }),
    loadAdvisor,
    logRequest({ module: 'courses', action: 'UPDATE_STUDENT' }),
    studentController.updateStudent
  )
  .delete(
    restrictTo({
      module: 'courses-students',
      permissions: ['delete', 'deleteAll'],
    }),
    logRequest({ module: 'courses', action: 'DELETE_STUDENT' }),
    studentController.deleteStudent
  );

router
  .route('/students/:studentId/courses')
  .get(studentController.getStudentCourses)
  .post(
    restrictTo({
      module: 'courses-questionnaires',
      permissions: ['create'],
    }),
    // TODO: validateCourseStatus,
    logRequest({ module: 'courses', action: 'START_STUDENT_COURSE' }),
    courseStatusController.startCourse
  );

router
  .route('/students/:studentId/courses/exists')
  .get(studentController.studentCourseExists);

// router.route('/students/:studentId/courses/:courseId').get(studentController.getStudent);

router.route('/students/:studentId/disable').patch(
  restrictTo({
    module: 'courses-students',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'DISABLE_STUDENT' }),
  studentController.disableStudent
);

router.route('/students/:studentId/enable').patch(
  restrictTo({
    module: 'courses-students',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'ENABLE_STUDENT' }),
  studentController.enableStudent
);
//#endregion

//#region Chats
// -----------------------------------------------
router
  .route('/providers/:providerId/chats')
  .get(validateProvider, loadAdvisor, chatController.getAllChats)
  .post(
    restrictTo({
      module: 'courses-messages',
      permissions: ['create'],
    }),
    validateProvider,
    logRequest({ module: 'courses-messages', action: 'CREATE_CHAT' }),
    chatController.createChat
  );

router
  .route('/chats/:chatId')
  .get(chatController.getChat)
  .patch(
    restrictTo({
      module: 'courses-messages',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses-messages', action: 'UPDATE_CHAT' }),
    chatController.updateChat
  )
  .delete(
    restrictTo({
      module: 'courses-messages',
      permissions: ['delete'],
    }),
    logRequest({ module: 'courses-messages', action: 'DELETE_CHAT' }),
    chatController.deleteChat
  );

// router.route('/chats/:chatId/pin').patch(
//   restrictTo({
//     module: 'courses-messages',
//     permissions: ['update'],
//   }),
//   chatController.pinChat
// );

// router.route('/chats/:chatId/archive').patch(
//   restrictTo({
//     module: 'courses-messages',
//     permissions: ['update'],
//   }),
//   chatController.archiveChat
// );

router
  .route('/providers/:providerId/chats/:chatId/messages')
  .get(validateProvider, loadAdvisor, chatController.getAllChatMessages)
  .post(
    validateProvider,
    restrictTo({
      module: 'courses-messages',
      permissions: ['create'],
    }),
    loadAdvisor,
    logRequest({ module: 'courses-messages', action: 'CREATE_MESSAGE' }),
    chatController.createMessage
  );

router.route('/providers/:providerId/messages/:messageId/read').patch(
  validateProvider,
  restrictTo({
    module: 'courses-messages',
    permissions: ['update'],
  }),
  loadAdvisor,
  logRequest({ module: 'courses-messages', action: 'READ_CHAT' }),
  chatController.readMessage
);

router.route('/providers/:providerId/messages/:messageId/unread').patch(
  loadAdvisor,
  restrictTo({
    module: 'courses-messages',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses-messages', action: 'UNREAD_CHAT' }),
  chatController.unreadMessage
);
//#endregion

//#region Advisors
// -----------------------------------------------
router
  .route('/providers/:providerId/advisors')
  .get(validateProvider, advisorController.getAllAdvisors)
  .post(
    restrictTo({
      module: 'courses-advisors',
      permissions: ['create'],
      paramId: 'providerId',
    }),
    validateProvider,
    validateNewAdvisor,
    logRequest({ module: 'courses', action: 'CREATE_ADVISOR' }),
    advisorController.createAdvisor
  );

router.get(
  '/providers/:providerId/advisors/current',
  advisorController.currentAdvisor
);

router
  .route('/advisors/:advisorId')
  .get(advisorController.getAdvisor)
  .patch(
    restrictTo({
      module: 'courses-advisors',
      permissions: ['update'],
    }),
    logRequest({ module: 'courses', action: 'UPDATE_ADVISOR' }),
    advisorController.updateAdvisor
  )
  .delete(
    restrictTo({
      module: 'courses-advisors',
      permissions: ['delete'],
    }),
    logRequest({ module: 'courses', action: 'DELETE_ADVISOR' }),
    advisorController.deleteAdvisor
  );

router.route('/advisors/:advisorId/restore').patch(
  restrictTo({
    module: 'courses-advisors',
    permissions: ['delete'],
  }),
  logRequest({ module: 'courses', action: 'RESTORE_ADVISOR' }),
  advisorController.restoreAdvisor
);

router.route('/advisors/:advisorId/disable').patch(
  restrictTo({
    module: 'courses-advisors',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'DISABLE_ADVISOR' }),
  advisorController.disableAdvisor
);

router.route('/advisors/:advisorId/enable').patch(
  restrictTo({
    module: 'courses-advisors',
    permissions: ['update'],
  }),
  logRequest({ module: 'courses', action: 'ENABLE_ADVISOR' }),
  advisorController.enableAdvisor
);
//#endregion

//#region Courses
// -----------------------------------------------
router
  .route('/providers/:providerId/courses')
  .get(courseController.getAllCourses)
  .post(
    restrictTo({
      module: 'courses',
      permissions: ['create'],
      paramId: 'providerId',
    }),
    validateProvider,
    logRequest({ module: 'courses', action: 'CREATE_COURSE' }),
    courseController.createCourse
  );

router
  .route('/:courseId')
  .get(courseController.getCourse)
  .patch(
    restrictTo({
      module: 'courses',
      permissions: ['update'],
      paramId: 'courseId',
    }),
    logRequest({ module: 'courses', action: 'UPDATE_COURSE' }),
    courseController.updateCourse
  )
  .delete(
    restrictTo({
      module: 'courses',
      permissions: ['delete'],
      paramId: 'courseId',
    }),
    logRequest({ module: 'courses', action: 'DELETE_COURSE' }),
    courseController.deleteCourse
  );

router
  .route('/:courseId/hard-delete')
  .delete(
    restrictTo(),
    logRequest({ module: 'courses', action: 'HARD_DELETE_COURSE' }),
    courseController.hardDeleteCourse
  );

router.route('/:courseId/clone').post(
  restrictTo({
    module: 'courses',
    permissions: ['create'],
    paramId: 'courseId',
  }),
  logRequest({ module: 'courses', action: 'CLONE_COURSE' }),
  courseController.cloneCourse
);

router.route('/:courseId/restore').patch(
  restrictTo({
    module: 'courses',
    permissions: ['delete'],
    paramId: 'courseId',
  }),
  logRequest({ module: 'courses', action: 'RESTORE_COURSE' }),
  courseController.restoreCourse
);

router.route('/:courseId/disable').patch(
  restrictTo({
    module: 'courses',
    permissions: ['update'],
    paramId: 'courseId',
  }),
  logRequest({ module: 'courses', action: 'DISABLE_COURSE' }),
  courseController.disableCourse
);

router.route('/:courseId/enable').patch(
  restrictTo({
    module: 'courses',
    permissions: ['update'],
    paramId: 'courseId',
  }),
  logRequest({ module: 'courses', action: 'ENABLE_COURSE' }),
  courseController.enableCourse
);

router.route('/:courseId/content').get(courseController.getCourseContent);
//#endregion

export default router;
