import mongoose from 'mongoose';

import QuestionnaireStatus from '../models/QuestionnaireStatus.js';
import LessonStatus from '../models/LessonStatus.js';

export async function getActiveStudents({
  providerId,
  dateTo,
  dayDiff = -30,
  studentIds,
  courseIds,
}) {
  // Get date difference
  const activeDate = new Date(dateTo);
  activeDate.setDate(activeDate.getDate() + dayDiff);

  const activeOnlineStudents = await QuestionnaireStatus.distinct('student', {
    provider: new mongoose.Types.ObjectId(providerId),
    ...(courseIds && { course: { $in: courseIds } }),
    ...(studentIds && { student: { $in: studentIds } }),
    $or: [
      {
        submittedAt: {
          $gte: activeDate,
          $lte: dateTo,
        },
      },
      {
        savedAt: {
          $gte: activeDate,
          $lte: dateTo,
        },
      },
    ],
  });

  const activeCorrespondenceStudents = await LessonStatus.distinct('student', {
    provider: new mongoose.Types.ObjectId(providerId),
    ...(courseIds && { course: { $in: courseIds } }),
    ...(studentIds && { student: { $in: studentIds } }),
    submittedAt: {
      $gte: activeDate,
      $lte: dateTo,
    },
  });

  // get distinct student IDs
  const activeStudentIds = new Set([
    ...activeOnlineStudents,
    ...activeCorrespondenceStudents,
  ]);

  return [...activeStudentIds];
}

export default {
  getActiveStudents,
};
