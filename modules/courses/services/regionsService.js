import mongoose from 'mongoose';

import { areEqualIDs } from '#utils/helpers.js';
import { sendEmail } from '#utils/notifier.js';
import getFullName from '#utils/getFullName.js';
import { t } from '#utils/translator.js';

import siteServices from '#modules/web/services/siteServices.js';

import Advisor from '../models/Advisor.js';
import Student from '../models/Student.js';
import studentService from './studentService.js';
import providerService from './providerService.js';

export async function reassignStudents({ region, data, entity }) {
  const previousAdvisorId = region.advisor;
  const { config, advisor: newAdvisorId } = data;

  // Do nothing if the advisor didn't change
  if (areEqualIDs(previousAdvisorId, newAdvisorId)) return;

  // Get students of this region
  const students = await Student.find(
    {
      deleted: false,
      courseCountry: new mongoose.Types.ObjectId(region.country),
      ...(region.type === 'region'
        ? {
            courseCountryRegion: new mongoose.Types.ObjectId(region._id),
          }
        : {
            $and: [
              { 'address.zip': { $gte: config.start } },
              { 'address.zip': { $lte: config.end } },
            ],
          }),
    },
    {
      _id: 1,
    }
  );

  for (const student of students) {
    // Assign new advisor
    await studentService.assignAdvisor(student._id, newAdvisorId);

    // Remove previous advisor
    await studentService.removeAdvisorMapping({
      studentId: student._id,
      advisorId: previousAdvisorId,
    });
  }

  // Get advisors
  const advisors = await Advisor.find(
    {
      _id: { $in: [previousAdvisorId, newAdvisorId] },
    },
    { firstName: 1, lastName: 1, email: 1 }
  );
  const previousAdvisor = advisors.find((a) =>
    areEqualIDs(a._id, previousAdvisorId)
  );
  const newAdvisor = advisors.find((a) => areEqualIDs(a._id, newAdvisorId));

  // Get region name
  const regionName =
    region.type === 'region' ? config.name : `${config.start} - ${config.end}`;

  // Get language for email
  const provider = await providerService.getProvider({
    providerId: region.provider,
    fields: ['language', 'site'],
  });

  // Get site
  const { site } = await siteServices.getSiteById(provider.site);

  // Set language
  const language = site.language || provider?.language || 'en';

  // Notify new advisor
  sendEmail({
    language,
    entity,
    site,
    to: newAdvisor.email,
    subject: t(language, 'reassignedStudentsSubject'),
    templateName: 'reassignedStudents',
    templateValues: {
      title: t(language, 'reassignedStudentsTitle', {
        newAdvisor: getFullName(newAdvisor),
      }),
      text: t(language, 'reassignedStudentsText', {
        regionName: regionName,
        studentsCount: students.length,
        previousAdvisor: getFullName(previousAdvisor),
      }),
    },
  });
}

export default {
  reassignStudents,
};
