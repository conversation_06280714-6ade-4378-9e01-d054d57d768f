import { isEmpty } from '#utils/arrays.js';
import { areEqualIDs } from '#utils/helpers.js';
import { sendEmail, sendPushNotification } from '#utils/notifier.js';
import getFullName from '#utils/getFullName.js';
import { t } from '#utils/translator.js';
import { getPageUrlById } from '#modules/web/services/pageServices.js';
import siteServices from '#modules/web/services/siteServices.js';

import Entity from '#modules/entities/models/Entity.js';

import CourseStatus from '../models/CourseStatus.js';
import '../models/Advisor.js';
import {
  getCourse,
  getLessonSlides,
  getQuestionnaire,
} from './courseService.js';
import {
  getQuestionnaireStatus,
  getQuestionnaireStatuses,
} from './questionnaireStatusService.js';
import { getAdvisor } from './advisorService.js';
import { getProvider } from './providerService.js';
import { getStudent } from './studentService.js';
import Course from '../models/Course.js';
import Student from '../models/Student.js';

export async function getCourseStatus({
  courseStatusId,
  courseId,
  studentId,
  fields = [],
}) {
  const defaultFields = ['course', 'student', 'activeLesson', 'activeSlide'];
  const courseStatusFields = !isEmpty(fields) ? fields : defaultFields;

  const query = courseStatusId
    ? CourseStatus.findById(courseStatusId)
    : CourseStatus.findOne({
        course: courseId,
        student: studentId,
      });

  const courseStatus = await query
    .where({ deleted: false, enabled: true })
    .select(courseStatusFields);

  return courseStatus;
}

export async function updateCourseStatus(courseStatusId, data) {
  const updatedCourseStatus = await CourseStatus.findByIdAndUpdate(
    courseStatusId,
    data,
    {
      new: true,
      runValidators: true,
    }
  );
  return updatedCourseStatus;
}

// Updates completed lessons, course, reached lesson
export async function updateCourseStatusOnCorrectQuestionnaire({
  courseStatusId,
  questionnaireStatusId,
}) {
  const courseStatus = await getCourseStatus({
    courseStatusId,
    fields: ['completedSlides', 'mode'],
  });

  const questionnaireStatus = await getQuestionnaireStatus({
    questionnaireStatusId,
    fields: ['questionnaire'],
  });

  // Do nothing if questionnaire or course status is missing
  if (!questionnaireStatus || !courseStatus) return;

  // Init variables
  const slideId = questionnaireStatus.questionnaire.slide.toString();
  const lessonId = questionnaireStatus.questionnaire.lesson.toString();
  const completedSlideIds =
    courseStatus.completedSlides?.map((id) => id.toString()) || [];

  // - Mark slide as completed
  if (!completedSlideIds.includes(slideId)) {
    courseStatus.completedSlides = [...completedSlideIds, slideId];
    await courseStatus.save();
  }

  // Update reached lesson and eventually mark lesson as completed
  const isCourseCompleted = await completeLessonAndUpdateProgress({
    courseStatusId,
    lessonId,
  });

  // Send email and push notifications
  sendQuestionnaireCorrectedNotifications({
    courseStatusId,
    questionnaireId: questionnaireStatus.questionnaire._id,
    isCourseCompleted,
  });
}

// Updates completed lessons, completed course, reached lesson
async function completeLessonAndUpdateProgress({ courseStatusId, lessonId }) {
  const courseStatus = await getCourseStatus({
    courseStatusId,
    fields: [
      'completedLessons',
      'completedSlides',
      'reachedLesson',
      'course',
      'mode',
    ],
  });

  // Init variables
  const completedSlides = courseStatus.completedSlides || [];
  const completedSlideIds = completedSlides.map((id) => id.toString()) || [];
  const completedLessons = courseStatus.completedLessons || [];
  let completedLessonIds = completedLessons.map((id) => id.toString()) || [];
  const reachedLessonId = courseStatus.reachedLesson?.toString();
  const courseId = courseStatus.course;
  const course = await getCourse({
    courseId,
    fields: ['lessons', 'progression'],
  });
  const courseLessonIds = course.lessons.map((id) => id.toString());
  const lessonSlides = await getLessonSlides(lessonId);
  const lessonSlideIds = lessonSlides.map((slide) => slide._id.toString());
  const questionnaireStatuses = await getQuestionnaireStatuses({
    courseStatusId,
    fields: ['submittedAt', 'correctedAt', 'questionnaire', 'reviewRequested'],
  });
  const currentDate = new Date();

  // Complete lesson and eventually course
  const isLessonCompleted = lessonSlideIds.every((_slideId) =>
    completedSlideIds.includes(_slideId)
  );
  if (isLessonCompleted && !completedLessonIds.includes(lessonId)) {
    completedLessonIds = [...completedLessonIds, lessonId];
    courseStatus.completedLessons = completedLessonIds;

    // Finish course if all lessons are completed
    const isCourseCompleted = courseLessonIds.every((_lessonId) =>
      completedLessonIds.includes(_lessonId)
    );
    if (isCourseCompleted) {
      courseStatus.finishedAt = currentDate;
    }
  }

  // Update reached lesson (check if next lesson can be unlocked, linear courses only)
  const reachedLessonIndex = courseLessonIds.indexOf(reachedLessonId);
  const currentLessonIndex = courseLessonIds.indexOf(lessonId);
  if (
    !courseStatus.finishedAt &&
    course.progression === 'linear' &&
    reachedLessonIndex <= currentLessonIndex
  ) {
    // Check whether all slides have been processed
    const allSlidesProcessed = lessonSlides.every((slide) => {
      // Content slides: must be completed
      if (slide.type === 'content') {
        return completedSlideIds.includes(slide._id.toString());
      }
      // Questionnaires: must be submitted and corrected (if review required)
      if (slide.type === 'questionnaire') {
        const questionnaireStatus = questionnaireStatuses.find(
          (_questionnaireStatus) =>
            areEqualIDs(
              _questionnaireStatus.questionnaire,
              slide.questionnaire._id
            )
        );
        const isSubmitted = !!questionnaireStatus?.submittedAt;
        const isCorrected = !!questionnaireStatus?.correctedAt;

        return isSubmitted // <-- questionnaire submitted
          ? questionnaireStatus.reviewRequested // <-- submitted in supervised mode
            ? slide.questionnaire.requiresReview // <-- questionnaire requires review
              ? isCorrected // <-- questionnaire needs to be corrected (only after this, the student can continue)
              : true // <-- questionnaire doesn't require review (student can continue)
            : true // <-- submitted in autonomous mode (no review required, student can continue)
          : false; // <-- questionnaire not submitted yet (student can't continue)
      }
      return false;
    });

    // Unlock next lesson
    if (allSlidesProcessed) {
      const nextLessonId = courseLessonIds[currentLessonIndex + 1];
      if (nextLessonId) courseStatus.reachedLesson = nextLessonId;
    }
  }

  await courseStatus.save();

  return !!courseStatus.finishedAt;
}

async function sendQuestionnaireCorrectedNotifications({
  courseStatusId,
  questionnaireId,
  isCourseCompleted,
}) {
  // Get course status
  const courseStatus = await getCourseStatus({
    courseStatusId,
    fields: [
      // 'totalLessons',
      'startedLessons',
      'course',
      'student',
      'mode',
    ],
  });

  // Only send notifications for supervised courses
  if (!courseStatus.mode === 'supervised') return;

  // Get student
  const student = await getStudent({
    studentId: courseStatus.student,
    fields: [
      'advisor',
      'email',
      'firstName',
      'lastName',
      'username',
      'preferences',
      'webUser',
    ],
  });
  if (!student.advisor) return;

  // Check if student has notifications enabled
  const notifications = student?.preferences?.notifications;
  if (
    !notifications?.questionnaireCorrected?.email &&
    !notifications?.questionnaireCorrected?.push &&
    !notifications?.courseFinished?.email &&
    !notifications?.courseFinished?.push
  )
    return;

  // Get student name
  const { username, email: studentEmail, webUser } = student;
  const studentName = getFullName(student) || username;

  // Get advisor
  const advisor = await getAdvisor({
    advisorId: student.advisor,
    fields: ['firstName', 'lastName', 'email'],
  });
  if (!advisor?.email) return;
  const advisorName = getFullName(advisor);

  // Get course
  const course = await getCourse({
    courseId: courseStatus.course,
    fields: ['title', 'provider', 'slug', 'lessons'],
  });
  const courseName = course.title;

  // Get provider
  const provider = await getProvider({
    providerId: course.provider,
    fields: ['language', 'entity', 'site', 'pushSite', 'pages'],
  });

  // Get entity
  const entity = await Entity.findById(
    provider.entity,
    'emailFooter email phone config.email.appearance.logo config.email.appearance.colors'
  ).lean();

  // Get questionnaire
  const questionnaire = await getQuestionnaire({ questionnaireId });
  const slide = questionnaire?.slide;
  const lesson = questionnaire?.lesson;

  // Get site
  const { site } = await siteServices.getSiteById(provider.site);
  const baseUrl = await siteServices.getBaseUrl({ site });

  // Set language + date
  const language = site.language || provider.langage || 'en';
  const dateNow = new Date();

  // Build login page URL
  let loginPageUrl = await getPageUrlById(site.auth?.pages?.loginPage);
  loginPageUrl = `${baseUrl}${loginPageUrl}`;

  // Build questionnaire URL
  const courseContentPath = (
    await getPageUrlById(provider.pages?.courseContentPage)
  )
    ?.replace('[Course]', course.slug)
    .replace('[CourseLesson]', lesson?.slug);

  const questionnaireUrl = `${loginPageUrl}?callbackUrl=${baseUrl}${courseContentPath}?s=${slide?.slug}`;

  // Build courses list URL
  const coursesListPath = await getPageUrlById(provider.pages?.coursesListPage);
  const coursesListUrl = `${loginPageUrl}?callbackUrl=${baseUrl}${coursesListPath}`;

  // Send email
  if (
    (!isCourseCompleted && notifications?.questionnaireCorrected?.email) ||
    (isCourseCompleted && notifications?.courseFinished?.email)
  ) {
    // Set email values
    const emailSubject = isCourseCompleted
      ? t(language, 'cFinishedSubject', { course: courseName })
      : t(language, 'qCorrectedSubject', { course: courseName });

    const emailTitle = isCourseCompleted
      ? t(language, 'cFinishedTitle', { student: studentName })
      : t(language, 'qCorrectedTitle', { student: studentName });

    const emailSubtitle = isCourseCompleted
      ? t(language, 'cFinishedSubtitle', {
          course: courseName,
          advisor: advisorName,
        })
      : t(language, 'qCorrectedSubtitle', {
          course: courseName,
          advisor: advisorName,
        });

    // Get course progress
    const startedLessons = courseStatus.startedLessons?.length || 0;
    const totalLessons = course.lessons?.length || 0;

    // Send email
    sendEmail({
      language,
      entity,
      site,
      to: studentEmail,
      subject: emailSubject,
      templateName: 'courses/questionnaireCorrected',
      templateValues: {
        title: emailTitle,
        subtitle: emailSubtitle,
        advisorLabel: t(language, 'advisor'),
        advisor: advisorName,
        dateLabel: t(language, 'correctedOn'),
        date: dateNow.toLocaleString(language),
        courseLabel: t(language, 'course'),
        course: courseName,
        lessonLabel: t(language, 'qCorrectedCourseProgress', {
          startedLessons,
          totalLessons,
        }),
        lesson: lesson?.title || '',
        questionnaireLabel: t(language, 'questionnaire'),
        questionnaire: slide?.title || '',
        linkText: t(language, 'qCorrectedLinkText'),
        linkURL: questionnaireUrl,
        linkNotWorking: t(language, 'linkNotWorking'),
        ...(isCourseCompleted
          ? {
              courseCompleted: true,
              linkMoreTitle: t(language, 'cFinishedLinkMoreTitle'),
              linkMoreURL: coursesListUrl,
              linkMoreText: t(language, 'cFinishedLinkMoreText'),
            }
          : {
              courseCompleted: false,
              requiresReview: questionnaire.requiresReview,
              courseUnlockedText: t(language, 'qCorrectedCourseUnlockedText'),
            }),
      },
    });
  }

  // Send push notification
  if (
    (!isCourseCompleted && notifications?.questionnaireCorrected?.push) ||
    (isCourseCompleted && notifications?.courseFinished?.push)
  ) {
    // We get the url the notification points to.

    const pushBaseUrl = await siteServices.getBaseUrl({
      siteId: provider.pushSite,
    });
    const pushContentPage = (
      await getPageUrlById(provider.pages?.pushCourseContentPage)
    )
      ?.replace('[Course]', course.slug)
      .replace('[CourseLesson]', lesson?.slug);

    const targetUrl =
      pushBaseUrl && pushContentPage
        ? `${pushBaseUrl}${pushContentPage}`
        : undefined;

    // Heading of the notification (same as email subject for now?)
    const heading = isCourseCompleted
      ? t(language, 'cFinishedSubject', { course: courseName })
      : t(language, 'qCorrectedSubject', { course: courseName });

    await sendPushNotification({
      site,
      to: [webUser], // Array of webUser ids
      headings: {
        en: heading,
      },
      contents: { en: t(language, 'qCorrectedLinkText') },
      targetUrl: targetUrl,
    });
  }
}

export async function startCourse({
  courseId,
  advisorId,
  providerId,
  studentId,
  mode,
}) {
  const course = await Course.findById(courseId)
    .select('lessons progression')
    .where({ deleted: false, enabled: true })
    .populate({
      path: 'lessons',
      match: { deleted: false, enabled: true },
      select: '_id',
    });

  // Check if course exists
  if (!course) return { error: 'ERROR_COURSE_MISSING' };

  // Check if student is already taking this course
  const existingCourseStatus = await getCourseStatus({
    studentId,
    courseId,
    fields: ['finishedAt'],
  });
  if (existingCourseStatus) return { error: 'ERROR_COURSE_ALREADY_STARTED' };

  // Get student
  const student = await Student.findById(studentId, 'advisor');

  // Get current date
  const currentDate = new Date();

  // Create new course status
  const courseStatus = await CourseStatus.create({
    course: courseId,
    student: studentId,
    advisor: advisorId ?? student?.advisor,
    startedAt: currentDate,
    lastActivity: currentDate,
    finishedAt: null,
    mode,
    provider: providerId,
    reachedLesson:
      course.progression === 'linear' ? course.lessons[0]._id : null,
  });

  return { courseStatus };
}

export default {
  getCourseStatus,
  updateCourseStatus,
  startCourse,
  updateCourseStatusOnCorrectQuestionnaire,
};
