import { isEmpty } from '#utils/arrays.js';

import Advisor from '../models/Advisor.js';

export async function getAdvisor({
  advisorId,
  backendUserId,
  providerId,
  fields = [],
}) {
  const defaultFields = ['firstName', 'lastName', 'email'];
  const advisorFields = !isEmpty(fields) ? fields : defaultFields;

  const query = advisorId
    ? Advisor.findById(advisorId)
    : backendUserId
      ? Advisor.findOne({
          provider: providerId,
          backendUser: backendUserId,
        })
      : null;

  const advisor = query
    ? await query.select(advisorFields).where({ deleted: false, enabled: true })
    : null;

  return advisor;
}

export default {
  getAdvisor,
};
