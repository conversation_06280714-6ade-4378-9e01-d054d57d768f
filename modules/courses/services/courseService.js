import omit from 'lodash/omit.js';
import mongoose from 'mongoose';

import { isEmpty } from '#utils/arrays.js';
import { randomString, slugify } from '#utils/strings.js';

import { getProjection } from '#utils/aggregations.js';
import Course from '../models/Course.js';
import Lesson from '../models/Lesson.js';
import Slide from '../models/Slide.js';
import Questionnaire from '../models/Questionnaire.js';
import Provider from '../models/Provider.js';
import CourseStatus from '../models/CourseStatus.js';
import QuestionnaireStatus from '../models/QuestionnaireStatus.js';

export async function getCourse({ courseId, fields = [] }) {
  const defaultFields = ['abstract', 'images', 'title'];
  const courseFields = !isEmpty(fields) ? fields : defaultFields;
  const courseProjection = getProjection(courseFields);

  const courseAggregation = Course.aggregate().match({
    _id: new mongoose.Types.ObjectId(courseId),
    deleted: false,
    enabled: true,
  });

  if (fields.includes('lessons')) {
    courseAggregation
      .lookup({
        from: 'courselessons',
        let: { lessonIds: '$lessons' },
        pipeline: [
          {
            $match: {
              $expr: { $in: ['$_id', '$$lessonIds'] },
              deleted: false,
              enabled: true,
            },
          },
          {
            $addFields: {
              position: { $indexOfArray: ['$$lessonIds', '$_id'] },
            },
          },
          { $sort: { position: 1 } },
          { $project: { _id: 1 } },
        ],
        as: 'lessons',
      })
      .addFields({ lessons: '$lessons._id' });
  }

  courseAggregation.project(courseProjection);

  const course = await courseAggregation;

  return course[0];
}

export async function cloneCourse({ courseId, title, providerId, language }) {
  const course = await getCourseContent(courseId, {
    lessonsFilter: { deleted: false },
    slidesFilter: { deleted: false },
  });

  // Init default values
  const provider = await Provider.findOne(
    { _id: providerId || course.provider },
    '_id entity'
  );

  // Get unique title
  const existingCourse = await Course.findOne({
    provider: provider._id,
    title,
    deleted: false,
  });
  if (existingCourse) {
    const randomNumber = Math.floor(10000 + Math.random() * 90000);
    title = `${title} (${randomNumber})`;
  }

  // Get unique slug
  const slug = await Course.getAvailableSlug(slugify(title), provider);

  // Create clone
  const newCourse = await Course.create({
    ...omit(course, ['_id', 'createdAt', 'updatedAt']),
    title,
    language: language || course.language,
    slug,
    provider,
    entity: provider.entity,
    lessons: [],
  });

  const lessonIds = await cloneLessons({
    newCourse,
    lessons: course.lessons,
    language,
  });
  newCourse.lessons = lessonIds;
  await newCourse.save();

  return newCourse;
}

async function cloneLessons({ newCourse, lessons, language }) {
  const lessonIds = [];
  for (const lesson of lessons) {
    const newLesson = await Lesson.create({
      ...omit(lesson, ['_id', 'createdAt', 'updatedAt']),
      course: newCourse._id,
      provider: newCourse.provider,
      slides: [],
    });

    const slideIds = await cloneSlides({
      newLesson,
      slides: lesson.slides,
      language,
    });
    newLesson.slides = slideIds;
    await newLesson.save();

    lessonIds.push(newLesson._id);
  }
  return lessonIds;
}

async function cloneSlides({ newLesson, slides, language }) {
  const slideIds = [];
  for (const slide of slides) {
    const newSlide = await Slide.create({
      ...omit(slide, ['_id', 'createdAt', 'updatedAt']),
      lesson: newLesson._id,
      course: newLesson.course,
      provider: newLesson.provider,
      questionnaire: null,
    });

    if (slide.type === 'questionnaire' && slide.questionnaire) {
      const questionnaire = await cloneQuestionnaire({
        newSlide,
        questionnaire: slide.questionnaire,
        language,
      });
      newSlide.questionnaire = questionnaire._id;
      await newSlide.save();
    }

    slideIds.push(newSlide._id);
  }
  return slideIds;
}

async function cloneQuestionnaire({ newSlide, questionnaire, language }) {
  // Clone questions
  const questions = await cloneQuestions({
    questions: questionnaire.questions,
    language,
  });

  // Create questionnaire
  const newQuestionnaire = await Questionnaire.create({
    ...omit(questionnaire, ['_id', 'createdAt', 'updatedAt']),
    questions,
    slide: newSlide._id,
    course: newSlide.course,
    lesson: newSlide.lesson,
    provider: newSlide.provider,
  });

  return newQuestionnaire;
}

async function cloneQuestions({ questions }) {
  const newQuestions = questions?.map((question) => {
    const newQuestion = omit(question, ['_id']);
    switch (question.type) {
      case 'openAnswer':
        break;
      case 'multipleOpenAnswers':
        newQuestion.config.textBoxes = newQuestion.config.textBoxes.map(
          (textBox) => ({
            ...omit(textBox, ['id']),
            id: randomString({ length: 24 }).toLowerCase(),
          })
        );
        break;
      case 'multipleChoice':
      case 'singleChoice':
        newQuestion.config.options = newQuestion.config.options.map(
          (option) => ({
            ...omit(option, ['id']),
            id: randomString({ length: 24 }).toLowerCase(),
          })
        );
        break;
      case 'sectionTitle':
        break;
      case 'richText':
        break;
      case 'fillInTheBlank':
        break;
      case 'scale':
        break;
      default:
        break;
    }
    return newQuestion;
  });
  return newQuestions || [];
}

export async function getCourseContent(
  courseId,
  { lessonsFilter = { deleted: false }, slidesFilter = { deleted: false } } = {}
) {
  const course = await Course.aggregate()
    .match({
      _id: new mongoose.Types.ObjectId(courseId),
    })
    .lookup({
      from: 'courselessons',
      let: { lessonIds: '$lessons' },
      pipeline: [
        {
          $match: {
            $expr: { $in: ['$_id', '$$lessonIds'] },
            ...lessonsFilter,
          },
        },
        {
          $addFields: { position: { $indexOfArray: ['$$lessonIds', '$_id'] } },
        },
        { $sort: { position: 1 } },
        { $project: { position: 0 } },
        {
          $lookup: {
            from: 'courseslides',
            let: { slideIds: '$slides' },
            pipeline: [
              {
                $match: {
                  $expr: { $in: ['$_id', '$$slideIds'] },
                  ...slidesFilter,
                },
              },
              {
                $addFields: {
                  position: { $indexOfArray: ['$$lessonIds', '$_id'] },
                },
              },
              { $sort: { position: 1 } },
              { $project: { position: 0 } },
              {
                $lookup: {
                  from: 'coursequestionnaires',
                  localField: 'questionnaire',
                  foreignField: '_id',
                  as: 'questionnaire',
                },
              },
              {
                $addFields: {
                  questionnaire: { $arrayElemAt: ['$questionnaire', 0] },
                },
              },
            ],
            as: 'slides',
          },
        },
      ],
      as: 'lessons',
    });

  return course[0];
}

export async function getQuestionnaire({ questionnaireId }) {
  const questionnaire = await Questionnaire.findById(questionnaireId)
    .populate({
      path: 'slide',
      select: 'title slug',
    })
    .populate({
      path: 'lesson',
      select: 'title slug',
    })
    .select('slide lesson requiresReview');
  return questionnaire;
}

export async function getLessonSlides(lessonId) {
  const lessons = await Lesson.aggregate()
    .match({
      _id: new mongoose.Types.ObjectId(lessonId),
      deleted: false,
      enabled: true,
    })
    .lookup({
      from: 'courseslides',
      let: { slideIds: '$slides' },
      pipeline: [
        {
          $match: {
            $expr: { $in: ['$_id', '$$slideIds'] },
            deleted: false,
            enabled: true,
          },
        },
        {
          $lookup: {
            from: 'coursequestionnaires',
            let: { questionnaireId: '$questionnaire' },
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$questionnaireId'] } } },
              { $project: { requiresReview: 1 } },
            ],
            as: 'questionnaire',
          },
        },
        {
          $project: {
            _id: 1,
            title: 1,
            type: 1,
            questionnaire: { $arrayElemAt: ['$questionnaire', 0] },
          },
        },
      ],
      as: 'slides',
    })
    .project({
      slides: 1,
    });

  return lessons[0]?.slides || [];
}

export async function hardDeleteCourse(courseId) {
  const course = await Course.findById(courseId);

  if (!course) {
    return { status: 404, error: 'COURSE_NOT_FOUND' };
  }

  const courseStatusResult = await CourseStatus.deleteMany({
    course: courseId,
  });
  const questionnaireStatusesResult = await QuestionnaireStatus.deleteMany({
    course: courseId,
  });
  const questionnairesResult = await Questionnaire.deleteMany({
    course: courseId,
  });
  const slidesResult = await Slide.deleteMany({ course: courseId });
  const lessonsResult = await Lesson.deleteMany({ course: courseId });
  const courseResult = await Course.deleteOne({ _id: courseId });

  return {
    results: {
      courseStatuses: courseStatusResult.deletedCount,
      questionnaireStatuses: questionnaireStatusesResult.deletedCount,
      questionnaires: questionnairesResult.deletedCount,
      slides: slidesResult.deletedCount,
      lessons: lessonsResult.deletedCount,
      course: courseResult.deletedCount,
    },
  };
}

export default {
  getCourse,
  getCourseContent,
  cloneCourse,
  getLessonSlides,
  hardDeleteCourse,
};
