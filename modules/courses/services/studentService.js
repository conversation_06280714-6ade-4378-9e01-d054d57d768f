import mongoose from 'mongoose';

import { isEmpty } from '#utils/arrays.js';
import { areEqualIDs } from '#utils/helpers.js';

import webUserServices from '#modules/web-auth/services/webUserServices.js';

import Student from '../models/Student.js';
import StudentAdvisor from '../models/StudentAdvisor.js';
import chatService from './chatService.js';
import providerService from './providerService.js';
import StudentActivity from '../models/StudentActivity.js';
import AdvisorStudentNotes from '../models/AdvisorStudentNotes.js';
import CourseStatus from '../models/CourseStatus.js';
import QuestionnaireStatus from '../models/QuestionnaireStatus.js';
import Chat from '../models/Chat.js';
import ChatMessage from '../models/ChatMessage.js';

export async function getStudent({ studentId, username, fields = [] }) {
  const defaultFields = ['username', 'firstName', 'lastName', 'email'];
  const studentFields = !isEmpty(fields) ? fields : defaultFields;

  const studentQuery = username
    ? Student.findOne({ username })
    : Student.findById(studentId);

  const student = await studentQuery
    .select(studentFields)
    .where({ deleted: false, enabled: true });

  return student;
}

export async function getStudentIds({ advisorIds, providerId }) {
  if (!advisorIds?.length) return [];

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  const studentIds = await Student.distinct('_id', {
    provider: new mongoose.Types.ObjectId(providerId),
    advisor: { $in: advisorIds },
    deleted: false,
    ...(!isCorrespondenceCoursesEnabled && {
      webUser: { $exists: true },
    }),
  });

  return studentIds;
}

export async function updateStudent(studentId, data) {
  const updatedStudent = await Student.findByIdAndUpdate(studentId, data, {
    new: true,
    runValidators: true,
  });
  return updatedStudent;
}

export async function deleteStudentAccount({
  studentId,
  entity,
  siteId,
  deleteWebUser,
}) {
  const student = await Student.findById(studentId, 'webUser');

  // Soft delete student (keep for statistics, remove personal data)
  await Student.updateOne(
    { _id: studentId },
    {
      $set: { deleted: true },
      $unset: {
        enabled: '',
        slug: '',
        gender: '',
        username: '',
        salutation: '',
        title: '',
        firstName: '',
        lastName: '',
        notes: '',
        avatar: '',
        birthday: '',
        birthdayNotes: '',
        baptized: '',
        baptizedOn: '',
        denomination: '',
        source: '',
        maritalStatus: '',
        maritalStatusComments: '',
        child: '',
        profile: '',
        address: '',
        email: '',
        phone: '',
        mobile: '',
        courseCountry: '',
        courseCountryRegion: '',
        advisor: '',
        staticAdvisor: '',
        webUser: '',
        preferences: '',
        permissions: '',
      },
    }
  );

  // Hard delete student data
  await StudentAdvisor.deleteMany({ student: studentId });
  await StudentActivity.deleteMany({ student: studentId });
  await AdvisorStudentNotes.deleteMany({ student: studentId });

  // Soft delete course status (keep data for statistics)
  await CourseStatus.updateMany(
    { student: studentId },
    {
      deleted: true,
    }
  );

  // Soft delete questionnaire status (anonymize data)
  await QuestionnaireStatus.updateMany(
    {
      student: studentId,
    },
    {
      $set: { deleted: true },
      $unset: {
        answers: '',
        advisorNotes: '',
      },
    }
  );

  // Hard delete chats and messages
  const studentChats = await Chat.find(
    {
      members: {
        $elemMatch: {
          type: 'CourseStudent',
          id: studentId,
        },
      },
    },
    '_id'
  );
  const chatIds = studentChats.map((chat) => chat._id);
  await Chat.deleteMany({ _id: { $in: chatIds } });
  await ChatMessage.deleteMany({ chat: { $in: chatIds } });

  // Hard delete web user
  if (deleteWebUser && student.webUser && siteId) {
    await webUserServices.hardDeleteUser({
      userId: student.webUser,
      entity,
      siteId,
    });
  }
}

async function assignAdvisor(studentId, advisorId) {
  const student = await getStudent({
    studentId,
    fields: ['advisor', 'provider'],
  });
  if (!student || areEqualIDs(student.advisor, advisorId)) return false;

  // Set main advisor of student
  await updateStudent(studentId, {
    advisor: mongoose.isValidObjectId(advisorId) ? advisorId : null,
  });

  // Create advisor mapping (if it doesn't exist yet)
  if (advisorId) {
    await addStudentAdvisorMapping({
      studentId,
      advisorId,
      providerId: student.provider,
    });
  }
}

async function addStudentAdvisorMapping({ studentId, advisorId }) {
  const student = await getStudent({
    studentId,
    fields: ['provider'],
  });
  if (!student || !advisorId) return null;

  // Check if mapping already exists
  const studentAdvisorMapping = await getStudentAdvisorMapping({
    studentId,
    advisorId,
  });
  if (studentAdvisorMapping) return null;

  // Create new mapping
  await StudentAdvisor.create({
    student: studentId,
    advisor: advisorId,
    provider: student.provider,
  });

  // Create chat (if it doesn't exist yet)
  await chatService.createChat({
    studentId,
    advisorId,
    providerId: student.provider,
  });
}

async function removeAdvisorMapping({ studentId, advisorId }) {
  const student = await getStudent({
    studentId,
    fields: ['provider'],
  });
  if (!student || !advisorId) return null;

  // Get chat between student and advisor
  const chat = await chatService.getChat({
    providerId: student.provider,
    studentId: student._id,
    advisorId,
    fields: ['messagesCount'],
  });

  if (!chat || chat.messagesCount === 0) {
    // Delete chat
    if (chat) await chatService.hardDeleteChat(chat._id);

    // Delete student/advisor mappings)
    await StudentAdvisor.deleteMany({
      student: student._id,
      advisor: advisorId,
      provider: student.provider,
    });
  }
}

async function getStudentAdvisorMapping({ studentId, advisorId }) {
  const studentAdvisorMapping = await StudentAdvisor.findOne({
    student: studentId,
    advisor: advisorId,
  });
  return studentAdvisorMapping;
}

export default {
  getStudent,
  getStudentIds,
  assignAdvisor,
  addStudentAdvisorMapping,
  removeAdvisorMapping,
  deleteStudentAccount,
};
