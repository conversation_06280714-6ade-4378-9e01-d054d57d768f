import { isEmpty } from '#utils/arrays.js';

import mongoose from 'mongoose';

import { getCount } from '#utils/aggregations.js';

import '../models/Advisor.js';
import QuestionnaireStatus from '../models/QuestionnaireStatus.js';
import Student from '../models/Student.js';
import providerService from './providerService.js';

const COURSE_STATUS_LOOKUP = {
  from: 'coursestatuses',
  let: { courseStatusId: '$courseStatus' },
  pipeline: [
    {
      $match: {
        $expr: { $eq: ['$_id', '$$courseStatusId'] },
        deleted: false,
        enabled: true,
      },
    },
    {
      $lookup: {
        from: 'courseadvisors',
        let: { advisorId: '$advisor' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$_id', '$$advisorId'] },
              deleted: false,
            },
          },
          {
            $project: {
              _id: 1,
              firstName: 1,
              lastName: 1,
              absenceMessage: 1,
              enabled: 1,
            },
          },
        ],
        as: 'advisor',
      },
    },
    {
      $project: {
        mode: 1,
        advisor: { $arrayElemAt: ['$advisor', 0] },
        course: 1,
      },
    },
  ],
  as: 'courseStatus',
};

export async function getQuestionnaireStatus({
  courseStatusId,
  questionnaireStatusId,
  questionnaireId,
  fields = [],
}) {
  const defaultFields = ['answers', 'advisorNotes', 'advisor'];
  const questionnaireStatusFields = !isEmpty(fields) ? fields : defaultFields;

  const query = questionnaireStatusId
    ? QuestionnaireStatus.findById(questionnaireStatusId)
    : QuestionnaireStatus.findOne({
        courseStatus: courseStatusId,
        questionnaire: questionnaireId,
      });

  if (questionnaireStatusFields.includes('advisor')) {
    query.populate({
      path: 'advisor',
      select: 'firstName lastName',
    });
  }

  if (questionnaireStatusFields.includes('courseStatus')) {
    query.populate({
      path: 'courseStatus',
      select: 'mode',
    });
  }

  if (questionnaireStatusFields.includes('questionnaire')) {
    query.populate({
      path: 'questionnaire',
      select: 'slide lesson course requiresReview',
    });
  }

  const questionnaireStatus = await query
    .where({ deleted: false })
    .select(questionnaireStatusFields);

  return questionnaireStatus;
}

export async function getQuestionnaireStatuses({
  courseStatusId,
  fields = [],
}) {
  const defaultFields = [
    'answers',
    'advisorNotes',
    'advisor',
    'submittedAt',
    'correctedAt',
  ];
  const questionnaireStatusFields = !isEmpty(fields) ? fields : defaultFields;

  const query = QuestionnaireStatus.find({
    courseStatus: courseStatusId,
    deleted: false,
  });

  const questionnaireStatuses = await query
    .select(questionnaireStatusFields)
    .sort('createdAt')
    .lean();

  return questionnaireStatuses;
}

export async function getAllQuestionnaireStatuses({
  providerId,
  advisorIds,
  courseIds,
  studentIds,
  statusFilter,
  status = 'open', // open, completed
  mode = 'supervised', // supervised, autonomous (only for status=open)
  skip,
  limit,
}) {
  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  // Get all students for the given advisors
  if (advisorIds?.length > 0) {
    const advisorStudents = await Student.find({
      advisor: { $in: advisorIds },
      deleted: false,
      enabled: true,
    }).select('_id');
    const advisorStudentIds = advisorStudents.map((student) => student._id);

    // Append advisor students to studentIds
    studentIds = [...studentIds, ...advisorStudentIds];
  }

  const questionnaireStatusAggregation = QuestionnaireStatus.aggregate().match({
    provider: new mongoose.Types.ObjectId(providerId),
    ...(status === 'open' && {
      submittedAt: { $ne: null },
      correctedAt: { $eq: null },
    }),
    ...(status === 'completed' && {
      submittedAt: { $ne: null },
      correctedAt: { $ne: null },
    }),
    ...(mode === 'supervised' && { reviewRequested: true }),
    ...((studentIds?.length > 0 || advisorIds?.length > 0) && {
      student: { $in: studentIds },
    }),
    ...(courseIds?.length > 0 && { course: { $in: courseIds } }),
    ...statusFilter,
  });

  // Filter by course mode (TODO: is reviewRequested enough?)
  if (status === 'open') {
    questionnaireStatusAggregation
      .lookup(COURSE_STATUS_LOOKUP)
      .addFields({ courseStatus: { $arrayElemAt: ['$courseStatus', 0] } })
      .match({ 'courseStatus.mode': mode });
  }

  // Get total items
  const totalQuestionnaireStatuses = await getCount(
    questionnaireStatusAggregation
  );

  questionnaireStatusAggregation
    .sort({ submittedAt: -1 })
    .skip(skip)
    .limit(limit);

  if (status === 'completed') {
    questionnaireStatusAggregation
      .lookup(COURSE_STATUS_LOOKUP)
      .lookup({
        from: 'courseadvisors',
        let: { advisorId: '$advisor' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$_id', '$$advisorId'] },
              deleted: false,
            },
          },
          {
            $project: {
              _id: 1,
              firstName: 1,
              lastName: 1,
              absenceMessage: 1,
              enabled: 1,
            },
          },
        ],
        as: 'advisor',
      })
      .addFields({
        courseStatus: { $arrayElemAt: ['$courseStatus', 0] },
        advisor: { $arrayElemAt: ['$advisor', 0] },
      });
  }

  // Get questionnaire statuses
  const questionnaireStatuses = await questionnaireStatusAggregation
    .lookup({
      from: 'courses',
      let: { courseId: '$course' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$courseId'] },
            deleted: false,
            enabled: true,
          },
        },
        { $project: { title: 1 } },
      ],
      as: 'course',
    })
    .lookup({
      from: 'coursequestionnaires',
      let: { questionnaireId: '$questionnaire' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$questionnaireId'] },
            deleted: false,
            enabled: true,
          },
        },
        {
          $lookup: {
            from: 'courseslides',
            let: { slideId: '$slide' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$_id', '$$slideId'] },
                  deleted: false,
                  enabled: true,
                },
              },
              {
                $lookup: {
                  from: 'courselessons',
                  let: { lessonId: '$lesson' },
                  pipeline: [
                    {
                      $match: {
                        $expr: { $eq: ['$_id', '$$lessonId'] },
                        deleted: false,
                        enabled: true,
                      },
                    },
                    { $project: { title: 1 } },
                  ],
                  as: 'lesson',
                },
              },
              {
                $project: {
                  title: 1,
                  lesson: { $arrayElemAt: ['$lesson', 0] },
                },
              },
            ],
            as: 'slide',
          },
        },
        { $project: { slide: { $arrayElemAt: ['$slide', 0] } } },
      ],
      as: 'questionnaire',
    })
    .lookup({
      from: 'coursestudents',
      let: { studentId: '$student' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$studentId'] },
            deleted: false,
            enabled: true,
          },
        },
        {
          $lookup: {
            from: 'courseadvisors',
            let: { advisorId: '$advisor' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$_id', '$$advisorId'] },
                  deleted: false,
                },
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  absenceMessage: 1,
                  enabled: 1,
                },
              },
            ],
            as: 'advisor',
          },
        },
        {
          $project: {
            username: 1,
            advisor: { $arrayElemAt: ['$advisor', 0] },
            webUser: 1,
            profile: 1,
            ...(isCorrespondenceCoursesEnabled && {
              firstName: 1,
              lastName: 1,
            }),
          },
        },
      ],
      as: 'student',
    })
    .addFields({
      course: { $arrayElemAt: ['$course', 0] },
      student: { $arrayElemAt: ['$student', 0] },
      questionnaire: { $arrayElemAt: ['$questionnaire', 0] },
    });

  return {
    count: totalQuestionnaireStatuses,
    items: questionnaireStatuses,
  };
}

export async function upsertQuestionnaireStatus(
  questionnaireStatusId,
  action,
  {
    courseStatusId,
    questionnaireId,
    studentId,
    providerId,
    courseId,
    answers,
    reviewRequested,
  }
) {
  const questionnaireStatusData = {
    ...(courseStatusId && { courseStatus: courseStatusId }),
    ...(questionnaireId && { questionnaire: questionnaireId }),
    ...(studentId && { student: studentId }),
    ...(providerId && { provider: providerId }),
    ...(courseId && { course: courseId }),
    answers,
    reviewRequested,
    ...(action === 'save' && { savedAt: new Date() }),
    ...(action === 'submit' && { submittedAt: new Date() }),
  };

  const questionnaireStatus = questionnaireStatusId
    ? await QuestionnaireStatus.findByIdAndUpdate(
        questionnaireStatusId,
        questionnaireStatusData,
        {
          new: true,
          runValidators: true,
        }
      )
    : await QuestionnaireStatus.create(questionnaireStatusData);

  return questionnaireStatus;
}

export default {
  getQuestionnaireStatus,
  getQuestionnaireStatuses,
  upsertQuestionnaireStatus,
};
