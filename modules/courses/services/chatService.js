import mongoose from 'mongoose';

import { isEmpty } from '#utils/arrays.js';
import { getProjection } from '#utils/aggregations.js';

import Chat from '../models/Chat.js';

export async function getChat({
  chatId,
  studentId,
  advisorId,
  providerId,
  fields = [],
}) {
  const defaultFields = ['members', 'lastMessage'];
  const chatFields = !isEmpty(fields) ? fields : defaultFields;
  const chatProjection = getProjection(chatFields);

  const chatAggregation = Chat.aggregate().match({
    type: 'basic',
    deleted: false,
    enabled: true,
    ...(chatId
      ? { _id: new mongoose.Types.ObjectId(chatId) }
      : {
          provider: new mongoose.Types.ObjectId(providerId),
          members: {
            $all: [
              {
                $elemMatch: {
                  type: 'CourseStudent',
                  id: new mongoose.Types.ObjectId(studentId),
                },
              },
              {
                $elemMatch: {
                  type: 'CourseAdvisor',
                  id: new mongoose.Types.ObjectId(advisorId),
                },
              },
            ],
          },
        }),
  });

  if (fields.includes('messagesCount')) {
    chatAggregation
      .lookup({
        from: 'coursechatmessages',
        let: { chatId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$chat', '$$chatId'] },
              deleted: false,
              enabled: true,
            },
          },
          {
            $project: { _id: 1 },
          },
        ],
        as: 'messagesCount',
      })
      .addFields({
        messagesCount: { $size: '$messagesCount' },
      });
  }

  const chats = await chatAggregation.project(chatProjection);

  return chats[0];
}

export async function createChat({ studentId, advisorId, providerId }) {
  // Check if chat already exists
  const chat = await getChat({
    studentId,
    advisorId,
    providerId,
  });
  if (chat) return null;

  // Create new chat
  await Chat.create({
    type: 'basic',
    provider: providerId,
    members: [
      { type: 'CourseStudent', id: studentId },
      { type: 'CourseAdvisor', id: advisorId },
    ],
  });
}

export async function hardDeleteChat(chatId) {
  await Chat.findByIdAndDelete(chatId);
}

export default {
  getChat,
  createChat,
  hardDeleteChat,
};
