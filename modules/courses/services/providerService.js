import siteServices from '#modules/web/services/siteServices.js';
import { isEmpty } from '#utils/arrays.js';

import Provider from '../models/Provider.js';

export async function getProvider({ providerId, fields = [] }) {
  const defaultFields = ['title', 'language', 'advisor'];
  const providerFields = !isEmpty(fields) ? fields : defaultFields;

  const advisor = await Provider.findById(providerId)
    .select(providerFields)
    .where({ deleted: false, enabled: true });

  return advisor;
}

export async function getSiteLanguageByProviderId(providerId) {
  const provider = await getProvider({
    providerId,
    fields: ['language', 'site'],
  });
  const { site } = await siteServices.getSiteById(provider.site);
  return site.language || provider.langage || 'en';
}

export async function isCorrespondenceCoursesEnabled(providerId) {
  if (!providerId) return false;
  const provider = await getProvider({
    providerId,
    fields: ['correspondenceCoursesEnabled'],
  });
  return !!provider.correspondenceCoursesEnabled;
}

export default {
  getProvider,
  getSiteLanguageByProviderId,
  isCorrespondenceCoursesEnabled,
};
