import factory from '#utils/handlerFactory.js';
import {
  getEntityFilter,
  getIdsFilter,
  getPagination,
  getParam,
  getSearch,
  getStatusFilter,
} from '#utils/request.js';
import { slugify } from '#utils/strings.js';

import Provider from '../models/Provider.js';
import Collection from '../models/Collection.js';
import Course from '../models/Course.js';
import Advisor from '../models/Advisor.js';
import Student from '../models/Student.js';

export const getAllProviders = async (req, res) => {
  // Check permissions
  if (
    !req.user.isAdmin &&
    !req.user.hasPermission({
      module: 'courses-providers',
      permission: 'read',
    })
  ) {
    return res.status(200).json({ count: 0, items: [] });
  }

  const { limit, skip } = getPagination(req);
  const entityFilter = getEntityFilter(req);
  const statusFilter = getStatusFilter(req);
  const { searchFilter } = getSearch(req, ['title']);
  const ignoreProviderIdsFilter = getIdsFilter(req, { ignore: true });
  const restrictedRecords = req.user.getRestrictedRecords({
    module: 'courses-providers',
    permission: 'read',
  });

  const filter = {
    $and: [
      entityFilter,
      statusFilter,
      ignoreProviderIdsFilter,
      restrictedRecords ? { _id: { $in: restrictedRecords } } : {},
      searchFilter,
    ],
  };

  const providers = await Provider.find(filter)
    .sort({ title: 1 })
    .skip(skip)
    .limit(limit)
    .populate({
      path: 'entity',
      select: {
        '_id': 1,
        'name': 1,
        'config.backendDomains': 1,
      },
    })
    .select({
      id: 1,
      title: 1,
      entity: 1,
      enabled: 1,
      deleted: 1,
    });

  const providersCount = await Provider.countDocuments(filter);

  res.status(200).json({
    count: providersCount,
    items: providers,
  });
};

export const getPartnerProviders = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  // Check permissions
  // if (
  //   !req.user.isAdmin &&
  //   !req.user.hasPermission({
  //     module: 'courses-providers',
  //     permission: 'edit',
  //   })
  // ) {
  //   return res.status(200).json({ count: 0, items: [] });
  // }

  const providers = await Provider.find({
    _id: { $ne: providerId },
    deleted: false,
  })
    .sort({ title: 1 })
    .collation({ locale: 'en' })
    .select({
      id: 1,
      title: 1,
      enabled: 1,
    });

  res.status(200).json(providers);
};

export const getProvider = async (req, res) => {
  const data = await factory.getOne(Provider, req, {
    paramId: 'providerId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const createProvider = async (req, res) => {
  // Ensure provider has a valid slug within its siblings (or create one from  its title)
  const slug = await Provider.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    req.entity
  );

  // Create provider
  const provider = await Provider.create({
    ...req.body,
    language: req.body.language ? req.body.language : req.entity.language,
    entity: req.entity._id,
    slug,
  });

  res.status(200).json(provider);
};

export const updateProvider = async (req, res) => {
  const provider = await factory.getOne(Provider, req, {
    paramId: 'providerId',
    filterByEntity: false,
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Provider.getAvailableSlug(
        slugify(req.body.slug),
        provider.entity,
        provider._id
      )
    : provider.slug;

  // Update provider data
  const updatedProvider = await Provider.findByIdAndUpdate(
    provider._id,
    {
      ...req.body,
      slug,
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedProvider);
};

export const deleteProvider = async (req, res) => {
  const data = await factory.deleteOne(Provider, req, {
    paramId: 'providerId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const restoreProvider = async (req, res) => {
  const data = await factory.restoreOne(Provider, req, {
    paramId: 'providerId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const disableProvider = async (req, res) => {
  const data = await factory.disableOne(Provider, req, {
    paramId: 'providerId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const enableProvider = async (req, res) => {
  const data = await factory.enableOne(Provider, req, {
    paramId: 'providerId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const getProviderStats = async (req, res) => {
  const providerId = getParam(req, 'providerId');

  const totalStudents = await Student.countDocuments({
    provider: providerId,
    enabled: true,
  });

  const totalCollections = await Collection.countDocuments({
    provider: providerId,
    enabled: true,
  });

  const courses = await Course.find({
    $or: [
      { provider: providerId },
      { [`providers.${providerId}.sharingEnabled`]: true },
    ],
  });

  const { totalCourses, onlineCourses, correspondenceCourses } = courses.reduce(
    (acc, course) => {
      acc.totalCourses += 1;
      if (course.onlineCourse) acc.onlineCourses += 1;
      if (course.correspondenceCourse) acc.correspondenceCourses += 1;
      return acc;
    },
    { totalCourses: 0, onlineCourses: 0, correspondenceCourses: 0 }
  );

  const totalAdvisors = await Advisor.countDocuments({
    provider: providerId,
    enabled: true,
  });

  res.status(200).json({
    students: {
      total: totalStudents,
      // active: 0,
      // passive: 0,
      // online: 0,
      // paper: 0,
    },
    collections: {
      total: totalCollections,
    },
    courses: {
      total: totalCourses,
      online: onlineCourses,
      correspondence: correspondenceCourses,
      // completedCourses: 0
    },
    advisors: {
      total: totalAdvisors,
    },
  });
};

export default {
  getAllProviders,
  getPartnerProviders,
  getProvider,
  createProvider,
  updateProvider,
  deleteProvider,
  restoreProvider,
  disableProvider,
  enableProvider,
  getProviderStats,
};
