import factory from '#utils/handlerFactory.js';
import Questionnaire from '../models/Questionnaire.js';
import Slide from '../models/Slide.js';

export const getQuestionnaire = async (req, res) => {
  const data = await factory.getOne(Questionnaire, req, {
    paramId: 'questionnaireId',
  });

  res.status(200).json(data);
};

export const updateQuestionnaire = async (req, res) => {
  const questionnaire = await factory.getOne(Questionnaire, req, {
    paramId: 'questionnaireId',
  });

  // TODO: check if questions changed significantly (e.g. number of questions changed)
  // --> create new questionnaire
  // --> archive old questionnaire
  // --> link new questionnaire to slide

  // Update questionnaire's data
  const updatedQuestionnaire = await Questionnaire.findByIdAndUpdate(
    questionnaire._id,
    {
      ...req.body,
      slide: questionnaire.slide, // prevents moving a questionnaire to different slide
      provider: questionnaire.provider, // prevents moving a questionnaire to different provider
    },
    {
      new: true,
      runValidators: true,
    }
  );

  const slide = await Slide.findById(questionnaire.slide);
  if (slide.requiresReview !== updatedQuestionnaire.requiresReview) {
    slide.requiresReview = updatedQuestionnaire.requiresReview;
    await slide.save();
  }

  res.status(200).json(updatedQuestionnaire);
};

export default {
  getQuestionnaire,
  updateQuestionnaire,
};
