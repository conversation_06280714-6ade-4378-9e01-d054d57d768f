import { areEqualIDs } from '#utils/helpers.js';
import factory from '#utils/handlerFactory.js';
import { isEmpty } from '#utils/arrays.js';
import { slugify } from '#utils/strings.js';

import Course from '../models/Course.js';
import Lesson from '../models/Lesson.js';

export const getAllLessons = async (req, res) => {
  const course = await factory.getOne(Course, req, { paramId: 'courseId' });

  const data = await factory.getAll(Lesson, req, {
    filter: {
      course: course._id,
      deleted: false,
      _id: { $in: course.lessons },
    },
    fields: ['title', 'slides', 'enabled'],
  });

  const sortedLessons = course.lessons.map((lessonId) =>
    data.items.find((lesson) => areEqualIDs(lesson._id, lessonId))
  );

  res.status(200).json({
    count: data.count,
    items: sortedLessons,
  });
};

export const getLesson = async (req, res) => {
  const data = await factory.getOne(Lesson, req, {
    paramId: 'lessonId',
  });

  res.status(200).json(data);
};

export const createLesson = async (req, res) => {
  const course = await factory.getOne(Course, req, { paramId: 'courseId' });

  // Ensure lesson has a valid slug within its siblings (or create one from its name)
  const slug = await Lesson.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    course
  );

  // Create the lesson
  const lesson = await Lesson.create({
    ...req.body,
    slug,
    course: course._id,
    provider: course.provider,
  });

  // Add lesson to course
  course.lessons = [...course.lessons, lesson.id];
  await course.save();

  res.status(200).json(lesson);
};

export const updateLesson = async (req, res) => {
  const lesson = await factory.getOne(Lesson, req, {
    paramId: 'lessonId',
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Lesson.getAvailableSlug(
        slugify(req.body.slug),
        lesson.course,
        lesson._id
      )
    : lesson.slug;

  // Update lesson's data
  const updatedLesson = await Lesson.findByIdAndUpdate(
    lesson._id,
    {
      ...req.body,
      slug,
      course: lesson.course, // prevents moving a lesson to different course
      provider: lesson.provider, // prevents moving a lesson to different provider
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedLesson);
};

export const deleteLesson = async (req, res) => {
  const deletedLesson = await factory.deleteOne(Lesson, req, {
    paramId: 'lessonId',
  });

  // Remove lesson from course
  const course = await Course.findById(deletedLesson.course, 'lessons');
  if (course && !isEmpty(course.lessons)) {
    course.lessons = course.lessons.filter(
      (lesson) => !areEqualIDs(lesson._id, deletedLesson._id)
    );
    await course.save();
  }

  res.status(200).json(deletedLesson);
};

export const restoreLesson = async (req, res) => {
  const data = await factory.restoreOne(Lesson, req, {
    paramId: 'lessonId',
  });

  // TODO: add lesson to course again

  res.status(200).json(data);
};

export const disableLesson = async (req, res) => {
  const data = await factory.disableOne(Lesson, req, {
    paramId: 'lessonId',
  });

  res.status(200).json(data);
};

export const enableLesson = async (req, res) => {
  const data = await factory.enableOne(Lesson, req, {
    paramId: 'lessonId',
  });

  res.status(200).json(data);
};

export default {
  getAllLessons,
  getLesson,
  createLesson,
  updateLesson,
  deleteLesson,
  restoreLesson,
  disableLesson,
  enableLesson,
};
