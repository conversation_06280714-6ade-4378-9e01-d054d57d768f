import { getBoolean } from '#utils/request.js';

import filemakerImport from '../scripts/filemaker-import/filemaker-import.js';

export const importData = async (req, res) => {
  // Get query params
  const entityId = req.query.entity || req.entity._id;
  const providerId = req.query.provider;
  const advisorId = req.query.advisor;
  const fileName = req.query.file;
  const analyzeFields = req.query.analyze;
  const dataFilter = req.query.filter;
  const dryMode = getBoolean(req, 'dry');
  const onlineStudents = getBoolean(req, 'onlineStudents');

  // Validate query params
  if (!entityId) {
    res.status(404).json({ error: 'Entity missing' });
    return;
  }
  if (!providerId) {
    res.status(404).json({ error: 'Provider missing' });
    return;
  }
  if (!advisorId) {
    res.status(404).json({ error: 'Advisor missing' });
    return;
  }

  // Import data
  const results = await filemakerImport.importData({
    entityId,
    providerId,
    advisorId,
    fileName,
    filter: dataFilter,
    analyzeFields,
    dryMode,
    onlineStudents,
  });

  if (results.error) {
    res.status(results.status || 500).json({ error: results.error });
    return;
  }

  res.status(200).json(results);
};

export default {
  importData,
};
