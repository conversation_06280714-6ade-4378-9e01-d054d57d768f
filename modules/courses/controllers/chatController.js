import mongoose from 'mongoose';

import { areEqualIDs } from '#utils/helpers.js';
import factory from '#utils/handlerFactory.js';
import { sendEmail, sendPushNotification } from '#utils/notifier.js';
import getFullName from '#utils/getFullName.js';
import { t } from '#utils/translator.js';
import { getPagination, getParam, getSearch } from '#utils/request.js';
import { getPageUrlById } from '#modules/web/services/pageServices.js';
import siteServices from '#modules/web/services/siteServices.js';

import Chat from '../models/Chat.js';
import ChatMessage from '../models/ChatMessage.js';
import Student from '../models/Student.js';
import getAdvisorIds from '../helpers/getAdvisorIds.js';
import { getStudent } from '../services/studentService.js';
import { getAdvisor } from '../services/advisorService.js';
import providerService, { getProvider } from '../services/providerService.js';
import chatService from '../services/chatService.js';

export const getAllChats = async (req, res) => {
  const providerId = getParam(req, 'providerId');

  let data = { count: 0, items: [] };

  const canReadMessages = req.user.hasPermission({
    module: 'courses-messages',
    permission: 'read',
    recordModule: 'courses-providers',
    recordId: providerId,
  });
  const canReadAllMessages = req.user.hasPermission({
    module: 'courses-messages',
    permission: 'readAll',
    recordModule: 'courses-providers',
    recordId: providerId,
  });

  if (!req.user.isAdmin && !canReadAllMessages && !canReadMessages) {
    res.status(200).json(data);
    return;
  }

  const { limit, skip } = getPagination(req, 5000);

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  // Get advisors filter
  const advisorIds = canReadAllMessages
    ? getAdvisorIds(req)
    : req.advisor
      ? [req.advisor._id]
      : [];

  // Get students matching the search query
  let studentIds = [];
  if (req.query.search) {
    const { searchField, searchFilter } = getSearch(req, [
      'profile.firstName',
      'profile.lastName',
      'profile.username',
      ...(isCorrespondenceCoursesEnabled ? ['firstName', 'lastName'] : []),
    ]);
    const students = await Student.aggregate()
      .addFields(searchField)
      .match({
        provider: new mongoose.Types.ObjectId(providerId),
        deleted: false,
        enabled: true,
        ...searchFilter,
      })
      .project({ _id: 1 });
    studentIds = students.map((student) => student._id);
  }

  const memberFilter = [];
  if (advisorIds?.length > 0) {
    memberFilter.push({
      $elemMatch: { type: 'CourseAdvisor', id: { $in: advisorIds } },
    });
  }
  if (req.query.search) {
    memberFilter.push({
      $elemMatch: { type: 'CourseStudent', id: { $in: studentIds } },
    });
  }

  const filter = {
    provider: new mongoose.Types.ObjectId(providerId),
    deleted: false,
    archived: false,
    ...(memberFilter.length > 0 && { members: { $all: memberFilter } }),
  };

  // Get total items
  const totalChats = await Chat.countDocuments(filter);

  // Get chats
  const chats = await Chat.find(filter)
    .sort({ lastMessageDate: -1 })
    .skip(skip)
    .limit(limit)
    .populate({
      path: 'lastMessage',
      select: {
        _id: 1,
        sender: 1,
        subject: 1,
        text: 1,
        type: 1,
        readAt: 1,
        createdAt: 1,
      },
      populate: {
        path: 'sender.id',
        select: {
          _id: 1,
          username: 1,
          webUser: 1,
          profile: 1,
          ...(isCorrespondenceCoursesEnabled && {
            firstName: 1,
            lastName: 1,
          }),
        },
      },
    })
    .populate({
      path: 'members.id',
      match: { deleted: false },
      select: {
        _id: 1,
        username: 1,
        webUser: 1,
        profile: 1,
        ...(isCorrespondenceCoursesEnabled && {
          firstName: 1,
          lastName: 1,
        }),
      },
    })
    .select({
      _id: 1,
      type: 1,
      title: 1,
      members: 1,
      lastMessage: 1,
    });

  // Get unread messages count
  const unreadChatMessagesAggregation = Chat.aggregate()
    .match({ _id: { $in: chats.map((chat) => chat._id) } })
    // TODO: Doesn't work on Scalingo!!!
    .addFields({
      advisor: {
        $filter: {
          input: '$members',
          cond: { $eq: ['$$this.type', 'CourseAdvisor'] },
        },
      },
    })
    .unwind('$advisor')
    .lookup({
      from: 'coursechatmessages',
      let: {
        chatId: '$_id',
        advisorId: { $toString: '$advisor.id' },
      },
      pipeline: [
        {
          $match: {
            '$expr': { $eq: ['$chat', '$$chatId'] },
            'sender.type': 'CourseStudent',
          },
        },
        {
          $addFields: {
            // Get key-value-pair of readAt field for chat advisor.
            // Key (k): advisorId, value (v): readAt (Date string or null)
            advisorReadAt: {
              $arrayElemAt: [
                {
                  $filter: {
                    input: { $objectToArray: '$readAt' },
                    as: 'item',
                    cond: { $eq: ['$$item.k', '$$advisorId'] },
                  },
                },
                0,
              ],
            },
          },
        },
        { $addFields: { advisorReadAt: '$advisorReadAt.v' } },
        { $match: { advisorReadAt: { $eq: null } } },
        { $project: { _id: 1, readAt: 1 } },
      ],
      as: 'unreadMessages',
    })
    .project({
      chatId: { $toString: '$_id' },
      unreadMessages: { $size: '$unreadMessages' },
    });

  const unreadChatMessages = await unreadChatMessagesAggregation;

  // DEBUG
  // const test = await unreadChatMessagesAggregation.explain('executionStats');
  // console.log(test.stages);

  data = {
    count: totalChats,
    items: chats
      .filter((chat) => chat.members.every((member) => member.id !== null))
      .map((chat) => {
        chat = chat.toObject();

        // Add student and advisor
        if (chat.type === 'basic') {
          for (const member of chat.members) {
            if (member.type === 'CourseStudent') chat.student = member.id;
            if (member.type === 'CourseAdvisor') chat.advisor = member.id;
          }
          delete chat.members;
        }

        // Add unread messages count
        chat.unreadMessages =
          unreadChatMessages.find((unreadChat) =>
            areEqualIDs(unreadChat._id, chat._id)
          )?.unreadMessages || 0;

        return chat;
      }),
  };

  res.status(200).json(data);
};

export const getStudentMessages = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  // Get advisors
  const advisorIds = req.query.advisors
    ? req.query.advisors
        .split(',')
        .filter((advisorId) => mongoose.isValidObjectId(advisorId))
        .map((advisorId) => new mongoose.Types.ObjectId(advisorId))
    : [];

  // Gets all students matching the search query
  let studentIds;
  if (req.query.search) {
    const { searchField, searchFilter } = getSearch(req, [
      'firstName',
      'lastName',
      'username',
    ]);
    const studentsAggregation = Student.aggregate();
    if (searchField) studentsAggregation.addFields(searchField);
    const students = await studentsAggregation
      .match({
        deleted: false,
        enabled: true,
        ...searchFilter,
      })
      .project({ _id: 1 });
    studentIds = students.map(
      (student) => new mongoose.Types.ObjectId(student._id)
    );
  }

  const filter = {
    provider: new mongoose.Types.ObjectId(providerId),
    deleted: false,
    archived: false,
    ...(studentIds?.length > 0 && { student: { $in: studentIds } }),
    ...(advisorIds?.length > 0 && {
      members: {
        $elemMatch: {
          type: 'CourseAdvisor',
          id: { $in: advisorIds },
        },
      },
    }),
  };

  // Get chats
  const chats = await Chat.find(filter)
    .sort({ lastMessageDate: -1 })
    .skip(skip)
    .limit(limit)
    .populate({
      path: 'lastMessage',
      select: {
        _id: 1,
        sender: 1,
        subject: 1,
        text: 1,
        type: 1,
        readAt: 1,
        createdAt: 1,
      },
      populate: {
        path: 'sender.id',
        select: {
          _id: 1,
          username: 1,
          webUser: 1,
          profile: 1,
          ...(isCorrespondenceCoursesEnabled && {
            firstName: 1,
            lastName: 1,
          }),
        },
      },
    })
    .populate({
      path: 'members.id',
      match: { deleted: false },
      select: {
        _id: 1,
        username: 1,
        webUser: 1,
        profile: 1,
        ...(isCorrespondenceCoursesEnabled && {
          firstName: 1,
          lastName: 1,
        }),
      },
    })
    .select({
      _id: 1,
      type: 1,
      title: 1,
      members: 1,
      lastMessage: 1,
    });

  // Get total items
  const totalChats = await Chat.find(filter).countDocuments();

  res.status(200).json({
    count: totalChats,
    items: chats,
  });
};

export const getChat = async (req, res) => {
  const _chat = await chatService.getChat({
    chatId: getParam(req, 'chatId'),
    fields: ['provider'],
  });
  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(_chat?.provider);

  const data = await factory.getOne(Chat, req, {
    paramId: 'chatId',
    populate: [
      {
        path: 'members.id',
        match: { deleted: false },
        select: {
          _id: 1,
          username: 1,
          webUser: 1,
          profile: 1,
          ...(isCorrespondenceCoursesEnabled && {
            firstName: 1,
            lastName: 1,
          }),
        },
      },
    ],
  });

  const chat = data.toObject();

  // Add student and advisor
  if (chat.type === 'basic') {
    for (const member of chat.members) {
      if (member.type === 'CourseStudent') chat.student = member.id;
      if (member.type === 'CourseAdvisor') chat.advisor = member.id;
    }
  }

  res.status(200).json(chat);
};

export const createChat = async (req, res) => {
  const providerId = getParam(req, 'providerId');

  // Create the chat
  const chat = await Chat.create({
    ...req.body,
    provider: providerId,
  });

  res.status(200).json(chat);
};

export const pinChat = async (req, res) => {
  const chat = await factory.getOne(Chat, req, {
    paramId: 'chatId',
  });

  // Mark chat as pinned
  const updatedChat = await Chat.findByIdAndUpdate(
    chat._id,
    { pinned: true },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedChat);
};

export const unpinChat = async (req, res) => {
  const chat = await factory.getOne(Chat, req, {
    paramId: 'chatId',
  });

  // Unpin chat
  const updatedChat = await Chat.findByIdAndUpdate(
    chat._id,
    { pinned: false },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedChat);
};

export const archiveChat = async (req, res) => {
  const chat = await factory.getOne(Chat, req, {
    paramId: 'chatId',
  });

  // Archive chat
  const updatedChat = await Chat.findByIdAndUpdate(
    chat._id,
    { archived: true },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedChat);
};

export const updateChat = async (req, res) => {
  const chat = await factory.getOne(Chat, req, { paramId: 'chatId' });

  // Update chat's data
  const updatedChat = await Chat.findByIdAndUpdate(
    chat._id,
    {
      ...req.body,
      entity: chat.entity, // prevents moving a chat to different entity
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedChat);
};

export const deleteChat = async (req, res) => {
  const data = await factory.deleteOne(Chat, req, {
    paramId: 'chatId',
  });

  res.status(200).json(data);
};

export const getAllChatMessages = async (req, res) => {
  const chat = await factory.getOne(Chat, req, { paramId: 'chatId' });

  const { limit, skip } = getPagination(req);

  const filter = {
    chat: chat._id,
    deleted: false,
  };

  // Get total items
  const totalMessages = await ChatMessage.countDocuments(filter);

  // Get messages
  const messages = await ChatMessage.find(filter)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate([
      {
        path: 'sender.id',
        select: '_id username firstName lastName webUser profile',
      },
      {
        path: 'citedMessage',
        select: '_id subject text',
      },
    ])
    .select({
      sender: 1,
      subject: 1,
      text: 1,
      citedMessage: 1,
      createdAt: 1,
      readAt: 1,
    })
    .sort('-createdAt');

  res.status(200).json({
    count: totalMessages,
    items: messages,
  });
};

export const createMessage = async (req, res) => {
  const chat = await factory.getOne(Chat, req, {
    paramId: 'chatId',
  });

  const { entity } = req;
  const advisorId = req.advisor?.id;

  // Create chat message
  const chatMessage = await ChatMessage.create({
    ...req.body,
    chat: chat._id,
    type: 'text',
    sender: {
      type: 'CourseAdvisor',
      id: advisorId,
    },
    provider: chat.provider,
  });

  // Update chat's last message
  chat.lastMessage = chatMessage._id;
  chat.lastMessageDate = chatMessage.createdAt;
  await chat.save();

  // Send email and push notifications
  await sendNewMessageNotifications({ chat, chatMessage, entity });

  res.status(200).json(chatMessage);
};

async function sendNewMessageNotifications({ chat, chatMessage, entity }) {
  const advisorId = chat.members.find((m) => m.type === 'CourseAdvisor')?.id;
  const studentId = chat.members.find((m) => m.type === 'CourseStudent')?.id;

  // Get student
  const student = await getStudent({
    studentId,
    fields: [
      'email',
      'firstName',
      'lastName',
      'username',
      'preferences',
      'webUser',
    ],
  });

  // Check if student has notifications enabled
  const notifications = student?.preferences?.notifications?.newMessage; // Contains permissions for email and push

  if (!notifications?.email && !notifications?.push) return;

  // Get student name
  const { username, email: studentEmail, webUser } = student;

  const studentName = getFullName(student) || username;

  // Get advisor
  const advisor = await getAdvisor({
    advisorId,
    fields: ['firstName', 'lastName', 'email'],
  });
  if (!advisor?.email) return;
  const advisorName = getFullName(advisor);

  // Get provider
  const provider = await getProvider({
    providerId: chat.provider,
    fields: ['language', 'entity', 'site', 'pages'],
  });

  // Get site (Email)
  const { site } = await siteServices.getSiteById(provider.site);
  const baseUrl = await siteServices.getBaseUrl({ site });

  // Set language
  const language = site.language || provider.langage || 'en';

  // Build login page URL
  let loginPageUrl = await getPageUrlById(site.auth?.pages?.loginPage);
  loginPageUrl = `${baseUrl}${loginPageUrl}`;

  // Get chat URL
  const messagesPath = await getPageUrlById(provider.pages?.messagesPage);
  const chatUrl = `${loginPageUrl}?callbackUrl=${baseUrl}${messagesPath}?chat=${chat._id}`;

  // Don't show message, student should read it in the platform
  const showMessage = false;

  // Send email
  if (notifications?.email) {
    sendEmail({
      language,
      entity,
      site,
      to: studentEmail,
      subject: t(language, 'newMessageSubject', {
        advisor: advisorName,
      }),
      templateName: 'courses/newMessage',
      templateValues: {
        // Intro
        title: t(language, 'newMessageTitle', { student: studentName }),
        subtitle: t(language, 'newMessageText', { advisor: advisorName }),
        // Message
        ...(showMessage && {
          showMessage: true,
          messageSubject: chatMessage.subject,
          messageText: (chatMessage.text || '').split('\n'),
        }),
        // Call to action
        linkURL: chatUrl,
        linkText: t(language, 'newMessageLinkText'),
        linkNotWorking: t(language, 'linkNotWorking'),
      },
    });
  }

  const { site: pushSite } = await siteServices.getSiteById(
    provider.push?.site
  );
  const pushBaseUrl = await siteServices.getBaseUrl({ site: pushSite });
  const pushMessagesPath = await getPageUrlById(
    provider.push?.pages?.messagesPage
  );
  const pushChatUrl = `${pushBaseUrl}${pushMessagesPath}`; // TODO: Find a way to include query parameters

  // Send push notification
  if (notifications?.push) {
    await sendPushNotification({
      site,
      to: [webUser], // Array of webUser ids
      headings: {
        en: t(language, 'newMessageSubject', { advisor: advisorName }),
      },
      contents: { en: chatMessage.subject || chatMessage.text }, // Format should be { en: 'All languages message' } unless more than one language is supported
      targetUrl: pushChatUrl,
    });
  }
}

export const readMessage = async (req, res) => {
  const chatMessage = await factory.getOne(ChatMessage, req, {
    paramId: 'messageId',
  });

  const advisorId = req.advisor?.id;

  // Update readAt field
  chatMessage.readAt = {
    ...(chatMessage.readAt || {}),
    [advisorId]: new Date(),
  };

  // Save changes
  await chatMessage.save();

  res.status(200).json(chatMessage);
};

export const unreadMessage = async (req, res) => {
  const chatMessage = await factory.getOne(ChatMessage, req, {
    paramId: 'messageId',
  });

  const advisorId = req.advisor?.id;

  // Update readAt field
  chatMessage.readAt = {
    ...(chatMessage.readAt || {}),
    [advisorId]: null,
  };

  // Save changes
  await chatMessage.save();

  res.status(200).json(chatMessage);
};

export default {
  getAllChats, // chats (GET)
  createChat, // chats (POST)
  getChat, // chat/:chatId
  pinChat, // __messages/:messageId/pin
  archiveChat, // __messages/:messageId/archive
  deleteChat, // chats/:chatId (DELETE)
  updateChat, // chats/:chatId (PATCH)

  getAllChatMessages, // chats/:chatId/messages
  createMessage, // chats/:chatId/messages (POST)
  readMessage, // messages/:messageId/read
  unreadMessage, // messages/:messageId/unread
};
