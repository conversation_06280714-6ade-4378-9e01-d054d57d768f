import factory from '#utils/handlerFactory.js';

import courseStatusService from '../services/courseStatusService.js';
import LessonStatus from '../models/LessonStatus.js';
import courseService from '../services/courseService.js';

export const createLessonStatus = async (req, res) => {
  const { courseStatusId } = req.params;
  const advisorId = req.advisor?.id;

  const courseStatus = await courseStatusService.getCourseStatus({
    courseStatusId,
    fields: ['course', 'mode', 'provider', 'startedLessons', 'student'],
  });

  // Get request data
  const { lessonId, submittedAt } = req.body;

  // Validate request data
  // -------------------------------------------------------
  if (!submittedAt) {
    return res.status(400).json({
      error: 'Submitted date is required',
    });
  }

  if (!courseStatus) {
    return res.status(404).json({
      error: 'Course status not found',
    });
  }

  if (courseStatus.mode !== 'correspondence') {
    return res.status(400).json({
      error: 'Course status mode must be correspondence',
    });
  }

  const startedLessons = courseStatus?.startedLessons || [];
  const startedLessonIds = startedLessons.map((l) => l.toString());
  if (startedLessonIds.includes(lessonId)) {
    return res.status(400).json({
      error: 'Lesson already started',
    });
  }

  const existingLessonStatus = await LessonStatus.findOne({
    courseStatus: courseStatus._id,
    lesson: lessonId,
  });

  if (existingLessonStatus) {
    return res.status(400).json({
      error: 'Lesson status already exists for this lesson',
    });
  }
  // -------------------------------------------------------

  // Create lesson status
  const lessonStatus = await LessonStatus.create({
    courseStatus: courseStatus._id,
    lesson: lessonId,
    student: courseStatus.student,
    course: courseStatus.course,
    provider: courseStatus.provider,
    submittedAt,
    advisor: advisorId,
  });

  // Update course status
  await courseStatusService.updateCourseStatus(courseStatus._id, {
    startedLessons: [...(courseStatus.startedLessons || []), lessonId],
    lastActivity: new Date(),
  });

  // TODO: Update StudentActivity
  // date, provider, student, course, type: 'questionnaireSubmitted', value

  res.status(200).json(lessonStatus);
};

export const updateLessonStatus = async (req, res) => {
  const lessonStatus = await factory.getOne(LessonStatus, req, {
    paramId: 'lessonStatusId',
  });
  const advisorId = req.advisor?.id;

  const lessonId = lessonStatus.lesson.toString();

  const courseStatus = await courseStatusService.getCourseStatus({
    courseStatusId: lessonStatus.courseStatus,
    fields: [
      'course',
      'mode',
      'provider',
      'startedLessons',
      'completedLessons',
      'student',
    ],
  });

  // Get request data
  const { submittedAt, correctedAt, advisorNotes } = req.body;

  // Validate request data
  // -------------------------------------------------------
  if (!submittedAt && !correctedAt) {
    return res.status(400).json({
      error: 'Submitted date or corrected date is required',
    });
  }
  // -------------------------------------------------------

  // Update lesson status
  const updatedLessonStatus = await LessonStatus.findByIdAndUpdate(
    lessonStatus._id,
    submittedAt
      ? {
          submittedAt,
        }
      : {
          correctedAt,
          advisor: advisorId,
          advisorNotes,
        },
    { new: true, runValidators: true }
  );

  const currentDate = new Date();

  const courseStatusData = {
    lastActivity: currentDate,
  };

  if (correctedAt) {
    // Update completed lessons
    const completedLessons = courseStatus?.completedLessons || [];
    if (!completedLessons.includes(lessonId)) {
      completedLessons.push(lessonId);
    }
    courseStatusData.completedLessons = completedLessons;

    // Get course content
    const course = await courseService.getCourseContent(courseStatus.course, {
      lessonsFilter: {
        deleted: false,
        enabled: true,
      },
    });

    // Check if course is finished
    const courseFinished = course.lessons.every((lesson) =>
      completedLessons.includes(lesson._id.toString())
    );
    if (courseFinished) {
      courseStatusData.finishedAt = currentDate;
    }
  }

  // Update course status
  await courseStatusService.updateCourseStatus(
    courseStatus._id,
    courseStatusData
  );

  res.status(200).json(updatedLessonStatus);
};

export default {
  createLessonStatus,
  updateLessonStatus,
};
