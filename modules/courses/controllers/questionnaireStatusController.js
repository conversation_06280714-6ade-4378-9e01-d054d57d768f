import mongoose from 'mongoose';

import { getPagination, getParam, getStatusFilter } from '#utils/request.js';
import factory from '#utils/handlerFactory.js';
import { areEqualIDs } from '#utils/helpers.js';

import QuestionnaireStatus from '../models/QuestionnaireStatus.js';
import { updateCourseStatusOnCorrectQuestionnaire } from '../services/courseStatusService.js';
import { getAllQuestionnaireStatuses } from '../services/questionnaireStatusService.js';
import getAdvisorIds from '../helpers/getAdvisorIds.js';
import getStudentIds from '../helpers/getStudentIds.js';
import getCourseIds from '../helpers/getCourseIds.js';
import getQuestionnairePermissions from '../helpers/getQuestionnairePermissions.js';
import { getStudent } from '../services/studentService.js';
import providerService from '../services/providerService.js';

export const getOpenQuestionnaireStatuses = async (req, res) => {
  const providerId = getParam(req, 'providerId');

  const {
    canReadAllQuestionnaires,
    canUpdateQuestionnaires,
    canUpdateAllQuestionnaires,
    canDeleteAllQuestionnaires,
    canDeleteQuestionnaires,
  } = getQuestionnairePermissions(providerId, req);

  const { limit, skip } = getPagination(req);

  // Create status filter
  const statusFilter = getStatusFilter(req);

  // Get questionnaires of these advisors
  const advisorIds =
    canReadAllQuestionnaires || req.user.isAdmin
      ? getAdvisorIds(req)
      : req.advisor
        ? [req.advisor._id]
        : [];

  // Get courses
  const courseIds = getCourseIds(req);

  // Gets all students matching the search query
  const studentIds = await getStudentIds(req);

  // Get open questionnaires
  const questionnaireStatuses = await getAllQuestionnaireStatuses({
    providerId,
    advisorIds,
    courseIds,
    studentIds,
    statusFilter,
    status: 'open',
    limit,
    skip,
  });

  res.status(200).json({
    count: questionnaireStatuses.count,
    items: questionnaireStatuses.items.map((questionnaireStatus) => ({
      ...questionnaireStatus,
      editable:
        canUpdateAllQuestionnaires ||
        (canUpdateQuestionnaires &&
          areEqualIDs(
            req.advisor?._id,
            questionnaireStatus.student?.advisor?._id
          )),
      deletable:
        canDeleteAllQuestionnaires ||
        (canDeleteQuestionnaires &&
          areEqualIDs(
            req.advisor?._id,
            questionnaireStatus.student?.advisor?._id
          )),
    })),
  });
};

export const getArchivedQuestionnaireStatuses = async (req, res) => {
  const providerId = getParam(req, 'providerId');

  const { canReadAllQuestionnaires } = getQuestionnairePermissions(
    providerId,
    req
  );

  const { limit, skip } = getPagination(req);

  // Create status filter
  const statusFilter = getStatusFilter(req);

  // Get advisors
  const advisorIds =
    canReadAllQuestionnaires || req.user.isAdmin
      ? getAdvisorIds(req)
      : req.advisor
        ? [req.advisor._id]
        : [];

  // Get courses
  const courseIds = getCourseIds(req);

  // Gets all students matching the search query
  const studentIds = await getStudentIds(req);

  // Get completed questionnaires
  const questionnaireStatuses = await getAllQuestionnaireStatuses({
    providerId,
    advisorIds,
    courseIds,
    studentIds,
    statusFilter,
    status: 'completed',
    limit,
    skip,
  });

  res.status(200).json(questionnaireStatuses);
};

export const getQuestionnaireStatus = async (req, res) => {
  const data = await factory.getOne(QuestionnaireStatus, req, {
    paramId: 'questionnaireStatusId',
  });

  const providerId = data.provider;
  const {
    canUpdateAllQuestionnaires,
    canUpdateQuestionnaires,
    canDeleteAllQuestionnaires,
    canDeleteQuestionnaires,
  } = getQuestionnairePermissions(providerId, req);

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  const student = await getStudent({
    studentId: data.student,
    fields: ['advisor'],
  });
  const studentAdvisorId = student.advisor;

  const questionnaireStatus = await QuestionnaireStatus.aggregate()
    .match({
      _id: new mongoose.Types.ObjectId(data._id),
      deleted: false,
    })
    .lookup({
      from: 'courses',
      let: { courseId: '$course' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$courseId'] },
            deleted: false,
          },
        },
        { $project: { _id: 1, title: 1, enabled: 1, deleted: 1 } },
      ],
      as: 'course',
    })
    .lookup({
      from: 'coursestudents',
      let: { studentId: '$student' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$studentId'] },
            deleted: false,
          },
        },
        {
          $project: {
            _id: 1,
            username: 1,
            enabled: 1,
            deleted: 1,
            webUser: 1,
            profile: 1,
            ...(isCorrespondenceCoursesEnabled && {
              firstName: 1,
              lastName: 1,
            }),
          },
        },
      ],
      as: 'student',
    })
    .lookup({
      from: 'courseadvisors',
      let: { advisorId: '$advisor' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$advisorId'] },
            deleted: false,
          },
        },
        {
          $project: {
            _id: 1,
            firstName: 1,
            lastName: 1,
            enabled: 1,
            deleted: 1,
          },
        },
      ],
      as: 'advisor',
    })
    .lookup({
      from: 'coursequestionnaires',
      let: { questionnaireId: '$questionnaire' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$questionnaireId'] },
            deleted: false,
            enabled: true,
          },
        },
        { $project: { _id: 1, questionNumbering: 1, questions: 1, slide: 1 } },
      ],
      as: 'questionnaire',
    })
    .addFields({
      editable:
        canUpdateAllQuestionnaires ||
        (canUpdateQuestionnaires &&
          areEqualIDs(req.advisor?._id, studentAdvisorId)),
      deletable:
        canDeleteAllQuestionnaires ||
        (canDeleteQuestionnaires &&
          areEqualIDs(req.advisor?._id, studentAdvisorId)),
    })
    .addFields({
      course: { $arrayElemAt: ['$course', 0] },
      student: { $arrayElemAt: ['$student', 0] },
      advisor: { $arrayElemAt: ['$advisor', 0] },
      questionnaire: { $arrayElemAt: ['$questionnaire', 0] },
    })
    .lookup({
      from: 'courseslides',
      let: { slideId: '$questionnaire.slide' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$slideId'] },
            deleted: false,
          },
        },
        { $project: { _id: 1, title: 1 } },
      ],
      as: 'slide',
    })
    .unwind('slide');

  res.status(200).json(questionnaireStatus[0]);
};

export const saveAdvisorNotes = async (req, res) => {
  let questionnaireStatus = await factory.getOne(QuestionnaireStatus, req, {
    paramId: 'questionnaireStatusId',
  });
  const advisorId = req.advisor?.id;

  questionnaireStatus = await QuestionnaireStatus.findByIdAndUpdate(
    questionnaireStatus._id,
    {
      advisor: advisorId,
      advisorNotes: req.body,
      advisorNotesSavedAt: new Date(),
    },
    { new: true, runValidators: true }
  );

  res.status(200).json(questionnaireStatus);
};

export const submitAdvisorNotes = async (req, res) => {
  const questionnaireStatus = await factory.getOne(QuestionnaireStatus, req, {
    paramId: 'questionnaireStatusId',
  });
  const advisorId = req.advisor?.id;

  // Save corrections (advisor notes)
  const updatedQuestionnaireStatus =
    await QuestionnaireStatus.findByIdAndUpdate(
      questionnaireStatus._id,
      {
        advisor: advisorId,
        advisorNotes: req.body,
        advisorNotesRead: false,
        correctedAt: new Date(),
      },
      { new: true, runValidators: true }
    );

  // Update course status (slide, lesson, course completed, reached lesson, ...)
  await updateCourseStatusOnCorrectQuestionnaire({
    courseStatusId: updatedQuestionnaireStatus.courseStatus,
    questionnaireStatusId: updatedQuestionnaireStatus._id,
  });

  res.status(200).json(updatedQuestionnaireStatus);
};

export default {
  getOpenQuestionnaireStatuses,
  getArchivedQuestionnaireStatuses,
  getQuestionnaireStatus,
  saveAdvisorNotes,
  submitAdvisorNotes,
};
