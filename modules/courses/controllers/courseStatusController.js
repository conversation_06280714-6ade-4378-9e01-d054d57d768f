import mongoose from 'mongoose';

import { getParam, getPagination } from '#utils/request.js';
import factory from '#utils/handlerFactory.js';
import { areEqualIDs } from '#utils/helpers.js';
import { getCount } from '#utils/aggregations.js';

import Course from '../models/Course.js';
import LessonStatus from '../models/LessonStatus.js';
import Student from '../models/Student.js';
import CourseStatus from '../models/CourseStatus.js';
import getAdvisorIds from '../helpers/getAdvisorIds.js';
import getCourseIds from '../helpers/getCourseIds.js';
import getStudentIds from '../helpers/getStudentIds.js';
import QuestionnaireStatus from '../models/QuestionnaireStatus.js';
import studentService, { getStudent } from '../services/studentService.js';
import { getAdvisor } from '../services/advisorService.js';
import getQuestionnairePermissions from '../helpers/getQuestionnairePermissions.js';
import courseStatusService from '../services/courseStatusService.js';
import providerService from '../services/providerService.js';

export const getAllCourseStatuses = async (req, res) => {
  let data = { count: 0, items: [] };

  const providerId = getParam(req, 'providerId');
  const {
    canReadQuestionnaires,
    canReadAllQuestionnaires,
    canUpdateQuestionnaires,
    canUpdateAllQuestionnaires,
    canDeleteQuestionnaires,
    canDeleteAllQuestionnaires,
  } = getQuestionnairePermissions(providerId, req);

  if (!canReadAllQuestionnaires && !canReadQuestionnaires) {
    res.status(200).json(data);
    return;
  }

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  const { limit, skip } = getPagination(req);

  // Gets all students matching the search query
  const studentIds = await getStudentIds(req);

  // Get courses
  const courseIds = getCourseIds(req);

  // Get course statuses of these advisors
  const advisorIds = canReadAllQuestionnaires
    ? getAdvisorIds(req)
    : req.advisor
      ? [req.advisor._id]
      : [];

  // Get course statuses that have questionnaires that are not corrected yet
  const courseStatusIds = await QuestionnaireStatus.distinct('courseStatus', {
    provider: new mongoose.Types.ObjectId(providerId),
    submittedAt: { $ne: null },
    correctedAt: { $eq: null },
    reviewRequested: true,
    deleted: false,
    enabled: true,
    ...(courseIds.length > 0 && { course: { $in: courseIds } }),
    ...(studentIds.length > 0 && { student: { $in: studentIds } }),
  });

  // Build aggregation
  const courseStatusesAggregation = CourseStatus.aggregate().match({
    provider: new mongoose.Types.ObjectId(providerId),
    mode: 'supervised',
    finishedAt: { $eq: null },
    deleted: false,
    ...(courseIds.length > 0 && { course: { $in: courseIds } }),
    ...(studentIds.length > 0 && { student: { $in: studentIds } }),
    ...(courseStatusIds.length > 0 && { _id: { $in: courseStatusIds } }),
  });

  // Filter by advisors
  if (advisorIds.length > 0) {
    courseStatusesAggregation
      .lookup({
        from: 'coursestudents',
        let: { studentId: '$student' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$_id', '$$studentId'] },
              deleted: false,
              enabled: true,
            },
          },
          {
            $project: { _id: 1, advisor: 1 },
          },
        ],
        as: 'tempStudent',
      })
      .unwind('$tempStudent')
      .match({
        'tempStudent.advisor': { $in: advisorIds },
      });
  }

  // Get questionnaire statuses that where not corrected yet
  courseStatusesAggregation
    .lookup({
      from: 'coursequestionnairestatuses',
      let: { courseStatusId: '$_id' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$courseStatus', '$$courseStatusId'] },
            submittedAt: { $ne: null },
            correctedAt: { $eq: null },
            reviewRequested: true,
            deleted: false,
            enabled: true,
          },
        },
        {
          $project: {
            _id: 1,
            submittedAt: 1,
            correctedAt: 1,
          },
        },
      ],
      as: 'questionnaireStatuses',
    })
    .match({
      questionnaireStatuses: { $ne: [] },
    })
    .addFields({
      latestSubmissionDate: { $max: '$questionnaireStatuses.submittedAt' },
      oldestSubmissionDate: { $min: '$questionnaireStatuses.submittedAt' },
    });

  // Get total items
  const totalCourseStatuses = await getCount(courseStatusesAggregation);

  // Get total questionnaires
  const questionnaireStatusesCount = await courseStatusesAggregation._model
    .aggregate(courseStatusesAggregation.pipeline())
    .project({ questionnaireStatuses: { $size: '$questionnaireStatuses' } })
    .group({
      _id: null,
      questionnaireStatuses: { $sum: '$questionnaireStatuses' },
    });
  const totalQuestionnaires =
    questionnaireStatusesCount[0]?.questionnaireStatuses || 0;

  // Get course statuses
  const courseStatuses = await courseStatusesAggregation
    .sort({ oldestSubmissionDate: -1 })
    .skip(skip)
    .limit(limit)
    .lookup({
      from: 'courses',
      let: { courseId: '$course' },
      pipeline: [
        { $match: { $expr: { $eq: ['$_id', '$$courseId'] } } },
        { $project: { title: 1, lessons: 1 } },
      ],
      as: 'course',
    })
    .lookup({
      from: 'coursestudents',
      let: { studentId: '$student' },
      pipeline: [
        { $match: { $expr: { $eq: ['$_id', '$$studentId'] } } },
        {
          $project: {
            username: 1,
            advisor: 1,
            webUser: 1,
            profile: 1,
            ...(isCorrespondenceCoursesEnabled && {
              firstName: 1,
              lastName: 1,
            }),
          },
        },
      ],
      as: 'student',
    })
    .addFields({
      student: { $arrayElemAt: ['$student', 0] },
    })
    .lookup({
      from: 'courseadvisors',
      let: { advisorId: '$student.advisor' },
      pipeline: [
        { $match: { $expr: { $eq: ['$_id', '$$advisorId'] } } },
        {
          $project: {
            firstName: 1,
            lastName: 1,
            enabled: 1,
            absenceMessage: 1,
          },
        },
      ],
      as: 'advisor',
    })
    .addFields({
      course: { $arrayElemAt: ['$course', 0] },
      advisor: { $arrayElemAt: ['$advisor', 0] },
      latestActivity: { $max: '$questionnaireStatuses.submittedAt' },
      oldestActivity: { $min: '$questionnaireStatuses.submittedAt' },
    })
    .project({
      id: '$_id',
      course: 1,
      student: 1,
      advisor: 1,
      startedAt: 1,
      lastActivity: 1,
      latestSubmissionDate: 1,
      oldestSubmissionDate: 1,
      questionnaireStatuses: 1,
      totalLessons: { $size: '$course.lessons' },
      startedLessons: { $size: '$startedLessons' },
    });

  data = {
    count: totalCourseStatuses,
    totalQuestionnaires,
    items: courseStatuses.map((courseStatus) => ({
      ...courseStatus,
      editable:
        canUpdateAllQuestionnaires ||
        (canUpdateQuestionnaires &&
          areEqualIDs(req.advisor?._id, courseStatus.student?.advisor)),
      deletable:
        canDeleteAllQuestionnaires ||
        (canDeleteQuestionnaires &&
          areEqualIDs(req.advisor?._id, courseStatus.student?.advisor)),
    })),
  };

  res.status(200).json(data);
};

export const getCourseStatus = async (req, res) => {
  const data = await factory.getOne(CourseStatus, req, {
    paramId: 'courseStatusId',
  });

  const providerId = data.provider;
  const {
    canUpdateQuestionnaires,
    canUpdateAllQuestionnaires,
    canDeleteQuestionnaires,
    canDeleteAllQuestionnaires,
  } = getQuestionnairePermissions(providerId, req);

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  const student = await getStudent({
    studentId: data.student,
    fields: ['advisor'],
  });
  const studentAdvisorId = student.advisor;

  const courseStatus = await CourseStatus.aggregate()
    .match({
      _id: new mongoose.Types.ObjectId(data._id),
      deleted: false,
    })
    .lookup({
      from: 'courses',
      let: { courseId: '$course' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$courseId'] },
          },
        },
        {
          $lookup: {
            from: 'courselessons',
            let: { lessonIds: '$lessons' },
            pipeline: [
              {
                $match: {
                  $expr: { $in: ['$_id', '$$lessonIds'] },
                  deleted: false,
                  enabled: true,
                },
              },
              { $project: { _id: 1 } },
            ],
            as: 'lessons',
          },
        },
        { $project: { title: 1, lessons: 1, progression: 1 } },
      ],
      as: 'course',
    })
    .lookup({
      from: 'coursestudents',
      let: { studentId: '$student' },
      pipeline: [
        { $match: { $expr: { $eq: ['$_id', '$$studentId'] } } },
        {
          $project: {
            username: 1,
            advisor: 1,
            webUser: 1,
            profile: 1,
            ...(isCorrespondenceCoursesEnabled && {
              firstName: 1,
              lastName: 1,
            }),
          },
        },
      ],
      as: 'student',
    })
    .unwind('$student')
    .lookup({
      from: 'courseadvisors',
      let: { advisorId: '$student.advisor' },
      pipeline: [
        { $match: { $expr: { $eq: ['$_id', '$$advisorId'] } } },
        { $project: { firstName: 1, lastName: 1 } },
      ],
      as: 'advisor',
    })
    .addFields({
      course: { $arrayElemAt: ['$course', 0] },
      advisor: { $arrayElemAt: ['$advisor', 0] },
    })
    .addFields({
      editable:
        canUpdateAllQuestionnaires ||
        (canUpdateQuestionnaires &&
          areEqualIDs(req.advisor?._id, studentAdvisorId)),
      deletable:
        canDeleteAllQuestionnaires ||
        (canDeleteQuestionnaires &&
          areEqualIDs(req.advisor?._id, studentAdvisorId)),
    })
    .project({
      course: 1,
      student: 1,
      advisor: 1,
      startedAt: 1,
      finishedAt: 1,
      mode: 1,
      notes: 1,
      totalLessons: { $size: '$course.lessons._id' },
      startedLessons: { $size: '$startedLessons' },
      reachedLesson: 1,
      startedSlides: 1,
      editable: 1,
      deletable: 1,
    });

  res.status(200).json(courseStatus[0]);
};

export const getCourseStatusQuestionnaires = async (req, res) => {
  const courseStatus = await factory.getOne(CourseStatus, req, {
    paramId: 'courseStatusId',
  });

  // Get student with advisor
  const student = await Student.findById(courseStatus.student).populate({
    path: 'advisor',
    select: ['absenceMessage', 'enabled', 'firstName', 'lastName'],
  });

  // Check if course is online or correspondence
  const onlineCourse = ['autonomous', 'supervised'].includes(courseStatus.mode);
  const correspondenceCourse = courseStatus.mode === 'correspondence';

  // Check if requesting own student
  const isRequestingOwnStudent = areEqualIDs(
    req.advisor?._id,
    student.advisor?._id
  );

  const { canUpdateQuestionnaires, canUpdateAllQuestionnaires } =
    getQuestionnairePermissions(courseStatus.provider, req);

  const _canUpdateQuestionnaires =
    canUpdateAllQuestionnaires ||
    (canUpdateQuestionnaires && isRequestingOwnStudent);

  // Get course content
  const course = await Course.findById(courseStatus.course)
    .select(['title', 'lessons'])
    .populate({
      path: 'lessons',
      match: { deleted: false, enabled: true },
      select: ['title', 'slides'],
      populate: [
        {
          path: 'slides',
          match: { deleted: false, enabled: true },
          select: ['title', 'type', 'questionnaire', 'requiresReview'],
          // TODO: check if questionnaire is necessary, or if we can use the requiresReview directly from the slide (data correct in DB?)
          populate: [
            {
              path: 'questionnaire',
              match: { deleted: false, enabled: true },
              select: ['requiresReview'],
            },
          ],
        },
      ],
    })
    .lean();

  // Get questionnaire statuses (online course)
  const questionnaireStatuses = onlineCourse
    ? await QuestionnaireStatus.find({
        courseStatus: courseStatus._id,
        deleted: false,
        enabled: true,
        submittedAt: { $ne: null },
      })
        .select([
          'submittedAt',
          'correctedAt',
          'advisor',
          'advisorNotesSavedAt',
          'reviewRequested',
          'questionnaire',
        ])
        .populate({
          path: 'advisor',
          select: ['absenceMessage', 'enabled', 'firstName', 'lastName'],
        })
        .lean()
    : null;

  // Get lesson statuses (correspondence course)
  const lessonStatuses = correspondenceCourse
    ? await LessonStatus.find({
        courseStatus: courseStatus._id,
        deleted: false,
        enabled: true,
      })
        .select([
          'sentAt',
          'submittedAt',
          'correctedAt',
          'advisor',
          'advisorNotes',
          'advisorNotesSavedAt',
          'lesson',
        ])
        .populate({
          path: 'advisor',
          select: ['absenceMessage', 'enabled', 'firstName', 'lastName'],
        })
        .lean()
    : null;

  // Add course status to course content
  course.lessons = course.lessons.map((lesson) => {
    // Add lesson status (correspondence courses)
    if (correspondenceCourse) {
      const lessonStatus = lessonStatuses.find((_lessonStatus) =>
        areEqualIDs(_lessonStatus.lesson, lesson._id)
      );

      if (lessonStatus) {
        // Add student advisor (if not already added)
        if (lessonStatus && !lessonStatus.advisor) {
          lessonStatus.advisor = student.advisor;
        }

        // Add permission flags
        lessonStatus.editable = _canUpdateQuestionnaires;

        // Delete not used fields
        delete lessonStatus.lesson;
      }

      lesson.lessonStatus = lessonStatus;
    }

    // Add questionnaire status to slides (online courses)
    lesson.slides = lesson.slides.map((slide) => {
      if (slide.type === 'questionnaire' && onlineCourse) {
        const questionnaireStatus = questionnaireStatuses.find((status) =>
          areEqualIDs(status.questionnaire, slide.questionnaire._id)
        );

        if (questionnaireStatus) {
          const { reviewRequested, submittedAt, correctedAt } =
            questionnaireStatus;
          // Add student advisor if missing
          if (!questionnaireStatus.advisor) {
            questionnaireStatus.advisor = student.advisor;
          }

          // Add editable flags
          questionnaireStatus.editable = _canUpdateQuestionnaires;

          // Add pending review flag
          questionnaireStatus.pendingReview =
            reviewRequested && submittedAt && !correctedAt;

          // Delete not used fields
          delete questionnaireStatus.questionnaire;
        }

        slide.questionnaireStatus = questionnaireStatus;
      }

      return slide;
    });
    return lesson;
  });

  res.status(200).json(course.lessons);
};

export const getCourseStatusQuestionnairesOld = async (req, res) => {
  const data = await factory.getOne(CourseStatus, req, {
    paramId: 'courseStatusId',
  });

  const providerId = data.provider;
  const {
    canUpdateQuestionnaires,
    canUpdateAllQuestionnaires,
    canDeleteQuestionnaires,
    canDeleteAllQuestionnaires,
  } = getQuestionnairePermissions(providerId, req);

  const student = await getStudent({
    studentId: data.student,
    fields: ['advisor', 'firstName', 'lastName', 'username'],
  });
  const studentAdvisor = await getAdvisor({
    advisorId: student.advisor,
    fields: ['firstName', 'lastName'],
  });

  const courseStatusAggregation = CourseStatus.aggregate()
    .match({
      _id: new mongoose.Types.ObjectId(data._id),
      deleted: false,
    })
    .lookup({
      from: 'courses',
      let: { courseId: '$course' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$courseId'] },
            deleted: false,
          },
        },
        {
          $lookup: {
            from: 'courselessons',
            let: { lessonIds: '$lessons' },
            pipeline: [
              {
                $match: {
                  $expr: { $in: ['$_id', '$$lessonIds'] },
                  deleted: false,
                  enabled: true,
                },
              },
              {
                $addFields: {
                  position: { $indexOfArray: ['$$lessonIds', '$_id'] },
                },
              },
              { $sort: { position: 1 } },
              { $project: { position: 0 } },
              {
                $lookup: {
                  from: 'courseslides',
                  let: { slideIds: '$slides' },
                  pipeline: [
                    {
                      $match: {
                        $expr: { $in: ['$_id', '$$slideIds'] },
                        deleted: false,
                        enabled: true,
                        type: 'questionnaire',
                      },
                    },
                    {
                      $addFields: {
                        position: { $indexOfArray: ['$$slideIds', '$_id'] },
                      },
                    },
                    { $sort: { position: 1 } },
                    { $project: { position: 0 } },
                    {
                      $lookup: {
                        from: 'coursequestionnaires',
                        let: { questionnaireId: '$questionnaire' },
                        pipeline: [
                          {
                            $match: {
                              $expr: { $eq: ['$_id', '$$questionnaireId'] },
                              deleted: false,
                              enabled: true,
                            },
                          },
                          {
                            $project: {
                              _id: 1,
                              requiresReview: 1,
                            },
                          },
                        ],
                        as: 'questionnaire',
                      },
                    },
                    { $unwind: '$questionnaire' },
                    {
                      $lookup: {
                        from: 'coursequestionnairestatuses',
                        let: { questionnaireId: '$questionnaire._id' },
                        pipeline: [
                          {
                            $match: {
                              $expr: {
                                $and: [
                                  {
                                    $eq: [
                                      '$questionnaire',
                                      '$$questionnaireId',
                                    ],
                                  },
                                  { $eq: ['$courseStatus', data._id] },
                                  { $eq: ['$student', data.student] },
                                ],
                              },
                            },
                          },
                          {
                            $lookup: {
                              from: 'courseadvisors',
                              let: { advisorId: '$advisor' },
                              pipeline: [
                                {
                                  $match: {
                                    $expr: { $eq: ['$_id', '$$advisorId'] },
                                    deleted: false,
                                    enabled: true,
                                  },
                                },
                                {
                                  $project: {
                                    _id: 1,
                                    firstName: 1,
                                    lastName: 1,
                                  },
                                },
                              ],
                              as: 'advisor',
                            },
                          },
                          {
                            $project: {
                              _id: 1,
                              submittedAt: 1,
                              correctedAt: 1,
                              reviewRequested: 1,
                              advisor: { $arrayElemAt: ['$advisor', 0] },
                            },
                          },
                        ],
                        as: 'questionnaireStatus',
                      },
                    },
                    {
                      $project: {
                        title: 1,
                        questionnaire: 1,
                        questionnaireStatus: {
                          $arrayElemAt: ['$questionnaireStatus', 0],
                        },
                      },
                    },
                  ],
                  as: 'slides',
                },
              },
              {
                $match: {
                  slides: { $ne: [] },
                },
              },
              { $project: { title: 1, slides: 1 } },
            ],
            as: 'lessons',
          },
        },
        {
          $project: {
            _id: 1,
            title: 1,
            lessons: 1,
          },
        },
      ],
      as: 'course',
    })
    .unwind('$course')
    .project({
      _id: 0,
      course: 1,
    });

  const courseStatus = (await courseStatusAggregation)[0];

  const questionnaires =
    courseStatus?.course?.lessons.reduce((acc, lesson) => {
      acc.push({
        lessonId: lesson._id,
        lessonTitle: lesson.title,
        questionnaires: lesson.slides.map((slide) => ({
          slideId: slide._id,
          slideTitle: slide.title,
          requiresReview: slide.questionnaire.requiresReview,
          ...(slide.questionnaireStatus && {
            questionnaireStatus: {
              ...slide.questionnaireStatus,
              advisor: slide.questionnaireStatus?.advisor || studentAdvisor,
            },
          }),
        })),
        editable:
          canUpdateAllQuestionnaires ||
          (canUpdateQuestionnaires &&
            areEqualIDs(req.advisor?._id, studentAdvisor?._id)),
        deletable:
          canDeleteAllQuestionnaires ||
          (canDeleteQuestionnaires &&
            areEqualIDs(req.advisor?._id, studentAdvisor?._id)),
      });
      return acc;
    }, []) || [];

  res.status(200).json(questionnaires);
};

export const startCourse = async (req, res) => {
  const studentId = getParam(req, 'studentId');

  const student = await studentService.getStudent({
    studentId,
    fields: ['provider'],
  });

  const { courseId, advisorId, mode } = req.body;

  // Start course
  const { courseStatus, error } = await courseStatusService.startCourse({
    providerId: student.provider,
    courseId,
    studentId,
    advisorId,
    mode,
  });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json(courseStatus);
};

export const updateCourseStatus = async (req, res) => {
  const courseStatus = await factory.getOne(CourseStatus, req, {
    paramId: 'courseStatusId',
  });

  const updatedCourseStatus = await CourseStatus.findByIdAndUpdate(
    courseStatus._id,
    {
      ...req.body,
      student: courseStatus.student, // prevents changing the student
      course: courseStatus.course, // prevents changing the
      provider: courseStatus.provider, // prevents moving a collection to different provider
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedCourseStatus);
};

export default {
  getAllCourseStatuses,
  getCourseStatus,
  updateCourseStatus,
  startCourse,
  getCourseStatusQuestionnaires,
  getCourseStatusQuestionnairesOld,
};
