import factory from '#utils/handlerFactory.js';

import Country from '../models/Country.js';
import CountryRegion from '../models/CountryRegion.js';

import { getProvider } from '../services/providerService.js';
import regionsService from '../services/regionsService.js';

export const getAllRegions = async (req, res) => {
  const country = await factory.getOne(Country, req, {
    paramId: 'countryId',
    fields: ['id', 'regionType'],
  });

  const regions = await CountryRegion.find({
    country: country._id,
    type: country.regionType,
    deleted: false,
  })
    .populate('advisor')
    .sort({
      'config.name': 'asc',
      'config.start': 'asc',
      'config.end': 'asc',
    })
    .collation({ locale: 'en' });

  res.status(200).json(regions);
};

export const getRegion = async (req, res) => {
  const data = await factory.getOne(CountryRegion, req, {
    paramId: 'regionId',
  });

  res.status(200).json(data);
};

export const createRegion = async (req, res) => {
  const country = await factory.getOne(Country, req, {
    paramId: 'countryId',
    fields: ['id', 'provider', 'regionType'],
  });

  const { provider, regionType } = country;

  // TODO: check if region already exists

  // Create the region
  const region = await CountryRegion.create({
    ...req.body,
    type: regionType,
    country,
    provider,
  });

  res.status(200).json(region);
};

export const updateRegion = async (req, res) => {
  const region = await factory.getOne(CountryRegion, req, {
    paramId: 'regionId',
  });

  const { entity } = req;
  const { reassignStudents, ...data } = req.body;

  // Update region's data
  const updatedRegion = await CountryRegion.findByIdAndUpdate(
    region._id,
    {
      ...data,
      country: region.country, // prevents moving a region to different country
      provider: region.provider, // prevents moving a region to different provider
    },
    {
      new: true,
      runValidators: true,
    }
  );

  // Get provider
  const provider = await getProvider({
    providerId: region.provider,
    fields: ['regionsEnabled'],
  });

  // Reassign students to new advisor (if regions are enabled)
  if (provider.regionsEnabled && reassignStudents) {
    await regionsService.reassignStudents({ region, data, entity });
  }

  res.status(200).json(updatedRegion);
};

export const deleteRegion = async (req, res) => {
  const data = await factory.deleteOne(CountryRegion, req, {
    paramId: 'regionId',
  });

  res.status(200).json(data);
};

export const restoreRegion = async (req, res) => {
  const data = await factory.restoreOne(CountryRegion, req, {
    paramId: 'regionId',
  });

  res.status(200).json(data);
};

export const disableRegion = async (req, res) => {
  const data = await factory.disableOne(CountryRegion, req, {
    paramId: 'regionId',
  });

  res.status(200).json(data);
};

export const enableRegion = async (req, res) => {
  const data = await factory.enableOne(CountryRegion, req, {
    paramId: 'regionId',
  });

  res.status(200).json(data);
};

export default {
  getAllRegions,
  getRegion,
  createRegion,
  updateRegion,
  deleteRegion,
  restoreRegion,
  disableRegion,
  enableRegion,
};
