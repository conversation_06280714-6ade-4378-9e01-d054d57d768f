import mongoose from 'mongoose';

import { getCount } from '#utils/aggregations.js';
import { getPagination, getParam, getStatusFilter } from '#utils/request.js';
import factory from '#utils/handlerFactory.js';

import Country from '../models/Country.js';
import CountryRegion from '../models/CountryRegion.js';
import Provider from '../models/Provider.js';
import getAdvisorIds from '../helpers/getAdvisorIds.js';

export const getAllCountries = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);
  const statusFilter = getStatusFilter(req);
  const advisorIds = getAdvisorIds(req);

  const userLanguage = req.user.preferences.language;

  // Build query
  const aggregation = Country.aggregate().match({
    provider: new mongoose.Types.ObjectId(providerId),
    ...(advisorIds.length > 0 && { advisor: { $in: advisorIds } }),
    ...statusFilter,
  });

  // Get total items
  const totalCountries = await getCount(aggregation);

  // Get countries
  const countries = await aggregation
    .lookup({
      from: 'countries',
      let: { countryCode: '$countryCode' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$code', '$$countryCode'] },
            deleted: false,
          },
        },
        {
          $project: {
            name: 1,
          },
        },
      ],
      as: 'country',
    })
    .unwind('$country')
    .addFields({
      countryName: {
        $cond: {
          if: `$country.name.${userLanguage}`,
          then: `$country.name.${userLanguage}`,
          else: '$country.name.en',
        },
      },
    })
    .collation({ locale: 'en' })
    .sort({ countryName: 1 })
    .skip(skip)
    .limit(limit)
    .lookup({
      from: 'courseadvisors',
      let: { advisorId: '$advisor' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$advisorId'] },
            deleted: false,
          },
        },
        {
          $project: {
            id: '$_id',
            firstName: 1,
            lastName: 1,
            email: 1,
            absenceMessage: 1,
            enabled: 1,
          },
        },
      ],
      as: 'advisor',
    })
    .project({
      id: '$_id',
      enabled: 1,
      deleted: 1,
      createdAt: 1,
      country: 1,
      countryCode: 1,
      countryName: 1,
      regionType: 1,
      regionTitleSingular: 1,
      regionTitlePlural: 1,
      advisor: { $arrayElemAt: ['$advisor', 0] },
      provider: 1,
    });

  res.status(200).json({
    count: totalCountries,
    items: countries,
  });
};

export const getCountry = async (req, res) => {
  const userLanguage = req.user.preferences.language;

  const data = await Country.aggregate()
    .match({
      _id: new mongoose.Types.ObjectId(req.params.countryId),
    })
    .lookup({
      from: 'countries',
      let: { countryCode: '$countryCode' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$code', '$$countryCode'] },
            deleted: false,
          },
        },
        {
          $project: {
            name: 1,
          },
        },
      ],
      as: 'country',
    })
    .unwind('$country')
    .addFields({
      id: '$_id',
      countryName: {
        $cond: {
          if: `$country.name.${userLanguage}`,
          then: `$country.name.${userLanguage}`,
          else: '$country.name.en',
        },
      },
    });

  res.status(200).json(data[0]);
};

export const createCountry = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const provider = await Provider.findById(providerId, '_id');

  // Create the country
  const country = await Country.create({
    ...req.body,
    provider,
  });

  res.status(200).json(country);
};

export const updateCountry = async (req, res) => {
  const country = await factory.getOne(Country, req, {
    paramId: 'countryId',
  });

  // Update country's data
  const updatedCountry = await Country.findByIdAndUpdate(
    country._id,
    {
      ...req.body,
      provider: country.provider, // prevents moving a country to different provider
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedCountry);
};

export const deleteCountry = async (req, res) => {
  const data = await factory.deleteOne(Country, req, {
    paramId: 'countryId',
  });

  res.status(200).json(data);
};

export const restoreCountry = async (req, res) => {
  const data = await factory.restoreOne(Country, req, {
    paramId: 'countryId',
  });

  res.status(200).json(data);
};

export const disableCountry = async (req, res) => {
  const data = await factory.disableOne(Country, req, {
    paramId: 'countryId',
  });

  res.status(200).json(data);
};

export const enableCountry = async (req, res) => {
  const data = await factory.enableOne(Country, req, {
    paramId: 'countryId',
  });

  res.status(200).json(data);
};

export const getCountryRegions = async (req, res) => {
  const country = await factory.getOne(Country, req, {
    paramId: 'countryId',
    fields: ['id', 'regionType'],
  });

  const countryRegions = await CountryRegion.find({
    country: country._id,
    type: country.regionType,
    deleted: false,
  });

  res.status(200).json(countryRegions);
};

export default {
  getAllCountries,
  getCountry,
  createCountry,
  updateCountry,
  deleteCountry,
  restoreCountry,
  disableCountry,
  enableCountry,
  getCountryRegions,
};
