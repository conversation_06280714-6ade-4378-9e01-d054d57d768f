import _ from 'lodash';
import mongoose from 'mongoose';

import { slugify } from '#utils/strings.js';
import { getCount, getDateFilter } from '#utils/aggregations.js';
import { buildCsvContent } from '#utils/csv.js';
import getSortFilter from '#utils/api/sort/filters.js';
import {
  getPagination,
  getParam,
  getSearch,
  getStatusFilter,
} from '#utils/request.js';
import factory from '#utils/handlerFactory.js';
// import getFullName from '#utils/getFullName.js';
import { areEqualIDs } from '#utils/helpers.js';

import getFullName from '#utils/getFullName.js';
import webUserServices from '#modules/web-auth/services/webUserServices.js';

import Student from '../models/Student.js';
import CourseStatus from '../models/CourseStatus.js';
import Provider from '../models/Provider.js';
import getAdvisorIds from '../helpers/getAdvisorIds.js';
import studentService from '../services/studentService.js';
import providerService from '../services/providerService.js';
import AdvisorStudentNotes from '../models/AdvisorStudentNotes.js';
import { getCourseStatus } from '../services/courseStatusService.js';

function hasStudentPermission(providerId, permission, req) {
  return req.user.hasPermission({
    module: 'courses-students',
    permission: permission,
    recordModule: 'courses-providers',
    recordId: providerId,
  });
}

export const getAllStudents = async (req, res) => {
  let data = { count: 0, items: [] };

  const providerId = getParam(req, 'providerId');
  const canReadStudents = hasStudentPermission(providerId, 'read', req);
  const canReadAllStudents = hasStudentPermission(providerId, 'readAll', req);
  const canUpdateStudents = hasStudentPermission(providerId, 'update', req);
  const canUpdateAllStudents = hasStudentPermission(
    providerId,
    'updateAll',
    req
  );
  const canDeleteStudents = hasStudentPermission(providerId, 'delete', req);
  const canDeleteAllStudents = hasStudentPermission(
    providerId,
    'deleteAll',
    req
  );

  if (!req.user.isAdmin && !canReadAllStudents && !canReadStudents) {
    res.status(200).json(data);
    return;
  }

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  const { limit, skip } = getPagination(req);
  const statusFilter = getStatusFilter(req);

  // Get students for these courses
  let studentIds = [];
  if (req.query.courses) {
    const courseIds = req.query.courses.split(',');
    if (!_.isEmpty(courseIds)) {
      studentIds = await CourseStatus.find({
        course: {
          $in: courseIds
            .filter((courseId) => mongoose.isValidObjectId(courseId))
            .map((courseId) => new mongoose.Types.ObjectId(courseId)),
        },
      })
        .select('student')
        .distinct('student');
    }
  }

  // Filter by study modes (online, correspondence)
  if (req.query.studyModes) {
    const studyModes = req.query.studyModes.split(',');
    if (!_.isEmpty(studyModes)) {
      // Find all students with the study modes
      const enrolledStudentIds = await CourseStatus.distinct('student', {
        provider: providerId,
        mode: { $in: studyModes },
        deleted: false,
      });

      // Add enrolledStudentIds to studentIds (unique)
      studentIds = [...new Set([...studentIds, ...enrolledStudentIds])];
    }
  }

  // Get students of these advisors
  const advisorIds = canReadAllStudents
    ? getAdvisorIds(req)
    : req.advisor
      ? [req.advisor._id]
      : ['-1']; // No advisors

  // Create search field and filter
  const { searchField, searchFilter } = getSearch(
    req,
    req.query.search.includes('@')
      ? [
          'profile.firstName',
          'profile.lastName',
          'profile.username',
          'profile.email',
          ...(isCorrespondenceCoursesEnabled
            ? ['firstName', 'lastName', 'email']
            : []),
        ]
      : [
          'profile.firstName',
          'profile.lastName',
          'profile.username',
          ...(isCorrespondenceCoursesEnabled ? ['firstName', 'lastName'] : []),
        ]
  );

  // Build query
  const aggregation = Student.aggregate();

  // Add search field
  if (searchField) aggregation.addFields(searchField);

  // Filter query
  aggregation.match({
    provider: new mongoose.Types.ObjectId(providerId),
    ...(!isCorrespondenceCoursesEnabled && { webUser: { $exists: true } }),
    ...((req.query.courses || req.query.studyModes) && {
      _id: { $in: studentIds },
    }),
    ...(advisorIds.length > 0 && { advisor: { $in: advisorIds } }),
    ...statusFilter,
    ...searchFilter,
  });

  // Get total items
  const totalStudents = await getCount(aggregation);

  // Prepare sort options
  const sortOptions = getSortFilter({
    sort: req.query.sort,
    order: req.query.sortOrder,
    defaultFields: ['sortField'],
  });

  // Get students
  const students = await aggregation
    .addFields({
      // Create sort field (firstName + lastName + username)
      sortField: {
        $concat: [
          {
            $trim: {
              input: {
                $concat: [
                  { $ifNull: ['$firstName', ''] },
                  ' ',
                  { $ifNull: ['$lastName', ''] },
                ],
              },
            },
          },
          { $ifNull: ['$username', ''] },
        ],
      },
    })
    .collation({ locale: 'en' })
    .sort(sortOptions)
    .skip(skip)
    .limit(limit)
    .lookup({
      from: 'courseadvisors',
      let: { advisorId: '$advisor' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$advisorId'] },
            deleted: false,
          },
        },
        {
          $project: {
            id: '$_id',
            firstName: 1,
            lastName: 1,
            email: 1,
            absenceMessage: 1,
            enabled: 1,
          },
        },
      ],
      as: 'advisor',
    })
    .lookup({
      from: 'coursestatuses',
      let: { studentId: '$_id' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$student', '$$studentId'] },
            mode: {
              $in: [
                'autonomous',
                'supervised',
                ...(isCorrespondenceCoursesEnabled ? ['correspondence'] : []),
              ],
            },
            deleted: false,
          },
        },
        {
          $lookup: {
            from: 'courses',
            let: { courseId: '$course' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$_id', '$$courseId'] },
                },
              },
              {
                $project: {
                  id: '$_id',
                  title: 1,
                  enabled: 1,
                  deleted: 1,
                },
              },
            ],
            as: 'course',
          },
        },
        {
          $addFields: {
            course: { $arrayElemAt: ['$course', 0] },
          },
        },
        {
          $match: { course: { $ne: null } },
        },
        {
          $project: {
            id: '$_id',
            course: 1,
            mode: 1,
          },
        },
      ],
      as: 'courseStatuses',
    })
    .project({
      id: '$_id',
      enabled: 1,
      deleted: 1,
      createdAt: 1,
      username: 1,
      // fullName: 1, // TODO: implement fullName
      advisor: { $arrayElemAt: ['$advisor', 0] },
      courseStatuses: 1,
      webUser: 1,
      profile: 1,
      ...(isCorrespondenceCoursesEnabled && {
        firstName: 1,
        lastName: 1,
        email: 1,
      }),
    });

  data = {
    count: totalStudents,
    items: students.map((student) => ({
      ...student,
      editable:
        canUpdateAllStudents ||
        (canUpdateStudents &&
          areEqualIDs(req.advisor?._id, student.advisor?._id)),
      deletable:
        canDeleteAllStudents ||
        (canDeleteStudents &&
          areEqualIDs(req.advisor?._id, student.advisor?._id)),
    })),
  };

  res.status(200).json(data);
};

/**
 * Check if a student is enrolled in a course
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Object} req.body - Request body
 * @param {string} req.body.courseId - Course ID
 * @param {string} req.body.mode - Course mode
 * @returns {Object} - Response object
 */
export const studentCourseExists = async (req, res) => {
  const student = await factory.getOne(Student, req, {
    paramId: 'studentId',
    fields: ['id'],
  });

  const { courseId, mode } = req.query;

  const courseStatus = await getCourseStatus({
    courseId,
    studentId: student._id,
    fields: ['mode'],
  });

  const courseStatusExists = mode
    ? courseStatus?.mode === mode
    : !!courseStatus;

  if (courseStatusExists) {
    res.status(200).json({ exists: true });
  } else {
    res
      .status(404)
      .json({ exists: false, message: 'STUDENT_COURSE_NOT_FOUND' });
  }
};

export const getStudentCourses = async (req, res) => {
  const student = await factory.getOne(Student, req, {
    paramId: 'studentId',
    fields: ['id'],
  });

  const courseStatuses = await CourseStatus.aggregate()
    .match({
      student: new mongoose.Types.ObjectId(student._id),
    })
    .lookup({
      from: 'courses',
      let: { courseId: '$course' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$courseId'] },
          },
        },
        {
          $lookup: {
            from: 'courselessons',
            let: { lessonIds: '$lessons' },
            pipeline: [
              {
                $match: {
                  $expr: { $in: ['$_id', '$$lessonIds'] },
                  deleted: false,
                  enabled: true,
                },
              },
              { $project: { _id: 1 } },
            ],
            as: 'lessons',
          },
        },
        {
          $project: {
            _id: 1,
            deleted: 1,
            enabled: 1,
            title: 1,
            images: 1,
            lessons: 1,
          },
        },
      ],
      as: 'course',
    })
    .unwind('course')
    .sort('course.title')
    .project({
      course: {
        _id: 1,
        enabled: 1,
        deleted: 1,
        title: 1,
        images: 1,
      },
      startedAt: 1,
      finishedAt: 1,
      mode: 1,
      totalLessons: { $size: '$course.lessons._id' },
      startedLessons: { $size: '$startedLessons' },
      reachedLesson: {
        $add: [{ $indexOfArray: ['$course.lessons._id', '$reachedLesson'] }, 1],
      },
      enabled: 1,
      deleted: 1,
    });

  res.status(200).json(courseStatuses);
};

export const getStudent = async (req, res) => {
  let student = await factory.getOne(Student, req, {
    paramId: 'studentId',
    populate: [
      {
        path: 'webUser',
        match: { deleted: false },
        select: { _id: 1, profile: 1, email: 1 },
      },
    ],
  });
  student = student.toObject();

  const providerId = student.provider.toString();
  const canUpdateStudents = hasStudentPermission(providerId, 'update', req);
  const canUpdateAllStudents = hasStudentPermission(
    providerId,
    'updateAll',
    req
  );
  const canDeleteStudents = hasStudentPermission(providerId, 'delete', req);
  const canDeleteAllStudents = hasStudentPermission(
    providerId,
    'deleteAll',
    req
  );

  student.editable =
    canUpdateAllStudents ||
    (canUpdateStudents && areEqualIDs(req.advisor?._id, student.advisor));

  student.deletable =
    canDeleteAllStudents ||
    (canDeleteStudents && areEqualIDs(req.advisor?._id, student.advisor?._id));

  // Check if correspondence courses are enabled
  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  // Remove personal data if correspondence courses are disabled
  if (!isCorrespondenceCoursesEnabled) {
    delete student.firstName;
    delete student.lastName;
    delete student.email;
  }

  // Notes that only the advisor can see and edit
  let personalNotes;

  if (areEqualIDs(req.advisor?._id, student.advisor)) {
    personalNotes = await AdvisorStudentNotes.findOne({
      student: student._id,
      advisor: req.advisor._id,
    });
    student.privateNotes = personalNotes?.notes;
    student.advisorNotesEditable = true;
  }

  res.status(200).json(student);
};

export const createStudent = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const provider = await Provider.findById(providerId, '_id');

  // Ensure student has a valid slug within its siblings (or create one from its name)
  const { firstName, lastName } = req.body;
  const fullName = getFullName({ firstName, lastName });
  const stringToSlugify = req?.body?.slug || req?.body?.username || fullName;

  const slug = await Student.getAvailableSlug(
    slugify(stringToSlugify),
    provider
  );

  // Create the student
  const student = await Student.create({
    ...req.body,
    provider,
    entity: req.entity._id,
    slug,
  });

  res.status(200).json(student);
};

export const studentExists = async (req, res) => {
  const { firstName, lastName, email, username, phone } = req.query;
  const hasName = firstName && lastName;

  if (!email && !username && !phone && !hasName) {
    return res.status(400).json({
      message: 'Missing search parameters.',
    });
  }

  const conditions = [];
  if (email) {
    conditions.push({ email });
    conditions.push({ profile: email });
  }
  if (username) {
    conditions.push({ username });
    conditions.push({ profile: username });
  }
  if (hasName) {
    conditions.push({ firstName, lastName });
    conditions.push({ profile: firstName, lastName });
  }
  if (phone) {
    conditions.push({ phone });
    conditions.push({ profile: phone });
  }

  const students = await Student.find({
    $or: conditions,
    $and: [{ provider: getParam(req, 'providerId') }],
    deleted: false,
  });

  const matches = [];
  let matchVariant = null;

  students?.forEach((student) => {
    let exactMatch = false;
    const matchCriteria = [];

    if (email && student.email === email) {
      exactMatch = true;
      matchCriteria.push('email');
    }
    if (email && student.profile.email === email) {
      matchCriteria.push('profile email');
    }
    if (username && student.username === username) {
      exactMatch = true;
      matchCriteria.push('username');
    }
    if (username && student.profile.username === username) {
      matchCriteria.push('profile username');
    }
    if (
      hasName &&
      student.firstName === firstName &&
      student.lastName === lastName
    ) {
      matchCriteria.push('name');
    }
    if (
      student.profile.firstName === firstName &&
      student.profile.lastName === lastName
    ) {
      matchCriteria.push('profile name');
    }
    if (phone && student.phone === phone) {
      matchCriteria.push('phone');
    }

    if (matchCriteria.length > 0) {
      matches.push({
        exactMatch,
        matchCriteria: matchCriteria.join(', '),
        student,
      });
    }
    if (matchVariant === 'danger') return;

    if (
      matchCriteria.length > 0 &&
      (matchCriteria.includes('email') || matchCriteria.includes('username'))
    ) {
      matchVariant = 'danger';
    } else if (matchCriteria.length > 0) {
      matchVariant = 'warning';
    }
  });

  res.status(200).json({
    exists: matches.length > 0,
    exactMatch: matchVariant === 'danger',
    matches,
    matchVariant,
  });
};

export const updateStudent = async (req, res) => {
  const advisorId = req.advisor?._id;
  const student = await factory.getOne(Student, req, {
    paramId: 'studentId',
    fields: [
      '_id',
      'entity',
      'advisor',
      'provider',
      'profile',
      'slug',
      'username',
      'firstName',
      'lastName',
      'email',
    ],
  });

  // Advisor was passed (either with an ID or "")
  if (req.body.advisor !== undefined) {
    // Assign new advisor
    studentService.assignAdvisor(student._id, req.body.advisor);

    // Remove previous advisor
    await studentService.removeAdvisorMapping({
      studentId: student._id,
      advisorId: student.advisor,
    });

    // Prevent further changes to advisor
    delete req.body.advisor;
  }

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Student.getAvailableSlug(
        slugify(req.body.slug),
        student.provider,
        student._id
      )
    : student.slug;

  // Update student's data
  const updatedStudent = await Student.findByIdAndUpdate(
    student._id,
    {
      ...req.body,
      slug,
      provider: student.provider, // prevents moving a student to different provider
      entity: student.entity, // prevents moving a student to different entity
    },
    {
      new: true,
      runValidators: true,
    }
  );

  // Verify if advisor is the same as the one logged in and if there are private notes
  const { privateNotes } = req.body;
  let affectedAdvisorNotes;
  if (areEqualIDs(advisorId, student.advisor) && privateNotes) {
    const advisorNote = await AdvisorStudentNotes.findOne({
      student: student._id,
      advisor: advisorId,
    });

    // Update or create advisor notes
    if (advisorNote) {
      affectedAdvisorNotes = await AdvisorStudentNotes.findOneAndUpdate(
        {
          student: student._id,
          advisor: advisorId,
        },
        {
          notes: privateNotes,
        }
      );
    } else {
      affectedAdvisorNotes = await AdvisorStudentNotes.create({
        student: student._id,
        advisor: req.advisor._id,
        notes: privateNotes,
      });
    }
    updatedStudent.privateNotes = affectedAdvisorNotes.notes;
  }

  // Update web user data
  if (updatedStudent.webUser) {
    const webUserId = updatedStudent.webUser;

    const webUserData = {};

    // Update email address
    if (
      req.body.profile?.email &&
      req.body.profile.email !== student.profile?.email
    ) {
      webUserData.email = req.body.profile.email;
    }

    // Update name
    // if (
    //   (req.body.firstName || req.body.lastName) &&
    //   (req.body.firstName !== student.firstName ||
    //     req.body.lastName !== student.lastName)
    // ) {
    //   webUserData.name = getFullName(updatedStudent);
    // }

    // Update username
    if (
      req.body.profile?.username &&
      req.body.profile.username !== student.profile?.username
    ) {
      const provider = await providerService.getProvider({
        providerId: student.provider,
        fields: ['site'],
      });
      const webUser = await webUserServices.getWebUserById({
        id: webUserId,
        entity: req.entity,
        site: provider.site,
      });
      webUserData.profile = {
        ...webUser.profile,
        username: req.body.profile.username,
      };
    }

    if (!_.isEmpty(webUserData)) {
      await webUserServices.updateWebUser(webUserId, webUserData);
    }
  }

  res.status(200).json(updatedStudent);
};

export const deleteStudent = async (req, res) => {
  const studentId = getParam(req, 'studentId');

  const student = await studentService.getStudent({
    studentId,
    fields: ['provider'],
  });

  const provider = await providerService.getProvider({
    providerId: student.provider,
    fields: ['site'],
  });

  await studentService.deleteStudentAccount({
    studentId,
    entity: req.entity,
    deleteWebUser: true,
    siteId: provider.site,
  });

  res.status(200).json({ success: true });
};

export const disableStudent = async (req, res) => {
  const data = await factory.disableOne(Student, req, {
    paramId: 'studentId',
  });

  res.status(200).json(data);
};

export const enableStudent = async (req, res) => {
  const data = await factory.enableOne(Student, req, {
    paramId: 'studentId',
  });

  res.status(200).json(data);
};

export const getRegistrationStats = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);

  const { sort, sortOrder } = req.query;
  const sortFilter = getSortFilter({
    order: sortOrder,
    sort,
    defaultFields: ['createdAt'],
  });

  const { dateFrom, dateTo, format } = req.query;
  const dateFilter = getDateFilter(dateFrom, dateTo, 'createdAt');

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  const registrationsAggregation = Student.aggregate().match({
    provider: new mongoose.Types.ObjectId(providerId),
    webUser: { $exists: true },
    ...dateFilter,
  });

  if (format === 'csv') {
    // Get site language
    const language =
      await providerService.getSiteLanguageByProviderId(providerId);

    // Get registrations
    let registrations = await registrationsAggregation
      .sort({ createdAt: 1 })
      .project({
        username: 1,
        webUser: 1,
        profile: 1,
        createdAt: {
          $dateToString: {
            format: '%Y-%m-%d %H:%M',
            date: '$createdAt',
          },
        },
      });

    // Build object to be exported
    registrations = registrations.map((student) => ({
      username: student.profile.username || '',
      email: student.profile.email || '',
      firstName: student.profile.firstName || '',
      lastName: student.profile.lastName || '',
      registeredAt: student.createdAt,
    }));

    // Build CSV
    const csvContent = buildCsvContent(registrations, language);

    // Set headers and send CSV
    res.setHeader('Content-Type', 'text/csv');
    res.send(csvContent);
    return;
  }

  const totalRegistrations = await getCount(registrationsAggregation);
  const registrations = await registrationsAggregation
    .sort(sortFilter)
    .skip(skip)
    .limit(limit)
    .project({
      _id: 1,
      username: 1,
      createdAt: 1,
      deleted: 1,
      enabled: 1,
      webUser: 1,
      profile: 1,
      ...(isCorrespondenceCoursesEnabled && {
        firstName: 1,
        lastName: 1,
        email: 1,
      }),
    });

  res.status(200).json({
    count: totalRegistrations,
    items: registrations,
  });
};

export default {
  getAllStudents,
  getStudent,
  createStudent,
  studentExists,
  updateStudent,
  deleteStudent,
  disableStudent,
  enableStudent,
  getStudentCourses,
  studentCourseExists,
  getRegistrationStats,
};
