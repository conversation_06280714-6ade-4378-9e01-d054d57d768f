import mongoose from 'mongoose';

import factory from '#utils/handlerFactory.js';
import {
  getBoolean,
  getPagination,
  getParam,
  getStatusFilter,
} from '#utils/request.js';
import { getCount } from '#utils/aggregations.js';
import { slugify } from '#utils/strings.js';

import Collection from '../models/Collection.js';
import Provider from '../models/Provider.js';

export const getAllCollections = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);
  const statusFilter = getStatusFilter(req);

  // Build query
  const aggregation = Collection.aggregate().match({
    provider: new mongoose.Types.ObjectId(providerId),
    deleted: false,
    ...statusFilter,
  });

  // Get total items
  const totalCollections = await getCount(aggregation);

  // Get collections
  const collections = await aggregation
    .collation({ locale: 'en' })
    .sort({ title: 1 })
    .skip(skip)
    .limit(limit)
    .project({
      id: '$_id',
      deleted: 1,
      enabled: 1,
      title: 1,
      courses: 1,
    });

  res.status(200).json({
    count: totalCollections,
    items: collections,
  });
};

export const getCollection = async (req, res) => {
  const onlineCoursesOnly = getBoolean(req, 'onlineCoursesOnly');
  const collection = await factory.getOne(Collection, req, {
    paramId: 'collectionId',
    populate: [
      {
        path: 'courses',
        match: {
          deleted: false,
          ...(onlineCoursesOnly ? { onlineCourse: true } : {}),
        },
        fields: 'title enabled',
      },
    ],
  });

  res.status(200).json(collection);
};

export const createCollection = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const provider = await Provider.findById(providerId, '_id');

  // Ensure collection has a valid slug within its siblings (or create one from its title)
  const slug = await Collection.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    provider
  );

  // TODO: add position

  // Create the collection
  const collection = await Collection.create({
    ...req.body,
    provider,
    slug,
  });

  res.status(200).json(collection);
};

export const updateCollection = async (req, res) => {
  const collection = await factory.getOne(Collection, req, {
    paramId: 'collectionId',
  });

  // Ensures new slug doesn't exists
  req.body.slug = req.body.slug
    ? await Collection.getAvailableSlug(
        slugify(req.body.slug),
        collection.provider,
        collection._id
      )
    : collection.slug;

  const updatedCollection = await Collection.findByIdAndUpdate(
    collection._id,
    {
      ...req.body,
      provider: collection.provider, // prevents moving a collection to different provider
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedCollection);
};

export const deleteCollection = async (req, res) => {
  const deletedCollection = await factory.deleteOne(Collection, req, {
    paramId: 'collectionId',
  });

  res.status(200).json(deletedCollection);
};

export const restoreCollection = async (req, res) => {
  const data = await factory.restoreOne(Collection, req, {
    paramId: 'collectionId',
  });

  res.status(200).json(data);
};

export const disableCollection = async (req, res) => {
  const data = await factory.disableOne(Collection, req, {
    paramId: 'collectionId',
  });

  res.status(200).json(data);
};

export const enableCollection = async (req, res) => {
  const data = await factory.enableOne(Collection, req, {
    paramId: 'collectionId',
  });

  res.status(200).json(data);
};

export default {
  getAllCollections,
  getCollection,
  createCollection,
  updateCollection,
  deleteCollection,
  restoreCollection,
  disableCollection,
  enableCollection,
};
