import mongoose from 'mongoose';

import {
  getIdsFilter,
  getPagination,
  getParam,
  getSearch,
  getStatusFilter,
} from '#utils/request.js';
import { areEqualIDs } from '#utils/helpers.js';
import { getCount } from '#utils/aggregations.js';

import Advisor from '../models/Advisor.js';
import { getActiveStudents } from '../services/studentStatisticsService.js';
import Student from '../models/Student.js';

export const getAdvisorStudents = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);
  const statusFilter = getStatusFilter(req);
  const idsFilter = getIdsFilter(req);
  const { searchField, searchFilter } = getSearch(req, [
    'firstName',
    'lastName',
  ]);

  // Build query
  const aggregation = Advisor.aggregate();

  // Add search field
  if (searchField) aggregation.addFields(searchField);

  // Filter query
  aggregation.match({
    provider: new mongoose.Types.ObjectId(providerId),
    ...idsFilter,
    ...statusFilter,
    ...searchFilter,
  });

  // Get total items
  const totalAdvisors = await getCount(aggregation);

  // Get advisors
  const advisors = await aggregation
    .sort({ firstName: 1, lastName: 1 })
    .skip(skip)
    .limit(limit)
    .project({
      _id: 1,
      enabled: 1,
      firstName: 1,
      lastName: 1,
    })
    .collation({ locale: 'en' });

  const advisorIds = advisors.map((advisor) => advisor._id);
  const dateTo = new Date();

  // Get active students
  const activeLast30days = await getActiveStudents({
    providerId,
    dateTo,
    dayDiff: -30,
  });
  const activeLast6months = await getActiveStudents({
    providerId,
    dateTo,
    dayDiff: -180,
  });
  const activeLast12months = await getActiveStudents({
    providerId,
    dateTo,
    dayDiff: -365,
  });

  const advisorStudentsStats = await Student.aggregate()
    .match({
      provider: new mongoose.Types.ObjectId(providerId),
      advisor: { $in: advisorIds },
    })
    .group({
      _id: '$advisor',
      totalStudents: { $sum: 1 },
      activeLast30days: {
        $sum: {
          $cond: [{ $in: ['$_id', activeLast30days] }, 1, 0],
        },
      },
      activeLast6months: {
        $sum: {
          $cond: [{ $in: ['$_id', activeLast6months] }, 1, 0],
        },
      },
      activeLast12months: {
        $sum: {
          $cond: [{ $in: ['$_id', activeLast12months] }, 1, 0],
        },
      },
    });

  const advisorsWithStudents = advisors.map((advisor) => {
    const advisorStats = advisorStudentsStats.find((stats) =>
      areEqualIDs(stats._id, advisor._id)
    );

    return {
      ...advisor,
      totalStudents: advisorStats?.totalStudents || 0,
      activeStudentsLast30days: advisorStats?.activeLast30days || 0,
      activeStudentsLast6months: advisorStats?.activeLast6months || 0,
      activeStudentsLast12months: advisorStats?.activeLast12months || 0,
    };
  });

  res.status(200).json({
    count: totalAdvisors,
    items: advisorsWithStudents,
  });
};

export default {
  getAdvisorStudents,
};
