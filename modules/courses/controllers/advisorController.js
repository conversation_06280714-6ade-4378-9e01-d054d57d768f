import mongoose from 'mongoose';

import { slugify } from '#utils/strings.js';
import factory from '#utils/handlerFactory.js';
import {
  getIdsFilter,
  getPagination,
  getParam,
  getSearch,
  getStatusFilter,
} from '#utils/request.js';
import { getCount } from '#utils/aggregations.js';

import User from '#modules/users/models/User.js';

import Advisor from '../models/Advisor.js';
import Provider from '../models/Provider.js';

export const getAllAdvisors = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);
  const statusFilter = getStatusFilter(req);
  const idsFilter = getIdsFilter(req);
  const { searchField, searchFilter } = getSearch(req, [
    'firstName',
    'lastName',
  ]);

  // Build query
  const aggregation = Advisor.aggregate();

  // Add search field
  if (searchField) aggregation.addFields(searchField);

  // Filter query
  aggregation.match({
    provider: new mongoose.Types.ObjectId(providerId),
    ...idsFilter,
    ...statusFilter,
    ...searchFilter,
  });

  // Get total items
  const totalAdvisors = await getCount(aggregation);

  // Get advisors
  const advisors = await aggregation
    .sort({ firstName: 1, lastName: 1 })
    .skip(skip)
    .limit(limit)
    .lookup({
      from: 'coursestudentadvisors',
      let: { advisorId: '$_id' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$advisor', '$$advisorId'] },
            deleted: false,
          },
        },
        { $project: { _id: 1 } },
      ],
      as: 'students',
    })
    .lookup({
      from: 'users',
      let: { backendUserId: '$backendUser' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$backendUserId'] },
            deleted: false,
          },
        },
        { $project: { _id: 1, name: 1, enabled: 1 } },
      ],
      as: 'backendUser',
    })
    .project({
      id: '$_id',
      enabled: 1,
      deleted: 1,
      firstName: 1,
      lastName: 1,
      email: 1,
      students: { $size: '$students' },
      absenceMessage: 1,
      backendUser: { $arrayElemAt: ['$backendUser', 0] },
    })
    .collation({ locale: 'en' });

  res.status(200).json({
    count: totalAdvisors,
    items: advisors,
  });
};

export const getAdvisor = async (req, res) => {
  const data = await factory.getOne(Advisor, req, {
    paramId: 'advisorId',
  });

  res.status(200).json(data);
};

export const currentAdvisor = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const advisor = await Advisor.findOne(
    {
      provider: providerId,
      backendUser: req.user._id,
      deleted: false,
      enabled: true,
    },
    {
      _id: 1,
      firstName: 1,
      lastName: 1,
      email: 1,
    }
  );
  res.status(200).json(advisor);
};

export const createAdvisor = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const provider = await Provider.findById(providerId, '_id');
  const { backendUser, email, firstName, lastName } = req.body;

  // Set missing values based on provided backend user where missing
  if (backendUser && (!email || !firstName || !lastName)) {
    const user = await User.findById(backendUser, 'email name');

    const nameParts = user?.name.split(' ');
    const _firstName = nameParts?.[0];
    const _lastName = nameParts?.slice(1).join(' ');

    if (!email) req.body.email = user.email;
    if (!firstName) req.body.firstName = _firstName;
    if (!lastName) req.body.lastName = _lastName;
  }

  const fullName = `${req.body.firstName} ${req.body.lastName}`
    .replace(/undefined/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  // Ensure advisor has a valid slug within its siblings (or create one from its name)
  const slug = await Advisor.getAvailableSlug(
    slugify(req.body.slug || fullName),
    provider
  );

  // Create the advisor
  const advisor = await Advisor.create({
    ...req.body,
    provider,
    entity: req.entity._id,
    slug,
  });

  res.status(200).json(advisor);
};

export const updateAdvisor = async (req, res) => {
  const advisor = await factory.getOne(Advisor, req, {
    paramId: 'advisorId',
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Advisor.getAvailableSlug(
        slugify(req.body.slug),
        advisor.provider,
        advisor._id
      )
    : advisor.slug;

  // Update advisor's data
  const updatedAdvisor = await Advisor.findByIdAndUpdate(
    advisor._id,
    {
      ...req.body,
      slug,
      provider: advisor.provider, // prevents moving an advisor to different provider
      entity: advisor.entity, // prevents moving an advisor to different entity
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedAdvisor);
};

export const deleteAdvisor = async (req, res) => {
  const data = await factory.deleteOne(Advisor, req, {
    paramId: 'advisorId',
  });

  res.status(200).json(data);
};

export const restoreAdvisor = async (req, res) => {
  const data = await factory.restoreOne(Advisor, req, {
    paramId: 'advisorId',
  });

  res.status(200).json(data);
};

export const disableAdvisor = async (req, res) => {
  const data = await factory.disableOne(Advisor, req, {
    paramId: 'advisorId',
  });

  res.status(200).json(data);
};

export const enableAdvisor = async (req, res) => {
  const data = await factory.enableOne(Advisor, req, {
    paramId: 'advisorId',
  });

  res.status(200).json(data);
};

export default {
  getAllAdvisors,
  getAdvisor,
  currentAdvisor,
  createAdvisor,
  updateAdvisor,
  deleteAdvisor,
  restoreAdvisor,
  disableAdvisor,
  enableAdvisor,
};
