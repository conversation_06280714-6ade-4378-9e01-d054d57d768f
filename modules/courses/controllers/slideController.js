import { areEqualIDs } from '#utils/helpers.js';
import factory from '#utils/handlerFactory.js';
import { isEmpty } from '#utils/arrays.js';
import { slugify } from '#utils/strings.js';

import Lesson from '../models/Lesson.js';
import Slide from '../models/Slide.js';
import Questionnaire from '../models/Questionnaire.js';

export const getAllSlides = async (req, res) => {
  const lesson = await factory.getOne(Lesson, req, { paramId: 'lessonId' });

  const data = await factory.getAll(Slide, req, {
    filter: {
      lesson: lesson._id,
      deleted: false,
    },
    fields: ['title', 'enabled', 'type', 'questionnaire'],
  });

  const sortedSlides = lesson.slides.map((slideId) =>
    data.items.find((slide) => areEqualIDs(slide._id, slideId))
  );

  res.status(200).json({
    count: data.count,
    items: sortedSlides,
  });
};

export const getSlide = async (req, res) => {
  const data = await factory.getOne(Slide, req, {
    paramId: 'slideId',
  });

  res.status(200).json(data);
};

export const createSlide = async (req, res) => {
  const lesson = await factory.getOne(Lesson, req, { paramId: 'lessonId' });

  // Ensure slide has a valid slug within its siblings (or create one from its name)
  const slug = await Slide.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    lesson
  );

  // Create the slide
  const slide = await Slide.create({
    ...req.body,
    slug,
    lesson: lesson._id,
    course: lesson.course,
    provider: lesson.provider,
  });

  if (req.body.type === 'questionnaire') {
    const questionnaire = await Questionnaire.create({
      slide: slide._id,
      questions: [],
      course: lesson.course,
      lesson: lesson._id,
      provider: lesson.provider,
    });

    // Add questionnaire to slide
    slide.questionnaire = questionnaire._id;
    await slide.save();
  }

  // Add slide to lesson
  lesson.slides = [...lesson.slides, slide.id];
  await lesson.save();

  res.status(200).json(slide);
};

export const updateSlide = async (req, res) => {
  const slide = await factory.getOne(Slide, req, {
    paramId: 'slideId',
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Slide.getAvailableSlug(
        slugify(req.body.slug),
        slide.lesson,
        slide._id
      )
    : slide.slug;

  // Update slide's data
  const updatedSlide = await Slide.findByIdAndUpdate(
    slide._id,
    {
      ...req.body,
      slug,
      lesson: slide.lesson, // prevents moving a slide to different lesson
      course: slide.course, // prevents moving a slide to different course
      provider: slide.provider, // prevents moving a slide to different provider
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedSlide);
};

export const deleteSlide = async (req, res) => {
  const deletedSlide = await factory.deleteOne(Slide, req, {
    paramId: 'slideId',
  });

  // Remove slide from lesson
  const lesson = await Lesson.findById(deletedSlide.lesson, 'slides');
  if (lesson && !isEmpty(lesson.slides)) {
    lesson.slides = lesson.slides.filter(
      (slide) => !areEqualIDs(slide._id, deletedSlide._id)
    );
    await lesson.save();
  }

  res.status(200).json(deletedSlide);
};

export const restoreSlide = async (req, res) => {
  const data = await factory.restoreOne(Slide, req, {
    paramId: 'slideId',
  });

  res.status(200).json(data);
};

export const disableSlide = async (req, res) => {
  const data = await factory.disableOne(Slide, req, {
    paramId: 'slideId',
  });

  res.status(200).json(data);
};

export const enableSlide = async (req, res) => {
  const data = await factory.enableOne(Slide, req, {
    paramId: 'slideId',
  });

  res.status(200).json(data);
};

export default {
  getAllSlides,
  getSlide,
  createSlide,
  updateSlide,
  deleteSlide,
  restoreSlide,
  disableSlide,
  enableSlide,
};
