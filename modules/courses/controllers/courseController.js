import mongoose from 'mongoose';

import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';
import { getCount } from '#utils/aggregations.js';

import {
  getIdsFilter,
  getPagination,
  getParam,
  getSearch,
  getStatusFilter,
} from '#utils/request.js';

import getSortFilter from '#utils/api/sort/filters.js';
import Course from '../models/Course.js';
import Provider from '../models/Provider.js';
import courseService from '../services/courseService.js';
import Collection from '../models/Collection.js';

export const getAllCourses = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);
  const statusFilter = getStatusFilter(req);
  const courseIdsFilter = getIdsFilter(req);
  const { searchFilter } = getSearch(req, ['title']);

  const modesFilter = {};
  if (req.query.modes) {
    const modesArray = req.query.modes
      .split(',')
      .filter((mode) =>
        ['autonomous', 'supervised', 'correspondence'].includes(mode)
      );
    if (modesArray.length > 0) {
      modesFilter.$or = [];
      modesArray.forEach((mode) => {
        modesFilter.$or.push({ [`${mode}Course`]: true });
      });
    }
  }

  const filter = {
    $or: [
      { provider: new mongoose.Types.ObjectId(providerId) },
      { [`providers.${providerId}.sharingEnabled`]: true },
    ],
    $and: [courseIdsFilter, searchFilter],
  };

  const sortOptions = getSortFilter({
    sort: req.query.sortBy,
    order: req.query.sortOrder,
    defaultFields: ['title'],
  });
  const aggregation = Course.aggregate()
    .match(filter)
    .addFields({
      foreignCourse: {
        $cond: {
          if: { $eq: ['$provider', new mongoose.Types.ObjectId(providerId)] },
          then: false,
          else: true,
        },
      },
    })
    .addFields({
      enabled: {
        $cond: [
          '$foreignCourse',
          `$providers.${providerId}.enabled`,
          '$enabled',
        ],
      },
    })
    .match(statusFilter)
    // Add course mode. For shared courses, use the overwritten value (if set), else use the course value
    .addFields(
      [
        'onlineCourse',
        'autonomousCourse',
        'supervisedCourse',
        'correspondenceCourse',
      ].reduce((acc, mode) => {
        acc[mode] = {
          $cond: [
            {
              $and: [
                { $eq: ['$foreignCourse', true] },
                {
                  $ne: [
                    { $type: `$providers.${providerId}.${mode}` },
                    'missing',
                  ],
                },
              ],
            },
            `$providers.${providerId}.${mode}`,
            `$${mode}`,
          ],
        };
        return acc;
      }, {})
    )
    // Check if the onlineCourse flag is enabled
    .addFields(
      ['autonomousCourse', 'supervisedCourse'].reduce(
        (acc, mode) => ({
          ...acc,
          [mode]: {
            $cond: [{ $and: ['$onlineCourse', `$${mode}`] }, true, false],
          },
        }),
        {}
      )
    )
    .match(modesFilter);

  // Get total items
  const totalCourses = await getCount(aggregation);

  aggregation
    .sort(sortOptions)
    .skip(skip)
    .limit(limit)
    .lookup({
      from: 'coursestatuses',
      let: { courseId: '$_id' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$course', '$$courseId'] },
            deleted: false,
            provider: new mongoose.Types.ObjectId(providerId),
          },
        },
        { $project: { _id: 1, finishedAt: 1 } },
      ],
      as: 'courseStatuses',
    })
    .addFields({
      coursesCompleted: {
        $filter: {
          input: '$courseStatuses',
          as: 'courseStatus',
          cond: { $ne: ['$$courseStatus.finishedAt', null] },
        },
      },
      activeStudents: {
        $filter: {
          input: '$courseStatuses',
          as: 'courseStatus',
          cond: { $eq: ['$$courseStatus.finishedAt', null] },
        },
      },
    })
    .project({
      id: '$_id',
      foreignCourse: 1,
      enabled: 1,
      deleted: 1,
      title: 1,
      language: 1,
      supervisedCourse: 1,
      autonomousCourse: 1,
      correspondenceCourse: 1,
      images: 1,
      entity: 1,
      provider: 1,
      providers: 1,
      lessons: { $size: '$lessons' },
      activeStudents: { $size: '$activeStudents' },
      coursesCompleted: { $size: '$coursesCompleted' },
    })
    .collation({ locale: 'en' });

  const courses = await aggregation;

  res.status(200).json({
    count: totalCourses,
    items: courses,
  });
};

export const getCourse = async (req, res) => {
  const data = await factory.getOne(Course, req, {
    paramId: 'courseId',
    populate: [
      {
        path: 'provider',
        select: 'title',
      },
    ],
  });

  const collections = await Collection.find(
    {
      courses: { $in: data._id },
    },
    { title: 1 }
  );

  const course = data.toObject();

  res.status(200).json({
    ...course,
    collections,
  });
};

export const getCourseContent = async (req, res) => {
  const data = await factory.getOne(Course, req, {
    paramId: 'courseId',
    fields: ['lessons'],
    populate: [
      {
        path: 'lessons',
        match: { deleted: false },
        select: ['title', 'slides', 'enabled', 'preview'],
        populate: [
          {
            path: 'slides',
            match: { deleted: false },
            select: ['title', 'enabled', 'type', 'requiresReview', 'preview'],
          },
        ],
      },
    ],
  });

  res.status(200).json(data.lessons);
};

export const createCourse = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const provider = await Provider.findById(providerId, '_id language');

  // Ensure course has a valid slug within its siblings (or create one from its name)
  const slug = await Course.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    provider
  );

  // Create the course
  const course = await Course.create({
    ...req.body,
    language: req.body.language ? req.body.language : provider.language,
    entity: req.entity._id,
    provider,
    slug,
  });

  res.status(200).json(course);
};

export const cloneCourse = async (req, res) => {
  const courseId = getParam(req, 'courseId');
  const { title, provider } = req.body;
  const course = await courseService.cloneCourse({
    courseId,
    providerId: provider,
    title,
  });

  res.status(200).json(course);
};

export const updateCourse = async (req, res) => {
  const course = await factory.getOne(Course, req, {
    paramId: 'courseId',
  });

  // Update course's settings for shared courses
  let sharedCoursesSettings;
  const providerId = req.query.provider;
  if (providerId && req.body.providers) {
    sharedCoursesSettings = {
      ...(course.providers || {}),
      [providerId]: {
        ...course.providers[providerId],
        ...req.body.providers[providerId],
      },
    };
  }

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Course.getAvailableSlug(
        slugify(req.body.slug),
        course.entity,
        course._id
      )
    : course.slug;

  // Update course's data
  const updatedCourse = await Course.findByIdAndUpdate(
    course._id,
    {
      ...req.body,
      ...(sharedCoursesSettings && { providers: sharedCoursesSettings }),
      slug,
      provider: course.provider, // prevents moving a course to different provider
      entity: course.entity, // prevents moving a course to different entity
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedCourse);
};

export const deleteCourse = async (req, res) => {
  const data = await factory.deleteOne(Course, req, {
    paramId: 'courseId',
  });

  res.status(200).json(data);
};

export const hardDeleteCourse = async (req, res) => {
  const courseId = getParam(req, 'courseId');

  const { results, error, status } =
    await courseService.hardDeleteCourse(courseId);

  if (error) return res.status(status).json({ error });

  res.status(200).json(results);
};

export const restoreCourse = async (req, res) => {
  const data = await factory.restoreOne(Course, req, {
    paramId: 'courseId',
  });

  res.status(200).json(data);
};

export const disableCourse = async (req, res) => {
  let data;

  // Disable course for the provided provider
  const providerId = req.query.provider;
  if (providerId) {
    const course = await factory.getOne(Course, req, { paramId: 'courseId' });
    const provider = await Provider.findById(course.provider);
    if (provider && course.providers[providerId]) {
      course.providers = {
        ...course.providers,
        [providerId]: {
          ...course.providers[providerId],
          enabled: false,
        },
      };
      data = await course.save();
    }
  }

  // Disable course
  else {
    data = await factory.disableOne(Course, req, { paramId: 'courseId' });
  }

  res.status(200).json(data);
};

export const enableCourse = async (req, res) => {
  let data;

  // Enable course for the provided provider
  const providerId = req.query.provider;
  if (providerId) {
    const course = await factory.getOne(Course, req, { paramId: 'courseId' });
    const provider = await Provider.findById(course.provider);
    if (provider && course.providers[providerId]) {
      course.providers = {
        ...course.providers,
        [providerId]: {
          ...course.providers[providerId],
          enabled: true,
        },
      };
      data = await course.save();
    }
  }

  // Enable course
  else {
    data = await factory.enableOne(Course, req, { paramId: 'courseId' });
  }

  res.status(200).json(data);
};

export default {
  getAllCourses,
  getCourseContent,
  getCourse,
  createCourse,
  cloneCourse,
  updateCourse,
  deleteCourse,
  hardDeleteCourse,
  restoreCourse,
  disableCourse,
  enableCourse,
};
