import mongoose from 'mongoose';

import { buildCsvContent } from '#utils/csv.js';
import { t } from '#utils/translator.js';
import { getCount, getDateFilter } from '#utils/aggregations.js';
import { isValidObjectId } from '#utils/api/mongoose/id.js';
import { areEqualIDs } from '#utils/helpers.js';
import { endOfDay, startOfDay } from '#utils/dates.js';
import {
  getPagination,
  getParam,
  getSearch,
  getStatusFilter,
} from '#utils/request.js';

import Course from '../models/Course.js';
import CourseStatus from '../models/CourseStatus.js';

import providerService from '../services/providerService.js';
import studentStatisticsService from '../services/studentStatisticsService.js';
import Student from '../models/Student.js';
import getStudentName from '../helpers/getStudentName.js';
import StudentActivity from '../models/StudentActivity.js';
import QuestionnaireStatus from '../models/QuestionnaireStatus.js';
import LessonStatus from '../models/LessonStatus.js';
import getAdvisorIds from '../helpers/getAdvisorIds.js';
import studentService from '../services/studentService.js';

export const getCourseEnrolmentStats = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);

  const { dateFrom, dateTo, format } = req.query;
  const dateFilter = getDateFilter(dateFrom, dateTo, 'startedAt');
  const courseIds = req.query.courses ? req.query.courses.split(',') : [];
  const validCourseIds = courseIds
    .filter((id) => isValidObjectId(id))
    .map((id) => new mongoose.Types.ObjectId(id));

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  // Add course mode filter
  const courseModes = req.query.courseModes
    ? req.query.courseModes.split(',')
    : !isCorrespondenceCoursesEnabled
      ? ['autonomous', 'supervised']
      : [];

  const enrolmentsAggregation = CourseStatus.aggregate().match({
    provider: new mongoose.Types.ObjectId(providerId),
    ...dateFilter,
    ...(validCourseIds.length > 0 && { course: { $in: validCourseIds } }),
    ...(courseModes.length > 0 && { mode: { $in: courseModes } }),
  });

  if (format === 'csv') {
    // Get site language
    const language =
      await providerService.getSiteLanguageByProviderId(providerId);

    // Get registrations (fast query)
    let enrolments = await enrolmentsAggregation
      .sort({ createdAt: 1 })
      .project({
        student: 1,
        course: 1,
        createdAt: {
          $dateToString: {
            format: '%Y-%m-%d %H:%M',
            date: '$createdAt',
          },
        },
        mode: 1,
      });

    // Get distinct courses and students
    const { distinctCourseIds, distinctStudentIds } = enrolments.reduce(
      (acc, enrolment) => {
        const { course, student } = enrolment;
        if (!acc.distinctCourseIds.includes(course))
          acc.distinctCourseIds.push(course);
        if (!acc.distinctStudentIds.includes(student))
          acc.distinctStudentIds.push(student);
        return acc;
      },
      { distinctCourseIds: [], distinctStudentIds: [] }
    );

    const courses = await Course.find({ _id: { $in: distinctCourseIds } });
    const students = await Student.find({ _id: { $in: distinctStudentIds } });

    // Build object to be exported
    enrolments = enrolments.map((enrolment) => {
      const {
        student: studentId,
        course: courseId,
        createdAt,
        mode,
      } = enrolment;
      const course = courses.find((c) => areEqualIDs(c._id, courseId));
      const student = students.find((s) => areEqualIDs(s._id, studentId));

      const { username, firstName, lastName } = getStudentName({
        student,
        online: ['supervised', 'autonomous'].includes(mode),
      });

      return {
        username,
        firstName,
        lastName,
        course: course?.title || 'n/a',
        startedAt: createdAt,
        courseMode: t(language, `courseMode_${mode}`),
      };
    });

    // Build CSV
    const csvContent = buildCsvContent(enrolments, language);

    // Set headers and send CSV
    res.setHeader('Content-Type', 'text/csv');
    res.send(csvContent);
    return;
  }

  // Get total enrolments
  const totalEnrolments = await getCount(enrolmentsAggregation);

  // Get course statuses
  const courseStatuses = await enrolmentsAggregation
    .sort({ startedAt: -1 })
    .skip(skip)
    .limit(limit)
    .lookup({
      from: 'coursestudents',
      let: { studentId: '$student' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$studentId'] },
          },
        },
        {
          $project: {
            username: 1,
            deleted: 1,
            enabled: 1,
            webUser: 1,
            profile: 1,
            ...(isCorrespondenceCoursesEnabled && {
              firstName: 1,
              lastName: 1,
            }),
          },
        },
      ],
      as: 'student',
    })
    .lookup({
      from: 'courses',
      let: { courseId: '$course' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$courseId'] },
          },
        },
        { $project: { title: 1 } },
      ],
      as: 'course',
    })
    .addFields({
      student: { $arrayElemAt: ['$student', 0] },
      course: { $arrayElemAt: ['$course', 0] },
    })
    .project({
      _id: 1,
      student: 1,
      course: 1,
      startedAt: 1,
      mode: 1,
    });

  res.status(200).json({
    count: totalEnrolments,
    items: courseStatuses,
  });
};

export const getCourseCompletionStats = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);

  const { dateFrom, dateTo, format } = req.query;
  const dateFilter = getDateFilter(dateFrom, dateTo, 'finishedAt');
  const courseIds = req.query.courses ? req.query.courses.split(',') : [];
  const validCourseIds = courseIds
    .filter((id) => isValidObjectId(id))
    .map((id) => new mongoose.Types.ObjectId(id));

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  // Add course mode filter
  const courseModes = req.query.courseModes
    ? req.query.courseModes.split(',')
    : !isCorrespondenceCoursesEnabled
      ? ['autonomous', 'supervised']
      : [];

  const completionsAggregation = CourseStatus.aggregate().match({
    provider: new mongoose.Types.ObjectId(providerId),
    ...dateFilter,
    ...(validCourseIds.length > 0 && { course: { $in: validCourseIds } }),
    ...(courseModes.length > 0 && { mode: { $in: courseModes } }),
  });

  if (format === 'csv') {
    // Get site language
    const language =
      await providerService.getSiteLanguageByProviderId(providerId);

    // Get registrations (fast query)
    let completions = await completionsAggregation
      .sort({ finishedAt: 1 })
      .project({
        student: 1,
        course: 1,
        createdAt: {
          $dateToString: {
            format: '%Y-%m-%d %H:%M',
            date: '$createdAt',
          },
        },
        finishedAt: {
          $dateToString: {
            format: '%Y-%m-%d %H:%M',
            date: '$finishedAt',
          },
        },
        mode: 1,
      });

    // Get distinct courses and students
    const { distinctCourseIds, distinctStudentIds } = completions.reduce(
      (acc, enrolment) => {
        const { course, student } = enrolment;
        if (!acc.distinctCourseIds.includes(course))
          acc.distinctCourseIds.push(course);
        if (!acc.distinctStudentIds.includes(student))
          acc.distinctStudentIds.push(student);
        return acc;
      },
      { distinctCourseIds: [], distinctStudentIds: [] }
    );

    const courses = await Course.find({ _id: { $in: distinctCourseIds } });
    const students = await Student.find({ _id: { $in: distinctStudentIds } });

    // Build object to be exported
    completions = completions.map((completion) => {
      const {
        student: studentId,
        course: courseId,
        createdAt,
        finishedAt,
        mode,
      } = completion;
      const course = courses.find((c) => areEqualIDs(c._id, courseId));
      const student = students.find((s) => areEqualIDs(s._id, studentId));

      const { username, firstName, lastName } = getStudentName({
        student,
        online: ['supervised', 'autonomous'].includes(mode),
      });

      return {
        username,
        firstName,
        lastName,
        course: course?.title || 'n/a',
        startedAt: createdAt,
        finishedAt: finishedAt,
        courseMode: t(language, `courseMode_${mode}`),
      };
    });

    // Build CSV
    const csvContent = buildCsvContent(completions, language);

    // Set headers and send CSV
    res.setHeader('Content-Type', 'text/csv');
    res.send(csvContent);
    return;
  }

  // Get total completions
  const totalCompletions = await getCount(completionsAggregation);

  // Get course statuses
  const courseStatuses = await completionsAggregation
    .sort({ finishedAt: -1 })
    .skip(skip)
    .limit(limit)
    .lookup({
      from: 'coursestudents',
      let: { studentId: '$student' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$studentId'] },
          },
        },
        {
          $project: {
            username: 1,
            deleted: 1,
            enabled: 1,
            webUser: 1,
            profile: 1,
            ...(isCorrespondenceCoursesEnabled && {
              firstName: 1,
              lastName: 1,
            }),
          },
        },
      ],
      as: 'student',
    })
    .lookup({
      from: 'courses',
      let: { courseId: '$course' },
      pipeline: [
        {
          $match: {
            $expr: { $eq: ['$_id', '$$courseId'] },
          },
        },
        { $project: { title: 1 } },
      ],
      as: 'course',
    })
    .addFields({
      student: { $arrayElemAt: ['$student', 0] },
      course: { $arrayElemAt: ['$course', 0] },
    })
    .project({
      _id: 1,
      student: 1,
      course: 1,
      startedAt: 1,
      finishedAt: 1,
      mode: 1,
    });

  res.status(200).json({
    count: totalCompletions,
    items: courseStatuses,
  });
};

export const getCourseActivityStats = async (req, res) => {
  const providerId = getParam(req, 'providerId');
  const { limit, skip } = getPagination(req);
  const statusFilter = getStatusFilter(req);
  const { searchFilter } = getSearch(req, ['title']);

  const advisorIds = getAdvisorIds(req);

  // active: last 30 days, 6 months, 1 year
  // const { active } = req.query;
  // const dateFilter = getDateFilter(dateFrom, dateTo, 'finishedAt');

  const dateFrom = startOfDay(req.query.dateFrom).toJSDate();
  const dateTo = endOfDay(req.query.dateTo).toJSDate();

  const isCorrespondenceCoursesEnabled =
    await providerService.isCorrespondenceCoursesEnabled(providerId);

  const courseModes = !isCorrespondenceCoursesEnabled
    ? ['autonomous', 'supervised']
    : ['autonomous', 'supervised', 'correspondence'];

  const coursesAggregation = Course.aggregate()
    .match({
      $or: [
        { provider: new mongoose.Types.ObjectId(providerId) },
        { [`providers.${providerId}.sharingEnabled`]: true },
      ],
      ...searchFilter,
    })
    .addFields({
      foreignCourse: {
        $cond: {
          if: { $eq: ['$provider', new mongoose.Types.ObjectId(providerId)] },
          then: false,
          else: true,
        },
      },
    })
    .addFields({
      enabled: {
        $cond: [
          '$foreignCourse',
          `$providers.${providerId}.enabled`,
          '$enabled',
        ],
      },
    })
    .match(statusFilter);
  // console.log('------------------------------');

  // Get total items
  // console.time('getCoursesCount');
  const totalCourses = await getCount(coursesAggregation);
  // console.timeEnd('getCoursesCount');

  // Get courses
  // console.time('getCourses');
  const courses = await coursesAggregation
    .sort({ title: 1 })
    .skip(skip)
    .limit(limit)
    .addFields(
      // Add course mode. For shared courses, use the overwritten value (if set), else use the course value
      [
        'onlineCourse',
        'autonomousCourse',
        'supervisedCourse',
        'correspondenceCourse',
      ].reduce((acc, mode) => {
        acc[mode] = {
          $cond: [
            {
              $and: [
                { $eq: ['$foreignCourse', true] },
                {
                  $ne: [
                    { $type: `$providers.${providerId}.${mode}` },
                    'missing',
                  ],
                },
              ],
            },
            `$providers.${providerId}.${mode}`,
            `$${mode}`,
          ],
        };
        return acc;
      }, {})
    )
    .addFields(
      // Check if the onlineCourse flag is enabled
      ['autonomousCourse', 'supervisedCourse'].reduce(
        (acc, mode) => ({
          ...acc,
          [mode]: {
            $cond: [{ $and: ['$onlineCourse', `$${mode}`] }, true, false],
          },
        }),
        {}
      )
    )
    .addFields({
      enabled: {
        $cond: [
          '$foreignCourse',
          `$providers.${providerId}.enabled`,
          '$enabled',
        ],
      },
    })
    .project({
      _id: 1,
      title: 1,
      enabled: 1,
      correspondenceCourse: 1,
      autonomousCourse: 1,
      supervisedCourse: 1,
    })
    .collation({ locale: 'en' });
  // console.timeEnd('getCourses');

  const courseIds = courses.map((course) => course._id);

  // Get all students of the advisors
  // console.time('getStudentIds');
  const advisorStudentIds = advisorIds?.length
    ? await studentService.getStudentIds({
        providerId,
        advisorIds,
      })
    : null;
  // console.timeEnd('getStudentIds');

  // Get students that were active during the last 30 days
  // console.time('getActiveStudents');
  const activeStudentIds = await studentStatisticsService.getActiveStudents({
    providerId,
    dateTo,
    dayDiff: -30,
    studentIds: advisorStudentIds,
    courseIds,
  });
  // console.timeEnd('getActiveStudents');

  // Get course statuses (newEnrolments, completions, enrolledStudents, activeEnrolledStudents)
  // console.time('getEnrolments');
  const enrolments = await getEnrolments({
    providerId,
    isCorrespondenceCoursesEnabled,
    dateFrom,
    dateTo,
    courseIds,
    courseModes,
    studentIds: advisorStudentIds,
    activeStudentIds,
  });
  // console.timeEnd('getEnrolments');

  // Get processed questionnaires (submittedQuestionnaires, correctedQuestionnaires)
  // console.time('getProcessedOnlineQuestionnaires');
  const processedOnlineQuestionnaires = await getProcessedOnlineQuestionnaires({
    providerId,
    dateFrom,
    dateTo,
    studentIds: advisorStudentIds,
    courseIds,
  });
  // console.timeEnd('getProcessedOnlineQuestionnaires');

  // Get processed correspondence courses (submittedQuestionnaires, correctedQuestionnaires)
  const processedCorrespondenceLessons =
    await getProcessedCorrespondenceLessons({
      providerId,
      dateFrom,
      dateTo,
      courseIds,
      studentIds: advisorStudentIds,
    });

  // Get course mode changes (autonomous -> supervised / supervised -> autonomous)
  // console.time('getCourseModeChanges');
  const courseModeChanges = await getCourseModeChanges({
    providerId,
    dateFrom,
    dateTo,
    courseIds,
    studentIds: advisorStudentIds,
  });
  // console.log(courseModeChanges);
  // console.timeEnd('getCourseModeChanges');

  // console.log('------------------------------');

  const courseActivityStats = courses.map((course) => ({
    // course
    course,

    // newEnrolments, completions, enrolledStudents, activeEnrolledStudents
    ...(enrolments[course._id] || {}),

    // submittedOnlineQuestionnaires, correctedOnlineQuestionnaires
    ...(processedOnlineQuestionnaires[course._id] || {}),

    // submittedCorrespondenceLessons, correctedCorrespondenceLessons
    ...(processedCorrespondenceLessons[course._id] || {}),

    // courseModeChanges
    ...(courseModeChanges[course._id] || {}),
  }));

  res.status(200).json({
    count: totalCourses,
    items: courseActivityStats,
  });
};

async function getEnrolments({
  providerId,
  dateFrom,
  dateTo,
  courseIds,
  courseModes,
  studentIds,
  activeStudentIds,
}) {
  const courseStatuses = await CourseStatus.aggregate()
    .match({
      provider: new mongoose.Types.ObjectId(providerId),
      deleted: false,
      enabled: true,
      mode: { $in: courseModes },
      startedAt: { $lte: dateTo },
      $or: [{ finishedAt: null }, { finishedAt: { $gte: dateFrom } }],
      course: { $in: courseIds },
      ...(studentIds && { student: { $in: studentIds } }),
    })
    .group({
      _id: { course: '$course' },
      // enrolledStudents
      ...courseModes.reduce((acc, mode) => {
        acc[`enrolledStudents_${mode}`] = {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$mode', mode] },
                  { $lte: ['$startedAt', dateTo] },
                  {
                    $or: [
                      { $eq: ['$finishedAt', null] },
                      { $gt: ['$finishedAt', dateTo] },
                    ],
                  },
                ],
              },
              1,
              0,
            ],
          },
        };
        return acc;
      }, {}),
      // activeEnrolledStudents
      ...courseModes
        .filter((mode) => courseModes.includes(mode))
        .reduce((acc, mode) => {
          acc[`activeEnrolledStudents_${mode}`] = {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$mode', mode] },
                    { $in: ['$student', activeStudentIds] },
                  ],
                },
                1,
                0,
              ],
            },
          };
          return acc;
        }, {}),
      // newEnrolments
      ...courseModes
        .filter((mode) => courseModes.includes(mode))
        .reduce((acc, mode) => {
          acc[`newEnrolments_${mode}`] = {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$mode', mode] },
                    { $gte: ['$startedAt', dateFrom] },
                  ],
                },
                1,
                0,
              ],
            },
          };
          return acc;
        }, {}),
      // completions
      ...courseModes
        .filter((mode) => courseModes.includes(mode))
        .reduce((acc, mode) => {
          acc[`completions_${mode}`] = {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$mode', mode] },
                    { $lte: ['$finishedAt', dateTo] },
                    { $gte: ['$finishedAt', dateFrom] },
                  ],
                },
                1,
                0,
              ],
            },
          };
          return acc;
        }, {}),
    })
    .project({
      _id: 0,
      courseId: '$_id.course',
      enrolledStudents: {
        autonomous: '$enrolledStudents_autonomous',
        supervised: '$enrolledStudents_supervised',
        correspondence: '$enrolledStudents_correspondence',
      },
      activeEnrolledStudents: {
        autonomous: '$activeEnrolledStudents_autonomous',
        supervised: '$activeEnrolledStudents_supervised',
        correspondence: '$activeEnrolledStudents_correspondence',
      },
      newEnrolments: {
        autonomous: '$newEnrolments_autonomous',
        supervised: '$newEnrolments_supervised',
        correspondence: '$newEnrolments_correspondence',
      },
      completions: {
        autonomous: '$completions_autonomous',
        supervised: '$completions_supervised',
        correspondence: '$completions_correspondence',
      },
    });

  const courseStatusesObject = courseStatuses.reduce((acc, courseStatus) => {
    const { courseId } = courseStatus;
    delete courseStatus.courseId;
    acc[courseId] = courseStatus;
    return acc;
  }, {});

  return courseStatusesObject;
}

async function getCourseModeChanges({
  providerId,
  dateFrom,
  dateTo,
  courseIds,
  studentIds,
}) {
  const courseModeChanges = await StudentActivity.aggregate()
    .match({
      provider: new mongoose.Types.ObjectId(providerId),
      type: 'courseModeChanged',
      deleted: false,
      enabled: true,
      course: { $in: courseIds },
      ...(studentIds && { student: { $in: studentIds } }),
      ...getDateFilter(dateFrom, dateTo, 'date'),
    })
    .group({
      _id: {
        course: '$course',
        newMode: '$value.mode',
      },
      count: { $sum: 1 },
    })
    .group({
      _id: '$_id.course',
      autonomous: {
        $sum: {
          $cond: [{ $eq: ['$_id.newMode', 'autonomous'] }, '$count', 0],
        },
      },
      supervised: {
        $sum: {
          $cond: [{ $eq: ['$_id.newMode', 'supervised'] }, '$count', 0],
        },
      },
    })
    .project({
      _id: 0,
      courseId: '$_id',
      courseModeChanges: {
        autonomous: '$autonomous',
        supervised: '$supervised',
      },
    });

  const courseModeChangesObject = courseModeChanges.reduce(
    (acc, courseModeChange) => {
      const { courseId } = courseModeChange;
      delete courseModeChange.courseId;
      acc[courseId] = courseModeChange;
      return acc;
    },
    {}
  );
  return courseModeChangesObject;
}

async function getProcessedCorrespondenceLessons({
  providerId,
  dateFrom,
  dateTo,
  courseIds,
  studentIds,
}) {
  const lessonStatuses = await LessonStatus.aggregate()
    .match({
      provider: new mongoose.Types.ObjectId(providerId),
      course: { $in: courseIds },
      ...(studentIds && { student: { $in: studentIds } }),
    })
    .group({
      _id: { course: '$course' },
      submittedCorrespondenceLessons: {
        $sum: {
          $cond: [
            {
              $and: [
                { $gte: ['$submittedAt', dateFrom] },
                { $lte: ['$submittedAt', dateTo] },
              ],
            },
            1,
            0,
          ],
        },
      },
      correctedCorrespondenceLessons: {
        $sum: {
          $cond: [
            {
              $and: [
                { $gte: ['$correctedAt', dateFrom] },
                { $lte: ['$correctedAt', dateTo] },
              ],
            },
            1,
            0,
          ],
        },
      },
    });

  const lessonStatusesObject = lessonStatuses.reduce((acc, lessonStatus) => {
    const { _id } = lessonStatus;
    delete lessonStatus.courseId;
    acc[_id.course] = lessonStatus;
    return acc;
  }, {});

  return lessonStatusesObject;
}

async function getProcessedOnlineQuestionnaires({
  providerId,
  dateFrom,
  dateTo,
  courseIds,
  studentIds,
}) {
  const questionnaireStatuses = await QuestionnaireStatus.aggregate()
    .match({
      provider: new mongoose.Types.ObjectId(providerId),
      course: { $in: courseIds },
      $or: [
        {
          submittedAt: { $gte: dateFrom, $lte: dateTo },
        },
        {
          correctedAt: { $gte: dateFrom, $lte: dateTo },
        },
      ],
      ...(studentIds && { student: { $in: studentIds } }),
    })
    .group({
      _id: { course: '$course' },
      // submittedQuestionnaires
      ...['autonomous', 'supervised'].reduce((acc, courseMode) => {
        const reviewRequested = courseMode === 'supervised';
        acc[`submittedQuestionnaires_${courseMode}`] = {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$reviewRequested', reviewRequested] },
                  { $gte: ['$submittedAt', dateFrom] },
                  { $lte: ['$submittedAt', dateTo] },
                ],
              },
              1,
              0,
            ],
          },
        };
        return acc;
      }, {}),
      // correctedQuestionnaires
      correctedQuestionnairesSupervised: {
        $sum: {
          $cond: [
            {
              $and: [
                { $eq: ['$reviewRequested', true] },
                { $gte: ['$correctedAt', dateFrom] },
                { $lte: ['$correctedAt', dateTo] },
              ],
            },
            1,
            0,
          ],
        },
      },
    })
    .project({
      _id: 0,
      courseId: '$_id.course',
      submittedOnlineQuestionnaires: {
        autonomous: '$submittedQuestionnaires_autonomous',
        supervised: '$submittedQuestionnaires_supervised',
      },
      correctedOnlineQuestionnaires: {
        supervised: '$correctedQuestionnairesSupervised',
      },
    });

  const questionnaireStatusesObject = questionnaireStatuses.reduce(
    (acc, questionnaireStatus) => {
      const { courseId } = questionnaireStatus;
      delete questionnaireStatus.courseId;
      acc[courseId] = questionnaireStatus;
      return acc;
    },
    {}
  );

  return questionnaireStatusesObject;
}

export default {
  getCourseEnrolmentStats,
  getCourseCompletionStats,
  getCourseActivityStats,
};
