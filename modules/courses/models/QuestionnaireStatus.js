import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const questionnaireStatusSchema = SchemaFactory({
  student: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseStudent',
  },
  course: {
    type: mongoose.Types.ObjectId,
    ref: 'Course',
  },
  questionnaire: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseQuestionnaire',
  },
  courseStatus: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseStatus',
  },
  savedAt: {
    type: Date,
    default: null,
  },
  submittedAt: {
    type: Date,
    default: null,
  },
  correctedAt: {
    type: Date,
    default: null,
  },
  answers: {
    type: mongoose.SchemaTypes.Mixed,
  },
  reviewRequested: {
    type: Boolean,
  },
  advisor: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseAdvisor',
  },
  advisorNotes: {
    type: mongoose.SchemaTypes.Mixed,
  },
  advisorNotesSavedAt: {
    type: Date,
  },
  advisorNotesRead: {
    type: Boolean,
  },
  provider: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseProvider',
  },
});

questionnaireStatusSchema.index({ student: 1 });
questionnaireStatusSchema.index({ advisor: 1 });
questionnaireStatusSchema.index({ course: 1 });
questionnaireStatusSchema.index({ questionnaire: 1 });
questionnaireStatusSchema.index({ provider: 1 });
questionnaireStatusSchema.index({ savedAt: 1 });
questionnaireStatusSchema.index({ submittedAt: 1 });
questionnaireStatusSchema.index({ courseStatus: 1 });
questionnaireStatusSchema.index({ correctedAt: 1 });
questionnaireStatusSchema.index({ reviewRequested: 1 });
questionnaireStatusSchema.index({ advisorNotesRead: 1 });

// Index for lookup questionnaire statuses (getAllCourseStatuses)
questionnaireStatusSchema.index({
  courseStatus: 1,
  submittedAt: 1,
  correctedAt: 1,
  reviewRequested: 1,
  deleted: 1,
  enabled: 1,
});

export default mongoose.model(
  'CourseQuestionnaireStatus',
  questionnaireStatusSchema
);
