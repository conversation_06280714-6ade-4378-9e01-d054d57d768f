import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const studentActivitySchema = SchemaFactory({
  date: {
    type: Date,
    required: true,
  },
  provider: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseProvider',
  },
  student: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseStudent',
  },
  course: {
    type: mongoose.Types.ObjectId,
    ref: 'Course',
  },
  type: {
    type: String,
    enum: {
      values: [
        'accountCreated', // account creation date (self-registered, advisorId)
        'courseStarted', // name of the course
        'questionnaireSubmitted', // name of the course and questionnaire
        'courseModeChanged', // supervised or autonomous
        'courseCompleted', // name of the course
        'emailContact', // who, when, notes
        'phoneContact', // who, when, duration, notes
        'eventVisit', // name of the event
        'other', // any other activity
      ],
      message: 'INVALID_ENUM',
    },
    required: true,
  },
  value: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

studentActivitySchema.index({ date: 1 });
studentActivitySchema.index({ provider: 1 });
studentActivitySchema.index({ student: 1 });
studentActivitySchema.index({ course: 1 });
studentActivitySchema.index({ type: 1 });

export default mongoose.model('CourseStudentActivity', studentActivitySchema);
