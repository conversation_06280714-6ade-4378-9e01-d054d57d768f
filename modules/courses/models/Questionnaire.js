import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const QuestionSchema = new mongoose.Schema({
  enabled: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    enum: {
      values: [
        'openAnswer',
        'multipleOpenAnswers',
        'multipleChoice',
        'singleChoice',
        'fillInTheBlank',
        'scale',
        'richText',
        'sectionTitle',
      ],
      message: 'INVALID_QUESTION_TYPE',
    },
    required: true,
  },
  position: {
    type: Number,
  },
  text: {
    type: String,
    required: true,
  },
  config: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

const questionnaireSchema = SchemaFactory({
  slide: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseSlide',
  },
  questions: {
    type: [QuestionSchema],
    default: [],
  },
  questionNumbering: {
    type: Boolean,
    default: true,
  },
  requiresReview: {
    type: Boolean,
    default: false,
  },
  notifyAdvisor: {
    type: Boolean,
    default: false,
  },
  showConfirmationMessage: {
    type: Boolean,
    default: true,
  },
  format: {
    type: String,
    enum: {
      values: ['compact', 'sequential', 'conversation'],
      message: 'INVALID_QUESTIONNAIRE_FORMAT',
    },
    default: 'compact',
  },
  course: {
    type: mongoose.Types.ObjectId,
    ref: 'Course',
  },
  lesson: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseLesson',
  },
  provider: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseProvider',
  },
});

questionnaireSchema.index({ slide: 1 });
questionnaireSchema.index({ requiresReview: 1 });
questionnaireSchema.index({ notifyAdvisor: 1 });
questionnaireSchema.index({ lesson: 1 });
questionnaireSchema.index({ course: 1 });
questionnaireSchema.index({ provider: 1 });

export default mongoose.model('CourseQuestionnaire', questionnaireSchema);
