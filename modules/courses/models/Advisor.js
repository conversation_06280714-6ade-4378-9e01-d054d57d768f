import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const advisorSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  gender: {
    type: String,
    enum: {
      values: ['', 'male', 'female'],
      message: 'INVALID_ENUM',
    },
  },
  firstName: {
    type: String,
    trim: true,
  },
  lastName: {
    type: String,
    trim: true,
  },
  avatar: {
    type: mongoose.SchemaTypes.Mixed,
  },
  birthday: {
    type: Date,
  },
  address: {
    street: String,
    additionalAddress: String,
    zip: String,
    city: String,
    state: String,
    country: String,
  },
  email: {
    type: String,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
  mobile: {
    type: String,
    trim: true,
  },
  absenceMessage: {
    type: mongoose.SchemaTypes.Mixed,
  },
  backendUser: {
    type: mongoose.Types.ObjectId,
    ref: 'User',
  },
  provider: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseProvider',
  },
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
});

advisorSchema.index({ slug: 1 });
advisorSchema.index({ firstName: 1 });
advisorSchema.index({ lastName: 1 });
advisorSchema.index({ email: 1 });
advisorSchema.index({ backendUser: 1 });
advisorSchema.index({ provider: 1 });
advisorSchema.index({ entity: 1 });

advisorSchema.statics.getAvailableSlug = async function (
  slug,
  provider,
  advisorId = null
) {
  const query = { slug, provider, deleted: false };

  if (advisorId) {
    query._id = { $ne: advisorId };
  }

  const existingAdvisor = await this.findOne(query);

  return existingAdvisor ? uniquifySlug(slug) : slug;
};

export default mongoose.model('CourseAdvisor', advisorSchema);
