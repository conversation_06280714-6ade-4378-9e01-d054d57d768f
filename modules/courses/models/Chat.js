import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const ChatMember = new mongoose.Schema(
  {
    type: {
      type: 'String',
      required: true,
      enum: ['CourseAdvisor', 'CourseStudent'],
    },
    id: {
      type: mongoose.Types.ObjectId,
      required: true,
      refPath: 'members.type',
    },
  },
  { _id: false }
);

const chatSchema = SchemaFactory({
  type: {
    type: String,
    enum: {
      values: ['basic', 'group', 'channel'],
      message: 'INVALID_ENUM',
    },
    default: 'basic',
  },
  title: {
    type: String,
    trim: true,
    // required: true,
  },
  image: {
    type: mongoose.SchemaTypes.Mixed,
  },
  members: {
    type: [ChatMember],
  },
  lastMessage: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseChatMessage',
  },
  lastMessageDate: {
    type: Date,
  },
  pinned: {
    type: Boolean,
    default: false,
  },
  archived: {
    type: Boolean,
    default: false,
  },
  createdBy: {
    type: {
      type: 'String',
      enum: ['CourseAdvisor', 'CourseStudent'],
    },
    id: {
      type: mongoose.Types.ObjectId,
      refPath: 'createdBy.type',
    },
  },
  provider: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseProvider',
  },
});

chatSchema.index({ type: 1 });
chatSchema.index({ title: 1 });
chatSchema.index({ lastMessage: 1 });
chatSchema.index({ lastMessageDate: 1 });
chatSchema.index({ pinned: 1 });
chatSchema.index({ archived: 1 });
chatSchema.index({ provider: 1 });
chatSchema.index({ 'members.$**': 1 });

export default mongoose.model('CourseChat', chatSchema);
