import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const countryRegionSchema = SchemaFactory({
  type: {
    type: String,
    enum: {
      values: ['postalCode', 'region'],
      message: 'INVALID_REGION_TYPE_ENUM',
    },
    default: 'region',
  },
  config: {
    type: mongoose.SchemaTypes.Mixed,
  },
  advisor: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseAdvisor',
  },
  country: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseCountry',
  },
  provider: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseProvider',
  },
});

countryRegionSchema.index({ type: 1, country: 1 });
countryRegionSchema.index({ advisor: 1 });
countryRegionSchema.index({ country: 1 });

export default mongoose.model('CourseCountryRegion', countryRegionSchema);
