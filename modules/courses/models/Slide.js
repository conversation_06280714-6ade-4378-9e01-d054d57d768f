import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const slideSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  type: {
    type: String,
    enum: {
      values: ['content', 'questionnaire'],
      message: 'INVALID_ENUM',
    },
  },
  content: {
    type: mongoose.SchemaTypes.Mixed,
  },
  questionnaire: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseQuestionnaire',
  },
  requiresReview: {
    type: Boolean,
    default: false,
  },
  preview: {
    type: Boolean,
    default: false,
  },
  lesson: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseLesson',
  },
  course: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Course',
  },
  provider: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseProvider',
  },
});

slideSchema.index({ slug: 1 });
slideSchema.index({ title: 1 });
slideSchema.index({ questionnaire: 1 });
slideSchema.index({ requiresReview: 1 });
slideSchema.index({ preview: 1 });
slideSchema.index({ lesson: 1 });
slideSchema.index({ course: 1 });
slideSchema.index({ provider: 1 });

slideSchema.statics.getAvailableSlug = async function (
  slug,
  lesson,
  slideId = null
) {
  const query = { slug, lesson, deleted: false };

  if (slideId) {
    query._id = { $ne: slideId };
  }

  const existingSlide = await this.findOne(query);

  return existingSlide ? uniquifySlug(slug) : slug;
};

export default mongoose.model('CourseSlide', slideSchema);
