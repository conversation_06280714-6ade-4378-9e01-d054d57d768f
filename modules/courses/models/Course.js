import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const courseSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  subtitle: {
    type: String,
    trim: true,
  },
  abstract: {
    type: String,
    trim: true,
  },
  body: {
    type: mongoose.SchemaTypes.Mixed,
  },
  images: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      default: null,
      banner: null,
      poster: null,
    },
  },
  language: {
    type: String,
    required: true,
  },
  trailer: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      youtube: null,
      vimeo: null,
      jetstream: null,
    },
  },
  onlineCourse: {
    type: Boolean,
    default: false,
  },
  correspondenceCourse: {
    type: Boolean,
    default: false,
  },
  autonomousCourse: {
    type: Boolean,
    default: true,
  },
  supervisedCourse: {
    type: Boolean,
    default: true,
  },
  progression: {
    type: String,
    enum: {
      values: ['linear', 'free'],
      message: 'INVALID_PROGRESSION_ENUM',
    },
    default: 'linear',
  },
  lessons: {
    type: [mongoose.Types.ObjectId],
    ref: 'CourseLesson',
    default: [],
  },
  advisor: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseAdvisor',
  },
  provider: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseProvider',
  },
  providers: {
    type: mongoose.SchemaTypes.Mixed,
  },
  canonicalSite: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
  },
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
});

courseSchema.index(
  { title: 1, provider: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);
courseSchema.index({ slug: 1 });
courseSchema.index({ title: 1 });
courseSchema.index({ subtitle: 1 });
courseSchema.index({ onlineCourse: 1 });
courseSchema.index({ correspondenceCourse: 1 });
courseSchema.index({ autonomousCourse: 1 });
courseSchema.index({ supervisedCourse: 1 });
courseSchema.index({ language: 1 });
courseSchema.index({ provider: 1 });
courseSchema.index({ 'providers.$**': 1 });
courseSchema.index({ entity: 1 });

courseSchema.statics.getAvailableSlug = async function (
  slug,
  provider,
  courseId = null
) {
  const query = { slug, provider, deleted: false };

  if (courseId) {
    query._id = { $ne: courseId };
  }

  const existingCourse = await this.findOne(query);

  return existingCourse ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Course', courseSchema);
