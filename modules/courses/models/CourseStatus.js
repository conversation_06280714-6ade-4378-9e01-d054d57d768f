import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

/**
 * Schema for Course Status.
 *
 * This schema defines the structure of a course status document, which includes information about the course assignment, progress, activity, and position.
 *
 * @typedef {Object} CourseStatus
 *
 * @property {mongoose.Types.ObjectId} provider - Reference to the course provider.
 * @property {mongoose.Types.ObjectId} student - Reference to the course student.
 * @property {mongoose.Types.ObjectId} course - Reference to the course.
 * @property {string} mode - The mode of the course, can be 'autonomous', 'supervised', or 'correspondence'. Default is 'autonomous'.
 * @property {Date} startedAt - The date when the course was started.
 * @property {Date} finishedAt - The date when the course was finished.
 * @property {Date} lastActivity - The date of the last activity in the course.
 * @property {mongoose.Types.ObjectId[]} startedLessons - Array of references to the lessons that have been started.
 * @property {mongoose.Types.ObjectId[]} completedLessons - Array of references to the lessons that have been completed.
 * @property {mongoose.Types.ObjectId[]} startedSlides - Array of references to the slides that have been started.
 * @property {mongoose.Types.ObjectId[]} completedSlides - Array of references to the slides that have been completed.
 * @property {mongoose.Types.ObjectId} activeLesson - Reference to the currently active lesson.
 * @property {mongoose.Types.ObjectId} activeSlide - Reference to the currently active slide.
 * @property {mongoose.Types.ObjectId} reachedLesson - Reference to the lesson that has been reached, used for linear courses.
 * @property {mongoose.Types.ObjectId} advisor - Reference to the course advisor.
 * @property {mongoose.Types.ObjectId[]} slidesRead - Deprecated, replaced by startedSlides. Array of references to the slides that have been read.
 * @property {mongoose.Types.ObjectId} reachedLesson_deprecated - Deprecated, reference to the lesson that has been reached.
 */
const courseStatusSchema = SchemaFactory({
  // Course assignment
  provider: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseProvider',
    required: true,
  },
  student: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseStudent',
    required: true,
  },
  course: {
    type: mongoose.Types.ObjectId,
    ref: 'Course',
    required: true,
  },
  mode: {
    type: String,
    enum: {
      values: ['autonomous', 'supervised', 'correspondence'],
      message: 'INVALID_COURSE_STATUS_MODE',
    },
    default: 'autonomous',
    required: true,
  },
  notes: {
    type: String,
    trim: true,
  },

  // Course progress
  startedAt: {
    type: Date,
  },
  finishedAt: {
    type: Date,
  },
  lastActivity: {
    type: Date,
  },

  // Course activity
  startedLessons: {
    type: [mongoose.Types.ObjectId],
    ref: 'CourseLesson',
  },
  completedLessons: {
    type: [mongoose.Types.ObjectId],
    ref: 'CourseLesson',
  },
  startedSlides: {
    type: [mongoose.Types.ObjectId],
    ref: 'CourseLesson',
  },
  completedSlides: {
    type: [mongoose.Types.ObjectId],
    ref: 'CourseLesson',
  },

  // Course position
  activeLesson: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseLesson',
  },
  activeSlide: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseSlide',
  },
  // Only used for linear courses (course.progression === 'linear')
  // - Block access to lessons that are not yet available
  // - Should be >= activeLesson
  reachedLesson: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseLesson',
  },

  advisor: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseAdvisor',
  },

  // Deprecated fields
  slidesRead: {
    // replaced by startedSlides
    type: [mongoose.Types.ObjectId],
    ref: 'CourseSlide',
  },
  reachedLesson_deprecated: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseLesson',
  },
});

/*

  {
    // Course assignment
    provider: 123456,
    student: 123456,
    course: 123456,
    mode: 'autonomous',

    // Course status
       - Course starts when student enrolls in course
       - Course finishes when student completes last lesson
    startedAt: 01.02.2023,
    finishedAt: 10.03.2023,

    // Course activity
       - To track the last activity of the student (slide or lesson read, questionnaire submitted)
       - To remember the lesson and slide the student was reading
    lastActivity: 10.03.2023,
       - Set when a course is started, a slide or lesson is viewed, or a questionnaire is submitted
    activeLesson: 123456,
    activeSlide: 123456,

    // Preferred advisor of this course
       - Defines who will correct the questionnaire
       - Set when the course starts (1) or when the first questionnaire is corrected (2)
       - Empty for autonomous courses (except a student requires a review)
       1. student.advisor
       2. advisor that corrects first questionnaire 
    advisor: 123456,

    // Course navigation
       - To mark which slides were read in the course navigation
       - Set when a slide of type 'content' is viewed
       - Set when a slide of type 'questionnaire' is submitted (and eventually corrected)
    startedSlides: [123456, 123456, 123456], --> old: slidesRead
    completedSlides: [123456, 123456],

    // Course progression
    startedLessons: [123456, 123456, 123456],
       - To calculate course progress (2/10 lessons completed)
       - Set when the the lesson or a slide of the lesson is read
    completedLessons: [123456, 123456],
       - To know which lessons are completed
       - For gamification (e.g. to award a badge for completing a lesson)
   
    // Control course progression
       - Linear courses only (free courses are always available)
       - Block access to lessons that are not yet available
       - Reached lesson is set:
         - ???
         - when a course is started
         - when a lessons was completed
         - manually by an advisor (e.g. to allow a student to join an ongoing course)
    reachedLesson: 123456,
  }

*/

courseStatusSchema.index({ student: 1 });
courseStatusSchema.index({ course: 1 });
courseStatusSchema.index({ provider: 1 });
courseStatusSchema.index({ mode: 1 });
courseStatusSchema.index({ advisor: 1 });
courseStatusSchema.index({ startedAt: 1 });
courseStatusSchema.index({ finishedAt: 1 });
courseStatusSchema.index({ lastActivity: 1 });

// Index used when getting course content
courseStatusSchema.index({ student: 1, course: 1, enabled: 1, deleted: -1 });

export default mongoose.model('CourseStatus', courseStatusSchema);
