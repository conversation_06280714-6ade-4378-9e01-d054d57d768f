import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const lessonStatusSchema = SchemaFactory({
  student: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseStudent',
  },
  course: {
    type: mongoose.Types.ObjectId,
    ref: 'Course',
  },
  lesson: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseLesson',
  },
  courseStatus: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseStatus',
  },
  sentAt: {
    type: Date,
    default: null,
  },
  submittedAt: {
    type: Date,
    default: null,
  },
  correctedAt: {
    type: Date,
    default: null,
  },
  advisor: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseAdvisor',
  },
  advisorNotes: {
    type: String,
    trim: true,
  },
  advisorNotesSavedAt: {
    type: Date,
  },
  provider: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseProvider',
  },
});

lessonStatusSchema.index({ student: 1 });
lessonStatusSchema.index({ advisor: 1 });
lessonStatusSchema.index({ course: 1 });
lessonStatusSchema.index({ lesson: 1 });
lessonStatusSchema.index({ provider: 1 });
lessonStatusSchema.index({ sentAt: 1 });
lessonStatusSchema.index({ savedAt: 1 });
lessonStatusSchema.index({ submittedAt: 1 });
lessonStatusSchema.index({ courseStatus: 1 });
lessonStatusSchema.index({ correctedAt: 1 });

export default mongoose.model('CourseLessonStatus', lessonStatusSchema);
