import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const Sender = new mongoose.Schema(
  {
    type: {
      type: 'String',
      required: true,
      enum: ['CourseAdvisor', 'CourseStudent'],
    },
    id: {
      type: mongoose.Types.ObjectId,
      required: true,
      refPath: 'sender.type',
    },
  },
  { _id: false }
);

const messageSchema = SchemaFactory({
  sender: {
    type: Sender,
    required: true,
  },
  chat: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseChat',
    required: true,
  },
  subject: {
    type: String,
    trim: true,
  },
  text: {
    type: String,
    trim: true,
    required: true,
  },
  type: {
    type: String,
    enum: {
      values: ['text'], // 'image', 'file', 'video', 'voiceMessage', ...,
      message: 'INVALID_ENUM',
    },
    required: true,
  },
  citedMessage: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseChatMessage',
  },
  readAt: {
    type: mongoose.SchemaTypes.Mixed,
  },
  provider: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseProvider',
  },
});

messageSchema.index({ sender: 1 });
messageSchema.index({ chat: 1 });
messageSchema.index({ subject: 1 });
// messageSchema.index({ text: 1 }); TODO: Causes an error for long messages on Scalingo (MongoDB 4.0.3) 🤦‍♂️
messageSchema.index({ type: 1 });
messageSchema.index({ citedMessage: 1 });
messageSchema.index({ readAt: 1 });
messageSchema.index({ provider: 1 });

// Index for chat contact list (unreadChatMessagesAggregation)
messageSchema.index({ 'chat': 1, 'sender.type': 1 });

export default mongoose.model('CourseChatMessage', messageSchema);
