import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const countrySchema = SchemaFactory({
  countryCode: {
    type: String,
    required: true,
    trim: true,
  },
  regionType: {
    type: String,
    enum: {
      values: ['postalCode', 'region'],
      message: 'INVALID_REGION_TYPE_ENUM',
    },
    default: 'region',
  },
  regionTitleSingular: {
    type: String,
    trim: true,
  },
  regionTitlePlural: {
    type: String,
    trim: true,
  },
  advisor: {
    type: mongoose.Types.ObjectId,
    ref: 'CourseAdvisor',
  },
  provider: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseProvider',
  },
});

countrySchema.index({ provider: 1 });
countrySchema.index({ countryCode: 1 });
countrySchema.index({ advisor: 1 });

export default mongoose.model('CourseCountry', countrySchema);
