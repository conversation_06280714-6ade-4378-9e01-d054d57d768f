import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';

import SchemaFactory from '#utils/schemaFactory.js';

const collectionSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  courses: {
    type: [mongoose.Types.ObjectId],
    ref: 'Course',
    default: [],
  },
  position: {
    type: Number,
    default: 0,
  },
  provider: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'CourseProvider',
  },
});

collectionSchema.index({ slug: 1 });
collectionSchema.index({ title: 1 });
collectionSchema.index({ provider: 1 });

collectionSchema.statics.getAvailableSlug = async function (
  slug,
  provider,
  collectionId = null
) {
  const query = { slug, provider, deleted: false };

  if (collectionId) {
    query._id = { $ne: collectionId };
  }

  const existingCollection = await this.findOne(query);

  return existingCollection ? uniquifySlug(slug) : slug;
};

export default mongoose.model('CourseCollection', collectionSchema);
