import factory from '#utils/handlerFactory.js';

import Log from '../models/Log.js';
import {
  getLogEntities,
  getLogModules,
  getLogs,
  getLogStatusCodes,
  getLogUsers,
} from '../services/logService.js';

export const getAllLogs = async (req, res) => {
  const { items, count } = await getLogs(req.query);

  const modules = await getLogModules();
  const statusCodes = await getLogStatusCodes();
  const users = await getLogUsers();
  const entities = await getLogEntities();

  res.status(200).json({
    items,
    count,
    meta: {
      modules,
      statusCodes,
      users,
      entities,
    },
  });
};

export const getLogEntry = async (req, res) => {
  const data = await factory.getOne(Log, req, {
    populate: ['entity', 'user'],
  });
  res.status(200).json(data);
};

export default {
  getAllLogs,
  getLogEntry,
};
