import APIFeatures from '#utils/apiFeatures.js';
import { isEmpty } from '#utils/arrays.js';
import { isArray } from '#utils/types.js';
import Log from '../models/Log.js';
import { getDateRangeQuery } from './utils.js';

export const getLogs = async ({
  page = 1,
  pageSize = 25,
  sortBy = 'createdAt',
  sortDir = 'desc',
  user,
  entity,
  module = [],
  method = [],
  statusCode = [],
  search,
  dateFrom,
  dateTo,
}) => {
  const userQuery = user ? [{ user: { $eq: user } }] : [];
  const entityQuery = entity ? [{ entity: { $eq: entity } }] : [];
  const actionQuery = search
    ? [{ action: { $regex: search, $options: 'i' } }]
    : [];
  const dateQuery = getDateRangeQuery('createdAt', dateFrom, dateTo);

  const filters = Object.entries({ module, method, statusCode }).reduce(
    (acc, [type, items]) => {
      if (isEmpty(items)) {
        return acc;
      }
      if (!isArray(items)) {
        return [...acc, { $or: [{ [type]: items }] }];
      }

      return [...acc, { $or: items.map((value) => ({ [type]: value })) }];
    },
    [...userQuery, ...entityQuery, ...actionQuery, ...dateQuery]
  );

  const query = filters.length > 0 ? { $and: filters } : {};

  const features = new APIFeatures(Log.find(query), {
    page,
    limit: pageSize,
    skip: 0,
  })
    .populate(['entity', 'user'])
    .sort([`${sortDir === 'desc' ? '-' : ''}${sortBy}`])
    .pagination();

  const featuresCount = new APIFeatures(Log.find(query), {});

  const items = await features.query;
  const count = await featuresCount.query.countDocuments();

  return { count, items };
};

export const getLogModules = async () =>
  await Log.aggregate([
    {
      $match: {
        module: {
          $exists: true,
          $ne: null,
        },
      },
    },
    {
      $group: {
        _id: '$module',
      },
    },
    {
      $sort: {
        _id: 1,
      },
    },
  ]);

export const getLogStatusCodes = async () =>
  await Log.aggregate([
    {
      $match: {
        module: {
          $exists: true,
          $ne: null,
        },
      },
    },
    {
      $group: {
        _id: '$statusCode',
      },
    },
    {
      $sort: {
        _id: 1,
      },
    },
  ]);

export const getLogUsers = async () =>
  await Log.aggregate([
    {
      $group: {
        _id: '$user',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user',
      },
    },
    {
      $unwind: {
        path: '$user',
      },
    },
    {
      $project: {
        user: {
          name: 1,
        },
      },
    },
  ]);

export const getLogEntities = async () =>
  await Log.aggregate([
    {
      $group: {
        _id: '$entity',
      },
    },
    {
      $lookup: {
        from: 'entities',
        localField: '_id',
        foreignField: '_id',
        as: 'entity',
      },
    },
    {
      $unwind: {
        path: '$entity',
      },
    },
    {
      $project: {
        entity: {
          name: 1,
        },
      },
    },
  ]);
