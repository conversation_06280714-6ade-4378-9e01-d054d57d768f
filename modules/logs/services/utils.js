export const getDateRangeQuery = (key, dateFrom, dateTo) => {
  if (dateFrom && dateTo) {
    return [
      {
        [key]: {
          $gte: new Date(dateFrom).setHours(0, 0, 0),
          $lte: new Date(dateTo).setHours(23, 59, 59),
        },
      },
    ];
  }

  if (dateFrom) {
    return [
      {
        [key]: {
          $gte: new Date(dateFrom).setHours(0, 0, 0),
        },
      },
    ];
  }

  if (dateTo) {
    return [
      {
        [key]: {
          $lte: new Date(dateTo).setHours(23, 59, 59),
        },
      },
    ];
  }

  return [];
};
