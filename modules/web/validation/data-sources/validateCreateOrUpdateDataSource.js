import Joi from 'joi';

import app from '#app';
import { validate } from '#utils/validationMiddleware.js';

import { getBaseDataSourceSchema } from './getBaseDataSourceSchema.js';

export const validateCreateOrUpdateDataSource = async (req, res, next) => {
  const registeredDataSources = app.get('dataSources');

  const dataSourceTypeSchema =
    registeredDataSources[req.body.type]?.validation?.update || {};

  const baseDataSourceSchema = getBaseDataSourceSchema();

  const designSchema = {
    design: Joi.object().optional(),
  };

  const mergedSchema = {
    ...baseDataSourceSchema,
    ...dataSourceTypeSchema,
    settings: dataSourceTypeSchema.settings
      ? dataSourceTypeSchema.settings.append(designSchema)
      : Joi.object(designSchema).optional(),
  };

  const schema = Joi.object().keys(mergedSchema);

  return validate(schema, 'body')(req, res, next);
};
