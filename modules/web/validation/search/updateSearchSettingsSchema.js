import Joi from 'joi';

export const updateSearchSettingsSchema = Joi.object().keys({
  resultsPageId: Joi.string().allow('', null),
  filters: Joi.array().items(Joi.object()).optional(),
  logicalOperator: Joi.string().optional().allow('AND', 'OR', null, ''),
  sortField: Joi.string()
    .optional()
    .allow('meta.publishedAt', 'title', null, ''),
  sortOrder: Joi.string().optional().allow('asc', 'desc', null, ''),
});
