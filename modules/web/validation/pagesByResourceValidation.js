import Joi from 'joi';

export const pagesByResourceSchema = Joi.object().keys({
  entityId: Joi.string(),
  groupBySite: Joi.boolean().allow(null),
  resourceType: Joi.alternatives().try(
    Joi.string().valid('model', 'param', 'dataSource'),
    Joi.array().items(Joi.string().valid('model', 'param', 'dataSource'))
  ),
  resourceName: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string())
  ),
  siteId: Joi.string(),
});
