import express from 'express';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { restrictTo } from '#modules/users/controllers/authController.js';

import siteDataSourcesController from './controllers/dataSourcesController.js';
import { validateCreateOrUpdateDataSource } from './validation/data-sources/validateCreateOrUpdateDataSource.js';

const siteDataSourcesRouter = express.Router({ mergeParams: true });

siteDataSourcesRouter
  .route('/')
  .get(
    restrictTo({
      module: 'dataSources',
      permissions: ['read'],
      paramId: 'siteId',
    }),
    siteDataSourcesController.getDataSources
  )
  .post(
    restrictTo({
      module: 'dataSources',
      permissions: ['create'],
      paramId: 'siteId',
    }),
    validateCreateOrUpdateDataSource,
    logRequest({ module: 'web', action: 'ADD_SITE_DATA_SOURCE' }),
    siteDataSourcesController.createDataSource
  );

// INFO: This route is public, but uses the same controller method as the private route above. If we need to add public-specific logic, we can add a new controller method and route.
siteDataSourcesRouter
  .route('/public')
  .get(siteDataSourcesController.getDataSources);

siteDataSourcesRouter.get(
  '/types',
  siteDataSourcesController.getDataSourceTypes
);

siteDataSourcesRouter
  .route('/:id')
  .get(
    restrictTo({
      module: 'dataSources',
      permissions: ['read'],
      paramId: 'siteId',
    }),
    siteDataSourcesController.getDataSource
  )
  .patch(
    restrictTo({
      module: 'dataSources',
      permissions: ['update'],
      paramId: 'siteId',
    }),
    validateCreateOrUpdateDataSource,
    logRequest({ module: 'web', action: 'UPDATE_SITE_DATA_SOURCE' }),
    siteDataSourcesController.updateDataSource
  )
  .delete(
    restrictTo({
      module: 'dataSources',
      permissions: ['delete'],
      paramId: 'siteId',
    }),
    logRequest({ module: 'web', action: 'DELETE_SITE_DATA_SOURCE' }),
    siteDataSourcesController.deleteDataSource
  );

// INFO: This route is public, but uses the same controller method as the private route above. If we need to add public-specific logic, we can add a new controller method and route.
siteDataSourcesRouter
  .route('/:id/public')
  .get(siteDataSourcesController.getDataSource);

siteDataSourcesRouter.patch(
  '/:id/enable',
  restrictTo({
    module: 'dataSources',
    permissions: ['delete'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'ENABLE_SITE_DATA_SOURCE' }),
  siteDataSourcesController.enableDataSource
);

siteDataSourcesRouter.patch(
  '/:id/disable',
  restrictTo({
    module: 'dataSources',
    permissions: ['delete'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'DISABLE_SITE_DATA_SOURCE' }),
  siteDataSourcesController.disableDataSource
);

export default siteDataSourcesRouter;
