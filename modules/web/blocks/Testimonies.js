import { stringArrayToString } from '#utils/strings.js';

/**
 * Testimonies block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function Testimonies() {
  return {
    name: 'Testimonies',
    getSearchableContent,
  };
}

/**
 * Get searchable content for Testimonies
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { items } = node?.props ?? {};

  return items?.map(({ label, text }) =>
    stringArrayToString([label, text], '\n')
  );
}
