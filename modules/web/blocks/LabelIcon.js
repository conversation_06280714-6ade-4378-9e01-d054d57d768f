/**
 * LabelIcon block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function LabelIcon() {
  return {
    name: 'LabelIcon',
    getSearchableContent,
  };
}

/**
 * Get searchable content for LabelIcon
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { label } = node?.props ?? {};

  return label;
}
