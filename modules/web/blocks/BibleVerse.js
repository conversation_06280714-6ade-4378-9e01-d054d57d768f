import { stringArrayToString } from '#utils/strings.js';

/**
 * BibleVerse block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function BibleVerse() {
  return {
    name: 'BibleVerse',
    getSearchableContent,
  };
}

/**
 * Get searchable content for BibleVerse
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { bible, passage, text } = node?.props ?? {};

  return stringArrayToString([bible, passage, text], '\n');
}
