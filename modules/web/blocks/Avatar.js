import { convertRichTextToPlainText } from '#utils/richText.js';
import { getSearchableAddress } from '#utils/search/getSearchableAddress.js';
import { stringArrayToString } from '#utils/strings.js';

/**
 * Avatar block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function Avatar() {
  return {
    name: 'Avatar',
    getSearchableContent,
  };
}

/**
 * Get searchable content for Avatar
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node, site }) {
  const design = site.design.name;

  // Node
  const {
    title,
    subtitle,
    description,
    dynamicPerson, // Default to true for designs other than `adventist` and `adventisten`
    person,
    personRole,
    showPersonDescription = design !== 'adventist', // Default to true for designs other than `adventist`
    showPersonFax = design !== 'adventist', // Default to true for designs other than `adventist`
    showPersonPhone = design !== 'adventist', // Default to true for designs other than `adventist`
  } = node?.props ?? {};

  // Person
  const { address, body, email, phone, mobile, fax } = dynamicPerson
    ? person || {}
    : {};

  const {
    description: roleDescription,
    email: roleEmail,
    phone: rolePhone,
  } = dynamicPerson ? personRole || {} : {};

  return stringArrayToString(
    [
      title,
      subtitle,
      showPersonDescription
        ? convertRichTextToPlainText(roleDescription || description)
        : null,
      roleEmail || email,
      showPersonPhone ? rolePhone || phone : null,
      showPersonPhone ? mobile : null,
      showPersonFax ? fax : null,
      convertRichTextToPlainText(body),
      address ? getSearchableAddress(address) : null,
    ],
    '\n'
  );
}
