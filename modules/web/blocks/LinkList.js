import { stringArrayToString } from '#utils/strings.js';

/**
 * LinkList block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function LinkList() {
  return {
    name: '<PERSON>List',
    getSearchableContent,
  };
}

/**
 * Get searchable content for LinkList
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { title, items } = node?.props ?? {};

  const itemContents = items?.map(({ label, description }) =>
    stringArrayToString([label, description], '\n')
  );

  return stringArrayToString([title, itemContents], '\n\n');
}
