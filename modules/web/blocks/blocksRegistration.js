import AccordionItem from './AccordionItem.js';
import Avatar from './Avatar.js';
import BibleVerse from './BibleVerse.js';
import Card from './Card.js';
import FeaturedContent from './FeaturedContent.js';
import Header from './Header.js';
import Hero from './Hero.js';
import LabelIcon from './LabelIcon.js';
import LinkList from './LinkList.js';
import QuoteCarousel from './QuoteCarousel.js';
import RichText from './RichText.js';
import SimpleText from './SimpleText.js';
import Testimonies from './Testimonies.js';

export const webBlocksRegistration = {
  AccordionItem,
  Avatar,
  BibleVerse,
  Card,
  FeaturedContent,
  <PERSON>er,
  Hero,
  LabelIcon,
  LinkList,
  QuoteCarousel,
  RichText,
  SimpleText,
  Testimonies,
};
