import { stringArrayToString } from '#utils/strings.js';

/**
 * Header block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function Header() {
  return {
    name: 'Header',
    getSearchableContent,
  };
}

/**
 * Get searchable content for Header
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { title, subtitle, kicker } = node?.props ?? {};

  return stringArrayToString([title, subtitle, kicker], '\n');
}
