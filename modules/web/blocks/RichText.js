import { convertRichTextToPlainText } from '#utils/richText.js';
import get from 'lodash/get.js';

/**
 * RichText block definition. TODO: Should we use a class 🤔?
 *
 * @param {Object} options
 * * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function RichText() {
  return {
    name: 'RichText',
    getSearchableContent,
  };
}

/**
 * Get searchable content for RichText
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node, page }) {
  const { doc, source } = node?.props ?? {};
  const { enabled, value } = source ?? {};

  // If the source is not enabled, return the text
  if (!enabled) {
    return convertRichTextToPlainText(doc);
  }

  return convertRichTextToPlainText(get(page.resources || {}, value || ''));
}
