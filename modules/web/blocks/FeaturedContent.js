import { stringArrayToString } from '#utils/strings.js';

/**
 * FeaturedContent block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function FeaturedContent() {
  return {
    name: 'FeaturedContent',
    getSearchableContent,
  };
}

/**
 * Get searchable content for FeaturedContent
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { title, description } = node?.props ?? {};

  return stringArrayToString([title, description], '\n');
}
