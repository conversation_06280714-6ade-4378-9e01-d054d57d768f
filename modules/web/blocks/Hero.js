import { stringArrayToString } from '#utils/strings.js';

/**
 * Hero block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export default function Hero() {
  return {
    name: '<PERSON>',
    getSearchableContent,
  };
}

/**
 * Get searchable content for Hero
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { title, kicker, description } = node?.props ?? {};

  return stringArrayToString([title, kicker, description], '\n');
}
