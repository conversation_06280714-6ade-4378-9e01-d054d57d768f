import express from 'express';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { restrictTo } from '#modules/users/controllers/authController.js';
// import { validate } from '#utils/validationMiddleware.js'; // TODO: Add validation middleware to this file

import automatedSiteController from './controllers/automatedSiteController.js';

const automatedSitesRouter = express.Router();

automatedSitesRouter.route('/').get(automatedSiteController.getAutomatedSites);

automatedSitesRouter
  .route('/:entityId')
  .get(automatedSiteController.getAutomatedSite)
  .patch(
    restrictTo({
      module: 'automatedSites',
      permissions: ['update'],
    }),
    logRequest({ module: 'web', action: 'UPDATE_REDIRECT' }),
    automatedSiteController.updateAutomatedEntity
  );

export default automatedSitesRouter;
