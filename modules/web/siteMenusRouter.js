import express from 'express';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { restrictTo } from '#modules/users/controllers/authController.js';
// import { validate } from '#utils/validationMiddleware.js'; // TODO: Add validation middleware to this file

import menuController from './controllers/menuController.js';

const siteMenusRouter = express.Router({ mergeParams: true });

siteMenusRouter
  .route('/')
  .get(menuController.getAllMenus)
  .post(
    restrictTo({
      module: 'menus',
      permissions: ['create'],
    }),
    logRequest({ module: 'web', action: 'CREATE_MENU' }),
    menuController.createMenu
  );

siteMenusRouter
  .route('/:menuId')
  .get(menuController.getMenu)
  .patch(
    restrictTo({
      module: 'menus',
      permissions: ['update'],
      paramId: 'menuId',
    }),
    logRequest({ module: 'web', action: 'UPDATE_MENU' }),
    menuController.updateMenu
  )
  .delete(
    restrictTo({
      module: 'menus',
      permissions: ['delete'],
      paramId: 'menuId',
    }),
    logRequest({ module: 'web', action: 'DELETE_MENU' }),
    menuController.deleteMenu
  );

siteMenusRouter.route('/:menuId/items').get(menuController.getMenuItems);

siteMenusRouter
  .route('/:menuId/translations')
  .get(menuController.getMenuTranslations);

siteMenusRouter.route('/:menuId/add-translation/:language').post(
  restrictTo({
    module: 'menus',
    permissions: ['translate'],
    paramId: 'menuId',
  }),
  logRequest({ module: 'web', action: 'TRANSLATE_MENU' }),
  menuController.addMenuTranslation
);

siteMenusRouter.route('/:menuId/restore').patch(
  restrictTo({
    module: 'menus',
    permissions: ['delete'],
    paramId: 'menuId',
  }),
  logRequest({ module: 'web', action: 'RESTORE_MENU' }),
  menuController.restoreMenu
);

siteMenusRouter.route('/:menuId/disable').patch(
  restrictTo({
    module: 'menus',
    permissions: ['update'],
    paramId: 'menuId',
  }),
  logRequest({ module: 'web', action: 'DISABLE_MENU' }),
  menuController.disableMenu
);

siteMenusRouter.route('/:menuId/enable').patch(
  restrictTo({
    module: 'menus',
    permissions: ['update'],
    paramId: 'menuId',
  }),
  logRequest({ module: 'web', action: 'ENABLE_MENU' }),
  menuController.enableMenu
);

export default siteMenusRouter;
