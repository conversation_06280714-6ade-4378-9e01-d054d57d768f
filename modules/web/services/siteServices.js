import mongoose from 'mongoose';

import searchIndexService from '#modules/search/services/searchIndexService.js';
import { ensureIdField } from '#utils/api/model/fields.js';
import { errors } from '#utils/appError.js';

import Logger from '#utils/logger.js';
import Page from '../models/Page.js';
import Site from '../models/Site.js';
import pageServices from './pageServices.js';
import * as siteSearchServices from './siteSearchServices.js';

async function getSiteById(siteId) {
  if (!mongoose.isValidObjectId(siteId)) {
    return {
      error: errors.not_found('Site', siteId),
    };
  }

  const site = await Site.findOne({ _id: siteId, deleted: false })
    .populate('entity')
    .populate('auth.userCollection');

  return { site };
}

async function getSiteOrAncestorSite({
  siteId,
  entity,
  includeAncestors = false,
}) {
  if (!mongoose.isValidObjectId(siteId)) {
    throw errors.not_found();
  }

  return await Site.findOne({
    _id: siteId,
    deleted: false,
    entity: includeAncestors
      ? { $in: [entity?._id, ...entity.ancestors] }
      : entity?._id,
  })
    .populate('entity')
    .populate('auth.userCollection');
}

export async function getSiteTranslations({ siteId, query }) {
  // We get the language from the locale and if the query includes sort by language we use the language field
  const { sort } = query;

  const siteTranslations = await Site.aggregate()
    .match({
      source: new mongoose.Types.ObjectId(siteId),
      deleted: false,
    })
    .lookup({
      from: 'languages',
      localField: 'language',
      foreignField: 'locale',
      as: 'languageInfo',
    })
    .unwind('$languageInfo')
    .sort(
      sort && sort === 'language' ? { 'languageInfo.name.en': 1 } : { _id: 1 }
    )
    .addFields({
      id: '$_id',
    });

  return siteTranslations;
}

/**
 * Adds a translation to a site
 * @param {Object} params
 * @param {String} params.entity
 * @param {String} params.siteId
 * @param {String} params.language
 * @returns {Promise<object>}
 */
export async function addSiteTranslation({ entity, siteId, language }) {
  // Fetch source site
  const sourceSite = await Site.findById(siteId);

  // Check if site doesn't exists
  if (!sourceSite) {
    return { error: errors.not_found('site', siteId) };
  }

  // Check if site already has this translation
  const existingSite = await Site.findOne({
    source: siteId,
    language,
    deleted: false,
  });

  if (existingSite) {
    return {
      error: errors.already_exists('Site already has this translation'),
    };
  }

  // Create the site translation
  const siteTranslation = await Site.create({
    enabled: false,
    entity: sourceSite.entity,
    language,
    source: sourceSite._id,
  });

  let searchIndex;
  let task;

  // Create a search index for the new site translation if the source site has search enabled and the index doesn't exist
  if (sourceSite.search?.provider === 'awe') {
    const {
      searchIndex: siteSearchIndex,
      task: siteSearchIndexTask,
      error,
    } = await siteSearchServices.createSiteSearchIndex({
      entity,
      siteId: sourceSite._id,
      language,
    });

    if (error) {
      Logger.error('Error creating search index', error);
    }

    searchIndex = siteSearchIndex;
    task = siteSearchIndexTask;
  }

  return { siteTranslation, searchIndex, task };
}

export async function getSites({ restrictedRecords, siteIds }) {
  const filter = {
    source: { $eq: null }, // Only get sites that are not translations
  };

  if (restrictedRecords) {
    filter._id = { $in: restrictedRecords };
  }

  // filter ids by restricted records
  if (siteIds) {
    const validIds =
      restrictedRecords?.length > 0
        ? siteIds.filter((siteId) => restrictedRecords?.includes(siteId))
        : siteIds;
    filter._id = { $in: validIds };
  }

  const sites = await Site.find(filter);
  const count = await Site.countDocuments(filter);

  return {
    items: sites,
    count,
  };
}

async function getLoginUrlsForSites(siteIds = []) {
  const sites = await Site.find({ _id: { $in: siteIds } })
    .select('domain title auth.pages.loginPage')
    .lean();

  const siteLogins = [];

  for (const site of sites) {
    if (!site.auth?.pages?.loginPage) {
      continue;
    }

    const page = await pageServices.getPage(site.auth.pages.loginPage);
    if (!page) {
      continue;
    }

    siteLogins.push({
      siteName: site.title,
      loginUrl: `http${site.domain?.startsWith('localhost') ? '' : 's'}://${site.domain}${page.path}`,
    });
  }

  return siteLogins;
}

async function getBaseUrl({ siteId, site }) {
  if (!site) {
    const { site: resultSite } = await getSiteById(siteId);
    site = resultSite;
  }

  return site?.domain
    ? `http${site.domain?.startsWith('localhost') ? '' : 's'}://${site.domain}`
    : '';
}

async function getAncestorsByDynamicResource({
  entity,
  excludeSiteIds = [],
  resourceName,
  resourceType,
  siteId,
}) {
  if (!entity) {
    return {
      error: errors.params(),
    };
  }

  // TODO: Do we need to look up the entity ancestors and filter by the same network?
  const sites = await Site.find({
    ...(excludeSiteIds.length > 0 ? { _id: { $nin: excludeSiteIds } } : {}),
    $or: [
      { _id: siteId },
      { entity: { $in: [entity?._id, ...entity.ancestors] } },
    ],
    deleted: false,
    enabled: true,
  })
    .select('_id, name')
    .lean();

  if (!sites) {
    return {
      error: errors.not_found('sites'),
    };
  }

  const pagesWithArticles = await Page.find({
    site: { $in: sites },
    enabled: true,
    deleted: false,
    dynamic: true,
    $or: [
      { dynamicResource: resourceName },
      {
        'dynamicResource.type': resourceType,
        'dynamicResource.name': resourceName,
      },
    ],
  })
    .select('site')
    .lean();

  const pageSitesWithArticles = pagesWithArticles.map((page) =>
    page.site.toString()
  );

  const sitesWithArticles = sites
    .filter((site) => pageSitesWithArticles.includes(site._id.toString()))
    .map(ensureIdField);

  return {
    items: sitesWithArticles,
    count: sitesWithArticles.length,
  };
}

async function getSiteLocales(site) {
  // Get site translations to create indexes for all languages
  const siteTranslations = await getSiteTranslations({
    siteId: site._id,
    query: { sort: 'language' },
  });

  const mainSiteLanguage = site.language ?? 'en';

  return siteTranslations.length === 0
    ? [mainSiteLanguage]
    : [mainSiteLanguage, ...siteTranslations.map((t) => t.language)];
}

async function deleteSite({ entity, siteId, sourceRecordId, user }) {
  if (!entity || !siteId) {
    return { error: errors.params(['entiy', 'siteId']) };
  }

  const deletedSite = await Site.findOneAndUpdate(
    {
      _id: siteId,
      entity: entity._id,
    },
    {
      deleted: true,
    },
    {
      new: true,
    }
  ).populate('source');

  if (!deletedSite) {
    return { error: errors.not_found('Site', siteId) };
  }

  // Remove domains from hosting provider
  await deletedSite.unregisterDomains({ user });

  // Remove from analytics if it has analytics enabled
  await deletedSite.removeAnalytics();

  // Remove search indexes
  const tasks = [];

  if (
    deletedSite.search?.provider === 'awe' ||
    deletedSite.source?.search?.provider === 'awe'
  ) {
    const { items = [], error: searchIndexesError } =
      await searchIndexService.getSearchIndexes({
        entityId: entity._id,
        siteId: sourceRecordId || deletedSite._id,
        language: sourceRecordId ? deletedSite.language : null,
        limit: 0,
      });

    if (searchIndexesError) {
      Logger.error('Error getting search indexes', searchIndexesError);
      return { deletedSite, tasks, error: searchIndexesError };
    }

    for (const searchIndex of items) {
      const { task, error: searchIndexError } =
        await searchIndexService.deleteSearchIndex({
          searchIndexId: searchIndex._id,
        });

      if (searchIndexError) {
        Logger.error('Error deleting search index', searchIndexError);
        return { deletedSite, tasks, error: searchIndexError };
      }

      tasks.push(task);
    }
  }

  return { deletedSite, tasks };
}

export default {
  getBaseUrl,
  getSiteById,
  getSiteOrAncestorSite,
  getSiteTranslations,
  getSiteLocales,
  addSiteTranslation,
  getLoginUrlsForSites,
  getSites,
  getAncestorsByDynamicResource,
  deleteSite,
};
