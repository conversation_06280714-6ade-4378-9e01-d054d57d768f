import getCollationFilter from '#utils/api/collation/filter.js';
import getListFilters from '#utils/api/list/filters.js';
import Redirect from '../models/Redirect.js';
import Site from '../models/Site.js';

/**
 * Get all redirects
 * @param {object} options The options object
 * @param {string} options.siteId Get redirects for the specified site (required)
 * @param {string[]} options.statuses Filter redirects by status
 * @param {string} options.search Search string to filter redirects by source or target
 * @param {number} options.limit Number of items to return (default: 25)
 * @param {number} options.page Page number (default: 1)
 * @param {string} options.sort Sort field (default: 'source')
 * @param {number} options.skip Number of items to skip (default: 0)
 * @param {string} options.language Collation language (default: 'en')
 * @returns {Promise<{ items: Redirect[], count: number }>} The response object with items and count
 */
export async function getRedirects({
  siteId,
  statuses = [],
  search = '',
  limit = 25,
  page = 1,
  sort = 'source',
  skip = 0,
  language = 'en',
}) {
  const site = await Site.findById(siteId);

  if (!site) {
    return { error: `Site not found with id: ${siteId}` };
  }

  const filters = getListFilters({
    statuses,
    limit,
    page,
    skip,
    sort,
    search,
    searchFields: ['source', 'target'],
  });

  const matchFiters = {
    site: site._id,
    $and: [filters.statuses, filters.search],
  };

  const collationFilters = getCollationFilter({ language });

  const itemsQuery = Redirect.find(matchFiters)
    .sort(filters.sort)
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit)
    .collation(collationFilters);

  const items = await itemsQuery;

  const count = await Redirect.find(matchFiters).countDocuments();

  return {
    items,
    count,
  };
}
