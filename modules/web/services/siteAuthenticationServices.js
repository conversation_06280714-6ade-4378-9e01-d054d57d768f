import webUserCollectionServices from '#modules/web-auth/services/webUserCollectionServices/webUserCollectionServices.js';
import getListFilters from '#utils/api/list/filters.js';
import { toObjectId } from '#utils/api/mongoose/id.js';
import { errors } from '#utils/appError.js';

import Site from '../models/Site.js';
import SiteUserGroup from '../models/SiteUserGroup.js';
import siteServices from './siteServices.js';

async function updateSiteAuth({ siteId, auth = {}, entity }) {
  if (!siteId || !entity) {
    throw errors.params();
  }

  const site = await Site.findOneAndUpdate(
    {
      _id: siteId,
      entity: entity._id,
    },
    {
      auth: {
        ...auth,
        userCollection: auth.userCollection || null,
      },
    },
    {
      new: true,
      runValidators: true,
    }
  );

  if (!site) {
    throw errors.not_found('Site', siteId);
  }

  // If auth is disabled we need to delete all possible sessions and tokens
  if (!site.auth?.enabled && site.auth?.userCollection) {
    await webUserCollectionServices.deleteUserCollectionUserSessions(
      site.auth?.userCollection
    );
  }

  return site;
}

async function getSiteUserGroupById({ groupId, siteId, populate = [] }) {
  if (!groupId || !siteId) {
    throw errors.params();
  }

  const { site, error } = await siteServices.getSiteById(siteId);

  if (error) {
    throw error;
  }

  const query = SiteUserGroup.findOne({
    _id: groupId,
    site: site._id,
    deleted: false,
  });

  if (populate.length) {
    query.populate(populate);
  }

  return await query.exec();
}

async function getSiteUserGroups({
  limit = 25,
  page,
  search,
  siteId,
  sortBy,
  sortDir,
  statuses = [],
}) {
  if (!siteId) {
    throw errors.params();
  }

  const filters = getListFilters({
    limit,
    order: sortDir,
    page,
    search,
    searchFields: ['name'],
    sort: sortBy,
    sortFields: ['name'],
    statuses,
  });

  const matchFiters = {
    site: toObjectId(siteId),
    $and: [filters.statuses, filters.search],
  };

  const items = await SiteUserGroup.find(matchFiters)
    .sort(filters.sort)
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit);

  const count = await SiteUserGroup.find(matchFiters).countDocuments();

  return {
    count,
    items,
  };
}

async function createSiteUserGroup({ siteId, name }) {
  if (!siteId || !name) {
    throw errors.params();
  }

  const { site, error } = await siteServices.getSiteById(siteId);

  if (error) {
    throw error;
  }

  return await SiteUserGroup.create({
    site: site._id,
    name,
  });
}

async function updateSiteUserGroup({ siteId, groupId, name }) {
  if (!siteId || !groupId || !name) {
    throw errors.params();
  }

  const { site, error } = await siteServices.getSiteById(siteId);

  if (error) {
    throw error;
  }

  return await SiteUserGroup.findOneAndUpdate(
    {
      site: site._id,
      _id: groupId,
      deleted: false,
    },
    {
      name,
    },
    {
      new: true,
      runValidators: true,
    }
  );
}

async function deleteSiteUserGroup({ siteId, groupId }) {
  if (!siteId || !groupId) {
    throw errors.params();
  }

  const { site, error } = await siteServices.getSiteById(siteId);

  if (error) {
    throw error;
  }

  return await SiteUserGroup.findOneAndUpdate(
    {
      site: site._id,
      _id: groupId,
    },
    {
      deleted: true,
    },
    {
      new: true,
      runValidators: true,
    }
  );
}

async function toggleSiteUserGroup({ siteId, groupId, enabled }) {
  if (!siteId || !groupId) {
    throw errors.params();
  }

  const { site, error } = await siteServices.getSiteById(siteId);

  if (error) {
    throw error;
  }

  return await SiteUserGroup.findOneAndUpdate(
    {
      site: site._id,
      _id: groupId,
    },
    {
      enabled,
    },
    {
      new: true,
      runValidators: true,
    }
  );
}

async function disableSiteUserGroup({ siteId, groupId }) {
  return await toggleSiteUserGroup({ siteId, groupId, enabled: false });
}

async function enableSiteUserGroup({ siteId, groupId }) {
  return await toggleSiteUserGroup({ siteId, groupId, enabled: true });
}

async function getSiteUserGroupUsers({
  groupId,
  limit = 25,
  page,
  search,
  siteId,
  sortBy,
  sortDir,
}) {
  if (!siteId || !groupId) {
    throw errors.params();
  }

  const filters = getListFilters({
    limit,
    order: sortDir,
    page,
    search,
    searchFields: ['email', 'name'],
  });

  const matchFiters = {
    _id: toObjectId(groupId),
    site: toObjectId(siteId),
    deleted: false,
  };

  const usersMatchPipeline = {
    $match: {
      $expr: {
        $in: ['$_id', '$$users'],
      },
      ...filters.search,
    },
  };

  const items = await SiteUserGroup.aggregate()
    .match(matchFiters)
    .lookup({
      from: 'webusers',
      let: {
        users: '$users',
      },
      pipeline: [
        usersMatchPipeline,
        {
          $sort: {
            [sortBy]: sortDir === 'asc' ? 1 : -1,
          },
        },
        {
          $skip: filters.pagination.skip,
        },
        {
          $limit: filters.pagination.limit,
        },
      ],
      as: 'users',
    });

  const countResult = await SiteUserGroup.aggregate()
    .match(matchFiters)
    .lookup({
      from: 'webusers',
      let: {
        users: '$users',
      },
      pipeline: [usersMatchPipeline],
      as: 'users',
    });

  return {
    count: countResult?.[0]?.users?.length || 0,
    items: items?.[0]?.users || [],
  };
}

async function addSiteUserGroupUser({ siteId, groupId, userId }) {
  if (!siteId || !groupId || !userId) {
    throw errors.params();
  }

  const userGroup = await getSiteUserGroupById({
    groupId,
    siteId,
    populate: ['users'],
  });

  const userGroupUsers = userGroup.users || [];
  userGroupUsers.push(userId);

  return await SiteUserGroup.findOneAndUpdate(
    {
      _id: groupId,
      site: siteId,
      deleted: false,
    },
    {
      users: userGroupUsers,
    },
    {
      new: true,
      runValidators: true,
    }
  );
}

async function deleteSiteUserGroupUser({ siteId, groupId, userId }) {
  if (!siteId || !groupId || !userId) {
    throw errors.params();
  }

  const userGroup = await getSiteUserGroupById({ groupId, siteId });

  if (!userGroup) {
    throw errors.not_found('SiteUserGroup', groupId);
  }

  userGroup.users = (userGroup.users || []).filter(
    (user) => user.toString() !== userId
  );
  await userGroup.save();

  return userGroup;
}

export default {
  updateSiteAuth,
  getSiteUserGroups,
  getSiteUserGroupById,
  createSiteUserGroup,
  updateSiteUserGroup,
  deleteSiteUserGroup,
  disableSiteUserGroup,
  enableSiteUserGroup,
  getSiteUserGroupUsers,
  addSiteUserGroupUser,
  deleteSiteUserGroupUser,
};
