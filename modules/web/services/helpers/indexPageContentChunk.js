import documentService from '#modules/search/services/documentService.js';

import { getPageDocumentForIndexing } from './getPageDocumentForIndexing.js';

export function indexPageContentChunk({ indexUId, language, logEvent, site }) {
  return async function ({ items, chunkNumber, searchIndexTask }) {
    // ------------------------------
    // 4. Create the document to index for each page
    await logEvent('');
    await logEvent(
      `    Creating the document to index for each page in chunk ${chunkNumber}`
    );

    const documents = items.map((page) =>
      getPageDocumentForIndexing({ language, page, site })
    );

    // Index the documents
    const { result, error } = await documentService.createOrUpdateDocument({
      indexUId,
      documents,
    });

    if (error) {
      await logEvent(
        `    Error indexing documents for content chunk ${chunkNumber}`,
        'error'
      );

      searchIndexTask.details.failed.count += 1;
      searchIndexTask.details.failed.items.push({
        chunkNumber,
        error,
        items: items.map((item) => ({ id: item.id, path: item.path })),
      });
      searchIndexTask.markModified('details');
      await searchIndexTask.save();

      return;
    }

    // Store the indexing task
    if (result?.taskUid) {
      searchIndexTask.tasks.push(result?.taskUid);
      await searchIndexTask.save();
    }
  };
}
