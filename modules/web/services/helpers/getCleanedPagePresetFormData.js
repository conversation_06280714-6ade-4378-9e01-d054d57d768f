// TODO: Revisit when block presets are done and update to match the new logic
export function getCleanedPagePresetFormData({
  formData,
  entity,
  site,
  user,
  method,
}) {
  // No need to clean if no network or entity related fields are not provided
  if (
    !formData.network &&
    !formData.entity &&
    method === 'update' &&
    formData.availableForNetwork === undefined
  ) {
    // We make sure that network and entity are not set to an empty string
    if (formData.network === '') {
      formData.network = null;
    }
    if (formData.entity === '') {
      formData.entity = null;
    }

    return formData;
  }

  const { availableForNetwork, ...cleanedFormData } = formData;

  // Only one of network or entity can be set
  if (cleanedFormData.network) {
    cleanedFormData.entity = null;
    return cleanedFormData;
  }
  if (cleanedFormData.entity) {
    cleanedFormData.network = null;
    return cleanedFormData;
  }

  if (!user?.isAdmin) {
    cleanedFormData.entity =
      availableForNetwork && entity?.network ? null : entity?._id;
    // const inheritedNetwork = getInheritedAttribute(entity, 'network'); // TODO: Revisit when block presets are done
    cleanedFormData.network =
      // availableForNetwork && inheritedNetwork ? inheritedNetwork : null;
      availableForNetwork && entity.network ? entity.network : null;
  }

  // If site is provided, set the site and design fields, and reset network and entity fields
  if (site) {
    cleanedFormData.site = site._id;
    cleanedFormData.entity = null;
    cleanedFormData.network = null;
    cleanedFormData.availableForNetwork = false;
    cleanedFormData.design = site.design.name;
    cleanedFormData.designVariant = site.design.options?.variant;
  }

  return cleanedFormData;
}
