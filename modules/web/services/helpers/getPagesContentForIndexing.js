import chunk from 'lodash/chunk.js';

import { fetchUrl } from '#utils/fetchClient.js';

const BATCH_SIZE = 10;

/**
 * Get pages content for indexing
 *
 * @param {Object} options
 * @param {Function} options.indexPageContentChunk - The function to index the page content chunk
 * @param {String} options.language - The language to get the content for
 * @param {Function} options.logEvent - The function to log an event
 * @param {Array} options.paths - The paths to get the content for
 * @param {Object} options.searchIndexTask - The search index task to update with progress
 * @param {Object} options.site - The site associated with the content
 *
 * @returns {Object}
 */
export async function getPagesContentForIndexing({
  indexPageContentChunk,
  language,
  logEvent,
  paths: allPaths,
  searchIndexTask,
  site,
}) {
  // Split the paths into chunks to avoid overloading the API
  const pathChunks = chunk(allPaths, BATCH_SIZE);

  if (!searchIndexTask.details.content.totalChunks) {
    searchIndexTask.details.content.totalChunks = pathChunks.length;
    await searchIndexTask.save();
  }

  let count = searchIndexTask.details.content.processedChunks + 1;
  const localeRe = new RegExp(`^/${language}/`);

  for (let i = count - 1; i < pathChunks.length; i++) {
    await logEvent(
      `  Fetching content for path chunk ${count}/${pathChunks.length}`
    );

    const paths = pathChunks[i];

    try {
      // Fetch the content for each page in the chunk in parallel
      const chunkPagesContent = await Promise.all(
        paths.map(async (path) => {
          const slug = path
            .replace(localeRe, '/')
            .split('/')
            .slice(1)
            .join(',');
          const url = `${process.env.PUBLIC_API_URL}/web/pages/${slug.length === 0 ? 'undefined' : slug}`;

          try {
            return await fetchUrl(url, {
              headers: {
                ClientToken: process.env.PUBLIC_API_CLIENT_TOKEN,
                Language: language,
                Origin: site.domain,
              },
            });
          } catch (error) {
            await logEvent(
              `    Error fetching pages content for url ${url}: ${error}`,
              'error'
            );
            return Promise.reject(error);
          }
        })
      );

      searchIndexTask.details.content.processedChunks = count;
      searchIndexTask.details.content.chunks.push({
        chunkNumber: count,
        items: chunkPagesContent?.map((item) => ({
          id: item?.id,
          path: item?.path,
        })),
      });
      searchIndexTask.details.paths.remaining = allPaths.slice(
        i * BATCH_SIZE + paths.length
      );
      searchIndexTask.markModified('details');
      await searchIndexTask.save();

      await indexPageContentChunk({
        items: chunkPagesContent,
        chunkNumber: count,
        searchIndexTask,
      });
    } catch (error) {
      await logEvent(
        `  Error fetching pages content for site in chunk ${count}/${pathChunks.length}: ${error}`,
        'error'
      );

      searchIndexTask.details.content.chunks.push({
        chunkNumber: count,
        error,
      });
      searchIndexTask.details.content.failedChunks += 1;
      searchIndexTask.details.paths.remaining = allPaths.slice(i * BATCH_SIZE);
      searchIndexTask.markModified('details');
      await searchIndexTask.save();
    }

    count += 1;
  }
}
