import get from 'lodash/get.js';

import app from '#app';

/**
 * Returns the searchable content of a page
 *
 * @param {Object} options
 * @param {String} options.content - The content to index
 * @param {String} options.language - The language of the page
 * @param {Object} options.page - The page to index
 * @param {Object} options.site - The site the page belongs to
 *
 * @returns {String} content - The searchable content
 */
export function getPageContentForIndexing({ content, language, page, site }) {
  // If the content is empty, return an empty string
  if (!content || !Object.keys(content).length) {
    return { content: '' };
  }

  // The registered block definitions from the app
  const blocks = app.get('blocks');

  const strings = Object.values(content).reduce((acc, node) => {
    // If the node is hidden, skip it
    if (node.hidden) {
      return acc;
    }

    const blockName = get(node, 'type.resolvedName');
    const blockDefinition = blocks[blockName]?.({ site });

    // If the block definition is not found or no searchable content is found, skip it
    if (
      !blockDefinition ||
      !blockDefinition?.getSearchableContent ||
      node.props?.doNotIndex // Nodes with doNotIndex prop are not indexed
    ) {
      return acc;
    }

    const blockContent = blockDefinition.getSearchableContent({
      language,
      node,
      page,
      site,
    });

    // If there is no block content, return the accumulator
    if (!blockContent) {
      return acc;
    }

    return [...acc, blockContent];
  }, []);

  return { content: strings.join(' ') };
}
