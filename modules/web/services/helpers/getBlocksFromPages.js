/**
 * Get blocks from pages, optionally filtered by search term
 * @param {Array} pages Array of pages
 * @param {String} searchTerm Search term to filter blocks by
 *
 * @returns {Array} Array of blocks with pageId, pageName, blockId and block content
 */
export function getBlocksFromPages({ pages, searchTerm }) {
  const blocks = [];
  pages.forEach((page) => {
    // Get content from each page
    const pageContent = JSON.parse(page.content || '{}') || {};
    // page content is an object where every block inside is a key with the block id and the value is the block content
    Object.keys(pageContent).forEach((blockId) => {
      const block = pageContent[blockId];

      // If there is a search term, filter blocks by it
      if (
        searchTerm &&
        typeof block === 'object' &&
        JSON.stringify(block).toLowerCase().includes(searchTerm.toLowerCase()) // Check which block content includes search term
      ) {
        blocks.push({
          ...block,
          blockId: blockId,
          pageId: page.id,
          pageName: page.name,
        });
      } else if (!searchTerm) {
        blocks.push({
          ...block,
          blockId: blockId,
          pageId: page.id,
          pageName: page.name,
        });
      }
    });
  });
  return blocks;
}

/**
 * Function that modifies a block (Use getBlocksFromPages if searching for the block)
 * This function will replace the block of that specific blockId and return the page.
 * @param {Object} params The function parameters.
 * @param {Object} params.page The page to save the content to.
 * @param {String} params.blockId The blockId to modify.
 * @param {Object} params.newBlock The new block to replace the old one.
 * @returns {Object} The page with the modified block.
 */
export function modifyBlockFromPage({ page, blockId, newBlock }) {
  // Get content from the page
  const pageContent = JSON.parse(page.content || '{}') || {};
  // page content is an object where every block inside is a key with the block id and the value is the block content
  pageContent[blockId] = newBlock;
  page.content = JSON.stringify(pageContent);
  return page;
}
