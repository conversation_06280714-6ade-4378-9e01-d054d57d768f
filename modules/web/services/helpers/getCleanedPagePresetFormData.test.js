import { expect, test } from 'vitest';

import { getCleanedPagePresetFormData } from './getCleanedPagePresetFormData.js';

test('getCleanedPagePresetFormData should return cleaned form data for non admin creation', () => {
  const entity = { _id: '123', network: '456' };
  const user = {
    id: 1,
    name: '<PERSON>',
  };
  const method = 'create';
  // Create with default entity and non admin user
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: '123',
    network: null,
  });
  // Create with default entity and non admin user
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
        availableForNetwork: false,
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: '123',
    network: null,
  });

  // Create with network and non admin user
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
        availableForNetwork: true,
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: null,
    network: '456',
  });
});

test('getCleanedPagePresetFormData should return cleaned form data for non admin update', () => {
  const entity = { _id: '123', network: '456' };
  const user = {
    id: 1,
    name: 'John Doe',
  };
  const method = 'update';
  // Update with default entity and non admin user
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
  });
  // Create with default entity and non admin user
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
        availableForNetwork: false,
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: '123',
    network: null,
  });

  // Create with network and non admin user
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
        availableForNetwork: true,
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: null,
    network: '456',
  });
});

test('getCleanedPagePresetFormData should return cleaned form data for admin creation', () => {
  const entity = { _id: '123', network: '456' };
  const user = {
    id: 1,
    name: 'John Doe',
    isAdmin: true,
  };
  const method = 'create';

  // Create with admin user
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
  });
});

test('getCleanedPagePresetFormData should return cleaned form data for admin update', () => {
  const entity = { _id: '123', network: '456' };
  const user = {
    id: 1,
    name: 'John Doe',
    isAdmin: true,
  };
  const method = 'update';

  // Update with admin user
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
  });

  // Update with admin user and entity
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
        entity: '789',
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: '789',
    network: null,
  });

  // Update with admin user and network
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
        entity: '789',
        network: '456',
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: null,
    network: '456',
  });

  // Update with admin user and network
  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
        network: '456',
      },
      entity,
      user,
      method,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: null,
    network: '456',
  });
});

test('getCleanedPagePresetFormData should return cleaned form data for site creation', () => {
  const entity = { _id: '123', network: '456' };
  const user = {
    id: 1,
    name: 'John Doe',
  };
  const method = 'create';
  const site = {
    _id: '789',
    entity: {
      _id: '123',
    },
    design: {
      name: 'test',
      options: {
        variant: 'default',
      },
    },
  };

  expect(
    getCleanedPagePresetFormData({
      formData: {
        title: 'Test Preset',
      },
      entity,
      user,
      method,
      site,
    })
  ).toEqual({
    title: 'Test Preset',
    entity: null,
    site: '789',
    network: null,
    availableForNetwork: false,
    design: 'test',
    designVariant: 'default',
  });
});
