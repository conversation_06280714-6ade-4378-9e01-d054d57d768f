import crypto from 'crypto';

import app from '#app';
import {
  logDebug,
  logError,
  logInfo,
  logSuccess,
  logWarning,
} from '#utils/logger.js';

const logger = {
  info: logInfo,
  success: logSuccess,
  error: logError,
  warn: logWarning,
  debug: logDebug,
};

/**
 * Logs a search index task events
 * @param {Object} searchIndexTask - The search index task
 * @returns {Function} - The event logger
 */
export function getSearchIndexTaskEvents(searchIndexTask) {
  const taskId = searchIndexTask._id.toString();

  // Track sent logs to avoid duplicates
  const sentLogIds = new Set(
    searchIndexTask?.details?.logs?.map((log) => log.id)
  );

  /**
   * Log an event
   * @param {String} message - The message
   * @param {String} [level='info'] - The log level
   * @returns {void}
   */
  async function logEvent(message, level = 'info') {
    const logEntry = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      level,
      message,
    };

    const io = app.get('socketio');
    const log = logger[level] || logger.info;
    log(message);

    searchIndexTask.details.logs = searchIndexTask.details.logs || [];
    searchIndexTask.details.logs.push(logEntry);
    searchIndexTask.markModified('details.logs');

    // Save immediately to ensure persistence
    try {
      await searchIndexTask.save();
    } catch (err) {
      logger.error(`Failed to save log: ${err.message}`);
    }

    if (!sentLogIds.has(logEntry.id)) {
      sentLogIds.add(logEntry.id);
      io.of('/searchIndexTasks').to(taskId).emit('log', logEntry); // Emit to task room
    }
  }

  /**
   * Update task status
   * @param {Object} statusUpdate - The status update
   * @returns {void}
   */
  function updateTaskStatus(statusUpdate) {
    const io = app.get('socketio');
    io.of('/searchIndexTasks').to(taskId).emit('taskUpdate', statusUpdate);
  }

  return { logEvent, updateTaskStatus };
}
