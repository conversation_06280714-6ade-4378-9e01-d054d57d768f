import mongoose from 'mongoose';

import { errors } from '#utils/appError.js';

import Layout from '../models/Layout.js';

export async function getLayoutTranslations(layoutId) {
  const layoutTranslations = await Layout.aggregate()
    .match({
      source: new mongoose.Types.ObjectId(layoutId),
      deleted: false,
    })
    .lookup({
      from: 'languages',
      localField: 'language',
      foreignField: 'locale',
      as: 'language',
    })
    .unwind('$language')
    .sort({ 'language.name.en': 1 })
    .project({
      name: '$language.name',
      language: '$language.locale',
    })
    .addFields({ id: '$_id' });

  return layoutTranslations;
}

export async function addLayoutTranslation(layoutId, language) {
  // Fetch source layout
  const sourceLayout = await Layout.findById(layoutId);

  // Check if layout doesn't exists
  if (!sourceLayout) {
    throw errors.not_found('layout', layoutId);
  }

  // Check if layout already has this translation
  const existingLayout = await Layout.findOne({
    source: layoutId._id,
    language: language,
  });

  if (existingLayout) {
    throw Error('Layout already has this translation');
  }

  // Create the layout translation
  const layoutTranslation = await Layout.create({
    site: sourceLayout.site,
    language,
    source: sourceLayout._id,
  });

  return layoutTranslation;
}

export default {
  getLayoutTranslations,
  addLayoutTranslation,
};
