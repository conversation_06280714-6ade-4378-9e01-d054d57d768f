import mongoose from 'mongoose';

import factory from '#utils/handlerFactory.js';

import { isValidObjectId, toObjectId } from '#utils/api/mongoose/id.js';
import { errors } from '#utils/appError.js';
import getStatusFilter from '#utils/api/statuses/filters.js';

import Provider from '#modules/courses/models/Provider.js';
import Article from '#modules/articles/models/Article.js';
import Page from '../models/Page.js';
import Site from '../models/Site.js';

import * as pageSearchServices from './pageSearchServices.js';
import { getBlocksFromPages } from './helpers/getBlocksFromPages.js';
import Menu from '../models/Menu.js';
import pageHasContent from './helpers/pageHasContent.js';

export async function getPagesForRequest(req) {
  const pages = [];
  const site = await factory.getOne(Site, req, { paramId: 'siteId' });
  const data = await factory.getAll(Page, req, {
    filter: {
      site: site._id,
      parent: req.params.pageId || null,
      source: { $exists: false },
      deleted: false,
    },
    populate: ['subpagesCount'],
    sort: ['position'],
  });

  for (const item of data.items) {
    const ancestors = await item.getAncestors();
    const page = {
      ...item.toObject(),
      ancestors,
      ancestorsIds: ancestors.map((a) => a.id),
      hasContent: pageHasContent(item),
    };
    pages.push(page);
  }
  return { items: pages, count: data.count };
}

export async function getPagesByIds({ pageIds, status }) {
  const pages = await Page.find({
    _id: {
      $in: pageIds,
    },
    ...(status ? getStatusFilter([status]) : {}),
  });

  return { items: pages, count: pages.length };
}

export async function getPagesByDynamicResource({
  entityId,
  resourceType = 'model',
  resourceName,
  resourceValue,
  siteId,
  groupBySite = false,
}) {
  if (!resourceName) {
    return [];
  }

  const pipeline = [
    {
      $match: {
        enabled: true,
        deleted: false,
        dynamic: true,
        source: { $exists: false },
        $or: [
          {
            'dynamicResource.type': Array.isArray(resourceType)
              ? { $in: resourceType }
              : resourceType,
            'dynamicResource.name': Array.isArray(resourceName)
              ? { $in: resourceName }
              : resourceName,
            ...(resourceValue
              ? { 'dynamicResource.value': resourceValue }
              : {}),
          },
          {
            dynamicResource: resourceName,
          },
        ],
        ...(siteId ? { site: new toObjectId(siteId) } : {}),
      },
    },
    {
      $lookup: {
        from: 'sites',
        localField: 'site',
        foreignField: '_id',
        as: 'site',
      },
    },
    {
      $unwind: {
        path: '$site',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $match: {
        'site.deleted': false,
        'site.enabled': true,
        'site.entity': toObjectId(entityId),
      },
    },
    ...(siteId
      ? []
      : [
          {
            $lookup: {
              from: 'sites',
              let: { site_id: '$site._id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ['$source', '$$site_id'],
                    },
                  },
                },
                {
                  $project: {
                    _id: 0,
                    language: 1,
                  },
                },
              ],
              as: 'otherSites',
            },
          },
          {
            $unwind: {
              path: '$otherSites',
              preserveNullAndEmptyArrays: true,
            },
          },
          ...(groupBySite
            ? [
                {
                  $group: {
                    _id: '$site._id',
                    id: {
                      $first: '$site._id',
                    },
                    name: {
                      $first: '$site.name',
                    },
                    domain: {
                      $first: '$site.domain',
                    },
                    flags: {
                      $first: '$site.flags',
                    },
                    pages: {
                      $push: '$$ROOT',
                    },
                    language: {
                      $first: '$site.language',
                    },
                    languages: {
                      $addToSet: '$otherSites.language',
                    },
                  },
                },
              ]
            : []),
          {
            // We join site.language and the translation languages
            $addFields: {
              languages: {
                $setUnion: ['$languages', ['$language']],
              },
            },
          },
        ]),
    {
      $sort: {
        name: 1,
      },
    },
  ];

  const items = await Page.aggregate(pipeline);

  const count = items.length;

  return { items, count };
}

/**
 * Get pages by data source. This is more specific than getPagesByDynamicResource.
 *
 * @param {Object} options The options
 * @param {String} options.dataSourceId The data source ID
 * @param {String} options.siteId The site ID
 * @returns {Object} The pages
 * @returns {Array} items The pages
 * @returns {Number} count The count
 * @returns {Error} error The error
 */
export async function getPagesByDataSource({ dataSourceId, siteId }) {
  const pages = await Page.find({
    'deleted': false,
    'enabled': true,
    'dynamicResource.type': 'model',
    'dataSource': toObjectId(dataSourceId),
    'site': toObjectId(siteId),
    'source': null,
  }).lean();

  if (!pages) {
    return {
      error: errors.not_found('Pages'),
    };
  }

  return { items: pages, count: pages.length };
}

export async function getPage(
  pageId,
  {
    includeAncestors = false,
    includeResources = false,
    populate = [],
    siteId,
  } = {}
) {
  const query = { _id: toObjectId(pageId) };
  // Only filter by site if siteId is provided
  if (siteId) {
    query.site = toObjectId(siteId);
  }
  const page = await Page.findOne(query).populate(['preset', ...populate]);

  if (!page) {
    return null;
  }

  const ancestors = includeAncestors ? await page.getAncestors() : undefined;
  const ancestorsIds = includeAncestors
    ? ancestors.map((a) => a.id)
    : undefined;
  const resources = includeResources ? await page.getResources() : undefined;

  return {
    ...page.toObject(),
    ancestors,
    ancestorsIds,
    resources,
    hasContent: pageHasContent(page),
  };
}

export async function getPageUrlById(pageId) {
  if (!pageId) {
    return null;
  }
  const page = await Page.findOne({
    _id: pageId,
    deleted: false,
    enabled: true,
  });

  return page?.path;
}

export async function getPageTranslations(siteId, pageId, _entity, query) {
  const { sort } = query;

  const pageTranslations = await Page.aggregate()
    .match({
      site: new mongoose.Types.ObjectId(siteId),
      source: new mongoose.Types.ObjectId(pageId),
      deleted: false,
    })
    .lookup({
      from: 'languages',
      localField: 'language',
      foreignField: 'locale',
      as: 'languageInfo',
    })
    .unwind('$languageInfo')
    .sort(
      sort && sort === 'language'
        ? { 'languageInfo.name.en': 1 }
        : { _id: 1, position: 1 }
    )
    .addFields({
      id: '$_id',
    });

  return pageTranslations;
}

async function duplicatePage({ pageId, slug, title, name }) {
  const isValidId = isValidObjectId(pageId);
  if (!isValidId) throw errors.not_found();

  const currentPage = await Page.findById(pageId)
    .select('-_id -__v -createdAt -updatedAt -id -isHome')
    .lean();

  if (!currentPage) throw errors.not_found('Page', pageId);

  // Ensure page has a valid slug within its siblings
  const pageSlug = await Page.getAvailableSlug({
    slug: slug || `${currentPage.slug}-copy`,
    site: currentPage.site,
    parent: currentPage.parent,
  });

  const duplicatePageData = {
    ...currentPage,
    publishStartsAt: new Date(),
    name: name || undefined,
    slug: pageSlug,
    title: title || `${currentPage.title} Copy`,
  };

  // Create the page
  let page = await Page.create(duplicatePageData);

  // Ensure page has a position
  page = await Page.movePage(
    page,
    currentPage.position + 1,
    currentPage.parent
  );

  // Update path
  page = await page.updatePath();

  return page;
}

async function updatePage({ entity, pageId, pageData }) {
  const page = await getPage(pageId);

  if (!page) {
    return { error: errors.not_found('Page', pageId) };
  }

  // Ensures new slug doesn't exists
  const slug = pageData.slug
    ? await Page.getAvailableSlug({
        slug: pageData.slug,
        site: page.site,
        parent: page.parent,
        pageId: page._id,
      })
    : page.slug;

  const content =
    typeof pageData.content === 'string' // If content is a string,
      ? pageData.content // leave it as it is
      : JSON.stringify(pageData.content || {}); // otherwise, make it a string (fallback to empty object)

  // If the page has an HTML content, replace the escaped characters
  if (pageData.html?.enabled && pageData.html?.content) {
    pageData.html.content = pageData.html.content?.replace(/&lt;/g, '<');
  }

  // Update page's  data
  let updatedPage = await Page.findByIdAndUpdate(
    page._id,
    {
      ...pageData,
      content,
      slug,
      site: page.site, // prevents moving a page to different site
    },
    {
      new: true,
      runValidators: true,
    }
  );

  // Update path
  updatedPage = await updatedPage.updatePath();

  // Update also affected sites translations, so they all match
  const pageTranslations = pageData.translations || {};
  const currentpageId = pageId;
  const pageLanguages = Object.keys(pageTranslations);

  for (const pageLanguage of pageLanguages) {
    const translationPageId = pageTranslations[pageLanguage];

    const translations = {
      [updatedPage.language]: currentpageId,
      ...pageLanguages.reduce((acc, lang) => {
        if (pageLanguage !== lang) {
          acc[lang] = pageTranslations[lang];
        }
        return acc;
      }, {}),
    };

    await Page.findOneAndUpdate(
      { _id: toObjectId(translationPageId) },
      { translations }
    );
  }

  // Update search index - this is an async operation and does not need to be awaited
  pageSearchServices.createOrUpdatePageSearchIndex({
    entity,
    pageId: updatedPage._id,
    siteId: updatedPage.site,
  });

  return {
    page: {
      ...updatedPage.toObject(),
      hasContent: pageHasContent(updatedPage),
    },
  };
}

/**
 * Deletes a page
 * @param {Object} options
 * @param {Object} options.entity - The entity
 * @param {String} options.pageId - The page ID
 * @returns {Promise<Object>} - The result of the operation
 */
async function deletePage({ entity, pageId }) {
  const page = await Page.findById(pageId);

  if (!page) {
    return { error: errors.not_found('Page', pageId) };
  }

  // Soft delete page
  const deletedPage = await Page.findByIdAndUpdate(
    pageId,
    { deleted: true },
    { new: true }
  );

  const { error } = await deletePageFromIndex({
    entity,
    page: deletedPage,
  });

  if (error) {
    return { error };
  }

  return { deletedPage };
}

/**
 * Enables a page
 * @param {Object} options
 * @param {Object} options.entity - The entity
 * @param {String} options.pageId - The page ID
 * @returns {Promise<Object>} - The result of the operation
 */
async function enablePage({ entity, pageId }) {
  const page = await Page.findById(pageId);

  if (!page) {
    return { error: errors.not_found('Page', pageId) };
  }

  // Soft delete page
  const updatedPage = await Page.findByIdAndUpdate(
    pageId,
    { enabled: true },
    { new: true }
  );

  // Update search index - this is an async operation and does not need to be awaited
  pageSearchServices.createOrUpdatePageSearchIndex({
    entity,
    pageId: updatedPage._id,
    siteId: updatedPage.site,
  });

  return {
    updatedPage: {
      ...updatedPage.toObject(),
      hasContent: pageHasContent(updatedPage),
    },
  };
}

/**
 * Disables a page
 * @param {Object} options
 * @param {Object} options.entity - The entity
 * @param {String} options.pageId - The page ID
 * @returns {Promise<Object>} - The result of the operation
 */
async function disablePage({ entity, pageId }) {
  const page = await Page.findById(pageId);

  if (!page) {
    return { error: errors.not_found('Page', pageId) };
  }

  // Soft delete page
  const updatedPage = await Page.findByIdAndUpdate(
    pageId,
    { enabled: false },
    { new: true }
  );

  const { error } = await deletePageFromIndex({
    entity,
    page: updatedPage,
  });

  if (error) {
    return { error };
  }

  return {
    updatedPage: {
      ...updatedPage.toObject(),
      hasContent: pageHasContent(updatedPage),
    },
  };
}

async function deletePageFromIndex({ entity, page }) {
  if (!page) {
    return { error: errors.not_found('Page') };
  }

  const tasks = [];

  const { tasks: deletePageTasks, error } =
    await pageSearchServices.deletePageDocumentFromIndex({
      entityId: entity._id,
      pageId: page.source || page.id,
      siteId: page.site,
      language: page.source ? page.language : null,
    });

  if (error) {
    return { error };
  }

  tasks.push(...deletePageTasks);

  if (!page.source) {
    const subPages = await getSubPages({ pageId: page._id });

    for (const subPage of subPages) {
      const { tasks: deleteSubpageTasks, error: subPageError } =
        await pageSearchServices.deletePageDocumentFromIndex({
          entityId: entity._id,
          pageId: subPage._id,
          siteId: page.site,
        });

      if (subPageError) {
        return { error: subPageError };
      }

      tasks.push(...deleteSubpageTasks);
    }
  }

  return { tasks };
}

async function getSubPages({ pageId }) {
  const subpages = await Page.find({
    parent: toObjectId(pageId),
    deleted: false,
    enabled: true,
  }).lean();

  const pages = [];

  for (const subpage of subpages) {
    pages.push(subpage);

    // Get subpages of subpage
    const subpagesOfSubpage = await getSubPages({ pageId: subpage._id });
    pages.push(...subpagesOfSubpage);
  }

  return pages;
}

/**
 * Checks if a page is being used, and by what, to know if it can be deleted. Returns the places where the page is being used.
 * @param {Object} options
 * @param {String} options.pageId - The page ID
 *
 * @returns {Promise<Object>} - The places where the page is being used
 */
async function isPageInUse({ pageId, siteId }) {
  const page = await Page.findById(pageId);
  if (!page) {
    return { error: errors.not_found('Page', pageId) };
  }

  const references = [];

  // Check if page has subpages.
  const subpages = await Page.find({
    parent: page._id,
    deleted: false, // We only want those not deleted. Being enabled or not, the page would still be in use unless deleted.
  });

  if (subpages.length > 0) {
    subpages.forEach((subpage) => {
      references.push({
        type: 'subpage',
        id: subpage._id,
        title: subpage.name || subpage.title,
      });
    });
  }

  // Check if the page is being used in a block. (Of which the page is not deleted, nor the block hidden?). Find if the page id is in the content string.
  // Only look for blocks in the same site. There might be copied blocks in other sites. They are broken references, but dont count as in use.
  const blocksInPages = await Page.find({
    site: siteId,
    content: { $regex: new RegExp(page._id, 'i') }, // Doesn't need to be toString because mongoose does it for us.
    deleted: false,
  });

  // With the pages, we can extract the blocks that contain the page.
  if (blocksInPages.length > 0) {
    const blocks = getBlocksFromPages({
      pages: blocksInPages,
      searchTerm: page._id.toString(),
    });

    blocks.forEach((block) => {
      references.push({
        type: 'block',
        blockId: block.blockId, // Used to select the block when the page is opened.
        id: block.pageId, // Page is added where the block is found.
        title: block.displayName,
      });
    });
  }

  // Check if the page is being used in a menu.
  const menus = await Menu.find({
    deleted: false,
    enabled: true,
    site: page.site, // Only look for menus in the same site
  });

  // Recursive search through nested objects, where if the page id is found, it returns true.
  function deepSearch(obj) {
    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        if (deepSearch(obj[key])) return true;
      } else if (key === 'value' && obj[key] === pageId) {
        return true;
      }
    }
    return false;
  }

  // Examine the menus to find the page.
  if (menus.length > 0) {
    menus.forEach((menu) => {
      // Recursive search through nested objects
      if (deepSearch(menu.items)) {
        references.push({
          type: 'menu',
          id: menu._id,
          title: menu.title,
        });
      }
    });
  }

  // Check if the page is being used in a course provider for messages.

  const providers = await Provider.find({
    deleted: false,
    enabled: true,
    $or: [
      { 'pages.messagesPage': page._id.toString() },
      { 'pages.courseContentPage': page._id.toString() },
      { 'pages.coursesListPage': page._id.toString() },
      { 'pages.pushMessagesPage': page._id.toString() },
      { 'pages.pushCourseContentPage': page._id.toString() },
    ],
  });

  const getTab = (provider, id) => {
    if (
      provider.pages.messagesPage === id ||
      provider.pages.courseContentPage === id ||
      provider.pages.coursesListPage === id
    ) {
      return 'email';
    }
    if (
      provider.pages.pushMessagesPage === id ||
      provider.pages.pushCourseContentPage === id
    ) {
      return 'pushNotifications';
    }
    return null;
  };

  if (providers.length > 0) {
    providers.forEach((provider) => {
      references.push({
        type: 'courseProvider',
        tab: getTab(provider, page._id.toString()),
        id: provider._id,
        title: provider.title,
      });
    });
  }

  // Find canonical site page references in articles.
  const articles = await Article.countDocuments({
    deleted: false,
    enabled: true,
    canonicalSitePage: page._id.toString(),
  });

  if (articles > 0) {
    references.push({
      type: 'article',
      count: articles,
    });
  }

  // Check if the page is being used as an article site page (for canonicalUrl).
  return { inUse: references.length > 0, references };
}

export default {
  getPage,
  getPagesByIds,
  getPageUrlById,
  getPageTranslations,
  getPagesForRequest,
  getPagesByDynamicResource,
  getPagesByDataSource,
  getSubPages,
  duplicatePage,
  updatePage,
  deletePage,
  enablePage,
  disablePage,
  isPageInUse,
};
