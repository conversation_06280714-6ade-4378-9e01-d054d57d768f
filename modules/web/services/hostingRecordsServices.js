/**
 * Used to save the automated site records to the database with the model.
 *
 */
import mongoose from 'mongoose';
import Logger from '#utils/logger.js';

import getListFilters from '#utils/api/list/filters.js';
import HostingRecord from '../models/HostingRecord.js';

// Adds a new record to the database
export const startHostingRecord = async ({
  entityId,
  domain,
  site,
  type = 'automated-site',
  user,
  transferEntity,
}) => {
  const filter =
    type === 'automated-site'
      ? { entity: entityId, type: 'automated-site' }
      : { site: site._id };

  const hostingRecord =
    (await HostingRecord.findOne({
      ...filter,
      endDate: null,
    })) || {};

  // Check if the site is a translation, so we don't create a new record if its a translation
  const isNotTranslation = type === 'full-site' ? !site.source : true; // only full sites have translations.

  // If there is no existing record, we create a new one
  if (!hostingRecord._id && isNotTranslation) {
    await HostingRecord.create({
      startDate: new Date(),
      entity: entityId,
      domain,
      site,
      type,
      startedBy: user,
      transferEntity,
    });
    Logger.info(
      `Hosting record created for ${
        type === 'automated-site' ? `entity of id ${entityId}` : site.name
      }`
    );
  }
};

export const endHostingRecord = async ({ entityId, user, site }) => {
  // If its an automated site, we filter by entity, otherwise we filter by site
  const filter = entityId
    ? { entity: entityId, type: 'automated-site' }
    : { site: site._id };

  // We find the latest record that is not ended
  // TODO: Check that there is not more than 1 record that is not ended.
  const hostingRecord = await HostingRecord.findOne({
    ...filter,
    endDate: null,
  });

  if (hostingRecord) {
    hostingRecord.endDate = new Date();
    hostingRecord.endedBy = user;
    await hostingRecord.save();

    Logger.info(
      `Hosting record ended for ${
        entityId ? `entity of id ${entityId}` : site.name
      }`
    );
  }
};

export async function getHostingRecords({
  entityId,
  noEntity = false, // Used to get all records, regardless of entity for the admin view.
  search = '',
  sort,
  limit = 10,
  page = 1,
  skip = 0,
  initialDate = `${new Date().getFullYear()} '-01-01'`,
  lastDate = new Date(),
  reportId, // A report is the collection of records grouped together of a full-site or automated-site
  type = 'all',
} = {}) {
  const filters =
    getListFilters({
      search,
      sort: `${sort},-startDate`,
      limit,
      page,
      skip,
      sortFields: [
        'domain',
        'startDate',
        'endDate',
        'site.name',
        'entity.name',
        'type',
        'network',
      ],
      searchFields: ['site.name', 'entity.name', 'domain', 'network'],
    }) || {};

  const lowerCutoff = new Date(initialDate);
  const now = new Date();
  const upperCutoff = new Date(lastDate) > now ? now : new Date(lastDate);

  // We build the pipeline for the aggregation query
  const pipeline = [
    // filter by date, where the start date is before the upper cutoff and the end date is after the lower cutoff
    {
      $match: {
        $and: [
          { startDate: { $lte: upperCutoff } },
          {
            $or: [
              { endDate: { $gte: lowerCutoff } },
              { endDate: { $exists: false } },
            ],
          },
        ],
      },
    },

    // With this sort, we can use the $first operator to get the latest hosting record of a distinct record
    { $sort: { startDate: -1 } },
    // We add the adjusted start and end dates to be used in calculating the time hosted since the lower cutoff, and not before.
    {
      $addFields: {
        // If the start date is before the lower cutoff, we use the lower cutoff as the start date
        adjustedStartDate: {
          $cond: [
            {
              $lt: ['$startDate', lowerCutoff],
            },
            lowerCutoff,
            '$startDate',
          ],
        },
        // If endDate does not exist, we use the current date
        adjustedEndDate: {
          $ifNull: ['$endDate', upperCutoff],
        },
        //
        isRunning: {
          $not: ['$endDate'],
        },
      },
    },
    {
      $group: {
        // If the type is full-site, we group by site, otherwise we group by entity, to form distinct records.
        _id: {
          $cond: [{ $eq: ['$type', 'full-site'] }, '$site', '$entity'],
        },
        type: { $first: '$type' },
        // We also push the start and end dates, and the started and ended by users, to be used in the detail views.
        adjustedStartDate: { $push: '$adjustedStartDate' },
        adjustedEndDate: { $push: '$adjustedEndDate' },
        startDates: { $push: '$startDate' },
        startDate: { $first: '$startDate' },
        endDates: { $push: '$endDate' },
        endDate: { $first: '$endDate' },
        domains: { $push: '$domain' },
        domain: { $first: '$domain' },
        site: { $first: '$site' },
        entity: { $first: '$entity' },
        updatedAt: { $first: '$updatedAt' },
        startedBy: { $push: '$startedBy' },
        endedBy: { $push: '$endedBy' },
        isRunning: { $first: '$isRunning' },
        // We sum the days hosted, by subtracting the start date from the end date, or the start date from the current date if the end date does not exist.
        days: {
          $sum: {
            $toInt: {
              $divide: [
                {
                  $subtract: ['$adjustedEndDate', '$adjustedStartDate'],
                }, // Subtract start date from end date
                24 * 60 * 60 * 1000, // Milliseconds to days conversion
              ],
            },
          },
        },
      },
    },

    {
      $lookup: {
        from: 'entities',
        localField: 'entity',
        foreignField: '_id',
        as: 'entity',
      },
    },
    { $unwind: '$entity' },

    {
      $lookup: {
        from: 'sites',
        localField: 'site',
        foreignField: '_id',
        as: 'site',
      },
    },
    { $unwind: '$site' },

    // Entity has an array of ancestors. We populate those and get the network from the first ancestor that has a network.
    {
      $lookup: {
        from: 'entities',
        localField: 'entity.ancestors',
        foreignField: '_id',
        as: 'ancestors',
      },
    },
    {
      $addFields: {
        ancestors: {
          $map: {
            input: '$ancestors',
            as: 'ancestor',
            in: {
              network: '$$ancestor.network', // only the network field is needed
            },
          },
        },
      },
    },

    // Add the first ancestor found with a network to a network field in root
    {
      $addFields: {
        network: {
          $let: {
            vars: {
              firstAncestorWithNetwork: {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: '$ancestors',
                      cond: { $ne: ['$$this.network', null] },
                    },
                  },
                  0,
                ],
              },
            },
            in: '$$firstAncestorWithNetwork.network',
          },
        },
      },
    },

    {
      $addFields: {
        network: {
          $cond: {
            if: { $gt: [{ $type: '$entity.network' }, 'missing'] }, // If the entity network exists
            then: '$entity.network',
            else: '$ancestors.network',
          },
        },
      },
    },

    // Populate the network title from the network field
    {
      $lookup: {
        from: 'networks',
        localField: 'network',
        foreignField: '_id',
        as: 'network',
      },
    },

    {
      $addFields: {
        network: {
          $arrayElemAt: ['$network.title', 0],
        },
      },
    },
  ];

  if (!noEntity) {
    pipeline.push(
      ...[
        {
          $match: {
            $or: [
              {
                'entity._id': {
                  $eq: new mongoose.Types.ObjectId(entityId),
                },
              },
              {
                'entity.ancestors': {
                  $in: [new mongoose.Types.ObjectId(entityId)],
                },
              },
            ],
          },
        },
      ]
    );
  }

  pipeline.push(
    ...[
      {
        $match: {
          'site.source': {
            $exists: false,
          },
        },
      },
      {
        $project: {
          '_id': 1,
          'domain': 1,
          'domains': 1,
          'entity.name': 1,
          'network': 1,
          'site.name': 1,
          'site.templateSite.enabled': 1,
          'site.title': 1,
          'site.deleted': 1,
          'type': 1,
          'startDate': 1,
          'startDates': 1,
          'endDate': 1,
          'endDates': 1,
          'startedBy': 1,
          'endedBy': 1,
          'days': 1,
          'adjustedStartDate': 1,
          'adjustedEndDate': 1,
          'isRunning': 1,
        },
      },
    ]
  );

  // If a report id is provided, we filter by the report id
  if (reportId) {
    pipeline.push(
      ...[{ $match: { _id: new mongoose.Types.ObjectId(reportId) } }]
    );
  }

  // We run the aggregation query and sort and paginate the results
  const countPromise = HostingRecord.aggregate(pipeline);
  try {
    const countItems = await countPromise;

    const totals = {
      total: countItems.length,
      fullSite: countItems.filter(
        (item) =>
          item.type === 'full-site' &&
          (item?.site?.templateSite?.enabled === false ||
            !item?.site?.templateSite?.enabled)
      ).length,
      automatedSite: countItems.filter((item) => item.type === 'automated-site')
        .length,
      templateSite: countItems.filter(
        (item) =>
          item.type === 'full-site' &&
          item?.site?.templateSite?.enabled === true
      ).length,
    };

    // now we run it again with the sort, skip and limit
    const items = await HostingRecord.aggregate(pipeline)
      // for every searchField we match the search term
      .match({
        ...filters.search,
        ...((type === 'full-site' || type === 'automated-site') && {
          type: { $eq: type },
        }),
        ...(type === 'template-site' && {
          $and: [
            {
              'site.templateSite.enabled': true,
            },
            {
              type: 'full-site',
            },
          ],
        }),
      })
      .sort(filters.sort)
      .skip(filters.pagination.skip)
      .limit(filters.pagination.limit);
    // console.log(JSON.stringify(items, null, 2));

    return { items, count: totals.total, totals };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
    // if the aggregation query fails, we return empty results
    return { items: [], count: 0, totals: {} };
  }
}

export default {
  startHostingRecord,
  endHostingRecord,
  getList: getHostingRecords,
};
