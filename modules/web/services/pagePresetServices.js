import getSearchFilters from '#utils/api/search/filters.js';
import getPaginationFilters from '#utils/api/pagination/filters.js';
import { errors } from '#utils/appError.js';
import { toObjectId } from '#utils/api/mongoose/id.js';

import PagePreset from '../models/PagePreset.js';
import siteServices from './siteServices.js';
import { getCleanedPagePresetFormData } from './helpers/getCleanedPagePresetFormData.js';

async function getAll({
  design,
  designVariant,
  designs,
  entity,
  groups,
  limit = 25,
  page,
  search = '',
  site,
  skip,
}) {
  const pagination = getPaginationFilters({ limit, page, skip });

  const filters = {
    ...getSearchFilters({ search, fields: ['title', 'description'] }),
    ...(groups?.length && { group: { $in: groups } }),
    ...(designs?.length && { design: { $in: designs } }),
    deleted: false,
  };

  if (site) {
    filters.$and = [
      ...(filters.$and || []),
      {
        $or: [{ site: toObjectId(site) }, { site: null }],
      },
    ];
  }

  if (entity) {
    filters.$and = [
      ...(filters.$and || []),
      {
        $or: [
          { $and: [{ entity: null }, { network: null }] },
          { entity: entity._id },
          ...(entity?.network ? [{ network: entity.network }] : []),
        ],
      },
    ];
  }

  if (design) {
    filters.$and = [
      ...(filters.$and || []),
      { $or: [{ design }, { design: '' }, { design: null }] },
    ];

    if (designVariant) {
      filters.$and = [
        ...(filters.$and || []),
        {
          $or: [
            { designVariant },
            { designVariant: '' },
            { designVariant: null },
          ],
        },
      ];
    }
  }

  const items = await PagePreset.find({
    ...filters,
  })
    .populate(['pagesCount', 'entity', 'network', 'site'])
    .skip(pagination.skip)
    .limit(pagination.limit) // NOTE: limit must be after skip!
    .sort({ title: 1 });

  const count = await PagePreset.find(filters).countDocuments();

  return { items, count };
}

async function createPagePreset({ formData, entity, user }) {
  const { site: siteId, ...restFormData } = formData;

  let site;

  if (siteId) {
    const { site: foundSite, error } = await siteServices.getSiteById(siteId);

    if (error) {
      return {
        error,
      };
    }

    site = foundSite;
  }

  const cleanedFormData = getCleanedPagePresetFormData({
    formData: restFormData,
    entity,
    site,
    user,
    method: 'create',
  });

  const pagePreset = await PagePreset.create(cleanedFormData);

  return { pagePreset };
}

async function updatePagePreset({ pagePresetId, formData, entity, user }) {
  const { site: siteId, ...restFormData } = formData;

  let site;

  if (siteId) {
    const { site: foundSite, error } = await siteServices.getSiteById(siteId);

    if (error) {
      return {
        error,
      };
    }

    site = foundSite;
  }

  const cleanedFormData = await getCleanedPagePresetFormData({
    formData: restFormData,
    entity,
    site,
    user,
    method: 'update',
  });

  const pagePreset = await PagePreset.findOneAndUpdate(
    {
      _id: pagePresetId,
    },
    cleanedFormData,
    {
      new: true,
      runValidators: true,
    }
  ).populate(['pagesCount', 'entity', 'network']);

  if (!pagePreset) {
    throw errors.not_found('PagePreset', pagePresetId);
  }

  return pagePreset;
}

export default {
  getAll,
  createPagePreset,
  updatePagePreset,
};
