import Page from '../models/Page.js';

// Imports Menu and PagePreset models, so mongoose can register them
import '../models/Menu.js';
import '../models/PagePreset.js';

/**
 * Returns mapping table for pages (key: dynamicRecord (showId, episodeId), value: pageId)
 *
 * @param {String} siteId - Site ID
 * @param {String} resource - Dynamic resource (Show, Episode, Person)
 * @returns {Object} Mapping table for pages
 */
export async function getDynamicRecordPagesByResource(siteId, resource) {
  const detailPages = await Page.find({
    site: siteId,
    dynamicRecord: { $ne: null },
    deleted: false,
    enabled: true,
    $or: [
      {
        'dynamicResource.type': 'model',
        'dynamicResource.name': resource,
      },
      {
        dynamicResource: resource,
      },
    ],
  });

  const mapping = {};

  for (const page of detailPages) {
    const { dynamicRecord } = page;
    if (dynamicRecord) mapping[dynamicRecord] = page;
  }

  return mapping;
}

export default {
  getDynamicRecordPagesByResource,
};
