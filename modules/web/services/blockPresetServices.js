import getSearchFilters from '#utils/api/search/filters.js';
import getPaginationFilters from '#utils/api/pagination/filters.js';
import { errors } from '#utils/appError.js';
import { toObjectId } from '#utils/api/mongoose/id.js';

import BlockPreset from '../models/BlockPreset.js';
import { getCleanedBlockPresetFormData } from './helpers/getCleanedBlockPresetFormData.js';

async function getBlockPresetsList({
  design,
  designVariant,
  designs,
  entity,
  groups,
  limit = 25,
  page,
  search = '',
  site,
  skip,
}) {
  const pagination = getPaginationFilters({ limit, page, skip });

  const filters = {
    ...getSearchFilters({
      search,
      fields: ['name', 'title.en', 'description'], // TODO: make title search for all languages
    }),
    ...(groups?.length && { group: { $in: groups } }),
    ...(designs?.length && { design: { $in: designs } }),
    deleted: false,
  };

  if (site) {
    filters.$and = [
      ...(filters.$and || []),
      {
        $or: [{ site: toObjectId(site) }, { site: null }],
      },
    ];
  }

  if (entity) {
    filters.$and = [
      ...(filters.$and || []),
      {
        $or: [
          { $and: [{ entity: null }, { network: null }] },
          { entity: entity._id },
          ...(entity?.network ? [{ network: entity.network }] : []),
        ],
      },
    ];
  }

  if (design) {
    filters.$and = [
      ...(filters.$and || []),
      { $or: [{ design }, { design: '' }, { design: null }] },
    ];

    if (designVariant) {
      filters.$and = [
        ...(filters.$and || []),
        {
          $or: [
            { designVariant },
            { designVariant: '' },
            { designVariant: null },
          ],
        },
      ];
    }
  }

  const items = await BlockPreset.find({
    ...filters,
  })
    .populate(['entity', 'network', 'site'])
    .skip(pagination.skip)
    .limit(pagination.limit) // NOTE: limit must be after skip!
    .sort({ title: 1 });

  const count = await BlockPreset.find(filters).countDocuments();

  return { items, count };
}

/**
 * Check if a block preset name is available for a given design
 * @param {Object} params
 * @param {String} params.name Block preset name to check
 * @param {String} params.design Design name where the name should be unique
 * @returns {Object} Object with available key (Boolean)
 */
async function checkBlockPresetName({ name, design }) {
  const filters = {
    name,
    design,
    deleted: false,
  };

  const blockPreset = await BlockPreset.findOne(filters);

  return { available: !blockPreset };
}

async function createBlockPreset({ formData, entity, user }) {
  const cleanedFormData = getCleanedBlockPresetFormData({
    formData,
    entity,
    user,
    method: 'create',
  });

  const blockPreset = await BlockPreset.create(cleanedFormData);

  return { blockPreset };
}

async function updateBlockPreset({ blockPresetId, formData, entity, user }) {
  const cleanedFormData = await getCleanedBlockPresetFormData({
    formData,
    entity,
    user,
    method: 'update',
  });

  const blockPreset = await BlockPreset.findOneAndUpdate(
    {
      _id: blockPresetId,
    },
    cleanedFormData,
    {
      new: true,
      runValidators: true,
    }
  ).populate(['entity', 'network']);

  if (!blockPreset) {
    throw errors.not_found('BlockPreset', blockPresetId);
  }

  return blockPreset;
}

export default {
  getBlockPresetsList,
  checkBlockPresetName,
  createBlockPreset,
  updateBlockPreset,
};
