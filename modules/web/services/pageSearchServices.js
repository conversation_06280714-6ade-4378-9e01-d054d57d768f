import featureFlagService from '#modules/feature-flags/services/featureFlagService.js';
import documentService from '#modules/search/services/documentService.js';
import { toObjectId } from '#utils/api/mongoose/id.js';
import { errors } from '#utils/appError.js';

import searchIndexService from '#modules/search/services/searchIndexService.js';
import Page from '../models/Page.js';
import { PAGES_INDEX_NAME } from '../search/pages/constants.js';
import { getPagesContentForIndexing } from './helpers/getPagesContentForIndexing.js';
import { getSiteMapPathsForSite } from './helpers/getSiteMapPathsForSite.js';
import { indexPageContentChunk } from './helpers/indexPageContentChunk.js';
import { getSearchIndexTaskEvents } from './helpers/logSearchIndexTaskEvent.js';

/**
 * Creates the search index for all pages
 *
 * @param {Object} options
 * @param {Object} options.entity - The entity object
 * @param {String} options.language - The language to index
 * @param {Object} options.site - The site object
 *
 * @returns {Promise<Object>} - The result of the operation
 */
export async function createPagesIndex({ entity, language, name, site }) {
  if (!entity || !site || !language) {
    return { error: errors.params(['entity', 'site', 'language']) };
  }

  return await searchIndexService.createSearchIndex({
    entityId: entity._id,
    language,
    name: name || `Pages (${language})`,
    siteId: toObjectId(site._id),
    type: PAGES_INDEX_NAME,
  });
}

/**
 * Deletes the page from all indexes
 * @param {Object} options
 * @param {Object} options.entityId - The entity Id
 * @param {String} [options.language] - The language to filter by
 * @param {String} options.pageId - The page Id
 * @param {Object} options.siteId - The site Id
 * @returns {Promise<Object>} - The result of the operation
 */
export async function deletePageDocumentFromIndex({
  entityId,
  language,
  pageId,
  siteId,
}) {
  if (!entityId || !siteId || !pageId) {
    return { error: errors.params(['entity', 'site', 'page']) };
  }

  const { items, error } = await searchIndexService.getSearchIndexes({
    entityId: entityId.toString(),
    language,
    limit: 0,
    siteId: siteId.toString(),
    type: PAGES_INDEX_NAME,
  });

  if (error) {
    return { error };
  }

  const tasks = [];

  for (const searchIndex of items) {
    const { result, error: deleteDocumentError } =
      await documentService.deleteDocumentsByFilter({
        indexUId: searchIndex._id.toString(),
        filter: `page = ${pageId.toString()}`,
      });

    if (deleteDocumentError) {
      return { error: deleteDocumentError };
    }

    tasks.push(result);
  }

  return { tasks };
}

/**
 * Creates or updates the search index for a page
 *
 * @param {Object} options
 * @param {Object} options.entity - The entity of the page
 * @param {String} options.pageId - The ID of the page to index
 * @param {String} options.siteId - The ID of the site the page belongs to
 *
 * @returns {Promise<Object>} - The result of the operation
 */
export async function createOrUpdatePageSearchIndex() {
  // TODO: Re implement indexing of a single page?
}

/**
 * Gets all model resources to index for a site
 *
 * @param {Object} options
 * @param {Object} options.site - The site object
 *
 * @returns {Object} The pages to index or an error
 */
export async function getModelResourcesBySite({ site }) {
  const filter = {
    'site': toObjectId(site._id),
    'enabled': true,
    'deleted': false,
    'noIndex': { $ne: true },
    'password': { $in: ['', null] },
    'xml.enabled': { $ne: true },
    'json.enabled': { $ne: true },
    'text.enabled': { $ne: true },
    'source': null,
    'dynamicResource': { $ne: null },
  };

  // Get the sites indexable pages with ancestors
  const pages = await Page.find(filter).lean();

  if (!pages || pages.length === 0) {
    return { error: errors.not_found('Pages') };
  }

  const uniqueResources = new Set(
    pages
      .filter(
        ({ dynamicResource }) =>
          typeof dynamicResource === 'string' ||
          dynamicResource?.type === 'model'
      )
      .map(({ dynamicResource }) =>
        typeof dynamicResource === 'string'
          ? dynamicResource
          : dynamicResource.name
      )
  );

  return { resources: Array.from(uniqueResources) };
}

/**
 * Re-index all pages for a site
 *
 * @param {Object} options
 * @param {Object} options.searchIndex The site search index
 * @param {Object} options.searchIndexTask The search index task
 * @param {Object} options.site The site object
 * @param {Boolean} [options.useLastSync] - Whether to use the last sync date
 *
 * @returns {Object} The indexing tasks or an error
 */
export async function reIndexPages({
  site,
  searchIndex,
  searchIndexTask,
  useLastSync = false,
}) {
  const isEnabled = featureFlagService.isFeatureEnabled('site-search', {
    site,
  });

  if (!isEnabled) {
    return { error: errors.not_implemented() };
  }

  if (!site || !searchIndex || !searchIndexTask) {
    return {
      error: errors.params(['site', 'searchIndex', ['searchIndexTask']]),
    };
  }

  const { _id: searchIndexId, type, language } = searchIndex.toObject();
  const indexUId = searchIndexId.toString();

  const { logEvent, updateTaskStatus } =
    getSearchIndexTaskEvents(searchIndexTask);

  if (type !== PAGES_INDEX_NAME) {
    await logEvent(`Invalid index type: ${type}`, 'error');
    // Abort the indexing process
    return;
  }

  await logEvent('-------------------------------');
  await logEvent(`Indexing pages for site: ${site.domain}`);
  await logEvent('-------------------------------');

  // Check if this is a resumption
  const isResuming =
    searchIndexTask.status === 'processing' && !searchIndexTask.finishedAt;

  if (!isResuming) {
    searchIndexTask.status = 'processing';
    searchIndexTask.startedAt = new Date();
    searchIndexTask.details = {
      runType: useLastSync ? 'incremental' : 'full',
      paths: {
        remaining: [],
      },
      content: {
        totalChunks: 0,
        processedChunks: 0,
        failedChunks: 0,
        chunks: [],
      },
      failed: {
        count: 0,
        items: [],
      },
      logs: searchIndexTask.details.logs || [],
    };
    await searchIndexTask.save();

    updateTaskStatus({
      status: 'processing',
      startedAt: searchIndexTask.startedAt,
    });
  }

  const failTask = async (message, errorDetails) => {
    await logEvent(message, 'error');
    searchIndexTask.status = 'failed';
    searchIndexTask.finishedAt = new Date();

    if (errorDetails) {
      searchIndexTask.details = { ...searchIndexTask.details, ...errorDetails };
    }

    await searchIndexTask.save();

    updateTaskStatus({
      status: 'failed',
      finishedAt: searchIndexTask.finishedAt,
    });
  };

  try {
    if (!searchIndexTask.details.resources) {
      // ------------------------------
      // 1. First get the page resource that should be indexed

      await logEvent('');
      await logEvent('Fetching resources to index');

      const { resources, error: resourcesError } =
        await getModelResourcesBySite({
          site,
        });

      if (resourcesError) {
        return await failTask(
          `Error fetching resources: ${resourcesError.message}`,
          {
            resources: { error: resourcesError },
          }
        );
      }

      searchIndexTask.details.resources = resources;
      searchIndexTask.markModified('details.resources');
      await searchIndexTask.save();
    }

    // ------------------------------
    // 2. Get the sitemap URLs per resource

    await logEvent('');
    await logEvent(
      `Found ${searchIndexTask.details.resources.length} resources to index`
    );

    if (
      !searchIndexTask.details.paths ||
      !searchIndexTask.details.paths.remaining.length
    ) {
      await logEvent('Retrieving all site map paths for resources');

      const { paths, error: pathsError } = await getSiteMapPathsForSite({
        resources: searchIndexTask.details.resources,
        site,
        updatedSince: useLastSync ? searchIndex.lastIndexedAt : null,
      });

      if (pathsError) {
        return await failTask(`Error retrieving paths: ${pathsError.message}`, {
          paths: { error: pathsError },
        });
      }

      searchIndexTask.details.paths = paths;
      searchIndexTask.details.paths.remaining = Array.from(
        paths[language] || []
      );
      searchIndexTask.markModified('details.paths');
      await searchIndexTask.save();

      if (!paths[language]) {
        await logEvent(`No paths found for language: ${language}`, 'warn');

        searchIndexTask.status = 'cancelled';
        searchIndexTask.finishedAt = new Date();
        await searchIndexTask.save();

        updateTaskStatus({
          status: 'cancelled',
          finishedAt: searchIndexTask.finishedAt,
        });

        // Abort the indexing process
        return;
      }
    }

    // paths = sitePaths;
    const languagePaths = isResuming
      ? searchIndexTask.details.paths.remaining
      : Array.from(searchIndexTask.details.paths[language]);

    // ------------------------------
    // 3. Retrieve the JSON content for the pages
    await logEvent('');
    await logEvent(
      `Found ${languagePaths.length} paths to index for language: ${language}`
    );
    await logEvent('Retrieving content for all paths');

    await getPagesContentForIndexing({
      indexPageContentChunk: indexPageContentChunk({
        indexUId,
        language,
        logEvent,
        site,
      }),
      language,
      paths: languagePaths,
      searchIndexTask,
      site,
      logEvent,
    });

    const failedCompletely =
      searchIndexTask.details.content.failedChunks ===
      searchIndexTask.details.content.totalChunks;
    searchIndexTask.status = failedCompletely ? 'failed' : 'completed';

    if (!failedCompletely) {
      searchIndex.lastIndexedAt = new Date();
    }
    searchIndexTask.finishedAt = new Date();

    if (failedCompletely) {
      await logEvent(
        'Pages content could not be retrieved for indexing',
        'error'
      );
    }
    await logEvent(
      `Indexing ${isResuming ? 'resumption' : 'initiation'} complete for site: ${site.domain}`
    );
    await logEvent('-------------------------------');

    await Promise.all([searchIndexTask.save(), searchIndex.save()]);

    updateTaskStatus({
      status: searchIndexTask.status,
      finishedAt: searchIndexTask.finishedAt,
      lastIndexedAt: searchIndex.lastIndexedAt,
    });
  } catch (error) {
    return await failTask(
      `Unexpected error during indexing: ${error.message}`,
      {
        error: error.stack || error.message,
      }
    );
  }
}
