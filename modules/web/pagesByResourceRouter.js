import express from 'express';

import { validate } from '#utils/validationMiddleware.js';

import { pagesByResourceSchema } from './validation/pagesByResourceValidation.js';
import pageController from './controllers/pageController.js';

const pagesByResourceRouter = express.Router();

pagesByResourceRouter
  .route('/')
  .get(validate(pagesByResourceSchema, 'query'), pageController.getAllPages);

export default pagesByResourceRouter;
