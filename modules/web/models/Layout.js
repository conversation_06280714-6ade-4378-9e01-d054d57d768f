import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const layoutSchema = SchemaFactory({
  name: {
    type: String,
    trim: true,
  },
  description: {
    type: String,
  },
  language: {
    type: String,
  },
  source: {
    type: mongoose.Types.ObjectId,
    ref: 'Layout',
  },
  site: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
  },
  design: {
    type: mongoose.SchemaTypes.Mixed,
  },
  content: {
    type: String,
  },
});

// Indexes
layoutSchema.index({ name: 1, description: 1, site: 1 });
layoutSchema.index({ language: 1, site: 1 });

export default mongoose.model('Layout', layoutSchema);
