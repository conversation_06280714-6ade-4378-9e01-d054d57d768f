import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const menuSchema = SchemaFactory({
  title: {
    type: String,
    trim: true,
  },
  site: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
  },
  automatic: {
    type: Boolean,
    default: true,
  },
  language: {
    type: String,
  },
  source: {
    type: mongoose.Types.ObjectId,
    ref: 'Menu',
  },

  // Fields for the "manual" mode:
  items: {
    type: mongoose.SchemaTypes.Mixed,
    default: { ROOT: { items: [] } },
  },

  // Fields for the "automatic" mode
  rootPage: {
    type: String,
  },
  maxLevels: {
    type: Number,
  },
});

menuSchema.methods.render = async function () {
  return this.automatic ? this.renderAutomatic() : this.renderManual();
};

menuSchema.methods.renderAutomatic = async function (
  parentId = null,
  currentLevel = 1
) {
  const pages = await mongoose.model('Page').find({
    parent: parentId || this.rootPage || { $eq: null },
    site: this.site,
    $or: [{ dynamic: { $eq: null } }, { dynamic: false }],
    enabled: true,
    deleted: false,
  });

  const items = [];

  for (const page of pages) {
    items.push({
      label: page.title,
      url: `${page.path}`,
      items:
        currentLevel <= this.maxLevels
          ? await this.renderAutomatic(page.id, currentLevel + 1)
          : undefined,
    });
  }

  return items.length > 0 ? items : null;
};

menuSchema.methods.renderManual = async function (id = 'ROOT', pages = {}) {
  const currentNode = this.items[id];
  const items = [];

  if (id === 'ROOT') {
    const pagesIds = Object.keys(this.items).reduce((acc, key) => {
      const node = this.items[key];

      if (node.type === 'page') {
        acc.push(node.value);
      }
      return acc;
    }, []);

    const pageItems = await mongoose.model('Page').find({
      _id: { $in: pagesIds },
      site: this.site,
      enabled: true,
      deleted: false,
    });

    pages = pageItems.reduce((acc, page) => {
      acc[page.id] = page;
      return acc;
    }, {});
  }

  for (const itemId of currentNode.items || []) {
    const item = this.items[itemId];
    if (!item.disabled) {
      const page = item.type === 'page' ? pages[item.value] : null;

      items.push({
        label: page ? page.title : item.title,
        url: page ? page.path : item.value,
        items: (await this.renderManual(item.id, pages)) || [],
      });
    }
  }

  return items.length > 0 ? items : null;
};

export default mongoose.model('Menu', menuSchema);
