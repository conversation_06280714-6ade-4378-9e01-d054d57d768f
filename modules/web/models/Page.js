import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';
import Logger from '#utils/logger.js';
import { isNonNullObject } from '#utils/types.js';

const reservedSlugs = [
  'admin',
  'api',
  'auth',
  'login',
  'logout',
  'sitemap',
  'webhook',
];

const pageSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  path: {
    type: String,
    trim: true,
  },
  name: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    trim: true,
  },
  breadcrumbTitle: {
    type: String,
    trim: true,
  },
  description: {
    type: String,
  },
  keywords: {
    type: [String],
  },
  language: {
    type: String,
  },
  source: {
    type: mongoose.Types.ObjectId,
    ref: 'Page',
  },
  translations: {
    type: mongoose.SchemaTypes.Mixed, // { es: <page-id>, de: <page-id>, ... }
  },
  icon: {
    type: String,
    trim: true,
  },
  image: {
    type: mongoose.SchemaTypes.Mixed,
  },
  site: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Site',
  },
  dynamic: {
    type: Boolean,
  },
  dynamicResource: {
    type: mongoose.SchemaTypes.Mixed,
  },
  dynamicResourceType: {
    type: String,
  },
  dynamicRecord: {
    type: String,
  },
  dynamicFilter: {
    type: String,
  },
  dataSource: {
    type: mongoose.Types.ObjectId,
    ref: 'DataSource',
  },
  isHome: {
    type: Boolean,
  },
  auth: {
    required: {
      type: Boolean,
    },
    groups: [
      {
        type: mongoose.Types.ObjectId,
        ref: 'SiteUserGroup',
      },
    ],
  },
  hideInMenus: {
    type: Boolean,
  },
  hideInBreadcrumbs: {
    type: Boolean,
  },
  hideInSitemap: {
    type: Boolean,
  },
  noFollow: {
    type: Boolean,
  },
  noIndex: {
    type: Boolean,
  },
  chromeless: {
    type: Boolean,
  },
  seo: {
    canonicalUrl: {
      type: String,
      trim: true,
    },
    twitter: {
      creator: {
        type: String,
      },
      site: {
        type: String,
      },
      cardType: {
        type: String,
      },
    },
    openGraph: {
      title: {
        type: String,
      },
      description: {
        type: String,
      },
      image: {
        type: mongoose.SchemaTypes.Mixed,
      },
      type: {
        type: String,
      },
    },
  },
  design: {
    type: mongoose.SchemaTypes.Mixed,
  },
  layout: {
    type: mongoose.Types.ObjectId,
    ref: 'Layout',
  },
  parent: {
    type: mongoose.Types.ObjectId,
    ref: 'Page',
  },
  position: {
    type: Number,
  },
  password: {
    type: String,
  },
  content: {
    type: String,
  },
  publishStartsAt: {
    type: Date,
  },
  publishEndsAt: {
    type: Date,
  },
  scripts: {
    type: [mongoose.SchemaTypes.Mixed],
  },
  preset: {
    type: mongoose.Types.ObjectId,
    ref: 'PagePreset',
  },
  xml: {
    enabled: {
      type: Boolean,
    },
    resource: {
      type: String,
    },
    settings: {
      type: mongoose.SchemaTypes.Mixed,
    },
  },
  json: {
    enabled: {
      type: Boolean,
    },
    resource: {
      type: String,
    },
    content: {
      type: String,
    },
  },
  text: {
    enabled: {
      type: Boolean,
    },
    content: {
      type: String,
    },
  },
  html: {
    enabled: {
      type: Boolean,
    },
    content: {
      type: String,
    },
  },
  cacheControl: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

// Virtuals
pageSchema.virtual('subpages', {
  ref: 'Page',
  foreignField: 'parent',
  localField: '_id',
});

pageSchema.virtual('subpagesCount', {
  ref: 'Page',
  foreignField: 'parent',
  localField: '_id',
  match: { deleted: false },
  count: true,
});

// Indexes
pageSchema.index(
  { slug: 1, parent: 1, site: 1, language: 1 },
  {
    unique: true,
    partialFilterExpression: { deleted: false, source: { $eq: null } },
  }
);
pageSchema.index({ position: 1, parent: 1 });
pageSchema.index({ path: 1, site: 1 });
pageSchema.index({ source: 1, language: 1 }); // NOTE: This index is key for perfomance!
pageSchema.index({
  parent: 1,
  site: 1,
  enabled: 1,
  deleted: 1,
  dynamic: 1,
  hideInMenus: 1,
  source: 1,
  publishStartsAt: 1,
  publishEndsAt: 1,
  position: 1,
}); // NOTE: This index is key for perfomance!

pageSchema.statics.setAsHome = async function (page) {
  if (page.parent) {
    Logger.error('Page.setAsHome: Cannot set a non-root page as home');
    return page;
  }

  await this.updateMany(
    { site: page.site, parent: null, isHome: true },
    { isHome: false }
  );

  page.isHome = true;

  await page.save();

  return page;
};

pageSchema.statics.getAvailableSlug = async function ({
  slug,
  site,
  parent,
  pageId = null,
  language = null,
}) {
  const query = { slug, parent, site, deleted: false };

  if (pageId) {
    query._id = { $ne: pageId };
  }

  if (language) {
    query.language = language;
  }

  // Some slugs are reserved for internal use
  if (reservedSlugs.includes(slug)) {
    return uniquifySlug(slug);
  }

  const existingPage = await this.findOne(query);

  return existingPage ? uniquifySlug(slug) : slug;
};

pageSchema.methods.updatePath = async function () {
  const { parent, slug, dynamic, dynamicRecord, dynamicResource } = this;

  const parentPage = await mongoose.model('Page').findById(parent);

  let recordSlug;
  const resourceType = isNonNullObject(dynamicResource)
    ? dynamicResource.type
    : 'model';
  const resourceIdentifier = isNonNullObject(dynamicResource)
    ? dynamicResource.name
    : dynamicResource;

  if (resourceIdentifier && resourceType === 'model' && dynamicRecord) {
    const record = await mongoose
      .model(resourceIdentifier)
      .findById(dynamicRecord);

    if (record) {
      recordSlug = record.slug;
    }
  }

  this.path = `${parentPage ? parentPage.path : ''}/${
    dynamic
      ? recordSlug || `[${resourceIdentifier || 'record-slug'}]`
      : slug || ''
  }`;

  await this.save();

  const subpages = await mongoose.model('Page').find({ parent: this.id });

  for (const childPage of subpages) {
    childPage.updatePath();
  }

  return this;
};

pageSchema.methods.getAncestors = async function () {
  const parentPage = await mongoose.model('Page').findById(this.parent);

  if (parentPage) {
    return [...(await parentPage.getAncestors()), parentPage];
  }
  return [];
};

pageSchema.methods.getResources = async function () {
  const resources = {};
  const fields = '-__v -enabled -deleted -entity';

  const pages = await this.getAncestors();
  pages.push(this);

  for (let i = 0; i < pages.length; i += 1) {
    const page = pages[i];
    const { dynamicResource, dynamicRecord } = page;

    if (dynamicResource) {
      let resource;

      const resourceType = isNonNullObject(dynamicResource)
        ? dynamicResource.type
        : 'model';
      const resourceIdentifier = isNonNullObject(dynamicResource)
        ? dynamicResource.name
        : dynamicResource;

      if (resourceType === 'model' && dynamicRecord) {
        resource = await mongoose
          .model(resourceIdentifier)
          .findById(dynamicRecord)
          .select(fields);
      }
      // else {
      //   resource = await mongoose.model(dynamicResource).findBySlug({
      //     slug: slugs[i],
      //     parentId: prevResourceId,
      //     siteId,
      //     fields,
      //   });
      // }

      if (resource) {
        resources[resourceIdentifier] = resource;
      }
    }
  }

  return resources;
};

pageSchema.statics.getSiblings = async function (site, parent, page = null) {
  const query = { parent, site, deleted: false };

  if (page) {
    query._id = { $ne: page._id };
  }

  return await this.find(query);
};

/**
 * Moves a page into a new position or/and a new parent page
 *
 * ### Moving within same parent
 *
 * - Move **Before**: update page's position to all siblings with a position **greater or equal (`>=`)** than the _new position_, but **less (`<`)** than the _prev one_.
 * - Move **After**:  update page's position to all siblings with a position **greater (`>`)** than the _prev position_, but **less or equal (`<=`)** than the _new one_.
 *
 * ### Moving to another parent:
 *
 * - Increments new siblings position by 1 (+1), only to new siblings that have a larger or equal position (>=) than the new position
 * - Decrements prev siblings position by 1 (-1), only to old siblings that have a larger position (>) than the prev position
 * - Updates page's parent with new parent
 * - Updates page's position with new position
 *
 * ## Params
 * @param {Page} page
 * @param {Number} newPosition
 * @param {mongoose.Types.ObjectId} newParentId
 * @returns `Object(Page)`
 */
pageSchema.statics.movePage = async function (
  page,
  newPosition = null,
  newParentId = null // if null it means the page will be moved to the root
) {
  const prevParentId = page.parent?.toString() || null;
  const prevPosition = page.position;

  // Retrieve the new parent page from the db
  const newParent = await mongoose.model('Page').findById(newParentId);

  // Prevents a circular assignment of pages (page._id and newParent.parent are both ObjectIDs)
  if (page._id.equals(newParent?.parent)) return;

  // Prevents assigning the page as its own parent (page.id and newParentId are both strings)
  if (page.id === newParentId) return;

  // Is the parent changing?
  const changingParent = prevParentId !== newParentId;

  newPosition =
    // If the page is being moved to the same parent, and the new position is after the prev position,
    !changingParent && prevPosition < newPosition
      ? newPosition - 1 // decrement the position by 1
      : newPosition; // otherwise, use the new position as it is

  // The current siblings
  const siblings = await this.find(
    {
      parent: prevParentId,
      site: page.site,
      _id: { $ne: page._id },
      deleted: false,
    },
    null,
    {
      sort: ['position'],
    }
  );

  // In case of a new parent, fetch the new siblings
  const newSiblings = changingParent
    ? await this.find({ parent: newParentId, site: page.site }, null, {
        sort: ['position'],
      })
    : null;

  const targetSiblings = newSiblings || siblings || [];

  // Set page position:
  page.position =
    newPosition !== null &&
    newPosition >= 0 &&
    newPosition <= targetSiblings.length
      ? newPosition // at the expected position, provided that is between a valid range
      : targetSiblings.length; // at the end if there are siblings, otherwise it will be at position 0

  if (newSiblings) {
    for (const sibling of siblings) {
      // All the old siblings with position greater than the page's old position need to be decreased by one.
      if (sibling.position > prevPosition) {
        sibling.position -= 1;
        sibling.save();
      }
    }

    for (const sibling of newSiblings) {
      // All the new siblings with position greater than the page's new position need to be increased by one.
      if (sibling.position >= page.position) {
        sibling.position += 1;
        sibling.save();
      }
    }
  } else {
    for (const sibling of siblings) {
      // Moving before:
      // - given [a(0), b(1), c(2), d(3), e(4), f(5)],
      // - moving e(4) to pos 1,
      // - requires the items from b(1) to d(3) to be increased by one.
      if (
        sibling.position < prevPosition &&
        sibling.position >= page.position
      ) {
        sibling.position += 1;
        sibling.save();
      }

      // Moving after:
      // - given [a(0), b(1), c(2), d(3), e(4), f(5)],
      // - moving b(1) to pos 4,
      // - requires the items from c(2) to e(4) to be decreased by one.
      if (
        sibling.position > prevPosition &&
        sibling.position <= page.position
      ) {
        sibling.position -= 1;
        sibling.save();
      }
    }
  }

  if (changingParent) {
    page.slug = await mongoose.model('Page').getAvailableSlug({
      slug: page.slug,
      site: page.site,
      parent: newParentId,
      pageId: page._id,
    });

    page.parent = newParentId;
  }

  await page.save();

  return page;
};

export default mongoose.model('Page', pageSchema);
