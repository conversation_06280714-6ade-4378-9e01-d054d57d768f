import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const blockPresetSchema = SchemaFactory({
  // Name of the block preset
  name: {
    type: String,
    required: true,
    trim: true,
  },

  // Short desctiptive title of the block preset
  title: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      en: '',
    },
  },

  // Longer description of the block preset
  // TODO: make description translatable
  description: {
    type: String,
    trim: true,
  },

  // Type of the block preset
  type: {
    type: String,
    enum: ['block', 'section'],
  },

  // Root node types of the block preset
  rootNodesTypes: [
    {
      type: String,
      // Only allow blocks that can have nested children, or are BlockPresets themselves
      enum: [
        'Section',
        'Container',
        'Box',
        'Grid',
        'Slider',
        'Modal',
        'BlockPreset',
      ],
    },
  ],

  // List of block presets nested inside this one
  nestedBlockPresets: [
    {
      type: mongoose.SchemaTypes.ObjectId,
      ref: 'BlockPreset',
    },
  ],

  // BlockPresets's properties (like "props" of a React component)
  properties: {
    type: Array,
    default: [],
  },

  // Icon for the block preset to be displayed in the UI
  // (must be a font-awesome icon, available in the library)
  icon: {
    // Font Awesome icon name (e.g. 'user', 'home', 'plus', etc.)
    name: String,
    // Font Awesome icon prefix ('fas', 'far', 'fal', 'fab', etc.)
    prefix: {
      type: 'String',
      default: 'far',
    },
  },

  // Image used as a preview of the block preset
  image: {
    type: mongoose.SchemaTypes.Mixed,
  },

  // Content for the block preset
  // (a stringified JSON object with the block's content)
  content: {
    type: String,
  },

  // Design to which the block preset belongs
  design: {
    type: String,
    required: true,
  },

  // Variant of the design (if any)
  designVariant: {
    type: String,
  },

  // Specificity of the block preset:
  // 1) Network: if the block preset is network-specific, makes it available to all entities and sites in the network
  network: {
    type: mongoose.Types.ObjectId,
    ref: 'Network',
  },

  // 2) Entity: if the block preset is entity-specific, makes it available to all sites in the entity
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },

  // 3) Site: if the block preset is site-specific, makes it available only to the selected site
  site: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
  },
});

// Indexes:
// - Names of block presets must be unique within a design
blockPresetSchema.index({ design: 1, name: 1 }, { unique: true });

export default mongoose.model('BlockPreset', blockPresetSchema);
