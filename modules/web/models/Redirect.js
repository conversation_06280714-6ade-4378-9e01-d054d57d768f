import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const redirectSchema = SchemaFactory({
  site: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Site',
  },
  source: {
    type: String,
    trim: true,
    required: true,
  },
  target: {
    type: String,
    trim: true,
  },
  permanent: {
    type: Boolean,
  },
});

redirectSchema.index({ site: 1, deleted: 1, enabled: 1, source: 1 });

export default mongoose.model('Redirect', redirectSchema);
