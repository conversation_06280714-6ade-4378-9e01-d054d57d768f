import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';
import { addDomain, removeDomain } from '#utils/domains/index.js';

import {
  createAnalyticsSite,
  createSharedLink,
  updateDomain,
  getAnalyticsSite,
} from '#modules/web/services/analyticsServices.js';
import {
  startHostingRecord,
  endHostingRecord,
} from '../services/hostingRecordsServices.js';

const SocialLinkSchema = new mongoose.Schema({
  type: { type: String, required: true },
  url: { type: String },
  label: { type: String },
  icon: { type: String },
});

const RegionSchema = new mongoose.Schema({
  name: { type: String, required: true },
  codes: { type: [String] },
});

const GeoRedirectSchema = new mongoose.Schema({
  region: { type: String, required: true },
  url: { type: String, required: true },
});

const SiteSearchIndexSchema = new mongoose.Schema({
  title: { type: String },
  type: { type: String, required: true },
  settings: { type: mongoose.SchemaTypes.Mixed },
});

const siteSchema = SchemaFactory({
  domain: {
    type: String,
    trim: true,
  },
  // Additional domains refer to other domains that the site should be accessible from.
  additionalDomains: {
    type: [String],
    default: [],
  },
  redirectDomains: {
    type: [String],
    default: [],
  },
  region: {
    type: String,
  },
  name: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  keywords: {
    type: [String],
  },
  language: {
    type: String,
    default: 'en',
  },
  source: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
  },
  languages: {
    type: [String],
  },
  translations: {
    type: [mongoose.Types.ObjectId],
    default: [],
  },
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
  logo: {
    type: mongoose.SchemaTypes.Mixed,
  },
  design: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  defaultLayout: {
    type: mongoose.Types.ObjectId,
    ref: 'Layout',
  },
  socialLinks: {
    type: [SocialLinkSchema],
  },
  scripts: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
  analytics: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      enabled: false,
      authToken: '',
    },
  },
  flags: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
  notifications: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
  auth: {
    enabled: {
      type: Boolean,
      default: false,
    },
    // NOTE: When set, anonymous users will be able to register on the site
    registrationEnabled: {
      type: Boolean,
      default: true,
    },
    // NOTE: When set, new users will be redirected to a registration step. The default behavior is to add new users to the site without a separate registration step.
    requireRegistration: {
      type: Boolean,
      default: false,
    },
    userCollection: {
      type: mongoose.Types.ObjectId,
      ref: 'WebUserCollection',
    },
    pages: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
    providers: {
      type: [mongoose.SchemaTypes.Mixed],
      default: [],
    },
    terms: {
      required: {
        type: Boolean,
        default: false,
      },
      description: {
        type: mongoose.SchemaTypes.Mixed,
      },
      url: {
        type: String,
        trim: true,
      },
      urlLabel: {
        type: String,
        trim: true,
      },
    },
    userMenu: {
      type: mongoose.Types.ObjectId,
      ref: 'Menu',
    },
  },
  chatwoot: {
    enabled: {
      type: Boolean,
      default: false,
    },
    baseUrl: {
      type: String,
    },
    websiteToken: {
      type: String,
    },
    settings: {
      locale: {
        type: String,
      },
      launcherTitle: {
        type: String,
      },
    },
  },
  donations: {
    provider: String,
    settings: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
  },
  seo: {
    gtm: {
      id: String,
    },
    twitter: {
      creator: {
        type: String,
      },
      site: {
        type: String,
      },
      cardType: {
        type: String,
      },
    },
    facebook: {
      appID: {
        type: String,
      },
    },
  },

  rss: {
    title: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    copyright: {
      type: String,
      default: '',
    },
    image: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
    language: {
      type: String,
      default: 'en',
    },
    author: {
      type: String,
      default: '',
    },
    authorEmail: {
      type: String,
      default: '',
    },
  },
  cacheControl: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  search: {
    provider: String,
    /**
     * @deprecated Use provider specific settings instead
     */
    settings: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
    awe: {
      resultsPageId: String,
      indexes: [SiteSearchIndexSchema],
      filters: {
        type: [mongoose.SchemaTypes.Mixed],
        default: [],
      },
      logicalOperator: {
        type: String,
        enum: ['AND', 'OR'],
        default: 'AND',
      },
      sortField: {
        type: String,
      },
      sortOrder: {
        type: String,
      },
    },
    brave: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
    googleProgrammableSearchEngine: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
  },
  newsletter: {
    provider: String,
    settings: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
  },
  templateSite: {
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  exposePublicEntityData: {
    type: Boolean,
    default: false,
  },
  favicon: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  timezone: {
    type: String,
  },
  /**
   * @deprecated: Use cookieSettings instead
   */
  cookieText: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  cookieSettings: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  regions: {
    type: [RegionSchema],
    default: [],
  },
  geoRedirects: {
    type: [GeoRedirectSchema],
    region: [],
  },
  pwa: {
    enabled: {
      type: Boolean,
      default: false,
    },
    name: {
      type: String,
    },
    shortName: {
      type: String,
    },
    startUrl: {
      type: String,
    },
    enableOfflineDownloads: {
      type: Boolean,
      default: false,
    },
    // NOTE: The icon saved is the 512x512 icon, the rest are generated from it in the frontends.
    largeIcon: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
    smallIcon: {
      type: mongoose.SchemaTypes.Mixed,
      default: {},
    },
    // Android promotional banner settings
    androidPackageName: {
      type: String,
    },
  },
  mobileApp: {
    onesignalAppId: {
      type: String,
    },
    onesignalRestApiKey: {
      type: String,
    },
    // Settings for deep linking
    androidPackageName: {
      type: String,
    },
    androidFingerprint: {
      type: String,
    },
    iosBundleId: {
      type: String,
    },
    iosPaths: {
      type: String,
    },
    // Id used to display a smart banner in iOS
    appleAppId: {
      type: String,
    },
    // Bottom bar navigation slugs
    secondNavigationSlug: {
      type: String,
      trim: true,
    },
    thirdNavigationSlug: {
      type: String,
      trim: true,
    },
  },
  email: {
    from: String,
    appearance: {
      footerText: String,
      logo: mongoose.SchemaTypes.Mixed,
      colors: {
        primary: String,
        secondary: String,
        neutral: String,
      },
    },
  },
});

siteSchema.index(
  { domain: 1, source: 1, language: 1 },
  {
    unique: true,
    partialFilterExpression: { deleted: false },
  }
);
siteSchema.index({ name: 1 });
siteSchema.index({ title: 1 });

siteSchema.methods.registerDomains = async function ({
  user,
  prevDomains = [],
  prevRegion,
  prevDesign,
}) {
  const domains = this.redirectDomains || [];

  if (
    !prevDomains.includes(this.domain) ||
    prevRegion !== this.region ||
    prevDesign !== this.design.name
  ) {
    await startHostingRecord({
      entityId: this.entity._id,
      domain: this.domain,
      site: this,
      type: 'full-site',
      user,
    });

    await addDomain({
      domain: this.domain,
      design: this.design.name,
      region: this.region,
      addRedirect: true,
    });
  }

  for (const domain of domains) {
    if (!prevDomains.includes(domain)) {
      await addDomain({
        domain,
        design: this.design.name,
        region: this.region,
      });
    }
  }
};

siteSchema.methods.unregisterDomains = async function ({ user }) {
  const domains = [this.domain, ...(this.redirectDomains || [])];

  const validDomains = domains.filter(Boolean);

  for (const domain of validDomains) {
    await removeDomain({
      domain,
      design: this.design.name,
      region: this.region,
    });
  }

  if (validDomains.length) {
    await endHostingRecord({
      site: this,
      user,
    });
  }
};

siteSchema.methods.registerAnalytics = async function () {
  if (this.analytics?.enabled) {
    // Checks if the site already exists in plausible
    const existingSite = await getAnalyticsSite(this.domain); // Returns undefined if it doesn't exist.

    // if it doesn't exist, create it
    if (!existingSite) {
      await createAnalyticsSite(this.domain, this.analytics);
    }

    // Create a shared link and assign the auth token to analytics['authToken']
    // Note that this endpoint will not create a new one if it already exists, but will return the existing one.
    // The auth token will be set to authToken in both cases, when it's created and when it already exists.
    const authToken = await createSharedLink(this.domain);
    this.analytics.authToken = authToken;
    this.markModified('analytics');

    await this.save();
  }
};

siteSchema.methods.updateAnalyticsDomain = async function (prevDomain) {
  if (this.analytics?.enabled) {
    await updateDomain(prevDomain, this.domain);
  }
};

siteSchema.methods.removeAnalytics = async function () {
  if (this.analytics?.enabled) {
    const removeDomainString = `removed-${new Date().getTime()}-${this.domain}`; // We don't delete, only update the domain to mark as removed.
    await updateDomain(this.domain, removeDomainString);
  }
};

export default mongoose.model('Site', siteSchema);
