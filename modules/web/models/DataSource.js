import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const dataSourceSchema = SchemaFactory({
  /**
   * The site the data source belongs to.
   */
  site: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
    required: true,
  },
  /**
   * The name of the data source. This is used to identify the data source in the UI.
   */
  name: {
    type: String,
    trim: true,
    required: true,
  },
  /**
   * The type of data source, e.g. 'article', 'show', 'episode', etc.
   */
  type: {
    type: String,
    required: true,
  },
  /**
   * The data source settings. The settings are used to configure and filter the data source.
   */
  settings: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

// Indexes
dataSourceSchema.index({ type: 1, site: 1 });
dataSourceSchema.index({ name: 1 });

// Export the model
export default mongoose.model('DataSource', dataSourceSchema);
