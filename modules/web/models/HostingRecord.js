import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const hostingRecordSchema = SchemaFactory({
  startDate: {
    type: Date,
    required: true,
  },
  endDate: {
    type: Date,
  },
  startedBy: {
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'User',
  },
  endedBy: {
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'User',
  },
  entity: {
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'Entity',
    comment: 'The entity that is being hosted as an automated site.',
    required: true,
  },
  site: {
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'Site',
    comment: 'The site that is being used to display the entity information.',
  },
  domain: {
    type: String,
  },
  type: {
    type: String,
  },
  transferEntity: {
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'Entity',
    comment: 'The entity that it was transferred to.',
  },
});

export default mongoose.model('HostingRecord', hostingRecordSchema);
