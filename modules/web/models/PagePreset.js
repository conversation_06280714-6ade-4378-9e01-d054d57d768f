import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const pagePresetSchema = SchemaFactory({
  title: {
    type: String,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  icon: {
    // Font Awesome icon name (e.g. 'user', 'home', 'plus', etc.)
    name: String,
    // Font Awesome icon prefix ('fas', 'far', 'fal', 'fab', etc.)
    prefix: {
      type: 'String',
      default: 'far',
    },
  },
  page: {
    title: {
      type: String,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    keywords: {
      type: [String],
    },
    dynamic: {
      type: Boolean,
    },
    dynamicResource: {
      type: String,
    },
    dynamicRecord: {
      type: String,
    },
  },
  content: {
    type: String,
  },
  copy: {
    type: Boolean,
  },
  copyOnly: {
    type: Boolean,
  },
  group: {
    type: String,
  },
  design: {
    type: String,
  },
  designVariant: {
    type: String,
  },
  network: {
    type: mongoose.Types.ObjectId,
    ref: 'Network',
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },
  site: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
  },
});

// Virtuals
pagePresetSchema.virtual('pagesCount', {
  ref: 'Page',
  foreignField: 'preset',
  localField: '_id',
  count: true,
});

export default mongoose.model('PagePreset', pagePresetSchema);
