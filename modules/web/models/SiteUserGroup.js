import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const siteUserGroupSchema = SchemaFactory({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  users: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'WebUser',
    },
  ],
  site: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
    required: true,
  },
});

siteUserGroupSchema.index({ name: 1, site: 1 });

export default mongoose.model('SiteUserGroup', siteUserGroupSchema);
