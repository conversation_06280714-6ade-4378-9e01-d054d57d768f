import fs from 'fs';

import { getSiteEnvironment } from '#modules/articles/scripts/modx-import/helpers/sites.js';
import { toJsonObjectId } from '#utils/api/mongoose/id.js';
import Logger from '#utils/logger.js';
import { saveToJsonFile } from '#utils/json.js';

import { INPUT_FOLDER, OUTPUT_FOLDER } from './constants.js';

function loadEpisodes(inputFolder) {
  const episodeFile = `${inputFolder}/hopeplatform.episodes.json`;

  if (fs.existsSync(episodeFile)) {
    try {
      const episodes = fs.readFileSync(episodeFile, 'utf8');
      return JSON.parse(episodes);
    } catch (error) {
      Logger.error('Failed to read article categories cache file', error);
    }
  }

  return {};
}

const { siteId } = getSiteEnvironment({ environment: 'production' });

const data = loadEpisodes(INPUT_FOLDER);

const redirects = [];

data.forEach((item) => {
  const importRecord = item.importIDs.find(
    (obj) => obj.type === 'modx-videos-import'
  );

  const [episodeType, episodeId, episodeLanguage] =
    importRecord.recordID.split('_');

  const metadata = {
    site: toJsonObjectId(siteId),
    deleted: false,
    enabled: true,
    importIDs: [{ type: 'modx-redirects', recordID: importRecord.recordID }],
  };

  switch (episodeType) {
    case 'episode':
      redirects.push({
        ...{
          source: `/e${episodeId}`,
          target: `/mission-awareness/mission-360/tv/episodes/${item.slug}`,
        },
        ...metadata,
      });
      break;
    case 'interview':
      redirects.push({
        ...{
          source: `/i${episodeId}`,
          target: `/mission-awareness/mission-360/tv/interviews/${item.slug}`,
        },
        ...metadata,
      });
      break;
    case 'monthly':
      redirects.push({
        ...{
          source: `/mission-spotlight?video=en-20${episodeId.slice(0, 2)}-${episodeId.slice(2, 3)}-month-${episodeId.slice(3)}`,
          target: `/mission-awareness/mission-spotlight/monthly/${item.slug}`,
        },
        ...metadata,
      });
      break;
    case 'weekly': {
      // Remove leading zero
      const cleanEpisodeId = episodeId.startsWith('0')
        ? episodeId.slice(1)
        : episodeId;

      // Remove non-numeric characters (language at the end of the ID)
      const veryCleanEpisodeId = cleanEpisodeId.replace(/\D/g, '');

      // Add redirects for am.adventistmission.com
      redirects.push({
        ...{
          source: `/mission-spotlight?video=${episodeLanguage}-20${veryCleanEpisodeId.slice(0, 2)}-${veryCleanEpisodeId.slice(2, 3)}-week-${veryCleanEpisodeId.slice(3)}${episodeLanguage === 'en' ? '' : `&lang=${episodeLanguage}`}`,
          target: `/mission-awareness/mission-spotlight/weekly/${item.slug}`,
        },
        ...metadata,
      });

      // Add redirects for m360.tv
      redirects.push({
        ...{
          source: `/s${cleanEpisodeId}`,
          target: `/mission-awareness/mission-spotlight/weekly/${item.slug}`,
        },
        ...metadata,
      });

      break;
    }
  }
});

saveToJsonFile({
  data: redirects,
  fileName: 'redirects',
  folder: OUTPUT_FOLDER,
});
