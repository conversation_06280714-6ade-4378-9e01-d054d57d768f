import { toJsonObjectId } from '#utils/api/mongoose/id.js';
import { toMongoDateObject } from '#utils/dates.js';
import { saveToJsonFile } from '#utils/json.js';
import { redirectsArr } from './inputs/redirectUrls.js';

const redirects = [];

redirectsArr.forEach(({ source, target }) => {
  redirects.push({
    _id: toJsonObjectId(),
    site: toJsonObjectId('681a5613e758fcc455f0403a'),
    permanent: true,
    deleted: false,
    enabled: true,
    source: source,
    target: target,
    createdAt: toMongoDateObject(),
    updatedAt: toMongoDateObject(),
    importIDs: [{ recordID: source, type: 'adventist-org-urls-import' }],
  });
});

saveToJsonFile({
  data: redirects,
  fileName: 'redirects',
  folder: './output',
});
