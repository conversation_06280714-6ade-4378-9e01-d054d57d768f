const db = {};

// TODO: This needs to be run on the page preset collection as well

// Iterate through all pages and update the dynamicResource field to be an object
db.pages.find({ dynamicResource: { $ne: null } }).forEach((page) => {
  if (typeof page.dynamicResource === 'string') {
    if (page.dynamicResource.trim().length === 0) {
      db.pages.updateOne(
        { _id: page._id },
        {
          $set: {
            dynamicResource: null,
          },
        }
      );
    } else {
      db.pages.updateOne(
        { _id: page._id },
        {
          $set: {
            dynamicResource: {
              type: 'model',
              name: page.dynamicResource,
            },
          },
        }
      );
    }
  }
});
