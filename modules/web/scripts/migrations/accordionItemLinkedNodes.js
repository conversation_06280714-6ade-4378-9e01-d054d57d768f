const db = {};

// Migration script for accordions to get rid of the AccordionItemContent node
db.pages
  .find({ content: { $regex: 'AccordionItem' }, deleted: false })
  .forEach((page) => {
    const pageContent = JSON.parse(page.content);

    Object.keys(pageContent).forEach((nodeKey) => {
      const node = pageContent[nodeKey];

      // Only look at AccordionItem nodes that are also linked to an AccordionItemContent
      if (
        node?.type?.resolvedName === 'AccordionItem' &&
        node?.linkedNodes?.content
      ) {
        const accordionItemContentId = node.linkedNodes.content;

        // Put the children of the AccordionItemContent as direct children of the AccordionItem
        node.nodes = pageContent[accordionItemContentId].nodes;

        // Delete the AccordionItemContent and the reference to it
        delete pageContent[accordionItemContentId];
        delete node.linkedNodes;

        // Update the parent of the children from the AccordionItemContent to the AccordionItem
        node.nodes.forEach((childKey) => {
          pageContent[childKey].parent = nodeKey;
        });
      }
    });

    db.pages.updateOne(
      { _id: page._id },
      { $set: { content: JSON.stringify(pageContent) } }
    );
  });
