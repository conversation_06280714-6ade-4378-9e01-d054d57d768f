const db = {};

// Migration script to update the publishesh houses email string field to an array
db.publishinghouses.updateMany(
  { email: { $exists: true, $type: 'string' } }, // Match documents where 'email' exists and is a string
  [
    {
      $set: {
        emails: { $cond: [{ $not: ['$emails'] }, ['$email'], '$emails'] }, // Create 'emails' array with 'email' value if 'emails' doesn't exist
      },
    },
    { $unset: 'email' }, // Remove the 'email' field
  ]
);
