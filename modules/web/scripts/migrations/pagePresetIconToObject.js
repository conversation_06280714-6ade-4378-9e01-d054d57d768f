const db = {};

// Version 6
// Convert string icon field to object with name and prefix fields
const updatePipelineMongoV6 = [
  {
    $set: {
      icon: {
        name: '$icon',
        prefix: 'far',
      },
    },
  },
];

// Update all page presets
db.getCollection('pagepresets').updateMany({}, updatePipelineMongoV6);
// End version 6

// ---- OR ----

// Version 4
// Convert string icon field to object with name and prefix fields
db.getCollection('pagepresets')
  .find({})
  .forEach((pagePreset) => {
    db.getCollection('pagepresets').updateOne(
      {
        _id: pagePreset._id,
      },
      {
        $set: {
          icon: {
            name: pagePreset.icon,
            prefix: 'far',
          },
        },
      }
    );
  });
// End version 4
