import express from 'express';

import { restrictTo } from '#modules/users/controllers/authController.js';

import hostingRecordController from './controllers/hostingRecordController.js';

const webReportsRouter = express.Router();

webReportsRouter.route('/').get(
  restrictTo({
    module: 'reports',
    permissions: ['currentEntitySites'],
  }),
  hostingRecordController.getCurrentEntityReports
);

// Special access routes for all sites in the server regardless of entity
webReportsRouter.route('/global').get(
  restrictTo({
    module: 'reports',
    permissions: ['globalSites'],
  }),
  hostingRecordController.getGlobalReports
);

export default webReportsRouter;
