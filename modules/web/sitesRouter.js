import express from 'express';

import imageController from '#modules/images/controllers/imageController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { restrictTo } from '#modules/users/controllers/authController.js';

import siteController from './controllers/siteController.js';

const sitesRouter = express.Router({ mergeParams: true });

const logoUploader = imageController.getUploader({
  maxFileSize: '16mb',
});

const faviconUploader = imageController.getUploader({
  maxFileSize: '1mb',
  allowedContentTypes: ['image/png', 'image/ico', 'image/svg+xml'],
});

sitesRouter
  .route('/')
  .get(siteController.getAllSites)
  .post(
    restrictTo({
      module: 'sites',
      permissions: ['create'],
    }),
    logRequest({ module: 'web', action: 'CREATE_SITE' }),
    siteController.createSite
  );

sitesRouter.route('/upload-logo').post(
  restrictTo({
    module: 'sites',
    permissions: ['create', 'update'],
  }),
  logRequest({ module: 'web', action: 'UPLOAD_SITE_LOGO' }),
  imageController.uploadImage({ upload: logoUploader }),
  imageController.cropAndSaveImage()
);

sitesRouter.route('/upload-favicon').post(
  restrictTo({
    entityManager: true,
    module: 'sites',
    permissions: ['create', 'update'],
  }),
  logRequest({ module: 'web', action: 'UPLOAD_SITE_FAVICON' }),
  imageController.uploadImage({ upload: faviconUploader }),
  imageController.cropAndSaveImage()
);

sitesRouter.route('/clone').post(
  restrictTo({
    module: 'sites',
    permissions: ['clone'],
    // paramId: 'siteId', // TODO: Check if this is really needed
  }),
  logRequest({ module: 'web', action: 'CLONE_SITE' }),
  siteController.uploadSiteJson(),
  siteController.cloneSite
);

export default sitesRouter;
