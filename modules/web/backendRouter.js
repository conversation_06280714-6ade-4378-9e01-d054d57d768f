import express from 'express';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { restrictTo } from '#modules/users/controllers/authController.js';

import backendController from './controllers/backendController.js';

const webBackendRouter = express.Router();

webBackendRouter.route('/addDomain').patch(
  // TODO: See which permission is appropriate for this route
  restrictTo({
    module: 'backendDomains',
    permissions: ['add'],
  }),
  logRequest({ module: 'web', action: 'ADD_BACKEND_DOMAIN' }),
  backendController.addBackendDomain
);

webBackendRouter.route('/removeDomain').patch(
  restrictTo({
    module: 'backendDomains',
    permissions: ['remove'],
  }),
  logRequest({ module: 'web', action: 'REMOVE_BACKEND_DOMAIN' }),
  backendController.removeBackendDomain
);

export default webBackendRouter;
