import express from 'express';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { restrictTo } from '#modules/users/controllers/authController.js';
import { validate } from '#utils/validationMiddleware.js';

import siteSearchController from './controllers/siteSearchController.js';
import { updateSearchSettingsSchema } from './validation/search/updateSearchSettingsSchema.js';
import { reIndexSiteSearchIndexSchema } from './validation/search/reIndexSiteSearchIndexSchema.js';
import { searchIndexTasksListSchema } from './validation/search/searchIndexTasksListSchema.js';

const siteSearchRouter = express.Router({ mergeParams: true });

siteSearchRouter.route('/').patch(
  restrictTo({
    module: 'siteSearch',
    permissions: ['update'],
    paramId: 'siteId',
  }),
  validate(updateSearchSettingsSchema, 'body'),
  logRequest({ module: 'web', action: 'UPDATE_SITE_SEARCH' }),
  siteSearchController.updateSiteSearchSettings
);

siteSearchRouter.route('/indexes').post(
  restrictTo({
    module: 'siteSearch',
    permissions: ['create'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'CREATE_SITE_SEARCH_INDEXES' }),
  siteSearchController.createSiteSearchIndexes
);

siteSearchRouter.route('/indexes/:searchIndexId/re-index').patch(
  restrictTo({
    module: 'siteSearch',
    permissions: ['update'],
    paramId: 'siteId',
  }),
  validate(reIndexSiteSearchIndexSchema, 'body'),
  logRequest({ module: 'web', action: 'RE-INDEX_SITE_SEARCH_INDEX' }),
  siteSearchController.reIndexSiteSearchIndex
);

siteSearchRouter.route('/indexes/:searchIndexId/tasks').get(
  restrictTo({
    module: 'siteSearch',
    permissions: ['read'],
    paramId: 'siteId',
  }),
  validate(searchIndexTasksListSchema, 'query'),
  siteSearchController.getSearchIndexTasks
);

siteSearchRouter.route('/indexes/:searchIndexId/tasks/:searchIndexTaskId').get(
  restrictTo({
    module: 'siteSearch',
    permissions: ['read'],
    paramId: 'siteId',
  }),
  siteSearchController.getSearchIndexTask
);

siteSearchRouter
  .route('/indexes/:searchIndexId/tasks/:searchIndexTaskId/resume')
  .patch(
    restrictTo({
      module: 'siteSearch',
      permissions: ['update'],
      paramId: 'siteId',
    }),
    siteSearchController.resumeSearchIndexTask
  );

export default siteSearchRouter;
