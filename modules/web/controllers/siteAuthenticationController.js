import siteAuthenticationServices from '../services/siteAuthenticationServices.js';

export const updateSiteAuth = async (req, res) => {
  const site = await siteAuthenticationServices.updateSiteAuth({
    siteId: req.params.siteId,
    entity: req.entity,
    auth: req.body.auth,
  });

  res.status(200).json(site);
};

const getSiteUserGroups = async (req, res) => {
  const { siteId } = req.params;
  const { limit, page, search, sortBy, sortDir, statuses } = req.query;

  const { items, count } = await siteAuthenticationServices.getSiteUserGroups({
    limit,
    page,
    search,
    siteId,
    sortBy,
    sortDir,
    statuses,
  });

  res.status(200).json({ items, count });
};

const getSiteUserGroup = async (req, res) => {
  const { siteId, groupId } = req.params;

  const group = await siteAuthenticationServices.getSiteUserGroupById({
    siteId,
    groupId,
    populate: ['users'],
  });

  res.status(200).json(group);
};

const createSiteUserGroup = async (req, res) => {
  const { siteId } = req.params;

  const userGroup = await siteAuthenticationServices.createSiteUserGroup({
    siteId,
    name: req.body.name,
  });

  res.status(200).json(userGroup);
};

const updateSiteUserGroup = async (req, res) => {
  const { siteId, groupId } = req.params;

  const userGroup = await siteAuthenticationServices.updateSiteUserGroup({
    siteId,
    groupId,
    name: req.body.name,
  });

  res.status(200).json(userGroup);
};

const deleteSiteUserGroup = async (req, res) => {
  const { siteId, groupId } = req.params;

  await siteAuthenticationServices.deleteSiteUserGroup({
    siteId,
    groupId,
  });

  res.status(204).json({});
};

const disableSiteUserGroup = async (req, res) => {
  const { siteId, groupId } = req.params;

  const data = await siteAuthenticationServices.disableSiteUserGroup({
    siteId,
    groupId,
  });

  res.status(200).json(data);
};

const enableSiteUserGroup = async (req, res) => {
  const { siteId, groupId } = req.params;

  const data = await siteAuthenticationServices.enableSiteUserGroup({
    siteId,
    groupId,
  });

  res.status(200).json(data);
};

const getSiteUserGroupUsers = async (req, res) => {
  const { siteId, groupId } = req.params;
  const { limit, page, search, sortBy, sortDir, statuses } = req.query;

  const { items, count } =
    await siteAuthenticationServices.getSiteUserGroupUsers({
      groupId,
      limit,
      page,
      search,
      siteId,
      sortBy,
      sortDir,
      statuses,
    });

  res.status(200).json({ items, count });
};

const addSiteUserGroupUser = async (req, res) => {
  const { siteId, groupId } = req.params;

  const data = await siteAuthenticationServices.addSiteUserGroupUser({
    siteId,
    groupId,
    userId: req.body.user,
  });

  res.status(200).json(data);
};

const deleteSiteUserGroupUser = async (req, res) => {
  const { siteId, groupId, userId } = req.params;

  const data = await siteAuthenticationServices.deleteSiteUserGroupUser({
    siteId,
    groupId,
    userId,
  });

  res.status(200).json(data);
};

export default {
  updateSiteAuth,
  getSiteUserGroups,
  getSiteUserGroup,
  createSiteUserGroup,
  updateSiteUserGroup,
  deleteSiteUserGroup,
  disableSiteUserGroup,
  enableSiteUserGroup,
  getSiteUserGroupUsers,
  addSiteUserGroupUser,
  deleteSiteUserGroupUser,
};
