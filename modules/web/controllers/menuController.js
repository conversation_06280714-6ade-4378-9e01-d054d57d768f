// import {errors} from '#utils/appError.js';
import factory from '#utils/handlerFactory.js';
import Menu from '../models/Menu.js';
import Site from '../models/Site.js';
import menuServices from '../services/menuServices.js';

export const getAllMenus = async (req, res) => {
  const site = await factory.getOne(Site, req, { paramId: 'siteId' });
  const data = await factory.getAll(Menu, req, {
    filter: {
      site: site._id,
      deleted: false,
      source: { $eq: null },
    },
    sort: ['createdAt'],
  });

  res.status(200).json(data);
};

export const getMenu = async (req, res) => {
  const data = await factory.getOne(Menu, req, { paramId: 'menuId' });

  res.status(200).json(data);
};

export const getMenuItems = async (req, res) => {
  const data = await factory.getOne(Menu, req, { paramId: 'menuId' });
  const items = await data.render();

  res.status(200).json(items);
};

export const createMenu = async (req, res) => {
  const site = await factory.getOne(Site, req, { paramId: 'siteId' });

  // Create the menu
  const menu = await Menu.create({
    ...req.body,
    site,
  });

  res.status(200).json(menu);
};

export const updateMenu = async (req, res) => {
  const menu = await factory.getOne(Menu, req, { paramId: 'menuId' });

  // Update menu's  data
  const updatedMenu = await Menu.findByIdAndUpdate(
    menu._id,
    {
      ...req.body,
      site: menu.site, // prevents moving a menu to different site
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedMenu);
};

export const getMenuTranslations = async (req, res) => {
  // TODO: check if menu's belongs to entity
  const { entity } = req; // eslint-disable-line no-unused-vars
  const { menuId } = req.params;

  const menuTranslations = await menuServices.getMenuTranslations(menuId);

  res.status(200).json(menuTranslations);
};

export const addMenuTranslation = async (req, res) => {
  const { menuId, language } = req.params;

  const menuTranslations = await menuServices.addMenuTranslation(
    menuId,
    language
  );

  res.status(200).json(menuTranslations);
};

export const deleteMenu = async (req, res) => {
  const data = await factory.deleteOne(Menu, req, { paramId: 'menuId' });

  res.status(200).json(data);
};

export const restoreMenu = async (req, res) => {
  const data = await factory.restoreOne(Menu, req, { paramId: 'menuId' });

  res.status(200).json(data);
};

export const disableMenu = async (req, res) => {
  const data = await factory.disableOne(Menu, req, { paramId: 'menuId' });

  res.status(200).json(data);
};

export const enableMenu = async (req, res) => {
  const data = await factory.enableOne(Menu, req, { paramId: 'menuId' });

  res.status(200).json(data);
};

export default {
  getAllMenus,
  getMenu,
  getMenuItems,
  createMenu,
  updateMenu,
  getMenuTranslations,
  addMenuTranslation,
  deleteMenu,
  restoreMenu,
  disableMenu,
  enableMenu,
};
