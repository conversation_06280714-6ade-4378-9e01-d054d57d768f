import _ from 'lodash';
import mongoose from 'mongoose';

import imageController from '#modules/images/controllers/imageController.js';
import { cleanDomain, getProvider } from '#utils/domains/helpers/utils.js';
import { removeDomain } from '#utils/domains/index.js';
import { fileTypes } from '#utils/files.js';
import factory from '#utils/handlerFactory.js';
import { logError } from '#utils/logger.js';
import { getUpload } from '#utils/storage.js';
import { errors } from '#utils/appError.js';
import { getInheritedAttribute } from '#modules/entities/helpers/getEntityAncestorAttribute.js';

import {
  decryptNewsletterSettings,
  encryptNewsletterSettings,
} from '../helpers/newsletter.js';
import Page from '../models/Page.js';
import Site from '../models/Site.js';
import cloneServices from '../services/cloneServices.js';
import hostingRecordsServices from '../services/hostingRecordsServices.js';
import siteServices from '../services/siteServices.js';
import { getModelResourcesBySite } from '../services/pageSearchServices.js';

const siteImportUpload = getUpload({
  folder: 'site-imports',
  maxFileSize: '50mb',
  allowedContentTypes: [...fileTypes.json],
});

export const getAllSites = async (req, res) => {
  const { user, entity, query } = req || {};

  const { ids, templateSite, designs } = query || {};

  let data = { count: 0, items: [] };

  if (
    !user.isAdmin &&
    !user.hasPermission({ module: 'sites', permission: 'read' })
  ) {
    res.status(200).json(data);
    return;
  }

  const restrictedRecords = user.getRestrictedRecords({
    module: 'sites',
    permission: 'read',
    currentEntity: entity?._id?.toString() || null,
    userEntity: user?.entity?.toString() || null,
    onlyForCurrentEntity: true, // TODO: check if this is needed, maybe it always needs to be true
  });

  const filter = {
    source: { $eq: null }, // Only get sites that are not translations
  };

  if (restrictedRecords) {
    filter._id = { $in: restrictedRecords };
  }

  // filter ids by restricted records
  if (ids) {
    const validIds =
      restrictedRecords?.length > 0
        ? ids.filter((id) => restrictedRecords?.includes(id))
        : ids;
    filter._id = { $in: validIds.map((id) => new mongoose.Types.ObjectId(id)) };
  }

  // Bring only template sites where templateSite.enabled is true.
  if (templateSite) {
    filter.templateSite = { enabled: true };
  }

  // Filter sites with the specified design(s)
  if (designs?.length) {
    filter['design.name'] = { $in: designs };
  }

  // TODO: Migrate this to a service!
  data = await factory.getAll(Site, req, {
    filter,
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const getAncestorsByDynamicResource = async (req, res) => {
  const { user, entity, params, query } = req || {};

  const { siteId } = params || {};

  const restrictedRecords = user.getRestrictedRecords({
    module: 'sites',
    permission: 'read',
    currentEntity: entity?._id?.toString() || null,
    userEntity: user?.entity?.toString() || null,
    onlyForCurrentEntity: true, // TODO: check if this is needed, maybe it always needs to be true
  });

  const { items, count, error } =
    await siteServices.getAncestorsByDynamicResource({
      entity,
      excludeSiteIds: restrictedRecords ?? [],
      resourceName: query.name,
      resourceType: query.type,
      siteId,
    });

  if (error) {
    throw error;
  }

  res.status(200).json({ items, count });
};

export const getSite = async (req, res) => {
  const data = await factory.getOne(Site, req, {
    filterByEntity: true,
    paramId: 'siteId',
    populate: ['entity'],
  });

  // Decrypt newsletter settings
  if (data.newsletter) {
    data.newsletter = decryptNewsletterSettings(data.newsletter);
  }

  res.status(200).json(data);
};

export const getPublicSite = async (req, res) => {
  const data = await factory.getOne(Site, req, {
    filterByEntity: false,
    paramId: 'siteId',
    populate: ['entity'], // This is populated to indicate where it belongs
    fields: ['name', 'templateSite'],
  });

  res.status(200).json(data);
};

export const getModelResources = async (req, res) => {
  const { siteId } = req.params;
  const { site, error } = await siteServices.getSiteById(siteId);

  if (error) {
    throw error;
  }

  if (!site) {
    throw errors.not_found('site');
  }

  const { resources, error: resourcesError } = await getModelResourcesBySite({
    site,
  });

  if (resourcesError) {
    throw resourcesError;
  }

  res.status(200).json(resources);
};

export const getSiteOrAncestorSite = async (req, res) => {
  const site = await siteServices.getSiteOrAncestorSite({
    siteId: req.params.siteId,
    entity: req.entity,
    includeAncestors: req.query.includeAncestors,
  });

  res.status(200).json(site);
};

export const getSiteSourceRecordId = async (req, res, next) => {
  const data = await factory.getOne(Site, req, {
    filterByEntity: true,
    paramId: 'siteId',
  });

  if (data?.source) {
    req.sourceRecordId = data.source;
  }
  next();
};

export const getSiteTranslations = async (req, res) => {
  // TODO: check if site belongs to entity
  const { query } = req;
  const { siteId } = req.params;

  const siteTranslations = await siteServices.getSiteTranslations({
    siteId,
    query,
  });

  res.status(200).json(siteTranslations);
};

export const addSiteTranslation = async (req, res) => {
  const { entity, params } = req;
  const { siteId, language } = params;

  const { siteTranslation, error } = await siteServices.addSiteTranslation({
    entity,
    siteId,
    language,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(siteTranslation);
};

export const createSite = async (req, res) => {
  // Ensure provided domain is valid
  req.body.domain = cleanDomain(req.body.domain);

  // Extract group and users from body
  const { groupName, users, design, region, supportedLanguages, ...body } =
    req.body || {};

  const siteDesign = !_.isEmpty(design)
    ? design // design should be an object with name of the design. { name: 'base'}
    : {
        name: await getInheritedAttribute({
          entity: req.entity,
          attributeName: 'config.defaultDesign',
          networkAttributeName: 'defaultDesign',
          fallbackValue: 'base',
        }),
      };

  const hostingProvider = getProvider(); // Get the default hosting provider

  const siteRegion = !_.isEmpty(region)
    ? region // region should be a name of the region. (e.g. 'eu-central')
    : getInheritedAttribute({
        entity: req.entity,
        attributeName: 'config.defaultRegion',
        networkAttributeName: 'defaultRegion',
        fallbackValue: hostingProvider?.defaultRegion || 'eu-central',
      });

  // Create site
  const site = await Site.create({
    ...body,
    design: siteDesign,
    region: siteRegion,
    entity: req.entity._id,
  });

  // Create root/home page
  await Page.create({
    // slug: '',
    title: 'Home',
    description: '',
    isHome: true,
    site: site._id,
    position: 0,
    parent: null,
  });

  // Register domains in hosting provider
  await site.registerDomains({ user: req.user });

  await site.registerAnalytics();

  // Update also affected sites translations, so they all match
  const siteTranslations = req.body.translations || [];
  const currentSiteId = req.params.siteId;

  for (const siteId of siteTranslations) {
    const translations = [
      currentSiteId,
      ...siteTranslations.filter((st) => st !== siteId),
    ];
    await Site.findOneAndUpdate({ _id: siteId }, { translations });
  }

  // If a group name was provided, allow the user to create it and add users to it
  const createGroupPermission =
    req.user.isAdmin ||
    req.user.hasPermission({
      module: 'groups',
      permission: 'create',
    });

  // Checks if the user has permission to create users in the entity
  // If not, the group will be created without users.
  const createUserPermission =
    req.user.isAdmin ||
    req.user.hasPermission({
      module: 'users',
      permission: 'create',
    });

  if (groupName && createGroupPermission) {
    await cloneServices.createSiteGroups({
      groupName,
      users,
      site,
      entityId: req.entity._id,
      createUserPermission,
      supportedLanguages,
    });
  }

  res.status(200).json(site);
};

export const updateSite = async (req, res) => {
  const prevSite = await factory.getOne(Site, req, {
    filterByEntity: true,
    paramId: 'siteId',
  });

  // Ensure provided domain is valid
  req.body.domain = cleanDomain(req.body.domain);

  // Encrypt newsletter settings
  if (req.body.newsletter) {
    req.body.newsletter = encryptNewsletterSettings(
      req.body.newsletter,
      prevSite.newsletter
    );
  }

  const updatedSite = await factory.updateOne(Site, req, {
    filterByEntity: true,
    paramId: 'siteId',
  });

  // If logo was changed or removed, delete the old one from storage
  if (prevSite.logo && prevSite.logo.name) {
    if (!updatedSite.logo || updatedSite.logo.name !== prevSite.logo.name) {
      imageController.deleteImageFile(`${req.entity.id}/${prevSite.logo.name}`);
    }
  }

  // If the site was transferred to another entity we want to end the hosting record in the previous entity, then start a new one in the new entity
  if (updatedSite.entity.toString() !== prevSite.entity.toString()) {
    await hostingRecordsServices.endHostingRecord({
      site: prevSite,
      user: req.user,
    });

    await hostingRecordsServices.startHostingRecord({
      entityId: updatedSite.entity,
      domain: updatedSite.domain,
      site: updatedSite,
      user: req.user,
      transferEntity: updatedSite.entity,
      type: 'full-site',
    });
  }

  const prevDomains = [
    prevSite.domain,
    ...(prevSite.redirectDomains || []),
    ...(prevSite.additionalDomains || []),
    ...(prevSite.redirects || []),
  ];
  const updatedDomains = [
    updatedSite.domain,
    ...(updatedSite.redirectDomains || []),
    ...(updatedSite.additionalDomains || []),
    ...(updatedSite.redirects || []),
  ];

  const prevRegion = prevSite.region;
  const prevDesign = prevSite.design.name;

  // If domains or region changed, remove the old ones in hosting provider
  // Generally we would use the updatedSite.unregisterDomains, but since we are only removing if they changed
  // we can do it manually here, and prevent the other domains from being down if only one of the domains change.
  for (const prevDomain of prevDomains) {
    if (
      !updatedDomains.includes(prevDomain) ||
      prevSite.region !== updatedSite.region ||
      prevSite.design.name !== updatedSite.design.name
    ) {
      await removeDomain({
        domain: prevDomain,
        design: prevSite.design.name,
        region: prevSite.region,
      });
    }
  }

  if (
    prevSite.domain !== updatedSite.domain ||
    prevSite.region !== updatedSite.region ||
    prevSite.design.name !== updatedSite.design.name
  ) {
    await hostingRecordsServices.endHostingRecord({
      site: prevSite,
      user: req.user,
    });
  }

  // Register domains in hosting provider
  await updatedSite.registerDomains({
    user: req.user,
    prevDomains,
    prevRegion,
    prevDesign,
  });

  // If analytics is enabled and the domain changed, update the domain in plausible
  if (
    updatedSite.analytics?.enabled &&
    prevSite.domain !== updatedSite.domain
  ) {
    await updatedSite.updateAnalyticsDomain(prevSite.domain);
  }

  // Also if analytics was not enabled and now is, create the site in plausible if it doesn't exist.
  // On any error, it will be logged and the site will be updated with the rest of the changes.
  if (!prevSite.analytics?.enabled && updatedSite.analytics?.enabled) {
    await updatedSite.registerAnalytics();
  }

  // Update also affected sites translations, so they all match
  const prevTranslations = prevSite.translations;
  const newTranslations = updatedSite.translations;
  const currentSiteId = req.params.siteId;

  for (const siteId of newTranslations) {
    const translations = [
      currentSiteId,
      ...newTranslations.filter((st) => st !== siteId),
    ];
    await Site.findOneAndUpdate({ _id: siteId }, { translations });
  }

  for (const siteId of prevTranslations) {
    if (!newTranslations.includes(siteId)) {
      await Site.findOneAndUpdate({ _id: siteId }, { translations: [] });
    }
  }

  if (updatedSite.newsletter?.settings?.secretKey) {
    delete updatedSite.newsletter?.settings?.secretKey;
  }

  res.status(200).json(updatedSite);
};

export const uploadSiteJson = (fieldName = 'file') =>
  siteImportUpload.single(fieldName);

export const cloneSite = async (req, res) => {
  const { body, user, entity, file } = req;
  const {
    name,
    domain,
    groupName,
    users: usersString,
    registerAnalytics,
    supportedLanguages,
    site: siteId,
  } = body || {};

  // Since the endpoint is multipart, the users are sent as a stringified JSON
  const users = usersString ? JSON.parse(usersString) : [];

  const analytics = registerAnalytics
    ? {
        enabled: true,
      }
    : {
        enabled: false,
      };

  // Fetch site to clone (if provided)
  const site = siteId
    ? await Site.findOne({
        _id: siteId,
        entity: entity._id,
      })
    : null;

  // Get the target entity
  const entityId = body.entity || site?.entity || entity._id; // TODO: check that the entity is accessible by the user

  // Initialize new site
  const { newSite, error } = site
    ? // If a site was provided, clone it
      await cloneServices.cloneSite({
        site,
        name,
        domain,
        analytics,
        entityId,
      })
    : file
      ? // Or if a file was provided, import it
        await cloneServices.importSite({
          file,
          name,
          domain,
          analytics,
          entityId,
        })
      : {}; // If this point is reached, it will throw an error

  // If the site could not be cloned not imported, return an error
  if (!newSite || error) {
    logError('Could not clone site', error);

    return res.status(400).json({
      message: 'Could not clone site',
      error,
    });
  }

  // Register domains in hosting provider
  await newSite.registerDomains({ user: req.user });

  // Register site in analytics if it has analytics enabled (The check is done inside the function)
  await newSite.registerAnalytics();

  // If a group name was provided, allow the user to create it and add users to it
  const createGroupPermission =
    user.isAdmin ||
    user.hasPermission({
      module: 'groups',
      permission: 'create',
    });

  // Checks if the user has permission to create users in the entity
  // If not, the group will be created without users.
  const createUserPermission =
    user.isAdmin ||
    user.hasPermission({
      module: 'users',
      permission: 'create',
    });

  const newGroup =
    groupName && createGroupPermission
      ? await cloneServices.createSiteGroups({
          groupName,
          users,
          site: newSite,
          entityId,
          createUserPermission, // Permission to create users is used inside
          supportedLanguages,
        })
      : null;

  res.status(200).json({ site: newSite, group: newGroup });
};

export const exportSite = async (req, res) => {
  const { entity, user, params } = req;
  const { siteId } = params;

  const exportedSiteJson = await cloneServices.exportSite({
    entity,
    user,
    siteId,
  });

  res.status(200).json(exportedSiteJson);
};

export const deleteSite = async (req, res) => {
  const { sourceRecordId, entity, user, params } = req;
  const { siteId } = params;

  const { deletedSite, error } = await siteServices.deleteSite({
    entity,
    siteId,
    sourceRecordId,
    user,
  });

  // If the site was deleted, but other site tasks failed, we still want to return the site
  if (!deletedSite && error) {
    throw error;
  }

  if (deletedSite && error) {
    logError('Error deleting site', error);
  }

  res.status(200).json(deletedSite);
};

export const restoreSite = async (req, res) => {
  const site = await factory.restoreOne(Site, req, {
    filterByEntity: true,
    paramId: 'siteId',
  });

  // Register domains in hosting provider
  await site.registerDomains({ user: req.user });

  res.status(200).json(site);
};

export const disableSite = async (req, res) => {
  const site = await factory.disableOne(Site, req, {
    filterByEntity: true,
    paramId: 'siteId',
  });

  // Remove domains from hosting provider
  await site.unregisterDomains({ user: req.user });

  res.status(200).json(site);
};

export const enableSite = async (req, res) => {
  const site = await factory.enableOne(Site, req, {
    filterByEntity: true,
    paramId: 'siteId',
  });

  // Register domains in hosting provider
  await site.registerDomains({ user: req.user });

  res.status(200).json(site);
};

export const getFrontendApps = async (req, res) => {
  const { FRONTEND_APPS } = process.env;

  // If FRONTEND_APPS is not set, return an empty object to show that there are no frontend apps
  if (!FRONTEND_APPS) {
    res.status(200).json({});
  }
  const frontendAppsEnv = FRONTEND_APPS?.split(' ') || [];

  // We make the first word before the dash be the key to the object, grouping by first word
  const frontendApps = frontendAppsEnv.reduce((acc, app) => {
    const [design, region] = app.split(/-(.+)/);
    if (!acc[design]) {
      acc[design] = [];
    }
    if (region) acc[design].push(region);
    return acc;
  }, {});

  res.status(200).json(frontendApps);
};

export default {
  getAllSites,
  getAncestorsByDynamicResource,
  getSite,
  getPublicSite,
  getModelResources,
  getSiteOrAncestorSite,
  getSiteSourceRecordId,
  getSiteTranslations,
  getFrontendApps,
  addSiteTranslation,
  createSite,
  updateSite,
  cloneSite,
  exportSite,
  uploadSiteJson,
  deleteSite,
  restoreSite,
  disableSite,
  enableSite,
};
