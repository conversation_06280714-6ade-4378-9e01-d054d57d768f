import HostingRecord from '../models/HostingRecord.js'; // eslint-disable-line no-unused-vars
import Site from '../models/Site.js'; // eslint-disable-line no-unused-vars
import hostingRecordsServices from '../services/hostingRecordsServices.js';

export const getCurrentEntityReports = async (req, res) => {
  const { query, entity } = req;
  const { limit, page, sort, search, filter, type } = query;

  const { reportId, initialDate, lastDate } = JSON.parse(filter) || {};

  const data = await hostingRecordsServices.getList({
    entityId: entity._id,
    limit,
    fields: [],
    page,
    sort,
    search,
    reportId,
    initialDate,
    lastDate,
    type,
  });

  res.status(200).json(data);
};

export const getGlobalReports = async (req, res) => {
  const { query } = req;
  const { limit, page, sort, search, filter, type } = query;

  const { reportId, initialDate, lastDate } = JSON.parse(filter) || {};

  const data = await hostingRecordsServices.getList({
    noEntity: true,
    limit,
    fields: [],
    page,
    sort,
    search,
    reportId,
    initialDate,
    lastDate,
    type,
  });

  res.status(200).json(data);
};

export default {
  getCurrentEntityReports,
  getGlobalReports,
};
