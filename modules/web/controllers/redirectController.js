// import {errors} from '#utils/appError.js';
import factory from '#utils/handlerFactory.js';
import Redirect from '../models/Redirect.js';
import Site from '../models/Site.js';
import { getRedirects } from '../services/redirectServices.js';

export const getAllRedirects = async (req, res) => {
  const { siteId } = req.params;
  const { search, sort, limit, page, skip, language } = req.query;

  const { items, count } = await getRedirects({
    siteId,
    search,
    limit,
    page,
    sort,
    skip,
    language,
  });

  res.status(200).json({
    items,
    count,
  });
};

export const getRedirect = async (req, res) => {
  const data = await factory.getOne(Redirect, req, { paramId: 'redirectId' });

  res.status(200).json(data);
};

export const createRedirect = async (req, res) => {
  const site = await factory.getOne(Site, req, { paramId: 'siteId' });

  // Create the redirect
  const redirect = await Redirect.create({
    ...req.body,
    site,
  });

  res.status(200).json(redirect);
};

export const updateRedirect = async (req, res) => {
  const redirect = await factory.getOne(Redirect, req, {
    paramId: 'redirectId',
  });

  // Update redirect's  data
  const updatedRedirect = await Redirect.findByIdAndUpdate(
    redirect._id,
    {
      ...req.body,
      site: redirect.site, // prevents moving a redirect to different site
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedRedirect);
};

export const deleteRedirect = async (req, res) => {
  const data = await factory.deleteOne(Redirect, req, {
    paramId: 'redirectId',
  });

  res.status(200).json(data);
};

export const restoreRedirect = async (req, res) => {
  const data = await factory.restoreOne(Redirect, req, {
    paramId: 'redirectId',
  });

  res.status(200).json(data);
};

export const disableRedirect = async (req, res) => {
  const data = await factory.disableOne(Redirect, req, {
    paramId: 'redirectId',
  });

  res.status(200).json(data);
};

export const enableRedirect = async (req, res) => {
  const data = await factory.enableOne(Redirect, req, {
    paramId: 'redirectId',
  });

  res.status(200).json(data);
};

export default {
  getAllRedirects,
  getRedirect,
  createRedirect,
  updateRedirect,
  deleteRedirect,
  restoreRedirect,
  disableRedirect,
  enableRedirect,
};
