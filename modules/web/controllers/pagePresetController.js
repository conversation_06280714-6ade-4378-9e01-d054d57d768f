import entityServices from '#modules/entities/services/entityServices.js';
import factory from '#utils/handlerFactory.js';

import PagePreset from '../models/PagePreset.js';
import pagePresetServices from '../services/pagePresetServices.js';

export const getAllPagePresets = async (req, res) => {
  const { entity: reqEntity } = req;
  const {
    design,
    designVariant,
    entity: entityId,
    limit,
    page,
    search,
    site,
    skip,
  } = req.query;

  const groups = req.query.groups?.split(',');
  const designs = req.query.designs?.split(',');

  let entity;

  if (!req.user?.isAdmin) {
    entity = reqEntity;
  }

  // Override current entity if entityId is provided
  if (entityId) {
    const entityById = await entityServices.getEntityById(entityId);
    if (entityById) {
      entity = entityById;
    }
  }

  const data = await pagePresetServices.getAll({
    design,
    designVariant,
    designs,
    entity,
    groups,
    limit,
    page,
    search,
    site,
    skip,
  });

  res.status(200).json(data);
};

export const getPagePreset = async (req, res) => {
  const data = await factory.getOne(PagePreset, req, {
    paramId: 'pagePresetId',
    populate: ['pagesCount', 'entity', 'network', 'site'],
  });

  if (!data) {
    return res.status(404).json({
      status: 'fail',
      message: 'PagePreset not found',
    });
  }

  data.content = JSON.parse(data.content || '{}') || {};

  res.status(200).json(data);
};

export const createPagePreset = async (req, res) => {
  // Create pagePreset
  const pagePreset = await pagePresetServices.createPagePreset({
    formData: req.body,
    entity: req.entity,
    user: req.user,
  });

  res.status(200).json(pagePreset);
};

export const updatePagePreset = async (req, res) => {
  const updatedPagePreset = await pagePresetServices.updatePagePreset({
    pagePresetId: req.params.pagePresetId,
    formData: req.body,
    entity: req.entity,
    user: req.user,
  });

  res.status(200).json(updatedPagePreset);
};

export const deletePagePreset = async (req, res) => {
  const data = await factory.deleteOne(PagePreset, req, {
    paramId: 'pagePresetId',
  });

  res.status(200).json(data);
};

export const restorePagePreset = async (req, res) => {
  const data = await factory.restoreOne(PagePreset, req, {
    paramId: 'pagePresetId',
  });

  res.status(200).json(data);
};

export const disablePagePreset = async (req, res) => {
  const data = await factory.disableOne(PagePreset, req, {
    paramId: 'pagePresetId',
  });

  res.status(200).json(data);
};

export const enablePagePreset = async (req, res) => {
  const data = await factory.enableOne(PagePreset, req, {
    paramId: 'pagePresetId',
  });

  res.status(200).json(data);
};

export default {
  getAllPagePresets,
  getPagePreset,
  createPagePreset,
  updatePagePreset,
  deletePagePreset,
  restorePagePreset,
  disablePagePreset,
  enablePagePreset,
};
