import searchIndexTaskService from '#modules/search/services/searchIndexTaskService.js';
import { errors } from '#utils/appError.js';
import * as siteSearchServices from '../services/siteSearchServices.js';

const createSiteSearchIndexes = async (req, res) => {
  const { params, entity } = req;

  const { searchIndexes, error } =
    await siteSearchServices.createSiteSearchIndexes({
      entity,
      siteId: params.siteId,
    });

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndexes);
};

const updateSiteSearchSettings = async (req, res) => {
  const { params, entity, body } = req;

  const { site, error } = await siteSearchServices.updateSiteSearchSettings({
    entity,
    settings: body,
    siteId: params.siteId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(site);
};

const reIndexSiteSearchIndex = async (req, res) => {
  const { params, entity, body } = req;
  const { siteId, searchIndexId } = params;
  const { useLastSync = false } = body;

  if (!siteId || !searchIndexId) {
    throw errors.params(['siteId', 'searchIndexId']);
  }

  const { searchIndexTask, error } =
    await siteSearchServices.reIndexSiteSearchIndex({
      entity,
      searchIndexId,
      siteId,
      useLastSync,
    });

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndexTask);
};

const getSearchIndexTask = async (req, res) => {
  const { params } = req;
  const { searchIndexTaskId } = params;

  const { searchIndexTask, error } =
    await searchIndexTaskService.getSearchIndexTaskById(searchIndexTaskId);

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndexTask);
};

const getSearchIndexTasks = async (req, res) => {
  const { params, query } = req;
  const { searchIndexId } = params;

  const { limit, page, skip, status } = query;

  const { searchIndexTasks, error } =
    await searchIndexTaskService.getSearchIndexTasks({
      limit,
      page,
      searchIndexId,
      skip,
      status,
    });

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndexTasks);
};

const resumeSearchIndexTask = async (req, res) => {
  const { params, entity } = req;
  const { siteId, searchIndexId, searchIndexTaskId } = params;

  if (!siteId || !searchIndexId || !searchIndexTaskId) {
    throw errors.params(['siteId', 'searchIndexId', 'searchIndexTaskId']);
  }

  const {
    searchIndexTask: resumedSearchIndexTask,
    error: searchIndexTaskError,
  } = await searchIndexTaskService.getSearchIndexTaskById(searchIndexTaskId, {
    lean: false,
  });

  if (searchIndexTaskError) {
    throw searchIndexTaskError;
  }

  if (!resumedSearchIndexTask) {
    throw errors.not_found('SearchIndexTask');
  }

  const { searchIndexTask, error } =
    await siteSearchServices.reIndexSiteSearchIndex({
      entity,
      resumeSearchIndexTask: resumedSearchIndexTask,
      searchIndexId,
      siteId,
    });

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndexTask);
};

export default {
  createSiteSearchIndexes,
  updateSiteSearchSettings,
  reIndexSiteSearchIndex,
  getSearchIndexTask,
  getSearchIndexTasks,
  resumeSearchIndexTask,
};
