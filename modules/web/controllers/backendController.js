import { addDomain, removeDomain } from '#utils/domains/index.js';

const { BACKEND_APP_NAME } = process.env;

export const addBackendDomain = async (req, res) => {
  // This is a very unique case where we need to add a domain to the hosting provider of the backend or the API's instead of just the frontends.

  if (!BACKEND_APP_NAME) {
    res
      .status(500)
      .json({ status: 'error', message: 'No backend app name was found.' });
    return;
  }
  const { domain } = req.body || {};

  const response = await addDomain({
    providerName: 'digitalOcean',
    domain,
    appName: BACKEND_APP_NAME,
  });

  // No hosting record is created here because this isn't for frontends.
  if (!response) {
    res.status(500).json({ status: 'error', message: 'Failed to add domain.' });
    return;
  }
  res.status(200).json({ status: 'success' });
};

export const removeBackendDomain = async (req, res) => {
  const { domain } = req.body || {};

  if (!BACKEND_APP_NAME) {
    res
      .status(500)
      .json({ status: 'error', message: 'No backend app name was found.' });
    return;
  }

  const response = await removeDomain({
    providerName: 'digitalOcean',
    domain,
    appName: BACKEND_APP_NAME,
  });

  if (!response) {
    res.status(500).json({ status: 'error' });
    return;
  }
  res.status(200).json({ status: 'success' });
};

export default {
  addBackendDomain,
  removeBackendDomain,
};
