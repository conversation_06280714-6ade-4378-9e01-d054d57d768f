// import {errors} from '#utils/appError.js';
import factory from '#utils/handlerFactory.js';
import Layout from '../models/Layout.js';
import Site from '../models/Site.js';
import layoutServices from '../services/layoutServices.js';

export const getLayouts = async (req, res) => {
  const site = await factory.getOne(Site, req, { paramId: 'siteId' });

  const data = await factory.getAll(Layout, req, {
    filter: {
      site: site._id,
      deleted: false,
      source: { $eq: null },
    },
    sort: ['createdAt'],
  });

  res.status(200).json(data);
};

export const getLayout = async (req, res) => {
  const data = await factory.getOne(Layout, req, { paramId: 'layoutId' });

  if (!data) {
    return res.status(404).json({
      status: 'fail',
      message: 'Layout not found',
    });
  }

  data.content = JSON.parse(data.content || '{}') || {};

  res.status(200).json(data);
};

export const createLayout = async (req, res) => {
  const site = await factory.getOne(Site, req, { paramId: 'siteId' });

  // Create the layout
  const layout = await Layout.create({ ...req.body, site });

  res.status(200).json(layout);
};

export const updateLayout = async (req, res) => {
  const layout = await factory.getOne(Layout, req, { paramId: 'layoutId' });

  // Update layout's  data
  const updatedLayout = await Layout.findByIdAndUpdate(
    layout._id,
    {
      ...req.body,
      site: layout.site, // prevents moving a layout to different site
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedLayout);
};

export const getLayoutTranslations = async (req, res) => {
  const { params } = req;
  const { layoutId } = params;

  const layoutTranslations =
    await layoutServices.getLayoutTranslations(layoutId);

  res.status(200).json(layoutTranslations);
};

export const addLayoutTranslation = async (req, res) => {
  const { layoutId, language } = req.params;

  const layoutTranslations = await layoutServices.addLayoutTranslation(
    layoutId,
    language
  );

  res.status(200).json(layoutTranslations);
};

export const deleteLayout = async (req, res) => {
  const data = await factory.deleteOne(Layout, req, { paramId: 'layoutId' });

  res.status(200).json(data);
};

export const restoreLayout = async (req, res) => {
  const data = await factory.restoreOne(Layout, req, { paramId: 'layoutId' });

  res.status(200).json(data);
};

export const disableLayout = async (req, res) => {
  const data = await factory.disableOne(Layout, req, { paramId: 'layoutId' });

  res.status(200).json(data);
};

export const enableLayout = async (req, res) => {
  const data = await factory.enableOne(Layout, req, { paramId: 'layoutId' });

  res.status(200).json(data);
};

export default {
  getLayouts,
  getLayout,
  createLayout,
  updateLayout,
  getLayoutTranslations,
  addLayoutTranslation,
  deleteLayout,
  restoreLayout,
  disableLayout,
  enableLayout,
};
