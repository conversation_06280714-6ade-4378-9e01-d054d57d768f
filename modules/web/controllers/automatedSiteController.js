import Entity from '#modules/entities/models/Entity.js';

import factory from '#utils/handlerFactory.js';

import Site from '#modules/web/models/Site.js';

import Network from '#modules/entities/models/Network.js';
import {
  endHostingRecord,
  startHostingRecord,
} from '#modules/web/services/hostingRecordsServices.js';
import { cleanDomain } from '#utils/domains/helpers/utils.js';
import { addDomain, removeDomain } from '#utils/domains/index.js';

export const getAutomatedSites = async (req, res) => {
  // We need to only get the entities below the current entity
  // we set the current entityId

  const entityId = req.entity._id.toString();
  const options = {
    filter: {
      ancestors: { $in: [entityId] },
      enabled: true,
      deleted: false,
    },
  };

  const data = await factory.getAll(Entity, req, options);

  const sites = [
    ...new Set(
      data.items?.map((entity) => entity.config.automatedSite.site || null)
    ),
  ];

  const templateSites = await Site.find({ _id: { $in: sites } });
  const siteMap = {};
  templateSites.forEach((site) => {
    siteMap[site._id.toString()] = site; // This is to create a map of sites by id
  });

  // Each item is an entity
  const items = data.items.map((item) => {
    const entity = item.toJSON() || {}; // This is to convert the mongoose object to a plain object
    const fullSite = siteMap[entity.config?.automatedSite?.site]; // This is to get the full site by id

    if (fullSite) {
      entity.config.automatedSite.site = fullSite;
    }

    return entity;
  });

  res.status(200).json({ count: data.count, items });
};

export const getAutomatedSite = async (req, res) => {
  const data = await factory.getOne(Entity, req, {
    paramId: 'entityId',
    populate: [
      {
        path: 'network',
        match: { enabled: true, deleted: false },
        select: 'title automatedDomain',
      },
    ],
  });

  res.status(200).json(data);
};

/**
 * Update automated entity
 */
export const updateAutomatedEntity = async (req, res) => {
  const entity = await factory.getOne(Entity, req, {
    paramId: 'entityId',
    populate: [
      {
        path: 'network',
        match: { enabled: true, deleted: false },
        select: 'title automatedDomain',
      },
    ],
  });

  const { config } = req.body;

  const data = { ...req.body };

  // We clean up the domain by slugifying it and removing the protocol
  const domain = config?.automatedSite?.domain || '';
  const newDomain = cleanDomain(domain);

  // We add the clean domain to data so it can be saved
  data.config.automatedSite.domain = newDomain;

  // Set domains from automatedSite in config
  // if something inside automatedSite changed
  if (
    config?.automatedSite &&
    config?.automatedSite !== entity?.config?.automatedSite
  ) {
    const { automatedSite } = config || {};
    const { automatedSite: prevAutomatedSite } = entity?.config || {};

    // We find the site chosen in the config
    const site = (await Site.findById(automatedSite.site)) || {};
    const network = (await Network.findById(entity?.network)) || {};

    const prevSite = (await Site.findById(prevAutomatedSite.site)) || {};
    // If it was disabled and now is enabled.
    if (!prevAutomatedSite.enabled && automatedSite.enabled) {
      // We add the domain (incoming data) to the site

      await addDomain({
        domain: newDomain,
        design: site.design.name || '',
        region: site.region || '',
        addRedirect: false,
        zoneIdentifier: network?.zoneIdentifier || '',
        cnameContent: network?.cnameContent || '',
      });
      await startHostingRecord({
        entityId: entity._id,
        domain,
        site,
        type: 'automated-site',
        user: req.user,
      });
    }

    // Was enabled, is now disabled
    else if (prevAutomatedSite.enabled && !automatedSite.enabled) {
      // We add the domain to the site using the previous site and entity
      const { domain: prevDomain } = prevAutomatedSite;

      await removeDomain({
        prevDomain,
        design: prevSite.design.name || '',
        region: prevSite.region || '',
        zoneIdentifier: network?.zoneIdentifier || '',
      });
      await endHostingRecord({
        entityId: entity._id,
        user: req.user,
      });
    }

    // enabled hasn't changed, domain and/or site changed
    else if (
      (automatedSite.domain !== prevAutomatedSite.domain ||
        automatedSite.site !== prevAutomatedSite.site) &&
      automatedSite.domain.length > 0 &&
      automatedSite.site.length > 0
    ) {
      // In theory, hosting providers should move all domains when the site changes. We can't do that here.
      // What we do here is remove the domain from a previous site and add it to the new one, should the site change
      const { domain: prevDomain } = prevAutomatedSite;

      await removeDomain({
        domain: prevDomain,
        design: prevSite.design.name || '',
        region: prevSite.region || '',
        zoneIdentifier: network?.zoneIdentifier || '',
      });
      await endHostingRecord({
        entityId: entity._id,
        user: req.user,
      });

      // We add the domain (incoming data)
      await addDomain({
        domain: newDomain,
        design: site.design.name || '',
        region: site.region || '',
        addRedirect: false,
        zoneIdentifier: network?.zoneIdentifier || '',
        cnameContent: network?.cnameContent || '',
      });
      await startHostingRecord({
        entityId: entity._id,
        domain,
        site,
        type: 'automated-site',
        user: req.user,
      });
    }
  }

  // Update entity
  const updatedEntity = await Entity.findByIdAndUpdate(entity._id, data, {
    new: true,
    runValidators: true,
  });

  res.status(200).json(updatedEntity);
};

export default {
  getAutomatedSites,
  getAutomatedSite,
  updateAutomatedEntity,
};
