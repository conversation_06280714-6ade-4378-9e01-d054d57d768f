import featureFlagService from '#modules/feature-flags/services/featureFlagService.js';
import siteServices from '#modules/web/services/siteServices.js';

import dataSourceServices from '../services/dataSourceServices.js';

const getDataSourceTypes = async (req, res) => {
  const { params, entity } = req;
  const { siteId } = params;

  const { site } = await siteServices.getSiteById(siteId);

  let types = dataSourceServices.getDataSourceTypes();

  const isMixedDataSourcesEnabled = featureFlagService.isFeatureEnabled(
    'mixed-data-sources',
    { site, entity }
  );
  if (!isMixedDataSourcesEnabled) {
    types = types.filter((type) => type !== 'mixed');
  }

  res.status(200).json(types);
};

const getDataSources = async (req, res) => {
  const { params, query } = req;
  const { siteId } = params;
  const {
    allowedTypes,
    excludeIds,
    ids,
    limit,
    page,
    resourceModels,
    search,
    sort,
    sortDir,
    statuses,
  } = query;

  const { items, count } = await dataSourceServices.getDataSources({
    allowedTypes,
    excludeIds,
    fields: [],
    ids,
    limit,
    page,
    resourceModels,
    search,
    siteId,
    sort,
    sortDir,
    statuses,
  });

  res.status(200).json({ items, count });
};

const getDataSource = async (req, res) => {
  const { params } = req;
  const { id, siteId } = params;

  const { dataSource, error } = await dataSourceServices.getDataSourceById({
    siteId,
    id,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(dataSource);
};

const createDataSource = async (req, res) => {
  const { siteId } = req.params;

  const { dataSource, error } = await dataSourceServices.createDataSource({
    siteId,
    dataSource: req.body,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(dataSource);
};

const updateDataSource = async (req, res) => {
  const { siteId, id } = req.params;

  const { dataSource, error } = await dataSourceServices.updateDataSource({
    id,
    siteId,
    dataSource: req.body,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(dataSource);
};

const deleteDataSource = async (req, res) => {
  const { siteId, id } = req.params;

  const { error } = await dataSourceServices.deleteDataSource({
    id,
    siteId,
  });

  if (error) {
    throw error;
  }

  res.status(204).json({});
};

const enableDataSource = async (req, res) => {
  const { siteId, id } = req.params;

  const { error, dataSource } = await dataSourceServices.enableDataSource({
    id,
    siteId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(dataSource);
};

const disableDataSource = async (req, res) => {
  const { siteId, id } = req.params;

  const { error, dataSource } = await dataSourceServices.disableDataSource({
    id,
    siteId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(dataSource);
};

export default {
  getDataSourceTypes,
  getDataSources,
  getDataSource,
  createDataSource,
  updateDataSource,
  deleteDataSource,
  enableDataSource,
  disableDataSource,
};
