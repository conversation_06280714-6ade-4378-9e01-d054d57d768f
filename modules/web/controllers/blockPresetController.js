import entityServices from '#modules/entities/services/entityServices.js';
import factory from '#utils/handlerFactory.js';

import BlockPreset from '../models/BlockPreset.js';
import blockPresetServices from '../services/blockPresetServices.js';

export const getBlockPresets = async (req, res) => {
  const { entity: reqEntity } = req;
  const {
    design,
    designVariant,
    entity: entityId,
    limit,
    page,
    search,
    site,
    skip,
  } = req.query;

  const groups = req.query.groups?.split(',');
  const designs = req.query.designs?.split(',');

  let entity;

  if (!req.user?.isAdmin) {
    entity = reqEntity;
  }

  // Override current entity if entityId is provided
  if (entityId) {
    const entityById = await entityServices.getEntityById(entityId);
    if (entityById) {
      entity = entityById;
    }
  }

  // Get block presets
  const data = await blockPresetServices.getBlockPresetsList({
    design,
    designVariant,
    designs,
    entity,
    groups,
    limit,
    page,
    search,
    site,
    skip,
  });

  res.status(200).json(data);
};

export const getBlockPreset = async (req, res) => {
  const data = await factory.getOne(BlockPreset, req, {
    paramId: 'blockPresetId',
    populate: ['entity', 'network', 'site'],
  });

  if (!data) {
    return res.status(404).json({
      status: 'fail',
      message: 'BlockPreset not found',
    });
  }

  data.content = JSON.parse(data.content || '{}') || {};

  res.status(200).json(data);
};

export const checkBlockPresetName = async (req, res) => {
  const { name, design } = req.params;

  const data = await blockPresetServices.checkBlockPresetName({
    name,
    design,
  });

  res.status(200).json(data);
};

export const createBlockPreset = async (req, res) => {
  // Create blockPreset
  const blockPreset = await blockPresetServices.createBlockPreset({
    formData: req.body,
    entity: req.entity,
    user: req.user,
  });

  res.status(200).json(blockPreset);
};

export const updateBlockPreset = async (req, res) => {
  const updatedBlockPreset = await blockPresetServices.updateBlockPreset({
    blockPresetId: req.params.blockPresetId,
    formData: req.body,
    entity: req.entity,
    user: req.user,
  });

  res.status(200).json(updatedBlockPreset);
};

export const deleteBlockPreset = async (req, res) => {
  const data = await factory.deleteOne(BlockPreset, req, {
    paramId: 'blockPresetId',
  });

  res.status(200).json(data);
};

export const restoreBlockPreset = async (req, res) => {
  const data = await factory.restoreOne(BlockPreset, req, {
    paramId: 'blockPresetId',
  });

  res.status(200).json(data);
};

export const disableBlockPreset = async (req, res) => {
  const data = await factory.disableOne(BlockPreset, req, {
    paramId: 'blockPresetId',
  });

  res.status(200).json(data);
};

export const enableBlockPreset = async (req, res) => {
  const data = await factory.enableOne(BlockPreset, req, {
    paramId: 'blockPresetId',
  });

  res.status(200).json(data);
};

export default {
  getBlockPresets,
  getBlockPreset,
  checkBlockPresetName,
  createBlockPreset,
  updateBlockPreset,
  deleteBlockPreset,
  restoreBlockPreset,
  disableBlockPreset,
  enableBlockPreset,
};
