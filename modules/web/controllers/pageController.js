import { errors } from '#utils/appError.js';
import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';

import Page from '../models/Page.js';
import Site from '../models/Site.js';
import pageServices from '../services/pageServices.js';

export const getAllPages = async (req, res) => {
  const {
    resourceType,
    resourceName,
    entityId,
    groupBySite,
    pageIds,
    siteId,
    status,
  } = req.query;

  if (resourceType && resourceName) {
    const { items, count } = await pageServices.getPagesByDynamicResource({
      entityId,
      groupBySite: Boolean(groupBySite),
      resourceName,
      resourceType,
      siteId,
    });
    return res.status(200).json({ items, count });
  }

  if (pageIds) {
    const { items, count } = await pageServices.getPagesByIds({
      pageIds,
      status,
    });
    return res.status(200).json({ items, count });
  }

  const { items, count } = await pageServices.getPagesForRequest(req);

  res.status(200).json({ items, count });
};

export const getPage = async (req, res) => {
  const { pageId, siteId } = req.params;
  const { includeAncestors = true, includeResources = true } = req.query;

  const page = await pageServices.getPage(pageId, {
    includeAncestors,
    includeResources,
    siteId, // Enforce siteId to ensure page belongs to site of the route.
  });

  if (!page) {
    return res.status(404).json({
      status: 'fail',
      message: 'Page not found',
    });
  }

  page.content = JSON.parse(page.content || '{}') || {};

  res.status(200).json(page);
};

export const getPageTranslations = async (req, res) => {
  // TODO: check if site belongs to entity
  const { entity, query } = req;
  const { siteId, pageId } = req.params;

  const pageTranslations = await pageServices.getPageTranslations(
    siteId,
    pageId,
    entity,
    query
  );

  res.status(200).json(pageTranslations);
};

export const createPage = async (req, res) => {
  const site = await factory.getOne(Site, req, { paramId: 'siteId' });
  const parent = req.params.pageId
    ? await factory.getOne(Page, req, { paramId: 'pageId' })
    : null;

  const { title, slug, position, publishStartsAt } = req.body;

  // Ensure page has a valid slug within its siblings (or create one from  its title)
  const pageSlug = await Page.getAvailableSlug({
    slug: slugify(slug || title),
    site,
    parent,
  });

  // Create the page
  let page = await Page.create({
    ...req.body,
    publishStartsAt: publishStartsAt || new Date(), // Set publish date (now by default)
    parent: parent?.id, // Set parent page
    slug: pageSlug,
    site,
  });

  // Ensure page has a position
  page = await Page.movePage(page, position, parent?.id);

  // Update path
  page = await page.updatePath();

  res.status(200).json(page);
};

export const updatePage = async (req, res) => {
  const { params, body, entity } = req;

  const { page, error } = pageServices.updatePage({
    entity,
    pageData: body,
    pageId: params.pageId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(page);
};

export const addPageTranslation = async (req, res) => {
  const { siteId, pageId, language } = req.params;

  const { publishStartsAt } = req.body;

  const site = await factory.getOne(Site, req, { paramId: 'siteId' });
  if (!site) {
    throw errors.not_found('site', siteId);
  }

  const sourcePage = await factory.getOne(Page, req, { paramId: 'pageId' });
  if (!site) {
    throw errors.not_found('sourcePage', pageId);
  }

  // Ensure page has a valid slug within its siblings (or create one from  its title)
  const pageSlug = await Page.getAvailableSlug({
    slug: slugify(`${sourcePage.slug}-${language}`),
    site,
    parent: sourcePage.parent,
    language,
  });

  // Create the page
  let page = await Page.create({
    publishStartsAt: publishStartsAt || new Date(), // Set publish date (now by default)
    source: sourcePage._id,
    slug: pageSlug,
    language,
    site,
  });

  // Update path
  page = await page.updatePath();

  res.status(200).json(page);
};

export const duplicatePage = async (req, res) => {
  const duplicatedPage = await pageServices.duplicatePage({
    pageId: req.params.pageId,
    name: req.body.name,
    slug: req.body.slug,
    title: req.body.title,
  });

  res.status(200).json(duplicatedPage);
};

export const deletePage = async (req, res) => {
  const { entity, params } = req;
  const { pageId } = params;

  const { deletedPage, error } = await pageServices.deletePage({
    entity,
    pageId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(deletedPage);
};

export const restorePage = async (req, res) => {
  const data = await factory.restoreOne(Page, req, { paramId: 'pageId' });

  res.status(200).json(data);
};

export const disablePage = async (req, res) => {
  const { entity, params } = req;
  const { pageId } = params;

  const { updatedPage, error } = await pageServices.disablePage({
    entity,
    pageId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(updatedPage);
};

export const enablePage = async (req, res) => {
  const { entity, params } = req;
  const { pageId } = params;

  const { updatedPage, error } = await pageServices.enablePage({
    entity,
    pageId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(updatedPage);
};

export const movePage = async (req, res) => {
  const { position, parent } = req.body;
  const page = await factory.getOne(Page, req, { paramId: 'pageId' });

  let updatedPage = await Page.movePage(page, position, parent);

  updatedPage = await updatedPage?.updatePath();

  res.status(200).json(updatedPage);
};

export const setAsHome = async (req, res) => {
  const page = await factory.getOne(Page, req, { paramId: 'pageId' });

  const updatedPage = await Page.setAsHome(page);

  res.status(200).json(updatedPage);
};

export const isPageInUse = async (req, res) => {
  const { pageId, siteId } = req.params;

  const { inUse, references } = await pageServices.isPageInUse({
    pageId,
    siteId,
  });

  res.status(200).json({ inUse, references });
};

export default {
  getAllPages,
  getPage,
  getPageTranslations,
  createPage,
  updatePage,
  addPageTranslation,
  duplicatePage,
  deletePage,
  restorePage,
  disablePage,
  enablePage,
  movePage,
  setAsHome,
  isPageInUse,
};
