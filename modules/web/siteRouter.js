import express from 'express';

import { isRouteEnabled } from '#modules/feature-flags/middleware/isRouteEnabled.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { restrictTo } from '#modules/users/controllers/authController.js';
import { validate } from '#utils/validationMiddleware.js';

import siteController from './controllers/siteController.js';
import { siteAncestorsByDynamicResourceSchema } from './validation/siteAncestorsByDynamicResourceSchema.js';

const siteRouter = express.Router({ mergeParams: true });

siteRouter
  .route('/')
  .get(siteController.getSite)
  .patch(
    siteController.getSiteSourceRecordId, // if it's a translation, get source site id instead of translation site id
    restrictTo({
      module: 'sites',
      permissions: ['update'],
      paramId: 'siteId',
    }),
    logRequest({ module: 'web', action: 'UPDATE_SITE' }),
    siteController.updateSite
  )
  .delete(
    siteController.getSiteSourceRecordId, // if it's a translation, get source site id instead of translation site id
    restrictTo({
      module: 'sites',
      permissions: ['delete'],
      paramId: 'siteId',
    }),
    logRequest({ module: 'web', action: 'DELETE_SITE' }),
    siteController.deleteSite
  );

siteRouter.route('/public').get(siteController.getPublicSite);

siteRouter.route('/resources').get(siteController.getModelResources);

siteRouter
  .route('/ancestors-by-dynamic-resource')
  .get(
    isRouteEnabled('articles-by-site'),
    validate(siteAncestorsByDynamicResourceSchema, 'query'),
    siteController.getAncestorsByDynamicResource
  );

siteRouter.route('/site-or-ancestor').get(siteController.getSiteOrAncestorSite);

siteRouter.route('/translations').get(siteController.getSiteTranslations);

siteRouter.route('/add-translation/:language').post(
  restrictTo({
    module: 'sites',
    permissions: ['translate'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'TRANSLATE_SITE' }),
  siteController.addSiteTranslation
);

siteRouter.route('/export').post(
  restrictTo({
    module: 'sites',
    permissions: ['clone'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'EXPORT_SITE' }),
  siteController.exportSite
);

siteRouter.route('/restore').patch(
  restrictTo({
    module: 'sites',
    permissions: ['delete'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'RESTORE_SITE' }),
  siteController.restoreSite
);

siteRouter.route('/disable').patch(
  restrictTo({
    module: 'sites',
    permissions: ['update'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'DISABLE_SITE' }),
  siteController.disableSite
);

siteRouter.route('/enable').patch(
  restrictTo({
    module: 'sites',
    permissions: ['update'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'ENABLE_SITE' }),
  siteController.enableSite
);

export default siteRouter;
