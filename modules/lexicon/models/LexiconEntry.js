import mongoose from 'mongoose';

import schemaFactory from '#utils/schemaFactory.js';
import { PersonBioRoleSchema } from '#modules/persons/models/Person.js';

// Schema for the LexiconEntry model
const lexiconEntrySchema = schemaFactory({
  // Entry's real author
  author: { type: PersonBioRoleSchema, ref: 'PersonBio' },
  body: {
    type: mongoose.SchemaTypes.Mixed,
    required: true,
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    default: null,
  },
  // Language of the entry (ISO 639-1 code)
  language: {
    type: String, // e.g. 'en', 'es', 'es-ar', 'pt-br', etc.
    trim: true,
    //required: true,
  },
  name: { type: String, trim: true, required: true },
  publishedAt: {
    type: Date,
  },
  slug: {
    type: String,
    trim: true,
  },
});

lexiconEntrySchema.index({
  name: 1,
  body: 1,
  entity: 1,
  slug: 1,
});

export default mongoose.model('LexiconEntry', lexiconEntrySchema);
