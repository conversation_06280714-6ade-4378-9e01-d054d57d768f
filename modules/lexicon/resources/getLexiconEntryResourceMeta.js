/**
 * Get Lexicon Entry resource meta
 * @param {Object} options
 * @param {Object} options.resource - The Lexicon Entry resource
 * @returns {Object}
 */
export function getLexiconEntryResourceMeta({ resource }) {
  if (!resource) {
    return null;
  }

  const { author, name, slug, publishedAt } = resource;

  const nameTag = name.charAt(0).toLowerCase();
  const slugTag = slug?.charAt(0).toLowerCase();

  const tags = [nameTag];

  if (slugTag && slugTag !== nameTag) {
    tags.push(slugTag);
  }

  return {
    ...(author ? { author: author.person } : {}),
    // Use the first letter of the name as the tag. This enables filtering by the first letter of the name.
    tags,
    publishedAt,
  };
}
