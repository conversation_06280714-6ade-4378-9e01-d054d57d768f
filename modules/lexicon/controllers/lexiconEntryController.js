import {
  editEntry,
  createEntry,
  getEntry,
  getAllEntries,
  deleteEntry,
  getAuthorsList,
  getAuthorsListFromPersons,
  restoreEntryService,
  toggleEntry,
  getAvailableSlug,
} from '../services/lexiconEntryService.js';

export const getAllEntryContents = async (req, res) => {
  const { query, entity } = req;
  const { limit, page, search, sort, sortDir, filter } = query;

  const { statuses, authors } = filter ? JSON.parse(filter) : {};

  const { items, count } = await getAllEntries({
    limit,
    page,
    authors,
    search,
    sortBy: sort,
    sortDir,
    statuses,
    entity,
  });
  res.status(200).json({ items, count });
};

export const getEntryContent = async ({ params: { id } }, res) => {
  const item = await getEntry(id);
  if (!item) {
    return res
      .status(404)
      .json({ status: 'error', message: 'LexiconEntry not found' });
  }
  res.status(200).json(item);
};

export const createEntryContent = async (req, res) => {
  const { body, entity, user } = req;

  if (body.slug) {
    body.slug = await getAvailableSlug({ slug: body.slug, entity: entity.id });
  }

  const entryInfoSent = {};
  for (const key of Object.keys(body)) {
    const currentKeyValue = body[key];
    if (currentKeyValue != null) {
      entryInfoSent[key] = currentKeyValue;
    }
  }

  const { data, error } = await createEntry(entryInfoSent, entity, user);

  if (error) {
    res.status(400).json({ status: 'error', message: error });
  }

  res.status(200).json({ data });
};

export const updateEntryContent = async (req, res) => {
  const { data, error } = await editEntry({
    body: req.body,
    entity: req.entity,
  });

  if (error) {
    res.status(400).json({ status: 'error', message: error });
  }

  res.status(200).json({ data });
};

export const deleteEntryContent = async ({ entity, params: { id } }, res) => {
  const { data, error } = await deleteEntry({
    id,
    entity,
  });
  if (error) {
    return res.status(400).json({ status: 'error', message: error.message });
  }

  res.status(200).json({ data });
};

export const getAuthors = async (req, res) => {
  const { authorSearch } = req.query;
  const authors = await getAuthorsList({
    search: authorSearch,
    entity: req.entity,
  });
  res.status(200).json(authors);
};

export const getAuthorsFromPersons = async (req, res) => {
  const { ids } = req.query;
  const data = await getAuthorsListFromPersons({
    ids,
  });
  res.status(200).json(data);
};

export const toggleEntryStatus = async (req, res) => {
  const { data, error } = await toggleEntry({
    id: req.params.id,
    entity: req.entity,
  });
  if (error) {
    return res.status(400).json({ status: 'error', message: error.message });
  }

  res.status(200).json({ data });
};

export const restoreEntry = async (req, res) => {
  const { data, error } = await restoreEntryService(req.params.id);
  if (error) {
    return res.status(400).json({ status: 'error', message: error.message });
  }

  res.status(200).json({ data });
};

export default {
  deleteEntryContent,
  updateEntryContent,
  createEntryContent,
  getEntryContent,
  getAuthors,
  getAuthorsFromPersons,
  getAllEntryContents,
  toggleEntryStatus,
  restoreEntry,
};
