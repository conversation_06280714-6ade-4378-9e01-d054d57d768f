import { htmlToTiptap } from '#scripts/import/tiptap/parse.js';

/**
 * Sanitizes an input string by removing or replacing special characters,
 * whitespace irregularities, zero-width spaces, and HTML entities to
 * produce a consistently formatted, safe output.
 *
 * @param {string} input The string to sanitize.
 * @returns {string} The sanitized string.
 */
export function sanitize(input) {
  // Ensure the input is a string; otherwise, return an empty string.
  if (typeof input !== 'string') return '';

  // First, normalize whitespace and line breaks.
  let sanitized = input
    .trim() // Remove leading/trailing whitespace.
    .replaceAll(/​/g, '') // Remove zero-width spaces
    .replaceAll(/ /g, ' ') // Replace non-breaking spaces with regular spaces
    .replaceAll(/[\u2028\u2029]/g, '\n') // Replace Unicode line separators with a newline.
    .replaceAll('\r\n', '\n'); // Normalize Windows-style new lines to Unix-style.

  // Define a map for named HTML entities to their corresponding characters.
  const replacements = {
    '&nbsp;': ' ',
    '&#160;': ' ',
    '&amp;': '&',
    '&#38;': '&',
    '&quot;': '"',
    '&#34;': '"',
    '&ldquo;': '"',
    '&rdquo;': '"',
    '&#8220;': '"',
    '&#8221;': '"',
    '&lsquo;': "'",
    '&rsquo;': "'",
    '&#8216;': "'",
    '&#8217;': "'",
    '&apos;': "'",
    '&ndash;': '-',
    '&#8211;': '-',
    '&mdash;': '—',
    '&#8212;': '—',
    '&hellip;': '…',
    '&#8230;': '…',
  };

  // Replace all occurrences of the named HTML entities.
  for (const [entity, replacement] of Object.entries(replacements)) {
    // Escape any regex special characters in the entity string.
    const entityRegex = new RegExp(
      entity.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
      'g'
    );
    sanitized = sanitized.replace(entityRegex, replacement);
  }

  // Replace decimal numeric entities (e.g., "&#225;").
  sanitized = sanitized.replace(/&#(\d+);/g, (_match, numStr) => {
    const code = parseInt(numStr, 10);
    return String.fromCharCode(code);
  });

  // Replace hexadecimal numeric entities (e.g., "&#xE1;").
  sanitized = sanitized.replace(/&#x([0-9a-fA-F]+);/g, (_match, hexStr) => {
    const code = parseInt(hexStr, 16);
    return String.fromCharCode(code);
  });

  return sanitized;
}
// Updated special patterns with more flexible whitespace matching.
const specialPatterns = {
  // Matches a paragraph with a drop-cap (class="p1 _drop-cap") that is immediately followed by another <p> tag.
  dropCapsP: {
    // Allow any whitespace (spaces, tabs, newlines) between the two paragraphs.
    pattern: /<p class="p1 _drop-cap">(\S)<\/p>\s*<p class="p1">/g,
    replacement: '<p>$1',
  },
  // Matches the drop cap pattern with spans; flexible whitespace is allowed between tags.
  dropCapsSpan: {
    pattern:
      /<p>\s*<span class="cap">(\S)<\/span>\s*<span class="cap">\s*<\/span>\s*<\/p>\s*<p>/g,
    replacement: '<p>$1',
  },
};

// MODX snippets regex map (unchanged)
const modxSnippetsRegexMap = {
  all: /\[\[\S+\]\]/g, // All MODX snippets: [[something]]
  links: /\[\[~\d+\]\]/g, // MODX internal links: [[~id]]
  uncached: /\[\[!\S+\]\]/g, // Uncached calls: [[!something]]
  staticHtml: /\[\[\$\S+\]\]/g, // Static HTML chunks: [[$something]]
  tags: /\[\[\*\S+\]\]/g, // MODX tags: [[*something]]
  placeholders: /\[\[\+?\S+\]\]/g, // Placeholders: [[+something]]
  settings: /\[\[\+\+\S+\]\]/g, // Settings: [[++something]]
};

/**
 * Convert HTML to Tiptap JSON, sanitizing the HTML and removing MODX snippets.
 *
 * @param {Object} options - The options object.
 * @param {String} options.html - The HTML string to convert.
 * @param {string|null} [options.debugId] - Optional debug ID for tracking.
 * @param {Object|null} [options.debugCollectorMap] - Optional object to collect debug logs.
 * @returns {Object|null} Tiptap JSON or null if no HTML is provided.
 */
export default function convertToTiptap({
  html = '',
  debugId = null,
  debugCollectorMap = null,
}) {
  // Early exit if the input is not a string or is empty.
  if (typeof html !== 'string' || !html) {
    return null;
  }

  // Array to collect debugging information if needed.
  const debugLogs = [];

  try {
    // STEP 1: Remove all MODX snippets.
    // Using .replace (or replaceAll) with the global regex to remove any snippet found.
    let replacedString = html.replace(modxSnippetsRegexMap.all, '');
    debugLogs.push({ step: 'removeModxSnippets', value: replacedString });

    // STEP 2: Apply special pattern replacements (e.g. drop caps)
    for (const modifier of Object.values(specialPatterns)) {
      replacedString = replacedString.replace(
        modifier.pattern,
        modifier.replacement
      );
    }
    debugLogs.push({ step: 'applySpecialPatterns', value: replacedString });

    // STEP 3: Sanitize the HTML.
    // (Assuming your sanitize() function is already robust as defined earlier.)
    const sanitizedHtml = sanitize(replacedString);
    debugLogs.push({ step: 'sanitizeHtml', value: sanitizedHtml });

    // STEP 4: Convert sanitized HTML to Tiptap JSON.
    // (Assuming htmlToTiptap() is a function that converts sanitized HTML to the expected JSON format.)
    const tiptapJson = htmlToTiptap(sanitizedHtml);
    debugLogs.push({ step: 'htmlToTiptapConversion', value: tiptapJson });

    // STEP 5: Remove paragraphs that are empty.
    if (tiptapJson && Array.isArray(tiptapJson.content)) {
      tiptapJson.content = tiptapJson.content.filter((node) => {
        // Remove node if it's a paragraph with no content.
        if (
          node.type === 'paragraph' &&
          (!node.content || node.content.length === 0)
        ) {
          return false;
        }
        return true;
      });
      debugLogs.push({
        step: 'filterEmptyParagraphs',
        value: tiptapJson.content,
      });
    }

    // If debugging is enabled, add the logs to the collector.
    if (debugCollectorMap && debugId) {
      debugCollectorMap[debugId] = debugLogs;
    }

    // Return the final Tiptap JSON.
    return tiptapJson;
  } catch (error) {
    // Log the error if debugging is enabled.
    debugLogs.push({ step: 'error', error });
    if (debugCollectorMap && debugId) {
      debugCollectorMap[debugId] = debugLogs;
    }
    // Rethrow or handle the error as needed.
    throw error;
  }
}
