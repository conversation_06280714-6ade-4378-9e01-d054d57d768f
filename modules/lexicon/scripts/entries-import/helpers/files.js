import fs from 'fs';
import Logger from '#utils/logger.js';
import lodash from 'lodash';

const { isEmpty } = lodash;

/**
 * Loads a cached JSON file from the specified temp folder and returns its content.
 *
 * @param {Object} params The function parameters.
 * @param {string} params.tempFolder Path to the temporary folder containing the cached file.
 * @param {string} params.fileName Name of the file (without the .json extension).
 * @returns {Object} Parsed JSON object from the cached file, or an empty object if the file isn't found or can't be read.
 */
export function loadCachedFile({ tempFolder, fileName }) {
  const map = `${tempFolder}/${fileName}.json`;

  // Read the images cache file if it exists
  if (fs.existsSync(map)) {
    try {
      const cachedMap = fs.readFileSync(map, 'utf8');

      if (isEmpty(cachedMap)) return [];

      return JSON.parse(cachedMap);
    } catch (error) {
      Logger.error('Failed to read images cache file', error);
    }
  }

  return {};
}
