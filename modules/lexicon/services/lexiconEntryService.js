import mongoose from 'mongoose';

import getListFilters from '#utils/api/list/filters.js';
import getFullName from '#utils/getFullName.js';
import { uniquifySlug } from '#utils/strings.js';
import { eventEmitter } from '#utils/eventEmitter.js';

import LexiconEntry from '../models/LexiconEntry.js';
import Person from '../../persons/models/Person.js';
import { lexiconEntryEvents } from '../events/lexiconEntryEvents.js';

const lexiconEntryFields = ['name', 'body'];
const autorSearchFilters = [
  'author.fullName',
  'author.prefix',
  'author.firstName',
  'author.middleName',
  'author.lastName',
  'author.suffix',
];
/**
 * @typedef {Object} LexiconEntryData
 * @property {Object} author The real author (based on PersonBioRoleSchema)
 * @property {any} body The content body (required)
 * @property {string} [entity] The ID of the associated Entity
 * @property {string} [language] The article language (ISO 639-1 code)
 * @property {string} name The name of the entry (required)
 * @property {Date} [publishedAt] The publication date
 * @property {string} [slug] The unique slug
 */
/**
 * Retrieves all lexicon entries.
 * @async
 * @function getAllEntries
 * @returns {Promise<LexiconEntryData[]>} Returns a promise that resolves to an array of lexicon entries.
 */
export const getAllEntries = async ({
  authors = [],
  limit = 25,
  page = 1,
  search = '',
  skip = 0,
  sortBy = 'name',
  sortDir = 'desc',
  entity = null,
  statuses = [],
}) => {
  const filters = getListFilters({
    statuses,
    limit,
    page,
    search,
    searchFields: [...lexiconEntryFields, ...autorSearchFilters],
    sort: sortBy,
    order: sortDir,
    skip,
  });
  const matchFilters = {
    entity: new mongoose.Types.ObjectId(entity.id),
    $and: [filters.search, filters.statuses],
  };

  // Filter by authors if provided
  if (authors?.length > 0) {
    const authorsWithMongoIds = authors.map(
      (author) => new mongoose.Types.ObjectId(author)
    );
    matchFilters['author._id'] = { $in: authorsWithMongoIds };
  }

  // Use aggregation so the 'author' field is populated before matching
  const pipeline = [
    {
      $lookup: {
        from: 'people',
        localField: 'author.person',
        foreignField: '_id',
        as: 'author',
      },
    },
    {
      $unwind: {
        path: '$author',
        preserveNullAndEmptyArrays: true, // In case entry has no author
      },
    },
    {
      // Overwrite the 'author.fullName' field with a new 'fullName' built from the author's name parts
      $addFields: {
        'author.fullName': {
          $cond: {
            if: {
              $eq: [{ $ifNull: ['$author.fullName', ''] }, ''],
            },
            then: {
              $trim: {
                input: {
                  $concat: [
                    { $ifNull: ['$author.prefix', ''] },
                    ' ',
                    { $ifNull: ['$author.firstName', ''] },
                    ' ',
                    { $ifNull: ['$author.middleName', ''] },
                    ' ',
                    { $ifNull: ['$author.lastName', ''] },
                    ' ',
                    { $ifNull: ['$author.suffix', ''] },
                  ],
                },
              },
            },
            else: '$author.fullName',
          },
        },
      },
    },
    {
      $match: matchFilters,
    },
    {
      $sort: filters.sort,
    },
  ];

  const itemsPipeline = [
    ...pipeline,
    {
      $skip: filters.pagination.skip,
    },
    { $limit: filters.pagination.limit },
  ]; // Paginate the results
  const countPipeline = [...pipeline, { $count: 'count' }]; // Count the total number of items without pagination nor limit

  const items = await LexiconEntry.aggregate(itemsPipeline);
  const [count] = await LexiconEntry.aggregate(countPipeline);

  return { items, count: count?.count || 0 };
};

/**
 * Retrieves a single lexicon entry by its unique identifier.
 * @async
 * @function getEntry
 * @param {string} id Unique identifier of the lexicon entry to retrieve.
 * @returns {Promise<LexiconEntryData|null>} Returns a promise that resolves to the retrieved entry or null if not found.
 */
export async function getEntry(id) {
  return await LexiconEntry.findById(id).populate('author.person');
}

/**
 * Creates a new lexicon entry with the provided properties.
 * @async
 * @function createEntry
 * @param {LexiconEntryData} entry Object containing the entry details.
 * @param {Object} entity The entity to associate the entry with.
 * @returns {Promise<LexiconEntryData>} Returns a promise resolving to the created entry.
 */
export const createEntry = async (entry, entity) => {
  if (!entity) {
    return {
      error: {
        method: 'addCategory',
        code: 'entity_required',
        message: 'Entity is required',
      },
    };
  }

  const entryToSave = {
    ...entry,
    entity: entity.id,
  };

  const data = await LexiconEntry.create(entryToSave);
  return { data };
};

/**
 * Updates a lexicon entry with the provided properties.
 * @async
 * @function editEntry
 * @param {Object} options Object containing the request parameters.
 * @returns {Promise<LexiconEntryData|{error: string|Error}>} Returns a promise resolving to the updated entry or an error object.
 */
export const editEntry = async ({ body = {}, entity = {} }) => {
  try {
    const entry = await LexiconEntry.findById(body._id).lean();
    if (!entry) {
      return { error: 'Entry not found.' };
    }

    // Handle the entry's slug
    if (body.slug && body.slug !== entry.slug) {
      body.slug = await getAvailableSlug({
        slug: body.slug,
        excludeId: body.id,
        entity: entity.id,
      });
    }

    const data = await LexiconEntry.findByIdAndUpdate(body._id, body, {
      runValidators: true,
    });

    return { data };
  } catch (error) {
    return { error };
  }
};

/**
 * @typedef {Object} SlugQueryParams
 * @property {string} slug The requested slug
 * @property {string} [entity] Optional entity associated with the slug
 * @property {string} [language] Optional language code for the slug
 * @property {string} [excludeId] Identifier to exclude from the search
 */
/**
 * Retrieves a unique slug based on the provided parameters.
 * If an entry with the same slug already exists, returns a modified slug
 * to ensure uniqueness.
 *
 * @async
 * @function getAvailableSlug
 * @param {SlugQueryParams} params The parameters for slug retrieval.
 * @returns {Promise<string>} Resolves with an original or uniquely generated slug.
 */
export async function getAvailableSlug({ slug, entity, language, excludeId }) {
  const query = { slug, deleted: false };

  if (entity) query.entity = entity;
  if (language) query.language = language;
  if (excludeId) query._id = { $ne: excludeId };

  const existingEntry = await LexiconEntry.findOne(query);

  return existingEntry ? uniquifySlug(slug) : slug;
}

/**
 * Deletes a lexicon entry by its ID.
 *
 * @async
 * @function deleteEntry
 * @param {Object} options - The options for the delete operation.
 * @param {string} options.id - The ID of the lexicon entry to delete.
 * @param {Object} options.entity - The entity associated with the request
 * @returns {Promise<Object|null>} A promise that resolves to the deleted lexicon entry, or null if no entry was found.
 */
export const deleteEntry = async ({ id, entity }) => {
  const entry = await LexiconEntry.findById(id);
  if (!entry) {
    return { error: 'Entry not found.' };
  }

  try {
    const deletedEntry = await LexiconEntry.findByIdAndUpdate(
      id,
      {
        deleted: true,
      },
      { runValidators: true }
    );

    eventEmitter.emit(lexiconEntryEvents.DELETE_LEXICON_ENTRY, {
      entry: deletedEntry,
      entity,
    });

    return { data: deletedEntry };
  } catch (error) {
    return { error };
  }
};

export const getAuthorsList = async ({
  search = null,
  sort = 'desc',
  entity = {},
}) => {
  const filters = getListFilters({
    search,
    searchFields: autorSearchFilters,
  });
  const matchFilters = {
    entity: new mongoose.Types.ObjectId(entity.id),
    $and: [filters.search, filters.statuses],
  };

  const pipeline = [
    {
      $lookup: {
        from: 'people',
        localField: 'author.person',
        foreignField: '_id',
        as: 'author',
      },
    },
    {
      $unwind: {
        path: '$author',
      },
    },
    { $match: matchFilters },
    {
      $replaceRoot: {
        newRoot: '$author',
      },
    },
    {
      $group: {
        _id: '$_id',
        doc: { $first: '$$ROOT' },
      },
    },
    {
      $replaceRoot: {
        newRoot: '$doc',
      },
    },
  ];

  const authorsPipeline = [...pipeline];
  if (!search) authorsPipeline.push({ $limit: 25 });
  const countPipeline = [...pipeline, { $count: 'count' }];

  let authors = await LexiconEntry.aggregate(authorsPipeline);
  const [count] = await LexiconEntry.aggregate(countPipeline);

  authors = authors.sort((aAuthor, bAuthor) => {
    const nameA = getFullName(aAuthor);
    const nameB = getFullName(bAuthor);

    if (sort === 'asc') return nameB.localeCompare(nameA);

    return nameA.localeCompare(nameB);
  });

  return { authors, count: count?.count || 0 };
};

/**
 * Retrieves a list of authors by matching the provided array of Person document IDs.
 *
 * @async
 * @function getAuthorsListFromPersons
 * @param {Object} params The parameters for the query.
 * @param {string[]} params.ids An array of Person document IDs.
 * @returns {Promise<Array>} A Promise that resolves to an array of Person documents.
 */
export const getAuthorsListFromPersons = async ({ ids }) => {
  const matchFilters = {
    _id: { $in: ids?.map((id) => new mongoose.Types.ObjectId(id)) },
  };

  return await Person.find(matchFilters);
};

/**
 * Toggles the enabled status of a lexicon entry.
 *
 * @async
 * @function toggleEntry
 * @param {Object} options The options for the toggle operation.
 * @param {String} options.id The ID of the lexicon entry to toggle.
 * @param {Object} options.entity The entity associated with the request.
 * @returns {Promise<LexiconEntryData>} A promise that resolves to the updated entry.
 */
export const toggleEntry = async ({ id, entity }) => {
  const entry = await LexiconEntry.findById(id);
  if (!entry) {
    return { error: 'Entry not found.' };
  }

  const enabled = !entry.enabled;

  const data = await LexiconEntry.findByIdAndUpdate(
    id,
    { enabled },
    {
      runValidators: true,
    }
  );

  if (!enabled) {
    eventEmitter.emit(lexiconEntryEvents.DISABLE_LEXICON_ENTRY, {
      entry: data,
      entity,
    });
  }

  return data;
};

/**
 * Restores a deleted lexicon entry.
 *
 * @async
 * @function restoreEntryService
 * @param {String} id The ID of the lexicon entry to restore.
 * @returns {Promise<LexiconEntryData>} A promise that resolves to the updated entry.
 */
export const restoreEntryService = async (id) => {
  const entry = await LexiconEntry.findById(id);
  if (!entry) {
    return { error: 'Entry not found.' };
  }

  return await LexiconEntry.findByIdAndUpdate(
    id,
    { deleted: false },
    { runValidators: true }
  );
};
