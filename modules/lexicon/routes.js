import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import lexiconEntryController from './controllers/lexiconEntryController.js';

const router = express.Router();

router.use(authorizeRequest());

router.use(protect);

router
  .route('/authors')
  .get(
    restrictTo({ module: 'lexiconEntry', permissions: ['read'] }),
    lexiconEntryController.getAuthors
  );

router
  .route('/selected-authors')
  .get(
    restrictTo({ module: 'lexiconEntry', permissions: ['read'] }),
    lexiconEntryController.getAuthorsFromPersons
  );

router
  .route('/')
  .get(
    restrictTo({ module: 'lexiconEntry', permissions: ['read'] }),
    lexiconEntryController.getAllEntryContents
  )
  .post(
    restrictTo({ module: 'lexiconEntry', permissions: ['create'] }),
    logRequest({
      module: 'lexicon',
      action: 'CREATE_LEXICON_ENTRY',
    }),
    lexiconEntryController.createEntryContent
  );

router
  .route('/:id')
  .get(lexiconEntryController.getEntryContent)
  .patch(
    restrictTo({ module: 'lexiconEntry', permissions: ['update'] }),
    logRequest({
      module: 'lexicon',
      action: 'UPDATE_LEXICON_ENTRY',
    }),
    lexiconEntryController.updateEntryContent
  )
  .delete(
    restrictTo({ module: 'lexiconEntry', permissions: ['delete'] }),
    logRequest({
      module: 'lexicon',
      action: 'DELETE_LEXICON_ENTRY',
    }),
    lexiconEntryController.deleteEntryContent
  );

router.route('/:id/toggle').patch(
  restrictTo({ module: 'lexiconEntry', permissions: ['update'] }),
  logRequest({
    module: 'lexicon',
    action: 'UPDATE_LEXICON_ENTRY',
  }),
  lexiconEntryController.toggleEntryStatus
);

router.route('/:id/restore').patch(
  restrictTo({ module: 'lexiconEntry', permissions: ['update'] }),
  logRequest({
    module: 'lexicon',
    action: 'UPDATE_LEXICON_ENTRY',
  }),
  lexiconEntryController.restoreEntry
);

export default router;
