import Joi from 'joi';

export const personsRequestSchema = Joi.object().keys({
  page: Joi.number().integer().min(1),
  pageSize: Joi.number().integer(),
  sortBy: Joi.string().valid('lastName', 'status', 'ids'),
  sortDir: Joi.string().valid('desc', 'asc'),
  roles: Joi.alternatives().try(Joi.string(), Joi.array()),
  status: Joi.alternatives().try(Joi.string(), Joi.array()),
  search: Joi.string().allow('', null),
  includeRoles: Joi.boolean().allow(null),
  ids: Joi.alternatives().try(Joi.string(), Joi.array()),
  ignoreIds: Joi.alternatives().try(Joi.string(), Joi.array()),
  entityId: Joi.string().optional(),
});
