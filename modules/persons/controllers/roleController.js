import entityServices from '#modules/entities/services/entityServices.js';
import roleServices from '../services/roleService.js';

/**
 * Get all roles based on an entity (either one provided, or the entity from the request by default)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} - List of roles with items and count
 */
export const getRoles = async (req, res) => {
  //  Get the entity ID from the request or use the entity ID from the request
  const entityId = req.query.entityId || req.entity.id;

  // Get the entity by ID
  const entity =
    (await entityServices.getEntityById(entityId, {
      select: 'type',
    })) || {};

  // If the entity type is not found, throw an error
  if (!entity?.type) {
    return res.status(404).json({
      error: 'getRoles: Entity not found',
    });
  }

  const roles = roleServices.getRoles({ entityType: entity?.type });

  res.status(200).json({ items: roles || [], count: roles?.length || 0 });
};

/**
 * Get a role by name
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} - Role object
 */
export const getRole = async (req, res) => {
  const role = roleServices.getRole({
    name: req.params.roleName,
  });

  if (!role) {
    return res.status(404).json({
      error: 'Role not found',
    });
  }

  res.status(200).json(role);
};

export default {
  getRoles,
  getRole,
};
