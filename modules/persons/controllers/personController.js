import getEntitiesPermissions from '#modules/entities/helpers/getEntitiesPermissions.js';
import factory from '#utils/handlerFactory.js';

import Person from '../models/Person.js';
import personService from '../services/personService.js';

export const getAllPersons = async (req, res) => {
  const { query, user, entity } = req;
  const {
    entityId,
    ids,
    ignoreIds,
    includeRoles,
    page,
    pageSize,
    roles,
    search,
    sortBy,
    sortDir,
    status,
  } = query;

  // Get the entityIds that the user can access
  let entityIds = entityId
    ? [entityId]
    : Array.from(new Set([user.entity?.toString(), entity._id?.toString()]));

  // Make sure user can access the entityId in the query if not admin
  if (!user.isAdmin && !entityId) {
    const { entitiesIds, withSubentities } = getEntitiesPermissions(user);

    entityIds = entityIds.filter((id) =>
      [...entitiesIds, ...withSubentities].includes(id?.toString())
    );
  }

  const data = await personService.getPersons({
    entityIds,
    ids,
    ignoreIds,
    includeRoles,
    page,
    pageSize,
    roles,
    search,
    sortBy,
    sortDir,
    status,
  });

  res.status(200).json(data);
};

export const getPerson = async (req, res) => {
  const data = await factory.getOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export const createPerson = async (req, res) => {
  const person = await personService.createPerson({
    entityId: req.entity._id,
    personData: req.body,
  });

  res.status(200).json(person);
};

export const updatePerson = async (req, res) => {
  const updatedPerson = personService.updatePerson({
    entityId: req.entity._id,
    personId: req.params.personId,
    personData: req.body,
  });

  res.status(200).json(updatedPerson);
};

export const deletePerson = async (req, res) => {
  const data = await factory.deleteOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export const restorePerson = async (req, res) => {
  const data = await factory.restoreOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export const disablePerson = async (req, res) => {
  const data = await factory.disableOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export const enablePerson = async (req, res) => {
  const data = await factory.enableOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export function mergeReferencedPersons(doc, field) {
  return {
    [field]: doc[field].map((ref) => ({
      ...ref,
      person: {
        ...ref.person,
        title: ref.title,
        avatar: ref.avatar || ref.person?.avatar,
      },
    })),
  };
}

export default {
  getAllPersons,
  getPerson,
  createPerson,
  updatePerson,
  deletePerson,
  restorePerson,
  disablePerson,
  enablePerson,
  mergeReferencedPersons,
};
