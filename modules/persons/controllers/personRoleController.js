import personRoleService from '../services/personRoleService.js';

export const getPersonRoles = async (req, res) => {
  const { personId } = req.params;
  const { entityId, showServices } = req.query;

  const roles = await personRoleService.getAllPersonRoles(
    { personId, entityId },
    { showServices: <PERSON><PERSON><PERSON>(showServices) }
  );
  res.status(200).json(roles);
};

export const getPersonRole = async (req, res) => {
  const personRole = await personRoleService.getPersonRole({
    personId: req.params.personId,
    entityId: req.query.entityId,
    roleId: req.params.roleId,
  });
  res.status(200).json(personRole);
};

export const createPersonRole = async (req, res) => {
  const personRole = await personRoleService.createPersonRole({
    personId: req.params.personId,
    personRoleData: req.body,
  });
  res.status(200).json(personRole);
};

export const updatePersonRole = async (req, res) => {
  const updatedPersonRole = await personRoleService.updatePersonRole({
    roleId: req.params.roleId,
    personRoleData: req.body,
  });
  res.status(200).json(updatedPersonRole);
};

export const enablePersonRole = async (req, res) => {
  const updatedPersonRole = await personRoleService.enablePersonRole({
    roleId: req.params.roleId,
  });
  res.status(200).json(updatedPersonRole);
};

export const disablePersonRole = async (req, res) => {
  const updatedPersonRole = await personRoleService.disablePersonRole({
    roleId: req.params.roleId,
  });
  res.status(200).json(updatedPersonRole);
};

export const deletePersonRole = async (req, res) => {
  const { roleId } = req.params;

  const deletedPersonRole = await personRoleService.deletePersonRole({
    roleId,
  });

  res.status(200).json(deletedPersonRole);
};

export function populateRole(field) {
  return {
    path: field,
    select: 'enabled deleted person title avatar role email phone',
    match: { deleted: false },
    populate: [
      {
        path: 'person',
        match: { deleted: false },
      },
    ],
  };
}

export default {
  getPersonRoles,
  getPersonRole,
  createPersonRole,
  updatePersonRole,
  enablePersonRole,
  disablePersonRole,
  deletePersonRole,
  populateRole,
};
