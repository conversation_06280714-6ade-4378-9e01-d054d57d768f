import isEmpty from 'lodash/isEmpty.js';

export function getPersonName(person, firstName, lastName) {
  return !isEmpty(person)
    ? getFullName(person)
    : firstName || lastName
      ? getFullName({ firstName, lastName })
      : '';
}

function getFullName({
  prefix,
  firstName,
  middleName,
  lastName,
  suffix,
  fullName,
}) {
  return (
    fullName ||
    `${[
      ...(prefix ? [prefix] : []),
      ...(firstName ? [firstName] : []),
      ...(middleName ? [`${middleName.slice(0, 1).toUpperCase()}.`] : []),
      ...(lastName ? [lastName] : []),
    ].join(' ')}${suffix ? `, ${suffix}` : ''}`
  );
}
