import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

/**
 * @typedef {mongoose.Types.ObjectId} MongooseObjectId Mongoose ObjectId type.
 * @typedef {mongoose.Schema.Types.Mixed} MongooseMixedObject Mongoose Mixed object type.
 * @typedef {import('./PersonRole.js').PersonRole} PersonRole The PersonRole object.
 */

/**
 * @typedef {Object} PersonBioRole A PersonBioRole object. It represents a relation between a person record, one of its bios, and a role.
 * @property {MongooseObjectId} person The person id in the PersonBioRole relation.
 * @property {MongooseObjectId} bio The bio id in the PersonBioRole relation.
 * @property {MongooseObjectId} role The role id in the PersonBioRole relation.
 */

// Schema for a Person Bio Role
export const PersonBioRoleSchema = new mongoose.Schema({
  person: {
    type: mongoose.Types.ObjectId,
    ref: 'Person',
  },
  bio: {
    type: mongoose.Types.ObjectId,
    ref: 'PersonBio',
  },
  role: {
    type: mongoose.Types.ObjectId,
    ref: 'PersonRole',
  },
});

/**
 * @typedef {Object} PersonBio A PersonBio object. It represents a person's bio or description.
 * @property {String} bio Short text description of the person's bio. (e.g. 'Director of the Biblical Research Institute')
 * @property {String} [bioLong] Longer description of the person's bio, in RichText format. (e.g. 'Dr. Ángel Manuel Rodríguez is the Director of the Biblical Research Institute, and...')
 * @property {Boolean} [isPrimary] Indicates if this bio is the primary bio of the person.
 * @property {String} [lastName] Override of person's last name. Like in the case of someone who got married and changed their last name. (e.g. "Ellen Harmon" -> "Ellen Gould White")
 * @property {String} [firstName] Override of person's first name. Like in the case of someone that want to use a nickname or and pseudonym. (e.g. "Harold Marshall Sylvester Richards" -> "H.M.S. Richards")
 * @property {String} [prefix] The prefix of the person for this bio (e.g. 'Mr.', 'Mrs.', 'Dr.', 'Prof.')
 * @property {String} [suffix] The suffix of the person for this bio (e.g. 'Jr.', 'Sr.', 'III')
 * @property {Date} [startsAt] Point in time when the data in this bio started to be valid.
 * @property {Date} [endsAt]  Point in time when the data in this bio stopped to be valid.
 */

// Schema for a Person Bio
const PersonBioSchema = SchemaFactory({
  bio: {
    type: String,
    trim: true,
    default: '',
    required: true,
  },
  bioLong: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      type: 'doc',
      content: [],
    },
  },
  isPrimary: {
    type: Boolean,
  },
  prefix: {
    type: String,
    trim: true,
  },
  suffix: {
    type: String,
    trim: true,
  },
  lastName: {
    type: String,
    trim: true,
  },
  firstName: {
    type: String,
    trim: true,
  },
  fullName: {
    type: String,
    trim: true,
  },
  startsAt: {
    type: Date,
  },
  endsAt: {
    type: Date,
  },
});

/**
 * @typedef {object} Person A Person object. It represents a person in the system, like articles authors, shows hosts, entity contact persons, etc. (not an user).
 * @property {String} _id The ID of the person.
 * @property {String} id alias of _id
 * @property {String} firstName The first name of the person (e.g. 'John')
 * @property {String} middleName The middle name of the person (e.g. 'Delano')
 * @property {String} lastName The last name of the person (e.g. 'Doe')
 * @property {String} prefix The prefix of the person (e.g. 'Mr.', 'Mrs.', 'Dr.', 'Prof.')
 * @property {String} suffix The suffix of the person (e.g. 'Jr.', 'Sr.', 'III')
 * @property {String} fullName (optional) Use this in case of a non-personal name, or a couple name (e.g. 'John Delano Doe Jr', 'ANN Staff', or "John and Jane Doe")
 * @property {String} email The email address of the person. (e.g. '<EMAIL>')
 * @property {String} slug The slug of the person. (e.g. 'john-doe')
 * @property {String} phone The landline phone number of the person. (e.g. '+****************')
 * @property {String} mobile The mobile number of the person. (e.g. '+****************')
 * @property {String} fax The fax number of the person. (e.g. '+****************')
 * @property {'male'|'female'|''} gender The gender of the person.
 * @property {RichText} body The person's bio or description in rich text format. (e.g. 'Hello, my name is John Doe...')
 * @property {ImageFile} avatar The avatar imagefile object of the person.
 * @property {String} entity The entity id where the person was created.
 * @property {String[]} roles The roles of the person (e.g. ['admin', 'user']) (// NOTE: this has been deprecated in favor of personRoles)
 * @property {PersonRole[]} personRoles The available person roles of the person.
 * @property {Object} address The address of the person.
 * @property {String} address.street The street address (e.g. '123 Main St')
 * @property {String} address.city The city or locality (e.g. 'San Francisco', 'Toronto', 'Frankfurt', 'Viale')
 * @property {String} address.state The state, province or region (e.g. 'California', 'Ontario', 'Bremen', 'Entre Ríos')
 * @property {String} address.country The country in ISO 3166-1 alpha-2 code (e.g. US, CA, DE, AR)
 * @property {String} address.zip The postal or zip code (e.g. '94105', 'M5V 3A4', '28215', '3109')
 * @property {boolean} enabled Indicates if the person's record is enabled.
 * @property {boolean} deleted Indicates if the person's record is deleted.
 * @property {String} createdAt The date when the person was created.
 * @property {String} updatedAt The date when the person was updated.
 */

const personSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  gender: {
    type: String,
    default: '',
    enum: {
      values: ['', 'male', 'female'],
      message: 'INVALID_ENUM',
    },
  },
  prefix: {
    type: String,
    trim: true,
    default: '',
  },
  firstName: {
    type: String,
    trim: true,
    default: '',
  },
  middleName: {
    type: String,
    trim: true,
    default: '',
  },
  lastName: {
    type: String,
    trim: true,
    default: '',
  },
  suffix: {
    type: String,
    trim: true,
    default: '',
  },
  fullName: {
    type: String,
    trim: true,
    default: '',
  },
  avatar: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  image: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  body: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      type: 'doc',
      content: [],
    },
  },
  address: {
    street: String,
    additionalAddress: String,
    zip: String,
    city: String,
    state: String,
    country: String,
  },
  email: {
    type: String,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
  mobile: {
    type: String,
    trim: true,
  },
  fax: {
    type: String,
    trim: true,
  },
  /**
   * @deprecated: Media library requires this until data is migrated to use PersonRole
   */
  roles: {
    type: [String],
    default: [], // host, guest, director, producer, ...
  },
  bios: {
    type: [PersonBioSchema],
    default: [],
  },
  channel: {
    type: mongoose.Types.ObjectId,
    ref: 'Channel',
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },
  importIDs: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
});

personSchema.index({ slug: 1 });
personSchema.index({ prefix: 1 });
personSchema.index({ firstName: 1 });
personSchema.index({ middleName: 1 });
personSchema.index({ lastName: 1 });
personSchema.index({ suffix: 1 });
personSchema.index({ roles: 1 });
personSchema.index({ channel: 1 });
personSchema.index({ entity: 1 });
personSchema.index({ importIDs: 1 });

personSchema.statics.getAvailableSlug = async function (
  slug,
  channel,
  personId = null
) {
  const query = { slug, channel, deleted: false };

  if (personId) {
    query._id = { $ne: personId };
  }

  const existingPerson = await this.findOne(query);

  return existingPerson ? uniquifySlug(slug) : slug;
};

personSchema.statics.getAvailableSlugEntity = async function (
  slug,
  entity,
  personId = null
) {
  const query = { slug, entity, deleted: false };

  if (personId) {
    query._id = { $ne: personId };
  }

  const existingPerson = await this.findOne(query);

  return existingPerson ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Person', personSchema);
