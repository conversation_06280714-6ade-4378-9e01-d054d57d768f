import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

/**
 * @typedef {object} PersonRole A Person Role object. A person role is a role that a person can have in an entity, including multiple roles in the same entity.
 * @property {String} _id The ID of the person role.
 * @property {String} role name of the person role.
 * @property {String} title The title of the person role.
 * @property {String} description The description of the person role.
 * @property {String} phone The phone number of the person role.
 * @property {String} email The email address of the person role.
 * @property {String} entity The entity id where the person role was created.
 * @property {String} person The person id to whom the PersonRole belongs.
 * @property {boolean} enabled Indicates if the person role's record is enabled.
 * @property {boolean} deleted Indicates if the person role's record is deleted.
 * @property {String} createdAt The date when the person role was created.
 * @property {String} updatedAt The date when the person role was updated.
 */

const personRoleSchema = SchemaFactory({
  person: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Person',
  },
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
  role: {
    type: String,
    required: true,
    trim: true,
    default: 'custom',
  },
  title: {
    type: String,
    trim: true,
  },
  avatar: {
    type: mongoose.SchemaTypes.Mixed,
  },
  description: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      type: 'doc',
      content: [],
    },
  },
  email: {
    type: String,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
});

personRoleSchema.index(
  { person: 1, entity: 1, role: 1, deleted: 1, title: 1 },
  { unique: true, name: 'unique_non_deleted' }
);
personRoleSchema.index({ person: 1 });
personRoleSchema.index({ channel: 1 });
personRoleSchema.index({ entity: 1 });
personRoleSchema.index({ service: 1 });
personRoleSchema.index({ role: 1 });
personRoleSchema.index({ title: 1 });

export default mongoose.model('PersonRole', personRoleSchema);
