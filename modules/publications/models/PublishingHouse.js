import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

/**
 * @typedef {object} PublishingHouse An object representing a publishing house
 * @property {Object} name The name of the publishing house
 * @property {Object} location The location of the publishing house
 * @property {String[]} languages The languages in which the publishing house publishes
 * @property {String} email The email of the publishing house
 * @property {String} website The website of the publishing house
 * @property {String} entity The entity ID of the publishing house
 */
const publishingHouseSchema = SchemaFactory({
  name: {
    type: mongoose.SchemaTypes.Mixed,
    default: { en: '' },
  },
  location: {
    type: mongoose.SchemaTypes.Mixed,
    default: { en: '' },
  },
  locationPicker: {
    type: mongoose.SchemaTypes.Mixed,
  },
  address: {
    type: mongoose.SchemaTypes.String,
    trim: true,
  },
  languages: {
    type: [String],
    default: [],
  },
  emails: {
    type: [String],
    trim: true,
  },
  phoneNumber: {
    type: String,
    trim: true,
  },
  website: {
    type: String,
    required: true,
    trim: true,
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },
});

publishingHouseSchema.index({ 'name.en': 1 });
publishingHouseSchema.index({ 'location.en': 1 });
publishingHouseSchema.index({ email: 1 });
publishingHouseSchema.index({ entity: 1 });

export default mongoose.model('PublishingHouse', publishingHouseSchema);
