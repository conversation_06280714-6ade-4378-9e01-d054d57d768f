import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

/**
 * @typedef {Object} PublicationVolume A publication volume (e.g. "2023", "2024 3rdQ", "Vol VI", etc.)
 * @property {String} title Volume title
 * @property {String} slug An identifier for the volume (URL friendly)
 * @property {'numeric'|'alpha'|'roman'} issueReleaseStyle Issue release style (numeric, alpha, roman)
 * @property {Number} maxIssuesPerVolume Maximum number of issues allowed in this volume
 */
export const PublicationVolumeSchema = new mongoose.Schema({
  title: { type: String, required: true },
  slug: { type: String, required: true },
  issueReleaseStyle: { type: String, default: 'numeric' },
  maxIssuesPerVolume: { type: Number },
});

/**
 * @typedef {import('#utils/richText.js').RichText} RichText
 * @typedef {import('#modules/images/models/Image.js').ImageFile} ImageFile
 * @typedef {import('#modules/documents/models/Document.js').DocumentFile} DocumentFile
 */

/**
 * @typedef {Object} Publication
 * @property {String} title Publication title
 * @property {String} slug An identifier for the publication (URL friendly)
 * @property {String} type PublicationType name (e.g. magazine, book, etc.)
 * @property {String} religion PublicationReligion name (e.g. christianity, islam, etc.)
 * @property {String} language Publication language (e.g. en, es, etc.)
 * @property {mongoose.Types.ObjectId} source Source publication id
 * @property {boolean} featured Indicates if the publication is featured
 * @property {ImageFile} cover Publication cover image
 * @property {RichText} body Publication body
 * @property {RichText} fullContent Publication full content
 * @property {String} publisher Publication publisher
 * @property {String} author Publication author's name (if applicable)
 * @property {String} isbn Publication ISBN (if applicable)
 * @property {Number} pages Publication number of pages
 * @property {Object} downloadCount Publication download count
 * @property {Number} downloadCount.current Current download count (for current translation, if applicable)
 * @property {Number} downloadCount.total Total download count (for the original publication)
 * @property {DocumentFile} designFile Publication design file
 * @property {DocumentFile} epub Publication EPUB file
 * @property {DocumentFile} pdfPrinting Publication PDF for printing
 * @property {DocumentFile} pdfDigital Publication PDF for digital use
 * @property {DocumentFile} mp3 Publication MP3 file
 * @property {String} mp3Url Publication MP3 file URL
 * @property {DocumentFile} legalFile Publication legal file
 * @property {DocumentFile} promotionalMaterials Publication promotional materials
 * @property {DocumentFile} studyGuide Publication study guide
 * @property {DocumentFile} readingPlan Publication reading plan
 * @property {mongoose.Types.ObjectId[]} categories Publication categories (specific of the publications module)
 * @property {mongoose.Types.ObjectId[]} generalCategories System categories
 * @property {mongoose.Types.ObjectId} entity Entity id
 * @property {boolean} shared Indicates if the publication is shared
 * @property {boolean} assetsRequired Publication requires assets
 * @property {PublicationVolume[]} volumes Publication volumes
 * @property {boolean} enabled Indicates if the publication is enabled
 * @property {boolean} deleted Indicates if the publication is deleted
 * @property {Date} createdAt Publication creation date
 * @property {Date} updatedAt Publication last update date
 * @property {Date} deletedAt Publication deletion date
 */
const publicationSchema = SchemaFactory({
  title: {
    type: String,
  },
  slug: {
    type: String,
    trim: true,
  },
  type: {
    type: String,
    trim: true,
  },
  religion: {
    type: String,
    trim: true,
  },
  language: {
    type: String,
    trim: true,
  },
  source: {
    type: mongoose.Types.ObjectId,
    ref: 'Publication',
  },
  featured: {
    type: Boolean,
    default: false,
  },
  cover: {
    type: mongoose.SchemaTypes.Mixed,
  },
  body: {
    type: mongoose.SchemaTypes.Mixed,
  },
  fullContent: {
    type: mongoose.SchemaTypes.Mixed,
  },
  publisher: {
    type: String,
    trim: true,
  },
  author: {
    type: String,
    trim: true,
  },
  isbn: {
    type: String,
    trim: true,
  },
  pages: {
    type: Number,
  },
  downloadCount: {
    current: { type: Number }, // For current translation
    total: { type: Number }, // Saved in the original publication
  },
  designFile: {
    type: mongoose.SchemaTypes.Mixed,
  },
  epub: {
    type: mongoose.SchemaTypes.Mixed,
  },
  pdfPrinting: {
    type: mongoose.SchemaTypes.Mixed,
  },
  pdfDigital: {
    type: mongoose.SchemaTypes.Mixed,
  },
  mp3: {
    type: mongoose.SchemaTypes.Mixed,
  },
  mp3Url: {
    type: String,
    trim: true,
  },
  legalFile: {
    type: mongoose.SchemaTypes.Mixed,
  },
  promotionalMaterials: {
    type: mongoose.SchemaTypes.Mixed,
  },
  studyGuide: {
    type: mongoose.SchemaTypes.Mixed,
  },
  readingPlan: {
    type: mongoose.SchemaTypes.Mixed,
  },
  // Publication categories (specific of the publications module)
  categories: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'PublicationCategory',
    },
  ],
  // System categories
  generalCategories: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'Category',
    },
  ],

  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
  shared: {
    type: Boolean,
    default: false,
  },
  assetsRequired: {
    type: Boolean,
    default: false,
  },

  volumes: {
    type: [PublicationVolumeSchema], // Array of PublicationVolumes
  },
});

publicationSchema.index({ name: 1 });
publicationSchema.index({ entity: 1 });
publicationSchema.index({ language: 1 });
publicationSchema.index({ type: 1 });
publicationSchema.index({ religion: 1 });

export default mongoose.model('Publication', publicationSchema);
