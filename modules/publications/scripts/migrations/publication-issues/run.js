import fs from 'fs';

import { slugify } from '#utils/strings.js';
import { toJsonObjectId } from '#utils/api/mongoose/id.js';
import { toMongoDateObject } from '#utils/dates.js';
import { logError, logInfo } from '#utils/logger.js';

// NOTE: Update these constants with the correct entity and publication IDs before running the script!
const entityId = '65de02451beb6f51dee0d1e4'; // Entity ID for the publication
const publicationId = '68401e4818b3af006f83b22a'; // Publication ID for the issue

// Read the issues input file if it exists
const issuesInputFile = `./data/publication-issues.json`;
let publicationIssues;

if (fs.existsSync(issuesInputFile)) {
  try {
    const issues = fs.readFileSync(issuesInputFile, 'utf8');
    publicationIssues = JSON.parse(issues);
  } catch (error) {
    logError('Failed to read issues input file', error);

    // Exit the process with an error code
    throw error;
  }
}

// Read the article series input file (to look for files)
const articleSeriesInputFile = `./data/article-series.json`;
let articleSeries = {};

if (fs.existsSync(articleSeriesInputFile)) {
  try {
    const seriesData = fs.readFileSync(articleSeriesInputFile, 'utf8');
    articleSeries = JSON.parse(seriesData);
  } catch (error) {
    logError('Failed to read article series input file', error);
    // Exit the process with an error code
    throw error;
  }
}

// Initialize the output arrays
const output = [];

const rootIssue = {
  deleted: false,
  enabled: true,
  specialEdition: false,
  articles: [],
  downloadCount: {},
  categories: [],
  generalCategories: [],
  shared: false,
  __v: 0,
};

// Sort the issues by release number
publicationIssues.sort((a, b) => a.release - b.release);

for (const issue of publicationIssues) {
  const { title, release, releaseDate } = issue;

  const { file } = articleSeries[release] || {};

  if (!file) {
    logError(`No file found for release ${release} in article series data.`);
  }

  output.push({
    ...rootIssue,
    _id: toJsonObjectId(),
    entity: toJsonObjectId(entityId),
    publication: toJsonObjectId(publicationId),
    title,
    slug: slugify(title),
    release,
    releaseDate: toMongoDateObject(releaseDate),
    pdf: file,
    // year,
    createdAt: toMongoDateObject(),
    updatedAt: toMongoDateObject(),
  });
}

// Output the result to a JSON file
const outputFilePath = './output/publication-issues.json';

logInfo('Saving publication issues to', outputFilePath);
fs.writeFileSync(outputFilePath, JSON.stringify(output, null, 2), 'utf8');

logInfo('Publication issues generation has completed successfully.');
