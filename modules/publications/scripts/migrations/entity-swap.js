/**
 * <PERSON><PERSON><PERSON> to migrate publications data from one entity to another
 *
 * There are two entities:
 * 1. General Conference (UID: 648710de30889f2e8a57dae8)
 * 2. GC Publishing Depatment (UID: 66ba0fc9abe146e990108aaa)
 *
 * The script will swap the entity field value with the correct UID from the General Conference to Publishing Depatment in the following collections:
 * 1. publications
 * 2. publicationcategories
 * 3. publicationreligions
 * 4. publicationtypes
 * 5. publicationsconfigs
 * 6. publishinghouses
 *
 */

// Mock some variables to prevent eslint errors
// 1. Mock Mongo db object
const db = {};

// 2. Mock ObjectId
const ObjectId = (id) => id;

// NOTE: The actual script to run in Mongo shell starts here:

const gcUID = ObjectId('648710de30889f2e8a57dae8'); // General Conference UID
const gcPubUID = ObjectId('66ba0fc9abe146e990108aaa'); // GC Publishing Depatment UID

db.publications.updateMany({ entity: gcUID }, { $set: { entity: gcPubUID } });

db.publicationcategories.updateMany(
  { entity: gcUID },
  { $set: { entity: gcPubUID } }
);

db.publicationreligions.updateMany(
  { entity: gcUID },
  { $set: { entity: gcPubUID } }
);

db.publicationtypes.updateMany(
  { entity: gcUID },
  { $set: { entity: gcPubUID } }
);

db.publicationsconfigs.updateMany(
  { entity: gcUID },
  { $set: { entity: gcPubUID } }
);

db.publishinghouses.updateMany(
  { entity: gcUID },
  { $set: { entity: gcPubUID } }
);
