import publicationsCategoriesRouter from './categoryRoutes.js';
import publishingHouseRoutes from './publishingHouseRoutes.js';
import publicationsReligionsRouter from './religionRoutes.js';
import publicationsRouter from './routes.js';
import publicationsTypesRouter from './typesRoutes.js';

export default function publications() {
  return {
    routes: {
      '/publications': publicationsRouter,
      '/publications-categories': publicationsCategoriesRouter,
      '/publications-religions': publicationsReligionsRouter,
      '/publications-types': publicationsTypesRouter,
      '/publishing-houses': publishingHouseRoutes,
    },
  };
}
