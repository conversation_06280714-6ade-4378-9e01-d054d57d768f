import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import listSchema from '#utils/api/list/schema.js';
import { validate } from '#utils/validationMiddleware.js';

import publishingHouseController from './controllers/publishingHouseController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(
    validate(listSchema, 'query'),
    publishingHouseController.getPublishingHouses
  )
  .post(
    restrictTo({
      module: 'publishing-houses',
      permissions: ['create'],
    }),
    logRequest({
      module: 'publications',
      action: 'CREATE_PUBLISHING_HOUSES',
    }),
    publishingHouseController.createPublishingHouse
  );

router
  .route('/:id')
  .get(publishingHouseController.getPublishingHouse)
  .patch(
    restrictTo({
      module: 'publishing-houses',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({
      module: 'publications',
      action: 'UPDATE_PUBLISHING_HOUSES',
    }),
    publishingHouseController.updatePublishingHouse
  )
  .delete(
    restrictTo({
      module: 'publishing-houses',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({
      module: 'publications',
      action: 'DELETE_PUBLISHING_HOUSES',
    }),
    publishingHouseController.deletePublishingHouse
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'publishing-houses',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'publications',
    action: 'DISABLE_PUBLISHING_HOUSES',
  }),
  publishingHouseController.disablePublishingHouse
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'publishing-houses',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'publications',
    action: 'ENABLE_PUBLISHING_HOUSES',
  }),
  publishingHouseController.enablePublishingHouse
);

export default router;
