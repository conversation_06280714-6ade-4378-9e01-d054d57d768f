import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import listSchema from '#utils/api/list/schema.js';
import { validate } from '#utils/validationMiddleware.js';

import religionController from './controllers/religionController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(validate(listSchema, 'query'), religionController.getReligions)
  .post(
    restrictTo({
      module: 'publication-religions',
      permissions: ['create'],
    }),
    logRequest({
      module: 'publications',
      action: 'CREATE_PUBLICATIONS_RELIGION',
    }),
    religionController.createReligion
  );

router
  .route('/:id')
  .get(religionController.getReligion)
  .patch(
    restrictTo({
      module: 'publication-religions',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({
      module: 'publications',
      action: 'UPDATE_PUBLICATIONS_RELIGION',
    }),
    religionController.updateReligion
  )
  .delete(
    restrictTo({
      module: 'publication-religions',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({
      module: 'publications',
      action: 'DELETE_PUBLICATIONS_RELIGION',
    }),
    religionController.deleteReligion
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'publication-religions',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'publications',
    action: 'DISABLE_PUBLICATIONS_RELIGION',
  }),
  religionController.disableReligion
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'publication-religions',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'publications',
    action: 'ENABLE_PUBLICATIONS_RELIGION',
  }),
  religionController.enableReligion
);

export default router;
