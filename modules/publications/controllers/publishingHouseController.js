import factory from '#utils/handlerFactory.js';
import PublishingHouse from '../models/PublishingHouse.js';
import publishingHouseServices from '../services/publishingHouseServices.js';

export const getPublishingHouses = async (req, res) => {
  const { query, entity } = req;
  const { statuses, limit, page, sort, search } = query;

  const data = await publishingHouseServices.getList({
    entity,
    statuses,
    limit,
    fields: [],
    page,
    sort,
    search,
  });

  res.status(200).json(data);
};

export const createPublishingHouse = async (req, res) => {
  const data = await PublishingHouse.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getPublishingHouse = async (req, res) => {
  const data = await factory.getOne(PublishingHouse, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const updatePublishingHouse = async (req, res) => {
  const data = await factory.updateOne(PublishingHouse, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disablePublishingHouse = async (req, res) => {
  const data = await factory.disableOne(PublishingHouse, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enablePublishingHouse = async (req, res) => {
  const data = await factory.enableOne(PublishingHouse, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deletePublishingHouse = async (req, res) => {
  const publication = await factory.getOne(PublishingHouse, req, {
    filterByEntity: true,
  });

  await PublishingHouse.deleteOne({ _id: publication._id });

  res.status(204).json({});
};

export default {
  getPublishingHouses,
  createPublishingHouse,
  getPublishingHouse,
  updatePublishingHouse,
  disablePublishingHouse,
  enablePublishingHouse,
  deletePublishingHouse,
};
