import factory from '#utils/handlerFactory.js';
import PublicationCategory from '../models/PublicationCategory.js';
import categoryTypes from '../data/categoryTypes.js';
import categoryServices from '../services/categoryServices.js';

export const getCategoryTypes = async (req, res) => {
  res.status(200).json({ items: categoryTypes, count: categoryTypes.length });
};

export const getCategories = async (req, res) => {
  const { query, entity } = req;
  const { statuses, limit, ids, page, sort, search, type } = query;

  const data = await categoryServices.getList({
    entity,
    fields: [],
    ids,
    limit,
    page,
    search,
    sort,
    statuses,
    type,
  });

  res.status(200).json(data);
};

export const createCategory = async (req, res) => {
  const data = await PublicationCategory.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getCategory = async (req, res) => {
  const data = await factory.getOne(PublicationCategory, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const updateCategory = async (req, res) => {
  const data = await factory.updateOne(PublicationCategory, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disableCategory = async (req, res) => {
  const data = await factory.disableOne(PublicationCategory, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enableCategory = async (req, res) => {
  const data = await factory.enableOne(PublicationCategory, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteCategory = async (req, res) => {
  const publication = await factory.getOne(PublicationCategory, req, {
    filterByEntity: true,
  });

  await PublicationCategory.deleteOne({ _id: publication._id });

  res.status(204).json({});
};

export default {
  getCategoryTypes,
  getCategories,
  createCategory,
  getCategory,
  updateCategory,
  disableCategory,
  enableCategory,
  deleteCategory,
};
