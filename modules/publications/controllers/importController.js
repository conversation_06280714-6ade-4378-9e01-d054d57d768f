import importServices from '../services/importServices.js';

/**
 * Import publications
 * @param {Object} req
 * @param {Object} res
 * @returns {JSON} JSON response
 * @async
 */
export const importPublications = async (req, res) => {
  const { entity } = req;

  // We get all publication downloads from the current s3 space and read them, later we move them to the new s3 space
  const { data, error } = await importServices.importPublications({
    entity,
  });

  if (error) {
    // If service response has error, return it and stop execution
    res.status(400).json({
      status: 'error',
      message: error,
    });
    return;
  }

  // Otherwise, return data
  res.status(200).json(data);
};

export default {
  importPublications,
};
