import Logger from '#utils/logger.js';

import {
  getPublicationsConfig as getPublicationsConfigService,
  setPublicationsConfig as setPublicationsConfigService,
} from '../services/publicationsConfigServices.js';

export const getPublicationsConfig = async (req, res) => {
  const { entity, user } = req;

  const { config, error } = await getPublicationsConfigService({
    entity,
    user,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(config);
};

export const setPublicationsConfig = async (req, res) => {
  const { body, entity, user } = req;
  const { network, ...config } = body;

  // Update publications config
  const { data, error } = await setPublicationsConfigService({
    entity,
    network,
    user,
    config,
  });

  if (error) {
    Logger.error(error);
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export default {
  getPublicationsConfig,
  setPublicationsConfig,
};
