import Language from '#modules/languages/models/Language.js';
import factory from '#utils/handlerFactory.js';
import Publication from '../models/Publication.js';
import publicationServices from '../services/publicationServices.js';

export const createPublication = async (req, res) => {
  const { body, entity } = req;

  const language = await Language.findOne({
    locale: body.language || 'en',
  });

  if (!language) {
    return res.status(400).json({
      status: 'error',
      message: 'Language not found.',
    });
  }

  const data = await Publication.create({
    ...body,
    language: language.locale,
    entity: entity._id,
  });

  res.status(200).json(data);
};

export const getPublications = async (req, res) => {
  const { query, entity, user } = req;
  const {
    featured,
    limit,
    order,
    page,
    publicationCategories,
    religion,
    search,
    sort,
    statuses,
    type,
    types,
  } = query;

  // Get publications list with count
  const { items, count } = await publicationServices.getList({
    entity,
    featured,
    fields: [],
    language: user?.preferences?.language || 'en',
    limit,
    order,
    page,
    publicationCategories,
    religion,
    search,
    sort,
    statuses,
    type,
    types,
  });

  res.status(200).json({ items, count });
};

export const getPublication = async (req, res) => {
  const data = await publicationServices.getDetails({
    id: req.params.id,
    entity: req.entity,
    translationsSort: req.query.translationsSort,
  });

  res.status(200).json(data);
};

export const updatePublication = async (req, res) => {
  const data = await factory.updateOne(Publication, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const translatePublication = async (req, res) => {
  const { id, locale } = req.params;

  const { data, error } = await publicationServices.translate({
    id,
    locale,
    entity: req.entity,
    data: req.body,
  });

  if (error) {
    // If service response has error, return it and stop execution
    res.status(400).json({
      status: 'error',
      message: error,
    });
    return;
  }

  // Otherwise, return data
  res.status(200).json(data);
};

export const getPublicationIssues = async (req, res) => {
  const { id } = req.params;
  const { volume, sort, order } = req.query;

  const data = await publicationServices.getIssues({
    publicationId: id,
    entity: req.entity,
    volume,
    sort: sort || 'number',
    order: order || 'asc',
  });

  res.status(200).json(data);
};

export const sortPublicationIssues = async (req, res) => {
  const { entity, params, body } = req;
  const { id } = params;
  const { issuesIds } = body;

  const { data, error } = await publicationServices.sortIssues({
    publicationId: id,
    entity,
    issuesIds,
  });

  if (error) {
    res.status(400).json({
      status: 'error',
      message: error,
    });
    return;
  }

  res.status(200).json({ items: data, count: data.length });
};

export const getPublicationIssue = async (req, res) => {
  const { id, issueId } = req.params;

  const { data, error } = await publicationServices.getIssue({
    publicationId: id,
    issueId,
    entity: req.entity,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(data);
};

export const addPublicationIssue = async (req, res) => {
  const { id } = req.params;

  const { data, error } = await publicationServices.addIssue({
    publicationId: id,
    data: req.body,
    entity: req.entity,
  });

  if (error) {
    res.status(400).json({
      status: 'error',
      message: error,
    });
    return;
  }

  res.status(200).json(data);
};

export const updatePublicationIssue = async (req, res) => {
  const { id, issueId } = req.params;

  const { data, error } = await publicationServices.updateIssue({
    publicationId: id,
    issueId,
    data: req.body,
    entity: req.entity,
  });

  if (error) {
    res.status(400).json({
      status: 'error',
      message: error,
    });
    return;
  }

  res.status(200).json(data);
};

export const deletePublicationIssue = async (req, res) => {
  const { id, issueId } = req.params;

  const { data, error } = await publicationServices.deleteIssue({
    publicationId: id,
    issueId,
    entity: req.entity,
  });

  if (error) {
    res.status(400).json({
      status: 'error',
      message: error,
    });
    return;
  }

  res.status(204).json(data);
};

export const disablePublication = async (req, res) => {
  const data = await factory.disableOne(Publication, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enablePublication = async (req, res) => {
  const data = await factory.enableOne(Publication, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deletePublication = async (req, res) => {
  const data = await factory.deleteOne(Publication, req, {
    filterByEntity: true,
  });

  res.status(204).json(data);
};

export default {
  createPublication,
  getPublications,
  getPublication,
  updatePublication,
  translatePublication,
  addPublicationIssue,
  getPublicationIssues,
  sortPublicationIssues,
  getPublicationIssue,
  updatePublicationIssue,
  deletePublicationIssue,
  disablePublication,
  enablePublication,
  deletePublication,
};
