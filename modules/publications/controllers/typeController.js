import factory from '#utils/handlerFactory.js';
import PublicationType from '../models/PublicationType.js';
import publicationTypeServices from '../services/typeServices.js';

export const getPublicationTypes = async (req, res) => {
  const { query, entity } = req;
  const { hasIssues, hasVolumes, limit, page, sort, search } = query;

  // Get the list of publication types from the service
  const { items, count } = await publicationTypeServices.getList({
    entity,
    fields: [],
    hasIssues,
    hasVolumes,
    limit,
    page,
    search,
    sort,
  });

  res.status(200).json({ items, count });
};

export const createPublicationType = async (req, res) => {
  const data = await PublicationType.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getPublicationType = async (req, res) => {
  const { entity, params } = req;
  const { id } = params;

  // Get the publication type data from the service
  const { publicationType, error } = await publicationTypeServices.getItem({
    id,
    entity,
  });

  // If there is an error, return it
  if (error) {
    return res
      .status(404) // TODO: The error defaults to 404 for now, the most common error given the current implementation
      .json({ status: 'error', message: error });
  }

  // Return the publication type data
  res.status(200).json(publicationType);
};

export const updatePublicationType = async (req, res) => {
  const data = await factory.updateOne(PublicationType, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disablePublicationType = async (req, res) => {
  const data = await factory.disableOne(PublicationType, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enablePublicationType = async (req, res) => {
  const data = await factory.enableOne(PublicationType, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deletePublicationType = async (req, res) => {
  const publication = await factory.getOne(PublicationType, req, {
    filterByEntity: true,
  });

  await PublicationType.deleteOne({ _id: publication._id });

  res.status(204).json({});
};

export default {
  getPublicationTypes,
  createPublicationType,
  getPublicationType,
  updatePublicationType,
  disablePublicationType,
  enablePublicationType,
  deletePublicationType,
};
