import factory from '#utils/handlerFactory.js';
import PublicationReligion from '../models/PublicationReligion.js';
import religionServices from '../services/religionServices.js';

export const getReligions = async (req, res) => {
  const { query, entity } = req;
  const { statuses, limit, page, sort, search, exceptions } = query;

  const data = await religionServices.getList({
    entity,
    statuses,
    limit,
    fields: [],
    page,
    sort,
    search,
    exceptions,
  });

  res.status(200).json(data);
};

export const createReligion = async (req, res) => {
  const data = await PublicationReligion.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getReligion = async (req, res) => {
  const { byName } = req.query;

  const data =
    byName === 'true'
      ? await PublicationReligion.findOne({
          name: req.params.id,
          entity: req.entity._id,
          deleted: false,
          enabled: true,
        })
      : await factory.getOne(PublicationReligion, req, {
          filterByEntity: true,
        });

  res.status(200).json(data);
};

export const updateReligion = async (req, res) => {
  const data = await factory.updateOne(PublicationReligion, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disableReligion = async (req, res) => {
  const data = await factory.disableOne(PublicationReligion, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enableReligion = async (req, res) => {
  const data = await factory.enableOne(PublicationReligion, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteReligion = async (req, res) => {
  const publication = await factory.getOne(PublicationReligion, req, {
    filterByEntity: true,
  });

  await PublicationReligion.deleteOne({ _id: publication._id });

  res.status(204).json({});
};

export default {
  getReligions,
  createReligion,
  getReligion,
  updateReligion,
  disableReligion,
  enableReligion,
  deleteReligion,
};
