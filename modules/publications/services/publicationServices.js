import mongoose from 'mongoose';

import { getTranslationDoneField } from '#utils/aggregations.js';
import getCollationFilter from '#utils/api/collation/filter.js';
import getListFilters from '#utils/api/list/filters.js';
import { toObjectId, toObjectIds } from '#utils/api/mongoose/id.js';
import { errors } from '#utils/appError.js';

import Publication from '../models/Publication.js';
import PublicationIssue from '../models/PublicationIssue.js';
import { getPublicationsConfig } from './publicationsConfigServices.js';
import PublicationType from '../models/PublicationType.js';

// Field names and types to check to be considered "completed" for each group (content, assets, legal)
const expectedFieldsMap = {
  content: [
    // Also known as metadata
    { name: 'title', type: 'string' },
    { name: 'body', type: 'object' },
    { name: 'publisher', type: 'string' },
    { name: 'pages', type: 'number' },
    // { name: 'fullContent', type: 'object' },
    // { name: 'author', type: 'string' },
    // { name: 'isbn', type: 'string' },
  ],
  assets: [
    { name: 'cover', type: 'object' },
    { name: 'epub', type: 'object' },
    { name: 'pdfPrinting', type: 'object' },
    { name: 'pdfDigital', type: 'object' },
    { name: 'designFile', type: 'object' },
  ],
  translationAssets: [
    { name: 'cover', type: 'object' },
    { name: 'epub', type: 'object' },
    { name: 'pdfPrinting', type: 'object' },
    { name: 'pdfDigital', type: 'object' },
  ],
  legal: [{ name: 'legalFile', type: 'object' }],
};

const expectedFieldsKeys = Object.keys(expectedFieldsMap).join(', ');

/**
 * Get the translation done field for the aggregation pipeline
 * @param {String} fieldGroup The group of completion to check (content, assets, or legal)
 * @param {Object} fieldsConfig The fields configuration for the group (from Entity's publicationsConfig record)
 * @returns {Object} The aggregation condition
 */
function getDoneField(
  fieldGroup = ''
  //  fieldsConfig = {}
) {
  // Get the fields for the group
  const fields = expectedFieldsMap?.[fieldGroup] || [];

  // TODO: This is causing some bugs as the expected fields do not match the actual config fields
  // fields.filter(
  //   ({ name }) => fieldsConfig?.[name] // Check if the field is enabled in publications config for the current entity
  // );

  if (!fields) {
    throw new Error(
      `Invalid completion type: ${fieldGroup}. Valid type are: ${expectedFieldsKeys}`
    );
  }

  return getTranslationDoneField(fields);
}

/**
 * Get publications list with filters
 * @param {Object} params The params object
 * @param {Object} params.entity Current entity object
 * @param {String[]} params.statuses List of statuses to filter
 * @param {String} params.search Search string
 * @param {Number} [params.limit=25] Number of items to return
 * @param {Number} [params.page=1] Page number to return items from. Default is 1.
 * @param {Array<String>} [params.publicationCategories] Publication categories to filter by
 * @param {String} [params.sort='title'] Field name to sort by. Default is 'title'.
 * @param {Number} params.skip Number of items to skip in the query (used for pagination). Default is 0.
 * @param {String} params.type Type of publication to filter by (e.g. 'book', 'magazine', etc.)
 * @param {Array<String>} [params.types] Types of publications to filter by (e.g. ['book', 'magazine'])
 * @param {Boolean} params.featured Filter to get only featured publications
 * @param {String} params.religion Filter by religion
 * @param {String} [params.language='en'] Filter by language
 *
 * @typedef {object} PublicationsList
 * @property {Object[]} items List of publications
 * @property {Number} count Total number of publications
 *
 * @returns {Promise<PublicationsList>} Object with items array and count number
 */
export async function getPublications({
  entity,
  featured,
  language = 'en',
  limit = 25,
  page = 1,
  publicationCategories,
  religion,
  search = '',
  skip = 0,
  sort = 'title',
  statuses = [],
  type,
  types,
} = {}) {
  const { config: publicationsConfig } = await getPublicationsConfig({
    entity,
  });
  const {
    hasPublicationReligions,
    hasPublicationTypes,
    fields: fieldsConfig = {},
  } = publicationsConfig || {};

  const filters = getListFilters({
    statuses,
    limit,
    page,
    skip,
    sort,
    sortFields: ['title'],
    search,
    searchFields: ['title', 'subtitle', 'description'],
  });

  const matchFilters = {
    source: null,
    entity: entity._id,
    $and: [filters.statuses, filters.search],
  };

  if (hasPublicationTypes && type) {
    matchFilters.type = type;
  }

  if (hasPublicationTypes && types && types.length > 0) {
    matchFilters.type = { $in: types };
  }

  // TODO: check why Joi doen't convert to boolean
  if (featured === 'true') {
    matchFilters.featured = true;
  }

  if (hasPublicationReligions && religion) {
    matchFilters.religion = religion;
  }

  if (publicationCategories?.length) {
    matchFilters.categories = {
      $in: toObjectIds(publicationCategories),
    };
  }

  const collationFilters = getCollationFilter({ language });

  let itemsAggregate = Publication.aggregate().match(matchFilters);

  if (hasPublicationReligions) {
    itemsAggregate = itemsAggregate
      .lookup({
        let: {
          religionName: '$religion', // $religion is the name of the religion in the publication
        },
        from: 'publicationreligions',
        as: 'religion',
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$$religionName', '$name'], // $name is the name of the religion in the religion collection
              },
              entity: entity._id, // Filter by entity
            },
          },
        ],
      })
      .unwind('$religion') // $religion is an array with one item, so we need to unwind it to get the item
      .addFields({
        religionTitle: {
          $cond: {
            if: { $ne: [`$religion.title.${language}`, undefined] }, // Check if the language exists
            then: `$religion.title.${language}`, // If yes, use the language title with user language
            else: '$religion.title.en', // If not, use the english title
          },
        },
      });
  }

  if (hasPublicationTypes) {
    itemsAggregate = itemsAggregate
      .lookup({
        let: {
          publicationType: '$type',
        },
        from: 'publicationtypes',
        as: 'type',
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$$publicationType', '$name'],
              },
              entity: entity._id, // Filter by entity
            },
          },
        ],
      })
      .unwind('$type') // $type is an array with one item, so we need to unwind it to get the item
      .addFields({
        typeTitle: {
          $cond: {
            if: { $ne: [`$type.title.${language}`, undefined] }, // Check if the language exists
            then: `$type.title.${language}`, // If yes, use the language title with user language
            else: '$type.title.en', // If not, use the english title
          },
        },
      });
  }

  const contentDoneField = getDoneField('content', fieldsConfig.content);
  const hasContentDoneField = Object.keys(contentDoneField).length > 0;

  const assetsDoneField = getDoneField('assets', fieldsConfig.assets);
  const hasAssetsDoneField = Object.keys(assetsDoneField).length > 0;

  const translationAssetsDoneField = getDoneField(
    'translationAssets',
    fieldsConfig.translationAssets
  );
  const hasTranslationAssetsDoneField =
    Object.keys(translationAssetsDoneField).length > 0;

  const legalDoneField = getDoneField('legal', fieldsConfig.legal || {});
  const hasLegalDoneField = Object.keys(legalDoneField).length > 0;

  itemsAggregate = itemsAggregate
    .lookup({
      from: 'publications',
      let: {
        publicationId: '$_id',
      },
      as: 'translations',
      pipeline: [
        {
          $match: {
            deleted: false,
            enabled: true,
            $expr: {
              $eq: ['$$publicationId', '$source'],
            },
          },
        },
        {
          $addFields: {
            ...(hasContentDoneField && { contentDone: contentDoneField }),
            ...(hasTranslationAssetsDoneField && {
              assetsDone: translationAssetsDoneField,
            }),
          },
        },
        {
          $project: {
            title: 1,
            publisher: 1,
            contentDone: 1,
            assetsDone: 1,
            assetsRequired: 1,
          },
        },
      ],
    })
    .addFields({
      id: '$_id',
      ...(hasContentDoneField && { contentDone: contentDoneField }),
      ...(hasAssetsDoneField && { assetsDone: assetsDoneField }),
      ...(hasLegalDoneField && { legalDone: legalDoneField }),
      translationsTotal: {
        $size: '$translations',
      },
      assetsRequiredTranslationsTotal: {
        $size: {
          $filter: {
            input: '$translations',
            as: 'translation',
            cond: {
              $eq: ['$$translation.assetsRequired', true],
            },
          },
        },
      },
      assetsTranslationsDone: {
        $size: {
          $filter: {
            input: '$translations',
            as: 'translation',
            cond: {
              $and: [
                { $eq: ['$$translation.assetsRequired', true] }, // Only count when assets are required
                { $eq: ['$$translation.assetsDone', true] }, // Only count when assets are done
              ],
            },
          },
        },
      },
      metaTranslationsDone: {
        $size: {
          $filter: {
            input: '$translations',
            as: 'translation',
            cond: {
              $eq: ['$$translation.contentDone', true],
            },
          },
        },
      },
    })
    .project({
      id: 1,
      cover: 1,
      title: 1,
      religion: 1,
      religionTitle: 1,
      type: 1,
      typeTitle: 1,
      subtitle: 1,
      contentDone: 1,
      assetsDone: 1,
      legalDone: 1,
      translationsTotal: 1,
      assetsRequiredTranslationsTotal: 1,
      assetsTranslationsDone: 1,
      metaTranslationsDone: 1,
      enabled: 1,
      deleted: 1,
    })
    .sort(filters.sort)
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit) // NOTE: limit must be after skip!
    .collation(collationFilters);

  const publicationsItems = await itemsAggregate;

  const countResults = await Publication.aggregate()
    .match(matchFilters)
    .count('count');
  const count = countResults[0]?.count ?? 0;

  return { items: publicationsItems, count };
}

export async function getPublication({ id, entity, translationsSort = '' }) {
  const { config: publicationsConfig } = await getPublicationsConfig({
    entity,
  });
  const { fields: fieldsConfig = {} } = publicationsConfig || {};

  const [sortField, sortDir] = translationsSort
    ? translationsSort.startsWith('-')
      ? [translationsSort.substring(1), -1]
      : [translationsSort, 1]
    : ['languageLocale', 1];

  const publicationsAggregate = Publication.aggregate()
    .match({
      _id: new mongoose.Types.ObjectId(id),
      entity: entity._id,
    })
    .addFields({
      id: '$_id', // Ensures id is passed.
    });

  // Call the aggregation to get the publications that match the query
  const intermediatePublications = await publicationsAggregate;

  // Get the first publication from the results (there should be only one or none)
  const publication = intermediatePublications[0];

  // If the publication is not found, return null
  if (!publication) {
    return { items: null, count: 0 };
  }

  // If the publication is a translation, return the publication
  if (publication?.source) {
    publication.isTranslation = true;
    return publication;
  }

  const contentDoneField = getDoneField('content', fieldsConfig.content);
  const hasContentDoneField = Object.keys(contentDoneField).length > 0;

  const translationAssetsDoneField = getDoneField(
    'translationAssets',
    fieldsConfig.translationAssets
  );
  const hasTranslationAssetsDoneField =
    Object.keys(translationAssetsDoneField).length > 0;

  const assetsDoneField = getDoneField('assets', fieldsConfig.assets);
  const hasAssetsDoneField = Object.keys(assetsDoneField).length > 0;

  // We check if the publication has translations
  publicationsAggregate.lookup({
    from: 'publications',
    let: {
      publicationId: '$_id',
    },
    as: 'translations',
    pipeline: [
      {
        $match: {
          deleted: false,
          enabled: true,
          $expr: {
            $eq: ['$$publicationId', '$source'],
          },
        },
      },
      {
        $addFields: {
          ...(hasContentDoneField && { contentDone: contentDoneField }),
          ...(hasTranslationAssetsDoneField && {
            assetsDone: translationAssetsDoneField,
          }),
        },
      },
      {
        $project: {
          id: '$_id',
          title: 1,
          language: 1,
          contentDone: 1,
          assetsDone: 1,
          assetsRequired: 1,
        },
      },
    ],
  });

  const translationsAggregate = await publicationsAggregate;

  // If the publication has no translations, return the publication
  if (!translationsAggregate[0]?.translations?.length > 0) {
    translationsAggregate[0].isTranslation = false; // TODO: Check if this is needed, copilot recommended and i considered it.
    return translationsAggregate[0];
  }

  // If the publication is not a translation, return the publication with the translations
  publicationsAggregate
    .unwind({
      path: '$translations',
    })
    // we add the name of the language to the translations
    .lookup({
      from: 'languages',
      // Replace with the actual name of your Language collection
      localField: 'translations.language',
      foreignField: 'locale',
      as: 'languageInfo',
    })
    .unwind({
      path: '$languageInfo',
    })
    .addFields({
      'translations.languageName': '$languageInfo.name',
    })
    .sort({
      [sortField]: sortDir,
    })
    // We group the translations back together
    .group({
      _id: '$_id',
      translations: {
        $push: '$translations',
      },
      otherFields: {
        $first: '$$ROOT',
      },
    })
    .replaceRoot({
      $mergeObjects: [
        '$otherFields',
        {
          translations: '$translations',
        },
      ],
    })

    .addFields({
      ...(hasContentDoneField && { contentDone: contentDoneField }),
      ...(hasAssetsDoneField && { assetsDone: assetsDoneField }),
      isTranslation: {
        $cond: {
          if: {
            $ne: [
              {
                $ifNull: ['$source', ''],
              },
              '',
            ],
          },
          then: true,
          else: false,
        },
      },
      translationsTotal: {
        $size: '$translations',
      },
      translationsDone: {
        // Is this still in use after the split of meta and assets columns?
        $size: {
          $filter: {
            input: '$translations',
            as: 'translation',
            cond: {
              $and: [
                { $eq: ['$$translation.contentDone', true] },
                { $eq: ['$$translation.assetsDone', true] },
              ],
            },
          },
        },
      },
    });

  const aggregatedPublications = await publicationsAggregate;

  return aggregatedPublications[0];
}

export async function translatePublication({ id, entity, locale, data }) {
  // Return error when either id or locale params are missing.
  if (!id || !locale) {
    return { error: 'Missing id or locale.' };
  }

  // Get source publication
  const sourcePublication = await Publication.findOne({
    _id: id,
    source: null,
    entity: entity._id,
  });

  // Return error when source publication is not found or already a translation.
  if (!sourcePublication) {
    return { error: 'Source publication not found or already a translation.' };
  }

  // Check if translation already exists
  const existingTranslation = await Publication.findOne({
    source: id,
    locale,
    entity: entity._id,
  });

  // Return error when translation already exists.
  if (existingTranslation) {
    return { error: 'Translation already exists.' };
  }

  try {
    // Create translation
    const publication = await Publication.create({
      ...data,
      source: sourcePublication._id,
      language: locale,
      entity: entity._id,
    });

    return { data: publication };
  } catch (error) {
    console.error(error); // eslint-disable-line no-console
    return { error: 'Error creating publication translation.' };
  }
}

/**
 * Add a publication issue
 * @param {Object} params The params object
 * @param {String} params.publicationId The publication ID
 * @param {Object} params.entity The entity object
 * @param {Object} params.data The publication issue data
 * @returns {Object} The publication issue
 */
export async function addPublicationIssue({ publicationId, entity, data }) {
  if (!publicationId) {
    return { error: 'addPublicationIssue: Missing publicationId.' };
  }

  if (!entity) {
    return { error: 'addPublicationIssue: Missing entity.' };
  }

  const publication = await Publication.findOne({
    _id: publicationId,
    entity: entity?._id,
  });

  if (!publication) {
    return {
      error: `addPublicationIssue: Publication not found with id ${publicationId}`,
    };
  }

  try {
    const newPublicationIssue = await PublicationIssue.create({
      ...data,
      publication: publication._id,
      entity: entity._id,
    });

    return { data: newPublicationIssue };
  } catch (error) {
    return {
      error: `addPublicationIssue: Error creating publication issue. ${error}`,
    };
  }
}

/**
 * Get the issues of a publication
 * @param {Object} params The params object
 * @param {String} params.publicationId The publication ID
 * @param {String} params.volume The volume ID to filter the issues
 * @param {String} params.sort The sort field
 * @param {String} params.order The sort order
 * @param {String[]} params.statuses The statuses to filter the issues
 * @param {Object} params.entity The entity object
 * @returns {Object} The publication issues
 */
export async function getPublicationIssues({
  publicationId,
  order = 'asc',
  volume = null,
  sort = 'release',
  statuses = [],
  entity,
}) {
  if (!publicationId) {
    return { error: 'getPublicationIssues: Missing publicationId.' };
  }

  if (!entity) {
    return { error: 'getPublicationIssues: Missing entity.' };
  }

  const publication = await Publication.findOne({
    _id: publicationId,
    entity: entity?._id,
  });

  if (!publication) {
    return {
      error: `getPublicationIssues: Publication not found with id ${publicationId}`,
    };
  }

  // Set list filters
  const filters = getListFilters({
    statuses, // Filter by system statuses ('active', 'disabled', or 'deleted')
    sort,
    order,
  });

  // Get the publication issues
  const publicationIssues = await PublicationIssue.find({
    publication: publication._id,
    entity: entity._id,
    ...(volume ? { volume: toObjectId(volume) } : {}),
    ...filters.statuses,
  }).sort(filters.sort);

  return { items: publicationIssues, count: publicationIssues.length };
}

/**
 * Sort the issues of a publication
 * @param {Object} params The params object
 * @param {String} params.publicationId The publication ID
 * @param {Object} params.issuesIds An array of publication issues IDs in the new order to be sorted.
 * @param {Object} params.entity The entity object
 * @returns {Object} The publication issues, sorted
 */
export async function sortPublicationIssues({
  publicationId,
  entity,
  issuesIds,
}) {
  if (!publicationId) {
    return { error: 'sortPublicationIssues: Missing publicationId.' };
  }

  if (!entity) {
    return { error: 'sortPublicationIssues: Missing entity.' };
  }

  if (!Array.isArray(issuesIds)) {
    return { error: 'sortPublicationIssues: Missing issuesIds.' };
  }

  const publication = await Publication.findOne({
    _id: publicationId,
    entity: entity?._id,
  });

  if (!publication) {
    return {
      error: `sortPublicationIssues: Publication not found with id ${publicationId}`,
    };
  }

  // find the publication type, to check if supports issues and volumes
  const publicationType = await PublicationType.findOne({
    name: publication.type,
    entity: entity._id,
  });

  const { hasIssues, hasVolumes } = publicationType || {};

  if (!hasIssues) {
    return {
      error: `sortPublicationIssues: Publication type ${publication.type} does not support issues`,
    };
  }

  // If the publication has volumes, get that volume from the first issue
  const firstIssue = await PublicationIssue.findOne({
    publication: publication._id,
    entity: entity._id,
  });

  if (!firstIssue) {
    return {
      error: `sortPublicationIssues: No issues found for publication with id ${publicationId}`,
    };
  }

  const volumeFilter = hasVolumes
    ? publication.volumes.find(
        (volume) => volume._id.toString() === firstIssue.volume.toString()
      )
    : null;

  try {
    const updatedPublicationIssues = await Promise.all(
      issuesIds.map((issueId, index) =>
        PublicationIssue.findOneAndUpdate(
          {
            _id: issueId,
            publication: publication._id,
            entity: entity._id,
            ...(volumeFilter ? { volume: volumeFilter._id } : {}),
          },
          { release: index + 1 },
          { new: true }
        )
      )
    );

    return { data: updatedPublicationIssues };
  } catch {
    return {
      error: 'sortPublicationIssues: Error sorting publication issues.',
    };
  }
}

/**
 * Get a publication issue
 * @param {Object} params The params object
 * @param {String} params.publicationId The publication ID
 * @param {String} params.issueId The issue ID
 * @param {Object} params.entity The entity object
 * @returns {Object} The publication issue
 */
export async function getPublicationIssue({ publicationId, issueId, entity }) {
  if (!publicationId) {
    return {
      error: errors.params('getPublicationIssue: Missing publicationId.'),
    };
  }

  if (!issueId) {
    return { error: errors.params('getPublicationIssue: Missing issueId.') };
  }

  if (!entity) {
    return { error: errors.params('getPublicationIssue: Missing entity.') };
  }

  try {
    const publicationIssue = await PublicationIssue.findOne({
      _id: toObjectId(issueId),
      publication: toObjectId(publicationId),
      entity: entity._id,
    }).populate([
      {
        path: 'publication',
        select: 'title volumes',
      },
    ]);

    if (!publicationIssue) {
      return {
        error: errors.not_found('Publication issue', issueId),
      };
    }

    return { data: publicationIssue };
  } catch {
    return {
      error: errors.params(
        'getPublicationIssue: Error updating publication issue.'
      ),
    };
  }
}

/**
 * Update a publication issue
 * @param {Object} params The params object
 * @param {String} params.publicationId The publication ID
 * @param {String} params.issueId The issue ID
 * @param {Object} params.entity The entity object
 * @param {Object} params.data The publication issue data
 * @returns {Object} The publication issue
 */
export async function updatePublicationIssue({
  publicationId,
  issueId,
  entity,
  data,
}) {
  if (!publicationId) {
    return { error: 'updatePublicationIssue: Missing publicationId.' };
  }

  if (!issueId) {
    return { error: 'updatePublicationIssue: Missing issueId.' };
  }

  if (!entity) {
    return { error: 'updatePublicationIssue: Missing entity.' };
  }

  try {
    const updatedPublicationIssue = await PublicationIssue.findOneAndUpdate(
      {
        _id: issueId,
        publication: publicationId,
        entity: entity._id,
      },
      { ...data },
      { new: true }
    );

    if (!updatedPublicationIssue) {
      return {
        error: `updatePublicationIssue: Publication issue not found with id ${issueId}`,
      };
    }

    return { data: updatedPublicationIssue };
  } catch {
    return {
      error: 'updatePublicationIssue: Error updating publication issue.',
    };
  }
}

/**
 * Delete a publication issue
 * @param {Object} params The params object
 * @param {String} params.publicationId The publication ID
 * @param {String} params.issueId The issue ID
 * @param {Object} params.entity The entity object
 * @returns {Promise<object>} The deleted publication issue
 */
export async function deletePublicationIssue({
  publicationId,
  issueId,
  entity,
}) {
  if (!publicationId) {
    return { error: 'deletePublicationIssue: Missing publicationId.' };
  }

  if (!issueId) {
    return { error: 'deletePublicationIssue: Missing issueId.' };
  }

  if (!entity) {
    return { error: 'deletePublicationIssue: Missing entity.' };
  }

  try {
    const deletedPublicationIssue = await PublicationIssue.findOneAndUpdate(
      {
        _id: issueId,
        publication: publicationId,
        entity: entity._id,
      },
      { deleted: true },
      { new: true }
    ).populate([
      {
        path: 'publication',
        select: 'title volumes',
      },
    ]);

    if (!deletedPublicationIssue) {
      return {
        error: `deletePublicationIssue: Publication issue not found with id ${issueId}`,
      };
    }

    return { data: deletedPublicationIssue };
  } catch {
    return {
      error: 'deletePublicationIssue: Error deleting publication issue.',
    };
  }
}

export default {
  getList: getPublications,
  getDetails: getPublication,
  translate: translatePublication,
  getIssues: getPublicationIssues,
  sortIssues: sortPublicationIssues,
  addIssue: addPublicationIssue,
  getIssue: getPublicationIssue,
  updateIssue: updatePublicationIssue,
  deleteIssue: deletePublicationIssue,
};
