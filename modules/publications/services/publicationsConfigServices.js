import getEntityAncestorAttribute from '#modules/entities/helpers/getEntityAncestorAttribute.js';
import Logger from '#utils/logger.js';

import PublicationsConfig from '../models/PublicationsConfig.js';

// Default publications module configuration (if no config is found)
const defaultPublicationsConfig = {
  hasPublicationTypes: false,
  hasPublicationCategories: false,
  hasPublicationReligions: false,
  hasPublishingHouses: false,

  fields: {
    content: {
      title: true,
      body: true,
      description: true,
      publisher: false,
      pages: false,
      fullContent: false,
    },
    assets: {
      cover: true,
      epub: false,
      pdfPrinting: false,
      pdfDigital: false,
      designFile: false,
    },
    translationAssets: {
      cover: true,
      epub: false,
      pdfPrinting: false,
      pdfDigital: false,
    },
    legal: {
      legalFile: false,
    },
  },
};

/**
 * Get publications module configuration for a given entity or network.
 * @param {Object} params - Function parameters
 * @param {Object} params.entity - Entity object (required if `entityId` is not provided)
 * @param {String} params.entityId - Entity ID (required if `entity` is not provided)
 * @param {String} params.networkId - Network ID (optional)
 * @returns {Promise<object>} - Object with publications module configuration
 */
export async function getPublicationsConfig({ entity, networkId }) {
  // If no entity is provided...
  if (!entity) {
    return { error: 'getPublicationsConfig: No entity provided.' };
  }

  try {
    // Check if a networkId is provided...
    if (networkId) {
      // Check if the network has a publications configuration
      const networkIdPublicationsConfig = await PublicationsConfig.findOne({
        network: networkId,
      });

      // If exists, return it
      if (networkIdPublicationsConfig) {
        return {
          config: networkIdPublicationsConfig,
        };
      }
    }

    // Check if the entity has a publications config...
    const entityPublicationsConfig = await PublicationsConfig.findOne({
      entity: entity._id,
    });

    // If the entity has a publications config, return it
    if (entityPublicationsConfig) {
      return {
        config: entityPublicationsConfig,
      };
    }

    // If the entity doesn't have a publications config, check if the network does
    const closestNetwork =
      entity.network || (await getEntityAncestorAttribute(entity, 'network'));
    const networkPublicationsConfig = await PublicationsConfig.findOne({
      network: closestNetwork?._id,
    });

    // If the network has a publications config, return it
    if (networkPublicationsConfig) {
      return {
        config: networkPublicationsConfig,
      };
    }

    // If neither the entity nor the network have a publications config, return null

    return { config: defaultPublicationsConfig };
  } catch (error) {
    Logger.error(error);
    return { error };
  }
}

/**
 * Set publications module configuration for a given entity.
 * @param {Object} params - Function parameters
 * @param {Object} params.entity - Entity object
 * @param {Object} params.user - User object
 * @param {Object} params.config - Publications module configuration
 * @returns {Object} - Object with updated publication data (in `data` attribute), or error (in `error` attribute)
 */
export async function setPublicationsConfig({
  entity,
  networkId,
  user,
  config = {},
}) {
  // Check if the user has permission to configure the publications module
  const canConfigure =
    user?.hasPermission({ module: 'publications', permission: 'configure' }) ||
    false;

  // If a network is provided and the user is not an admin...
  if (networkId && !user?.isAdmin) {
    // Return an error
    return {
      error:
        'You do not have permission to configure publications module in a network',
    };
  }

  // If the user doesn't have permission...
  if (!canConfigure) {
    // Return an error
    return {
      error: 'You do not have permission to configure publications module.',
    };
  }

  try {
    const { config: publicationsConfig, error } = await getPublicationsConfig({
      entity,
      networkId,
    });

    // If there's an error, return it
    if (error) {
      return { error };
    }

    // If the publicationConfig exists,
    if (publicationsConfig?._id) {
      // Update it with the new params
      const updatedPublicationsConfig =
        await PublicationsConfig.findByIdAndUpdate(
          publicationsConfig._id,
          { ...config },
          { new: true, runValidators: true }
        );

      // Return the updated publication
      return {
        data: updatedPublicationsConfig,
      };
    }

    // Otherwise, create a new publicationConfig
    const newPublicationsConfig = await PublicationsConfig.create({
      entity,
      ...config,
    });

    // Return the new publicationConfig
    return {
      data: newPublicationsConfig,
    };
  } catch (error) {
    // Return the error
    return { error };
  }
}
