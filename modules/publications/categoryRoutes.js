import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import listSchema from '#utils/api/list/schema.js';
import { validate } from '#utils/validationMiddleware.js';

import categoryController from './controllers/categoryController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(validate(listSchema, 'query'), categoryController.getCategories)
  .post(
    restrictTo({
      module: 'publication-categories',
      permissions: ['create'],
    }),
    logRequest({
      module: 'publications',
      action: 'CREATE_PUBLICATIONS_CATEGORY',
    }),
    categoryController.createCategory
  );

router.route('/types').get(categoryController.getCategoryTypes);

router
  .route('/:id')
  .get(categoryController.getCategory)
  .patch(
    restrictTo({
      module: 'publication-categories',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({
      module: 'publications',
      action: 'UPDATE_PUBLICATIONS_CATEGORY',
    }),
    categoryController.updateCategory
  )
  .delete(
    restrictTo({
      module: 'publication-categories',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({
      module: 'publications',
      action: 'DELETE_PUBLICATIONS_CATEGORY',
    }),
    categoryController.deleteCategory
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'publication-categories',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'publications',
    action: 'DISABLE_PUBLICATIONS_CATEGORY',
  }),
  categoryController.disableCategory
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'publication-categories',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'publications',
    action: 'ENABLE_PUBLICATIONS_CATEGORY',
  }),
  categoryController.enableCategory
);

export default router;
