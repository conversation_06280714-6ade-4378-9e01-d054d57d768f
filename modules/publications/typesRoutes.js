import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { validate } from '#utils/validationMiddleware.js';

import typeController from './controllers/typeController.js';
import publicationTypeListSchema from './validations/publicationTypeListSchema.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(
    validate(publicationTypeListSchema, 'query'),
    typeController.getPublicationTypes
  )
  .post(
    restrictTo({
      module: 'publication-types',
      permissions: ['create'],
    }),
    logRequest({
      module: 'publications',
      action: 'CREATE_PUBLICATIONS_TYPE',
    }),
    typeController.createPublicationType
  );

router
  .route('/:id')
  .get(typeController.getPublicationType)
  .patch(
    restrictTo({
      module: 'publication-types',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({
      module: 'publications',
      action: 'UPDATE_PUBLICATIONS_TYPE',
    }),
    typeController.updatePublicationType
  )
  .delete(
    restrictTo({
      module: 'publication-types',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({
      module: 'publications',
      action: 'DELETE_PUBLICATIONS_TYPE',
    }),
    typeController.deletePublicationType
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'publication-types',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'publications',
    action: 'DISABLE_PUBLICATIONS_TYPE',
  }),
  typeController.disablePublicationType
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'publication-types',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'publications',
    action: 'ENABLE_PUBLICATIONS_TYPE',
  }),
  typeController.enablePublicationType
);

export default router;
