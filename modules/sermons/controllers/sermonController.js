import { DateTime } from 'luxon';

import Entity from '#modules/entities/models/Entity.js';
import { isSameDay } from '#utils/dates.js';
import factory from '#utils/handlerFactory.js';

import Sermon from '../models/SermonEvent.js';

/**
 * Get all sermons
 */
export const getAllSermons = async (req, res) => {
  const { query } = req;
  const { churches = [] } = query;

  // Set dates for filtering
  const startsAt = query.startsAt
    ? DateTime.fromISO(query.startsAt).startOf('day')
    : DateTime.now().startOf('day'); // defaults to now

  const endsAt = query.endsAt
    ? DateTime.fromISO(query.endsAt).startOf('day')
    : DateTime.now().plus({ months: 6 }).startOf('day'); // defaults to 6 months from now

  // Set filters
  const filters = {
    deleted: false,
    date: { $gte: startsAt.toJSDate(), $lt: endsAt.toJSDate() },
  };

  // Add church filter if present
  if (Array.isArray(churches) && churches?.length > 0) {
    filters.entity = { $in: churches };
  }

  // Get all filtered sermons
  const sermons = await Sermon.find(filters)
    .populate(['preacher', 'entity'])
    .sort('date');

  // Get total count of filtered sermons
  const count = await Sermon.find(filters).countDocuments();

  const weekday = 6; // Sabbath (0 = Sunday, 1 = Monday, 2 = Tuesday, 3 = Wednesday, 4 = Thursday, 5 = Friday, 6 = Saturday/Sabbath)
  const sermonDates = []; // Array of dates with sermons

  // Select next Sabbath
  let current = startsAt.plus({ days: (weekday - startsAt.weekday + 7) % 7 });

  // Add every Sabbath date while current is less than the end date
  while (current <= endsAt) {
    sermonDates.push(current);
    current = current.plus({ days: 7 });
  }

  // Create an array of objects with date and sermons for each date
  const items = sermonDates.map((date) => ({
    date,
    sermons: churches.reduce((acc, churchId) => {
      const sermon = sermons.find(
        (s) =>
          s.entity.id === churchId &&
          isSameDay(DateTime.fromJSDate(s.date), date)
      );

      if (sermon) {
        acc[churchId] = sermon;
      }
      return acc;
    }, {}),
  }));

  res.status(200).json({ items, count });
};

/**
 * Get all churches, within current network and entity
 */
export const getChurches = async (req, res) => {
  const { entity, query } = req;
  const { ids = [], search = '', limit = 50 } = query;

  // Set basic filters
  const filters = {
    type: 'church', // Only churches
    enabled: true, // ...enabled
    deleted: false, // ...not deleted
    $or: [
      {
        ancestors: { $in: [entity.id] }, // ...within current entity
      },
      { _id: entity.id }, // or current entity itself (if is a church)
    ],
  };

  // Add ids filter if present
  if (ids?.length > 0) {
    filters._id = { $in: ids };
  }
  // Add search filter if present
  else if (search && search !== '') {
    // Escape special characters in the search term
    const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // Create a regular expression pattern for the soft search
    const searchPattern = new RegExp(escapedSearch, 'i'); // 'i' flag for case-insensitive search

    filters.name = { $regex: searchPattern };
  }

  // Get all filered churches
  const items = await Entity.find(filters)
    .sort('name') // sorted by name,
    .limit(limit); // limit results

  // Get total count of filtered churches
  const count = await Entity.find(filters).countDocuments();

  res.status(200).json({ items, count });
};

/**
 * Get a single church
 */
export const getChurch = async (req, res) => {
  const data = await factory.getOne(Entity, req, {
    paramId: 'churchId',
  });

  res.status(200).json(data);
};
/**
 * Get a single sermon
 */
export const getSermon = async (req, res) => {
  const data = await factory.getOne(Sermon, req, {
    paramId: 'sermonId',
    populate: ['preacher', 'entity'],
  });

  res.status(200).json(data);
};

/**
 * Create a new sermon
 */
export const createSermon = async (req, res) => {
  const { entity } = req;

  // Create the sermon
  const sermon = await Sermon.create({
    ...req.body,
    entity: req.body.entity || entity,
  });

  res.status(200).json(sermon);
};

/**
 * Update a sermon
 */
export const updateSermon = async (req, res) => {
  const { preacher, firstName, lastName } = req.body;

  const body = {
    ...req.body,
    // Prevents setting both preacher (person model) AND name simultaneously
    preacher: lastName ? null : preacher, // If lastName is present, preacher is null
    firstName: preacher ? null : firstName, // If preacher is present, firstName is null
    lastName: preacher ? null : lastName, // If preacher is present, lastName is null
  };

  const data = await factory.updateOne(
    Sermon,
    { ...req, body },
    { paramId: 'sermonId' }
  );

  res.status(200).json(data);
};

/**
 * Delete a sermon
 */
export const deleteSermon = async (req, res) => {
  const data = await factory.deleteOne(Sermon, req, {
    paramId: 'sermonId',
  });

  res.status(200).json(data);
};

/**
 * Restore a sermon
 */
export const restoreSermon = async (req, res) => {
  const data = await factory.restoreOne(Sermon, req, {
    paramId: 'sermonId',
  });

  res.status(200).json(data);
};

/**
 * Disable a sermon
 */
export const disableSermon = async (req, res) => {
  const data = await factory.disableOne(Sermon, req, {
    paramId: 'sermonId',
  });

  res.status(200).json(data);
};

/**
 * Enable a sermon
 */
export const enableSermon = async (req, res) => {
  const data = await factory.enableOne(Sermon, req, {
    paramId: 'sermonId',
  });

  res.status(200).json(data);
};

export default {
  getAllSermons,
  getChurches,
  getChurch,
  getSermon,
  createSermon,
  updateSermon,
  deleteSermon,
  restoreSermon,
  disableSermon,
  enableSermon,
};
