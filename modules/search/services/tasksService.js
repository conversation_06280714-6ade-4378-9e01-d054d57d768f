import { errors, generateError } from '#utils/appError.js';
import { delay } from '#utils/helpers.js';
import Logger from '#utils/logger.js';
import { client } from '../utils/client.js';

async function getTaskById(taskId) {
  if (!taskId) {
    return { error: errors.params(['taskId']) };
  }

  try {
    const task = await client.getTask(taskId);

    return { task };
  } catch (error) {
    Logger.error('Error getting task by id', error);

    return {
      error: generateError(error.message, error.code, error.httpStatus),
    };
  }
}

async function waitForTask({ taskId }) {
  if (!taskId) {
    return { error: errors.params(['taskId']) };
  }

  try {
    let taskStatus = null;

    while (taskStatus !== 'succeeded' && taskStatus !== 'failed') {
      const task = await client.getTask(taskId);

      taskStatus = task.status;

      if (taskStatus === 'succeeded') {
        return { task };
      }

      if (taskStatus === 'failed') {
        return { error: errors.task_error(task.error) };
      }

      if (taskStatus === 'enqueued' || taskStatus === 'processing') {
        await delay(1);
      } else {
        Logger.error(`Unknown task status: ${taskStatus}`);
        return {
          error: errors.task_error(`Unknown task status: ${taskStatus}`),
        };
      }
    }
  } catch (error) {
    Logger.error('Error waiting for task', error);

    return {
      error: generateError(error.message, error.code, error.httpStatus),
    };
  }
}

async function getLatestTaskForIndex({ indexUId }) {
  if (!indexUId) {
    return { error: errors.params(['indexUId']) };
  }

  try {
    const tasks = await client.getTasks({
      indexUids: [indexUId],
      limit: 1,
    });

    if (!tasks) {
      return { error: errors.not_found('Tasks') };
    }

    return { task: tasks?.results?.[0] };
  } catch (error) {
    Logger.error('Error getting latest task for index', error);

    return {
      error: generateError(error.message, error.code, error.httpStatus),
    };
  }
}

export default { waitForTask, getLatestTaskForIndex, getTaskById };
