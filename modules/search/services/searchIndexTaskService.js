import { errors } from '#utils/appError.js';
import { ensureIdField } from '#utils/api/model/fields.js';
import { toObjectId } from '#utils/api/mongoose/id.js';
import getPaginationFilter from '#utils/api/pagination/filters.js';
import Logger from '#utils/logger.js';

import SearchIndexTask from '../models/SearchIndexTask.js';

/**
 * Create a search index task
 * @param {Object} options
 * @param {String} options.searchIndexId - The search index ID
 * @returns {Promise<Object>} - The search index task
 */
async function createSearchIndexTask({ searchIndexId }) {
  if (!searchIndexId) {
    return {
      error: errors.params(['searchIndexId']),
    };
  }

  const searchIndexTask = await SearchIndexTask.create({
    searchIndex: searchIndexId,
  });

  return { searchIndexTask };
}

/**
 * Get search index task by ID
 * @param {String} searchIndexTaskId - The search index task ID
 * @param {Object} options
 * @param {Boolean} [options.lean=true] - Whether to return a lean object
 * @returns {Promise<Object>} - The search index task
 */
async function getSearchIndexTaskById(searchIndexTaskId, { lean = true } = {}) {
  if (!searchIndexTaskId) {
    return {
      error: errors.params(['searchIndexTaskId']),
    };
  }

  const searchIndexTaskQuery = SearchIndexTask.findById(
    toObjectId(searchIndexTaskId)
  ).populate('searchIndex');

  if (lean) {
    searchIndexTaskQuery.lean();
  }

  const searchIndexTask = await searchIndexTaskQuery;

  if (!searchIndexTask) {
    return {
      error: errors.not_found('SearchIndexTask'),
    };
  }

  return { searchIndexTask: ensureIdField(searchIndexTask) };
}

/**
 * Get the latest search index task
 * @param {Object} options
 * @param {String} options.searchIndexId - The search index ID
 * @returns {Promise<Object>} - The search index task
 */
async function getLatestSearchIndexTask({ searchIndexId }) {
  if (!searchIndexId) {
    return {
      error: errors.params(['searchIndexId']),
    };
  }

  const searchIndexTask = await SearchIndexTask.findOne({
    searchIndex: toObjectId(searchIndexId),
  }).sort({ createdAt: -1 });

  return { searchIndexTask };
}

/**
 * Update a search index task
 * @param {String} searchIndexTaskId - The search index task ID
 * @param {Object} update - The update object
 * @returns {Promise<Object>} - The search index task
 */
async function updateSearchIndexTask(searchIndexTaskId, update) {
  if (!searchIndexTaskId) {
    return {
      error: errors.params(['searchIndexTaskId']),
    };
  }

  const searchIndexTask = await SearchIndexTask.findByIdAndUpdate(
    toObjectId(searchIndexTaskId),
    update,
    { new: true }
  );

  if (!searchIndexTask) {
    return {
      error: errors.not_found('SearchIndexTask'),
    };
  }

  return { searchIndexTask };
}

/**
 * Stop all search index tasks. This is used when the server is shutting down
 * @returns {Promise<void>}
 */
async function stopAllSearchIndexTasks() {
  Logger.info('🗙 Cancelling all pending search indexing tasks...');

  await SearchIndexTask.updateMany(
    { $or: [{ status: 'pending' }] },
    { $set: { status: 'cancelled' } }
  );

  Logger.info('All pending search indexing tasks stopped.');
}

/**
 * Get all pending tasks for a search index
 * @param {Object} options
 * @param {Number} [options.limit=3] - The maximum number of tasks to return
 * @param {Number} [options.page] - The page number *
 * @param {String} options.searchIndexId - The search index ID
 * @param {Number} [options.skip=0] - The number of tasks to skip
 * @param {pending|processing|completed|cancelled|failed} [options.status] - The status of the search index tasks
 * @returns {Promise<Object>} - The search index tasks
 */
async function getSearchIndexTasks({
  limit = 3,
  page,
  searchIndexId,
  skip = 0,
  status,
}) {
  if (!searchIndexId) {
    return {
      error: errors.params(['searchIndexId']),
    };
  }

  const paginationFilter = getPaginationFilter({
    limit,
    page,
    skip,
  });

  const searchIndexTasks = await SearchIndexTask.find({
    searchIndex: toObjectId(searchIndexId),
    ...(status ? { status } : {}),
  })
    .sort({ startedAt: -1 })
    .skip(paginationFilter.skip)
    .limit(paginationFilter.limit);

  return { searchIndexTasks };
}

export default {
  createSearchIndexTask,
  getSearchIndexTaskById,
  getLatestSearchIndexTask,
  updateSearchIndexTask,
  stopAllSearchIndexTasks,
  getSearchIndexTasks,
};
