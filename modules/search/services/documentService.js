import { errors } from '#utils/appError.js';
import Logger from '#utils/logger.js';
import QueuedDocument from '../models/QueuedDocument.js';
import { client } from '../utils/client.js';

/**
 * Creates or updates a document in the search index
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 * @param {String} options.documentId - The document ID
 *
 * @returns {Object} - The result of the operation
 */
async function getDocument({ indexUId, documentId }) {
  try {
    const result = await client.index(indexUId).getDocument(documentId);
    return { result };
  } catch (error) {
    Logger.error(
      `Error fetching document "${documentId}" in index "${indexUId}"`,
      error
    );

    return { error };
  }
}

/**
 * Creates or updates a document in the search index
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 * @param {Array} options.documents - The documents to create or update
 *
 * @returns {Object} - The result of the operation
 */
async function createOrUpdateDocument({ indexUId, documents }) {
  try {
    const result = await client.index(indexUId).addDocuments(documents);
    return { result };
  } catch (error) {
    Logger.error(
      `Error creating or updating document in index "${indexUId}"`,
      error
    );

    return { error };
  }
}

/**
 * Deletes a document from the search index
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 * @param {String} options.documentId - The document ID
 *
 * @returns {Object} - The result of the operation
 */
async function deleteDocumentById({ indexUId, documentId }) {
  try {
    const result = await client.index(indexUId).deleteDocument(documentId);
    return { result };
  } catch (error) {
    Logger.error('Error deleting search document', error);

    return { error };
  }
}

/**
 * Deletes multiple documents from the search index
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 * @param {Array} options.documentIds - The document IDs
 *
 * @returns {Object} - The result of the operation
 */
async function deleteDocumentsByIds({ indexUId, documentIds }) {
  try {
    const result = await client.index(indexUId).deleteDocuments(documentIds);
    return { result };
  } catch (error) {
    Logger.error('Error deleting search documents', error);

    return { error };
  }
}

/**
 * Deletes documents from the search index by filter
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 * @param {Object} options.filter - The filter to apply
 *
 * @returns {Object} - The result of the operation
 */
async function deleteDocumentsByFilter({ indexUId, filter }) {
  if (!filter || !indexUId) {
    return { error: errors.params(['indexUId', 'filter']) };
  }

  try {
    const result = await client.index(indexUId).deleteDocuments({ filter });
    return { result };
  } catch (error) {
    Logger.error('Error deleting search documents for site', error);

    return { error };
  }
}

/**
 * Deletes all documents from the search index
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 *
 * @returns {Object} - The result of the operation
 */
async function deleteAllDocuments({ indexUId }) {
  try {
    const result = await client.index(indexUId).deleteAllDocuments();
    return { result };
  } catch (error) {
    Logger.error('Error deleting search documents', error);

    return { error };
  }
}

/**
 * Queues a document for indexing
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 * @param {String} options.documentId - The document ID
 * @param {Date} options.actionDate - The date of the action
 * @param {String} options.action - The action to perform
 * @param {Object} options.context - The context of the action
 *
 * @returns {Object} - The queued document
 * @returns {Error} error - The error that occurred
 */
async function queueDocument({
  indexUId,
  documentId,
  actionDate,
  action,
  context,
}) {
  const filter = {
    index: indexUId,
    action,
    documentId,
  };

  // Only set the action date if the action is to update. Otherwise, the action date should override the prvious action date
  if (action === 'update') {
    filter.actionDate = actionDate;
  }

  const doc = await QueuedDocument.updateOne(
    filter,
    {
      index: indexUId,
      documentId,
      actionDate,
      action,
      context,
    },
    { upsert: true }
  );

  if (!doc) {
    return { error: errors.not_found('QueuedDocument', documentId) };
  }

  return { doc };
}

/**
 * Deletes a queued document
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 * @param {String} options.documentId - The document ID
 *
 * @returns {Object} - The deleted document
 * @returns {Error} error - The error that occurred
 */
async function deleteQueuedDocument({ indexUId, documentId }) {
  const doc = await QueuedDocument.deleteOne({
    index: indexUId,
    documentId,
  });

  return doc;
}

export default {
  getDocument,
  createOrUpdateDocument,
  deleteDocumentById,
  deleteDocumentsByIds,
  deleteDocumentsByFilter,
  deleteAllDocuments,
  queueDocument,
  deleteQueuedDocument,
};
