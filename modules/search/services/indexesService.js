import app from '#app';
import { errors, generateError } from '#utils/appError.js';
import Logger from '#utils/logger.js';

import { client } from '../utils/client.js';
import tasksService from './tasksService.js';

/**
 * Handle MeiliSearch error
 * @param {Error} error - The error
 * @returns {Object} - The error data
 */
function handleMeiliSearchError(error) {
  if (error.name === 'MeiliSearchApiError') {
    return {
      error: generateError(
        error.message,
        error.cause?.code,
        error.response.status
      ),
    };
  }

  return {
    error: errors.internal_error(),
  };
}

/**
 * @typedef {Object} TaskResponse
 * @property {Number} taskUid - The unique identifier of the task.
 * @property {String} indexUid - The unique identifier of the index.
 * @property {enqueued|processing|succeeded|failed|canceled} status - The status of the task.
 * @property {indexCreation|indexUpdate|indexDeletion|indexSwap|documentAdditionOrUpdate|documentDeletion|settingsUpdate|dumpCreation|taskCancelation|taskDeletion|snapshotCreation} type - The type of the task.
 * @property {String} enqueuedAt - The timestamp when the task was enqueued.
 */

/**
 * @typedef {Object} TaskOrError
 * @property {TaskResponse} [task] - The task data
 * @property {Error} [error] - The error data
 */

async function getAllStats() {
  try {
    const stats = await client.getStats();
    return { stats };
  } catch (error) {
    Logger.error('Error getting all stats', error);
    return handleMeiliSearchError(error);
  }
}

/**
 * Get index by id
 *
 * @param {Object} options
 * @param {String} options.searchIndex - SearchIndex
 * @param {Boolean} options.settings - Include settings
 * @param {Boolean} options.stats - Include stats
 *
 * @returns {Object} Index data
 */
async function getIndex({ searchIndex, settings = false, stats = false }) {
  try {
    const { _id: indexUId, type } = searchIndex;

    const indexData = await client.index(indexUId).getRawInfo();

    const indexConfig = app.get('searchIndexes')[type] || {};
    const configSettings = indexConfig.settings ? indexConfig.settings() : {};

    return {
      index: {
        ...indexData,
        configSettings,
        ...(settings
          ? { settings: await client.index(indexUId).getSettings() }
          : {}),
        ...(stats ? { stats: await client.index(indexUId).getStats() } : {}),
      },
    };
  } catch (error) {
    Logger.error('Error getting index', error);
    return handleMeiliSearchError(error);
  }
}

/**
 * Get index settings
 * @param {String} indexId - Index id
 * @returns {Promise<Object>} - The index settings
 */
async function getIndexSettings(indexId) {
  try {
    const settings = await client.index(indexId).getSettings();
    return { settings };
  } catch (error) {
    Logger.error('Error getting index settings', error);
    return handleMeiliSearchError(error);
  }
}

/**
 * Get index settings
 * @param {String} indexId - Index id
 * @returns {Promise<Object>} - The index settings
 */
async function getIndexStats(indexId) {
  try {
    const stats = await client.index(indexId).getStats();
    return { stats };
  } catch (error) {
    Logger.error('Error getting index settings', error);
    return handleMeiliSearchError(error);
  }
}

/**
 * Create a new index
 * @param {Object} searchIndex - The search index data *
 * @returns {Promise<TaskOrError>} - Task data
 */
async function createIndex(searchIndex) {
  if (!searchIndex) {
    return { error: errors.params() };
  }

  try {
    const { _id, language, type } = searchIndex;
    const indexConfig = app.get('searchIndexes')[type];

    if (!indexConfig) {
      return { error: errors.not_found('Index type') };
    }

    const { primaryKey } = indexConfig;

    const indexUId = _id.toString();

    const { taskUid, status } = await client.createIndex(indexUId, {
      primaryKey: primaryKey || 'id',
    });

    if (status === 'failed') {
      return { error: errors.task_error('Failed to create index') };
    }

    // Wait for the create task to complete
    const { error: creatIndexTaskError } = await tasksService.waitForTask({
      taskId: taskUid,
    });

    if (creatIndexTaskError) {
      return { error: creatIndexTaskError };
    }

    // Update the created index settings
    const { task: settingsTask, error: settingsError } =
      await updateIndexSettings({
        indexUId,
        language,
        type,
      });

    if (settingsError) {
      return { error: settingsError };
    }

    return { task: settingsTask };
  } catch (error) {
    Logger.error('Error creating index', error);
    return handleMeiliSearchError(error);
  }
}

/**
 * Update index settings
 *
 * @param {Object} options
 * @param {String} options.indexId - Index id
 * @param {Object} options.index - Index settings
 *
 * @returns {Object} Task data
 */
async function updateIndex({ indexId, index }) {
  try {
    const task = await client.updateIndex(indexId, index);

    return { task };
  } catch (error) {
    Logger.error('Error updating index', error);
    return handleMeiliSearchError(error);
  }
}

/**
 * Update index settings based on the static index configuration
 *
 * @param {Object} options
 * @param {String} options.language - Index language
 * @param {String} options.type - Index type
 * @param {String} options.indexUId - Index UId
 * @returns {Promise<TaskOrError>} Task data
 */
async function updateIndexSettings({ indexUId, language, type }) {
  if (!indexUId || !language || !type) {
    return { error: errors.params(['indexUId', 'language', 'type']) };
  }

  const indexConfig = app.get('searchIndexes')[type] || {};

  if (!indexConfig || !indexConfig?.settings) {
    return { error: errors.not_found('Index type') };
  }

  const settings = indexConfig.settings({ language });

  // Settings update does not allow changing the primary key
  delete settings.primaryKey;
  // Ranking rules are set separately with updateIndexRankingRules
  delete settings.rankingRules;

  try {
    const task = await client.index(indexUId).updateSettings(settings);

    return { task };
  } catch (error) {
    Logger.error('Error updating index settings', error);
    return handleMeiliSearchError(error);
  }
}

async function updateIndexRankingRules({ indexUId, rankingRules }) {
  try {
    const task = await client.index(indexUId).updateRankingRules(rankingRules);

    return { task };
  } catch (error) {
    Logger.error('Error updating index ranking rules', error);
    return handleMeiliSearchError(error);
  }
}

async function resetIndexRankingRules({ indexUId }) {
  try {
    const task = await client.index(indexUId).resetRankingRules();

    return { task };
  } catch (error) {
    Logger.error('Error resetting index ranking rules', error);
    return handleMeiliSearchError(error);
  }
}

/**
 * Swap indexes
 *
 * @param {Object} options
 * @param {Array} options.indexes - Indexes to swap
 *
 * @returns {Object} Task data
 */
async function swapIndexes({ indexes = [] }) {
  try {
    const task = await client.swapIndexes([{ indexes }]);

    return { task };
  } catch (error) {
    Logger.error('Error updating index', error);
    return handleMeiliSearchError(error);
  }
}

/**
 * Delete index by id
 *
 * @param {String} uid - Index id
 *
 * @returns {Object} Task data
 */
async function deleteIndex(uid) {
  try {
    const task = await client.deleteIndex(uid);

    return { task };
  } catch (error) {
    Logger.error('Error deleting index', error);
    return handleMeiliSearchError(error);
  }
}

export default {
  getAllStats,
  getIndex,
  getIndexSettings,
  getIndexStats,
  createIndex,
  updateIndex,
  updateIndexSettings,
  swapIndexes,
  deleteIndex,
  updateIndexRankingRules,
  resetIndexRankingRules,
};
