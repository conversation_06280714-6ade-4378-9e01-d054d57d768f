import getPaginationFilters from '#utils/api/pagination/filters.js';
import Logger from '#utils/logger.js';

import { client } from '../utils/client.js';

/**
 * Searches for documents in the search index
 *
 * @param {Object} options
 * @param {String} options.indexUId - The index UID
 * @param {String} options.search - The search query
 * @param {Number} options.page - The page number
 * @param {Number} options.limit - The number of results per page
 *
 * @returns {Object} - The result of the operation
 */
async function searchDocuments({ indexUId, search, page, limit: limitParam }) {
  try {
    const { limit, skip } = getPaginationFilters({ limit: limitParam, page });
    const result = await client
      .index(indexUId)
      .search(search, { offset: skip, limit });
    return { result };
  } catch (error) {
    Logger.error(`Error searching documents in index "${indexUId}"`, error);
    return { error };
  }
}

export default {
  searchDocuments,
};
