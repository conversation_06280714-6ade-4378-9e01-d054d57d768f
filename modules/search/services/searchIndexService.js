import { ensureIdField } from '#utils/api/model/fields.js';
import { toObjectId } from '#utils/api/mongoose/id.js';
import getPaginationFilters from '#utils/api/pagination/filters.js';
import getSortFilter from '#utils/api/sort/filters.js';
import { errors } from '#utils/appError.js';
import { pipe } from '#utils/function.js';
import SearchIndex from '../models/SearchIndex.js';
import { appendStatsToSearchIndex } from './helpers/appendStatsToSearchIndex.js';
import indexesService from './indexesService.js';
import searchIndexTaskService from './searchIndexTaskService.js';

/**
 * @typedef {Object} SearchIndex
 * @property {String} _id - The unique identifier of the search index.
 * @property {String} entity - The entity the index belongs to.
 * @property {String} language - The language of the index.
 * @property {String} name - The name of the index.
 * @property {String} [site] - The site the index belongs to (if any).
 * @property {pages} type - The type of index. E.g. 'pages'. - Add more types here when needed
 * @property {Boolean} enabled - Whether the index is enabled.
 * @property {Boolean} deleted - Whether the index is deleted.
 * @property {Date} lastIndexedAt - The last time the index was updated.
 * @property {Object} [settings] - The index settings. These can be used to override the default settings. Currently not supported.
 */

/**
 * Get search indexes
 *
 * @param {Object} options
 * @param {String} options.entityId - The entity ID
 * @param {Boolean} [options.includeStats] - Include stats in the response
 * @param {Boolean} [options.includeTasks] - Include tasks in the response
 * @param {Number} [options.limit] - The limit of search indexes
 * @param {String} [options.order] - The order of the search indexes
 * @param {String} [options.language] - Filter by language
 * @param {Number} [options.page] - The page number
 * @param {String} [options.siteId] - The site ID to filter by
 * @param {Number} [options.skip] - The number of search indexes to skip
 * @param {String} [options.sort] - The sort order field
 * @param {String} [options.type] - Filter by type
 *
 * @returns {Promise<Array<SearchIndex>>} - The search indexes
 */
async function getSearchIndexes({
  entityId,
  includeStats = false,
  includeTasks = false,
  language,
  limit = 10,
  order = 'asc',
  page = 1,
  siteId,
  skip = 0,
  sort = 'name',
  type,
}) {
  if (!entityId) {
    return { error: errors.params(['entityId']) };
  }

  const pagination = getPaginationFilters({ limit, page, skip });
  const sortOptions = getSortFilter({ order, sort, defaultFields: ['name'] });

  const filter = { entity: toObjectId(entityId), deleted: false };

  if (language) {
    filter.language = language;
  }

  if (siteId) {
    filter.site = toObjectId(siteId);
  }

  if (type) {
    filter.type = type;
  }

  let searchIndexes = await SearchIndex.find(filter)
    .sort(sortOptions.sort)
    .skip(pagination.skip)
    .limit(pagination.limit)
    .lean();

  const count = await SearchIndex.countDocuments(filter);

  let stats;

  if (includeStats) {
    stats = await indexesService.getAllStats();
  }

  if (includeTasks) {
    searchIndexes = await Promise.all(
      searchIndexes.map(async (searchIndex) => {
        const { searchIndexTask, error } =
          await searchIndexTaskService.getLatestSearchIndexTask({
            searchIndexId: searchIndex._id,
          });

        if (error) {
          return {
            ...searchIndex,
            task: { status: 'failed' },
          };
        }

        if (!searchIndexTask) {
          return searchIndex;
        }

        return {
          ...searchIndex,
          searchIndexTask,
        };
      })
    );
  }

  return {
    items: searchIndexes.map(
      pipe(ensureIdField, appendStatsToSearchIndex(stats))
    ),
    count,
  };
}

/**
 * Get search index by ID
 * @param {String} searchIndexId - The search index ID
 * @returns {Promise<SearchIndex>} - The search index
 */
async function getSearchIndexById(searchIndexId) {
  if (!searchIndexId) {
    return { error: errors.params(['searchIndexId']) };
  }

  const searchIndex = await SearchIndex.findById(searchIndexId);

  if (!searchIndex) {
    return { error: errors.not_found('SearchIndex') };
  }

  return { searchIndex };
}

/**
 * Get search index
 *
 * @param {Object} options
 * @param {String} options.searchIndexId - The search index ID
 * @param {Boolean} [options.includeSettings] - Include settings in the response
 * @param {Boolean} [options.includeStats] - Include stats in the response
 *
 * @returns {Promise<SearchIndex>} - The search index
 */
async function getSearchIndex({
  searchIndexId,
  settings = false,
  stats = false,
}) {
  if (!searchIndexId) {
    return { error: errors.params(['searchIndexId']) };
  }

  const { searchIndex, error } = await getSearchIndexById(searchIndexId);

  if (error) {
    return { error };
  }

  const leanSearchIndex = { ...searchIndex.toObject() };

  const { index, error: indexError } = await indexesService.getIndex({
    searchIndex,
    settings,
    stats,
  });

  if (indexError) {
    if (indexError.statusCode === 404) {
      return {
        searchIndex: {
          indexError,
          ...leanSearchIndex,
        },
      };
    }

    return { error: indexError };
  }

  return {
    searchIndex: {
      ...index,
      ...leanSearchIndex,
    },
  };
}

/**
 * Creates a search index
 *
 * @param {Object} options
 * @param {String} options.entityId - The entity associated with the search index
 * @param {String} options.language - The language associated with the search index
 * @param {String} options.name - The name associated with the search index
 * @param {String} options.siteId - The site associated with the search index
 * @param {String} options.type - The type associated with the search index
 *
 * @returns {Promise<SearchIndex>}
 */
async function createSearchIndex({ entityId, language, name, siteId, type }) {
  if (!entityId || !language || !type) {
    return {
      error: errors.params(['entity', 'language', 'type']),
    };
  }

  const searchIndex = await SearchIndex.findOneAndUpdate(
    {
      entity: entityId,
      language,
      ...(siteId ? { site: siteId } : {}),
      type,
    },
    {
      entity: entityId,
      language,
      name,
      site: siteId,
      type,
    },
    { upsert: true, new: true }
  );

  return { searchIndex };
}

/**
 * Updates a search index
 *
 * @param {Object} options
 * @param {String} options.searchIndexId - The search index ID
 * @param {Object} options.searchIndexData - The search index data
 *
 * @returns {Promise<SearchIndex>}
 */
async function updateSearchIndex({ searchIndexId, searchIndexData }) {
  const searchIndex = await SearchIndex.findByIdAndUpdate(
    searchIndexId,
    searchIndexData,
    { new: true }
  );

  /**
   * NOTE: Make sure settings are not updated through this method. Use indexesService.updateIndexSettings instead.
   * This is because the settings are not stored in the SearchIndex model for now, but generated based on stating index settings defined in index registration.
   */
  delete searchIndexData.indexes;

  if (!searchIndex) {
    return { error: errors.not_found('SearchIndex') };
  }

  return { searchIndex };
}

/**
 * Enables a search index
 * @param {Object} options
 * @param {String} options.searchIndexId - The search index ID
 * @returns {Promise<SearchIndex>}
 */
async function enableSearchIndex({ searchIndexId }) {
  const searchIndex = await SearchIndex.findByIdAndUpdate(
    searchIndexId,
    { enabled: true },
    { new: true }
  );

  if (!searchIndex) {
    return { error: errors.not_found('SearchIndex') };
  }

  return { searchIndex };
}

/**
 * Disables a search index
 * @param {Object} options
 * @param {String} options.searchIndexId - The search index ID
 * @returns {Promise<SearchIndex>}
 */
async function disableSearchIndex({ searchIndexId }) {
  const searchIndex = await SearchIndex.findByIdAndUpdate(
    searchIndexId,
    { enabled: false },
    { new: true }
  );

  if (!searchIndex) {
    return { error: errors.not_found('SearchIndex') };
  }

  return { searchIndex };
}

/**
 * Deletes a search index. This performs a hard delete for the SearchIndex model, since the meilisearch index is always hard delete.
 * @param {Object} options
 * @param {String} options.searchIndexId - The search index ID
 * @returns {Promise<SearchIndex>}
 */
async function deleteSearchIndex({ searchIndexId }) {
  const searchIndex = await SearchIndex.findByIdAndDelete(searchIndexId);

  if (!searchIndex) {
    return { error: errors.not_found('SearchIndex') };
  }

  const { task, error } = await indexesService.deleteIndex(
    searchIndex._id.toString()
  );

  if (error) {
    return { error };
  }

  return { searchIndex, task };
}

export default {
  getSearchIndexes,
  getSearchIndexById,
  getSearchIndex,
  createSearchIndex,
  updateSearchIndex,
  enableSearchIndex,
  disableSearchIndex,
  deleteSearchIndex,
};
