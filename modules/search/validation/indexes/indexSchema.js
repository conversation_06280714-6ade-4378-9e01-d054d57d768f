import Joi from 'joi';

export const indexParamsSchema = Joi.object().keys({
  id: Joi.string().required(),
});

export const indexQuerySchema = Joi.object().keys({
  settings: Joi.boolean().default(false),
  stats: Joi.boolean().default(false),
});

export const updateIndexRankingRules = Joi.object().keys({
  rankingRules: Joi.array()
    .items(
      Joi.string().valid(
        'typo',
        'words',
        'proximity',
        'attribute',
        'exactness',
        'sort'
      )
    )
    .required()
    .unique(),
});
