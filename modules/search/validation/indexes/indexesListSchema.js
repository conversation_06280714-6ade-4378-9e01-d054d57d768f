import Joi from 'joi';

import paginationSchema from '#utils/api/pagination/schema.js';
import searchSchema from '#utils/api/search/schema.js';

export const indexesListSchema = Joi.object().keys({
  ...paginationSchema,
  ...searchSchema,
  includeStats: Joi.boolean().default(false),
  includeTasks: Joi.boolean().default(false),
  language: Joi.string().allow(null),
  order: Joi.string().valid('asc', 'desc').allow('', null),
  siteId: Joi.string().allow(null),
  sort: Joi.string().allow('', null),
  type: Joi.string().allow(null),
});
