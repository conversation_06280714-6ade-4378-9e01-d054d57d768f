import Joi from 'joi';

import app from '#app';
import { validate } from '#utils/validationMiddleware.js';

export const validateCreateOrUpdateIndex = async (req, res, next) => {
  const registeredIndexes = app.get('searchIndexes');
  const indexTypes = Object.keys(registeredIndexes) || [];

  const createIndexSchema = Joi.object().keys({
    language: Joi.string().required(),
    name: Joi.string().allow('', null),
    site: Joi.string().allow(null),
    type: Joi.string()
      .required()
      .valid(...indexTypes),
  });

  return validate(createIndexSchema)(req, res, next);
};
