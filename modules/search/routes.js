import express from 'express';

import { isRouteEnabled } from '#modules/feature-flags/middleware/isRouteEnabled.js';
import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';

import { validate } from '#utils/validationMiddleware.js';

import documentController from './controllers/documentController.js';
import searchController from './controllers/searchController.js';
import searchIndexController from './controllers/searchIndexController.js';
import searchIndexTaskController from './controllers/searchIndexTaskController.js';
import tasksController from './controllers/tasksController.js';
import { validateCreateOrUpdateIndex } from './validation/indexes/validateCreateOrUpdateIndex.js';
import { indexesListSchema } from './validation/indexes/indexesListSchema.js';
import {
  indexParamsSchema,
  indexQuerySchema,
  updateIndexRankingRules,
} from './validation/indexes/indexSchema.js';
import { searchDocumentsSchema } from './validation/search/searchDocumentsSchema.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

// Allow only admins to all routes after this middleware
router.use(restrictTo());

// TODO: Remove this middleware when site search feature is live
router.use(
  isRouteEnabled('site-search', {
    strategyOverride: { design: true, site: true },
  })
);

// Indexes
router
  .route('/indexes')
  .get(
    validate(indexesListSchema, 'query'),
    searchIndexController.getSearchIndexes
  )
  .post(validateCreateOrUpdateIndex, searchIndexController.createSearchIndex);

router.get('/indexes/types', searchIndexController.getIndexTypes);

router.get('/indexes/types/:type', searchIndexController.getIndexType);

router
  .route('/indexes/:id')
  .get(
    validate(indexParamsSchema, 'params'),
    validate(indexQuerySchema, 'query'),
    searchIndexController.getSearchIndex
  )
  .patch(
    validate(indexParamsSchema, 'params'),
    validateCreateOrUpdateIndex,
    searchIndexController.updateSearchIndex
  )
  .delete(
    validate(indexParamsSchema, 'params'),
    searchIndexController.deleteSearchIndex
  );

router
  .route('/indexes/:id/settings')
  .patch(
    validate(indexParamsSchema, 'params'),
    searchIndexController.updateSearchIndexSettings
  );

router
  .route('/indexes/:id/settings/ranking-rules')
  .put(
    validate(indexParamsSchema, 'params'),
    validate(updateIndexRankingRules, 'body'),
    searchIndexController.updateSearchIndexRankingRules
  )
  .delete(
    validate(indexParamsSchema, 'params'),
    searchIndexController.resetSearchIndexRankingRules
  );

router
  .route('/indexes/:id/enable')
  .patch(
    validate(indexParamsSchema, 'params'),
    searchIndexController.enableSearchIndex
  );

router
  .route('/indexes/:id/disable')
  .patch(
    validate(indexParamsSchema, 'params'),
    searchIndexController.disableSearchIndex
  );

// Search
router
  .route('/indexes/:id/search')
  .get(
    validate(searchDocumentsSchema, 'query'),
    searchController.searchDocuments
  );

// Documents

router
  .route('/indexes/:id/documents')
  .delete(
    validate(indexParamsSchema, 'params'),
    documentController.deleteAllDocuments
  );

router
  .route('/indexes/:id/documents/:documentId')
  .get(documentController.getDocument)
  .delete(documentController.deleteDocument);

// Search index tasks
router
  .route('/indexes/:id/tasks')
  .get(searchIndexTaskController.getSearchIndexTasks);

router
  .route('/indexes/:id/tasks/:taskId')
  .get(searchIndexTaskController.getSearchIndexTask);

// Meilisearch Tasks
router.route('/tasks/:id').get(tasksController.getTask);

export default router;
