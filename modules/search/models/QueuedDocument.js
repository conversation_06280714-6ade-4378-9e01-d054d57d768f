import mongoose from 'mongoose';

export const actions = ['create', 'update', 'delete'];

const queuedDocumentSchema = new mongoose.Schema(
  {
    // The search index for the queued document
    index: {
      type: String,
      required: true,
    },

    documentId: {
      type: String,
      required: true,
    },

    // The action to perform on the document
    action: {
      type: String,
      enum: actions,
      required: true,
    },

    // The date to run the operation
    actionDate: {
      type: Date,
      required: true,
    },

    // The context of the operation. This is used by the registered queTask to generate the document
    context: {
      type: mongoose.SchemaTypes.Mixed,
      required: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    minimize: false,
  }
);

// Indexes
queuedDocumentSchema.index({ index: 1, documentId: 1 });
queuedDocumentSchema.index({
  index: 1,
  documentId: 1,
  action: 1,
});

// Export the model
export default mongoose.model('QueuedDocument', queuedDocumentSchema);
