import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const searchIndexSchema = SchemaFactory({
  // The site the index belongs to, if any
  site: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
  },

  // The entity the index belongs to
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },

  name: {
    type: String,
    trim: true,
  },

  // The type of index, e.g. 'pages'
  type: {
    type: String,
    required: true,
  },

  // The language of the index
  language: {
    type: String,
    required: true,
  },

  // The index settings. These can be used to override the default settings.
  settings: {
    type: mongoose.SchemaTypes.Mixed,
  },

  // The last time the index was updated
  lastIndexedAt: {
    type: Date,
  },
});

// Indexes
searchIndexSchema.index(
  { site: 1, entity: 1, type: 1, language: 1 },
  { unique: true }
);
searchIndexSchema.index({ entity: 1 });
searchIndexSchema.index({ site: 1 });
searchIndexSchema.index({ language: 1 });
searchIndexSchema.index({ type: 1 });
searchIndexSchema.index({ name: 1 });

// Export the model
export default mongoose.model('SearchIndex', searchIndexSchema);
