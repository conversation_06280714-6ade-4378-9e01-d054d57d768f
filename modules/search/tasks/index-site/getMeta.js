import { errors } from '#utils/appError.js';
import siteServices from '#modules/web/services/siteServices.js';

/**
 * @typedef GetSiteIndexMetaOptions
 * @prop {object} task - The task object
 */

/**
 * Get the meta data for the site index task
 * @param {GetSiteIndexMetaOptions} options - The options for getting the site index meta
 * @returns {Object} The site index meta data
 */
export async function getSiteIndexMeta({ task }) {
  if (!task) {
    throw errors.params(['task']);
  }

  const { settings } = task;

  if (!settings?.site) {
    throw errors.params(['site']);
  }

  const { site, error } = await siteServices.getSiteById(settings.site);

  if (error) {
    throw error;
  }

  return {
    module: 'web',
    name: 'site-index',
    title: site.name,
    context: {},
  };
}
