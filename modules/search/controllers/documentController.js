import documentService from '../services/documentService.js';

const getDocument = async (req, res) => {
  const { params } = req;
  const { id, documentId } = params;

  const { result, error } = await documentService.getDocument({
    indexUId: id,
    documentId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(result);
};

const deleteDocument = async (req, res) => {
  const { params } = req;
  const { id, documentId } = params;

  const { result, error } = await documentService.deleteDocumentById({
    indexUId: id,
    documentId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(result);
};

const deleteAllDocuments = async (req, res) => {
  const { params } = req;
  const { id } = params;

  const { result, error } = await documentService.deleteAllDocuments({
    indexUId: id,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(result);
};

export default {
  getDocument,
  deleteDocument,
  deleteAllDocuments,
};
