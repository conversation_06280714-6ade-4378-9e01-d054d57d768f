import searchService from '../services/searchService.js';

const searchDocuments = async (req, res) => {
  const { params, query } = req;
  const { id } = params;
  const { limit, page, search } = query;

  const { result, error } = await searchService.searchDocuments({
    indexUId: id,
    limit,
    page,
    search,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(result);
};

export default {
  searchDocuments,
};
