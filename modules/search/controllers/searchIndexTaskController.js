import searchIndexTaskService from '#modules/search/services/searchIndexTaskService.js';
import { errors } from '#utils/appError.js';

const getSearchIndexTasks = async (req, res) => {
  const { params, query } = req;
  const { id } = params;
  const { limit, page, skip, status } = query;

  const { searchIndexTask, error } =
    await searchIndexTaskService.getSearchIndexTasks({
      limit,
      page,
      searchIndexId: id,
      skip,
      status,
    });

  if (error) {
    throw error;
  }

  if (!searchIndexTask) {
    throw errors.not_found('SearchIndexTask');
  }

  res.status(200).json(searchIndexTask);
};

const getSearchIndexTask = async (req, res) => {
  const { params } = req;
  const { taskId } = params;

  const { searchIndexTask, error } =
    await searchIndexTaskService.getSearchIndexTaskById(taskId);

  if (error) {
    throw error;
  }

  if (!searchIndexTask) {
    throw errors.not_found('SearchIndexTask');
  }

  res.status(200).json(searchIndexTask);
};

export default {
  getSearchIndexTasks,
  getSearchIndexTask,
};
