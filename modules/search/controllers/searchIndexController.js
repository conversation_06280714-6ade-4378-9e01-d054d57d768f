import app from '#app';
import { errors } from '#utils/appError.js';
import indexesService from '../services/indexesService.js';
import searchIndexService from '../services/searchIndexService.js';

const getIndexTypes = async (req, res) => {
  const registeredIndexes = app.get('searchIndexes');
  res.status(200).json(Object.keys(registeredIndexes) || []);
};

const getIndexType = async (req, res) => {
  const { params } = req;
  const { type } = params;

  const registeredIndexes = app.get('searchIndexes');
  const index = registeredIndexes[type];

  if (!index) {
    throw errors.not_found('Index type not found');
  }

  res.status(200).json(index.settings());
};

const getSearchIndexes = async (req, res) => {
  const { query, entity } = req;
  const {
    includeStats,
    includeTasks,
    language,
    limit,
    page,
    siteId,
    skip,
    type,
  } = query;

  const { items, count, error } = await searchIndexService.getSearchIndexes({
    entityId: entity._id,
    includeStats,
    includeTasks,
    language,
    limit,
    page,
    siteId,
    skip,
    type,
  });

  if (error) {
    throw error;
  }

  res.status(200).json({ items, count });
};

const getSearchIndex = async (req, res) => {
  const { params, query } = req;
  const { id } = params;
  const { settings, stats } = query;

  const { searchIndex, error } = await searchIndexService.getSearchIndex({
    searchIndexId: id,
    settings,
    stats,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndex);
};

const createSearchIndex = async (req, res) => {
  const { entity, body } = req;
  const { type } = body;

  const registeredIndexes = app.get('searchIndexes');

  if (!registeredIndexes[type] || registeredIndexes[type]?.createIndex) {
    throw errors.not_implemented('Index type not implemented');
  }

  // 1. First, create an instance of the index in the DB
  const { searchIndex, error: searchIndexError } = await registeredIndexes[
    type
  ].createIndex({
    ...body,
    entity,
  });

  if (searchIndexError) {
    throw searchIndexError;
  }

  // 2. Then, create the index in MeiliSearch
  const { error } = await indexesService.createIndex(searchIndex);

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndex);
};

const updateSearchIndex = async (req, res) => {
  const { params, body } = req;
  const { id } = params;

  const { searchIndex, error } = await searchIndexService.updateSearchIndex({
    searchIndexId: id,
    searchIndexData: body,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndex);
};

const updateSearchIndexSettings = async (req, res) => {
  const { params } = req;
  const { id } = params;

  const { searchIndex, error } =
    await searchIndexService.getSearchIndexById(id);

  if (error) {
    throw error;
  }

  const { _id, type, language } = searchIndex.toObject();

  const { task, error: indexSettingsError } =
    await indexesService.updateIndexSettings({
      indexUId: _id.toString(),
      language,
      type,
    });

  if (indexSettingsError) {
    throw indexSettingsError;
  }

  res.status(200).json({ searchIndex, task });
};

const updateSearchIndexRankingRules = async (req, res) => {
  const { params, body } = req;
  const { id } = params;

  const { searchIndex, error } =
    await searchIndexService.getSearchIndexById(id);

  if (error) {
    throw error;
  }

  const { _id } = searchIndex.toObject();

  const { task, error: indexSettingsError } =
    await indexesService.updateIndexRankingRules({
      indexUId: _id.toString(),
      rankingRules: body.rankingRules,
    });

  if (indexSettingsError) {
    throw indexSettingsError;
  }

  res.status(200).json({ searchIndex, task });
};

const resetSearchIndexRankingRules = async (req, res) => {
  const { params } = req;
  const { id } = params;

  const { searchIndex, error } =
    await searchIndexService.getSearchIndexById(id);

  if (error) {
    throw error;
  }

  const { _id } = searchIndex.toObject();

  const { task, error: indexSettingsError } =
    await indexesService.resetIndexRankingRules({
      indexUId: _id.toString(),
    });

  if (indexSettingsError) {
    throw indexSettingsError;
  }

  res.status(200).json({ searchIndex, task });
};

const enableSearchIndex = async (req, res) => {
  const { params } = req;
  const { id } = params;

  const { searchIndex, error } = await searchIndexService.enableSearchIndex({
    searchIndexId: id,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndex);
};

const disableSearchIndex = async (req, res) => {
  const { params } = req;
  const { id } = params;

  const { searchIndex, error } = await searchIndexService.disableSearchIndex({
    searchIndexId: id,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(searchIndex);
};

const deleteSearchIndex = async (req, res) => {
  const { params } = req;
  const { id } = params;

  const { searchIndex, task, error } =
    await searchIndexService.deleteSearchIndex({
      searchIndexId: id,
    });

  if (error) {
    throw error;
  }

  res.status(200).json({ searchIndex, task });
};

export default {
  getIndexTypes,
  getIndexType,
  getSearchIndexes,
  getSearchIndex,
  createSearchIndex,
  updateSearchIndex,
  updateSearchIndexSettings,
  updateSearchIndexRankingRules,
  resetSearchIndexRankingRules,
  enableSearchIndex,
  disableSearchIndex,
  deleteSearchIndex,
};
