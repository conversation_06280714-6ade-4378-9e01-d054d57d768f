import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const taskGroupSchema = SchemaFactory({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
    default: '',
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
});

taskGroupSchema.index({ title: 1 });
taskGroupSchema.index({ entity: 1 });

export default mongoose.model('TaskGroup', taskGroupSchema);
