import { errors } from '#utils/appError.js';
import factory from '#utils/handlerFactory.js';

import Task from '../models/Task.js';
import tasksService from '../services/tasksService.js';

export const getAllTasks = async (req, res) => {
  const {
    error,
    tasks,
    errors: taskErrors,
  } = await tasksService.getTasks({
    entityId: req.entity._id,
    filterByEntity: true,
  });

  if (error) {
    throw error;
  }

  if (taskErrors?.length > 0) {
    // Just throw the first error for now
    throw taskErrors[0];
  }

  res.status(200).json({ items: tasks, count: tasks.length });
};

export const getTask = async (req, res) => {
  const { taskId } = req.params;

  const { task, error } = await tasksService.getTaskById(taskId, {
    entityId: req.entity._id,
    filterByEntity: true,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(task);
};

export const createTask = async (req, res) => {
  const task = await tasksService.createTask(req.entity._id, req.body);
  res.status(200).json(task);
};

export const updateTask = async (req, res) => {
  const { task, error } = await tasksService.updateTask(
    req.params.taskId,
    req.entity._id,
    req.body
  );

  if (error) {
    throw error;
  }

  res.status(200).json(task);
};

export const deleteTask = async (req, res) => {
  const data = await factory.deleteOne(Task, req, {
    paramId: 'taskId',
  });

  res.status(200).json(data);
};

export const restoreTask = async (req, res) => {
  const data = await factory.restoreOne(Task, req, {
    paramId: 'taskId',
  });

  res.status(200).json(data);
};

export const disableTask = async (req, res) => {
  const data = await factory.disableOne(Task, req, {
    paramId: 'taskId',
  });

  res.status(200).json(data);
};

export const enableTask = async (req, res) => {
  const data = await factory.enableOne(Task, req, {
    paramId: 'taskId',
  });

  res.status(200).json(data);
};

export const runTask = async (req, res) => {
  if (!req.params.taskId) throw errors.params(['taskId']);

  const { results, type } = await tasksService.executeTask(req.params.taskId);

  res.status(200).json({
    type,
    results,
  });
};

export default {
  getAllTasks,
  getTask,
  createTask,
  updateTask,
  deleteTask,
  restoreTask,
  disableTask,
  enableTask,
  runTask,
};
