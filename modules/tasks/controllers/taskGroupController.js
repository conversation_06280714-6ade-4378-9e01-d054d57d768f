import factory from '#utils/handlerFactory.js';
import Task from '../models/Task.js';
import TaskGroup from '../models/TaskGroup.js'; // TODO: Remove this comment when TaskGroup is implemented

export const getAllTaskGroups = async (req, res) => {
  const data = await factory.getAll(Task, req, {
    filterByEntity: true,
    filter: {},
    populate: [
      {
        path: 'entity',
        select: 'name',
      },
    ],
  });

  res.status(200).json(data);
};

export const getTaskGroup = async (req, res) => {
  const data = await factory.getOne(TaskGroup, req, {
    paramId: 'taskGroupId',
  });

  res.status(200).json(data);
};

export const createTaskGroup = async (req, res) => {
  const task = await TaskGroup.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(task);
};

export const updateTaskGroup = async (req, res) => {
  const task = await factory.getOne(TaskGroup, req, {
    paramId: 'taskGroupId',
    filterByEntity: true,
  });

  // Update task group's data
  const updatedTaskGroup = await TaskGroup.findByIdAndUpdate(
    task._id,
    {
      ...req.body,
      entity: task.entity, // prevents moving a task group to different entity
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedTaskGroup);
};

export const deleteTaskGroup = async (req, res) => {
  const data = await factory.deleteOne(TaskGroup, req, {
    paramId: 'taskGroupId',
  });

  res.status(200).json(data);
};

export const restoreTaskGroup = async (req, res) => {
  const data = await factory.restoreOne(TaskGroup, req, {
    paramId: 'taskGroupId',
  });

  res.status(200).json(data);
};

export const disableTaskGroup = async (req, res) => {
  const data = await factory.disableOne(TaskGroup, req, {
    paramId: 'taskGroupId',
  });

  res.status(200).json(data);
};

export const enableTaskGroup = async (req, res) => {
  const data = await factory.enableOne(TaskGroup, req, {
    paramId: 'taskGroupId',
  });

  res.status(200).json(data);
};

export default {
  getAllTaskGroups,
  getTaskGroup,
  createTaskGroup,
  updateTaskGroup,
  deleteTaskGroup,
  restoreTaskGroup,
  disableTaskGroup,
  enableTaskGroup,
};
