import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import taskController from './controllers/taskController.js';
import taskGroupController from './controllers/taskGroupController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

// Allow only admins to all routes after this middleware
router.use(restrictTo());

//#region Tasks
// -----------------------------------------------
router
  .route('/tasks')
  .get(taskController.getAllTasks)
  .post(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'CREATE_TASK' }),
    taskController.createTask
  );

router
  .route('/tasks/:taskId')
  .get(taskController.getTask)
  .patch(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'UPDATE_TASK' }),
    taskController.updateTask
  )
  .delete(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'DELETE_TASK' }),
    taskController.deleteTask
  );

router
  .route('/tasks/:taskId/restore')
  .patch(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'RESTORE_TASK' }),
    taskController.restoreTask
  );

router
  .route('/tasks/:taskId/disable')
  .patch(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'DISABLE_TASK' }),
    taskController.disableTask
  );

router
  .route('/tasks/:taskId/enable')
  .patch(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'ENABLE_TASK' }),
    taskController.enableTask
  );

router.post(
  '/tasks/:taskId/run',
  logRequest({ module: 'tasks', action: 'RUN_TASK' }),
  taskController.runTask
);
//#endregion

//#region Task Groups
// -----------------------------------------------
router
  .route('/task-groups')
  .get(taskGroupController.getAllTaskGroups)
  .post(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'CREATE_TASK_GROUP' }),
    taskGroupController.createTaskGroup
  );

router
  .route('/task-groups/:id')
  .get(taskGroupController.getTaskGroup)
  .patch(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'UPDATE_TASK_GROUP' }),
    taskGroupController.updateTaskGroup
  )
  .delete(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'DELETE_TASK_GROUP' }),
    taskGroupController.deleteTaskGroup
  );

router
  .route('/task-groups/:id/restore')
  .patch(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'RESTORE_TASK_GROUP' }),
    taskGroupController.restoreTaskGroup
  );

router
  .route('/task-groups/:id/disable')
  .patch(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'DISABLE_TASK_GROUP' }),
    taskGroupController.disableTaskGroup
  );

router
  .route('/task-groups/:id/enable')
  .patch(
    restrictTo(),
    logRequest({ module: 'tasks', action: 'ENABLE_TASK_GROUP' }),
    taskGroupController.enableTaskGroup
  );
//#endregion

export default router;
