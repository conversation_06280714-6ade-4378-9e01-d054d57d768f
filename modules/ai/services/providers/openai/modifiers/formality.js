const levels = [
  'academic',
  'professional',
  'formal',
  'neutral',
  'relaxed',
  'casual',
  'informal',
];

const audiences = [
  'general',
  'business',
  'casual',
  'youth',
  'poetic',
  'religious',
  'technical',
  'scientific',
  'legal',
];

/**
 * Prompt for the formality modifier based on the settings
 * @param {Object} settings - The settings object
 * @param {String} settings.level - The level to set the formality to
 * @param {String} settings.audience - The audience to set the formality to
 * @returns {String} - The modifier prompt
 */
export default function formalityModifierPrompt({ settings }) {
  let { level, audience } = settings || {};

  // Check if level modifier exist and is valid
  if (!level || !levels.includes(level)) {
    // Default to neutral if not
    level = 'neutral';
  }

  // Check if audience modifier exist and is valid
  if (!audience || !audiences.includes(audience)) {
    // Default to general if not
    audience = 'general';
  }

  return `- Change Formality: Adjust the formality of the text to make it ${level}, targeting a ${audience} audience, and based on the context provided bellow.`;
}
