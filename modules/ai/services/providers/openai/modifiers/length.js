const units = ['paragraph', 'sentence', 'word', 'character'];

/**
 * Generates a text modifier prompt for the length modifier based on the settings
 *
 * @param {Object} settings
 * @param {String} settings.unit - The unit to limit the text to
 * @param {Number} settings.amount - The amount of units to limit the text to
 * @returns {String} - The modifier prompt
 */
export default function lengthModifierPrompt({ settings }) {
  let { unit, amount } = settings || {};

  amount = parseInt(amount, 10);

  // Check if amount modifier exist and is valid
  if (!amount || Number.isNaN(amount) || amount < 1 || amount > 100) {
    // Default to 1 if not valid
    amount = 1;
  }

  // Check if unit modifier exist and is valid
  if (!unit || !units.includes(unit)) {
    // Default to word if not valid
    unit = 'word';
  }

  // Pluralize unit if amount is greater than 1
  unit = amount > 1 ? `${unit}s` : unit;

  return `- Set Specific Length: Limit the text to ${amount} ${unit}, ensuring conciseness without losing key information.`;
}
