import ky from 'ky';

import providers from './providers/index.js';

// Default modifiers to be applied always to the text
const defaultModifiers = {
  // clarity: {},
  // consistency: {},
  // grammar: {},
};

/**
 *
 * @param {Object} params - The params object
 * @param {Object} params.entity - The entity object
 * @param {String} params.text - The text to modify
 * @param {String} params.context - Optional contextual information to help the AI
 * @param {String} params.modifiers - The modifiers to be applied
 * @returns {Promise<object>} - The translation object
 */
export async function improveText({
  entity,
  text,
  context,
  modifiers = {},
  providerName = 'openai',
  type = 'text',
} = {}) {
  const { config } = entity;
  const { ai } = config || {};

  // Check if the provider is valid
  if (!Object.keys(providers).includes(providerName)) {
    return {
      text,
      error: 'AI provider is not supported',
    };
  }

  let improvedText = '';
  let error;
  const provider = providerName || 'openai';

  const { apiKey, disabled } = ai.providers?.[provider] || {};

  // Merge provided modifiers with the default ones
  modifiers = { ...defaultModifiers, ...(modifiers || {}) };

  // Check if the AI is enabled and configured
  if (!ai.enabled || disabled || !apiKey) {
    return {
      text,
      error: 'AI is not enabled nor configured for this entity',
    };
  }

  // Check if the text and modifiers are valid
  if (!text || !Object.keys(modifiers).length) {
    return {
      text,
      error: 'AI improveText requires text and at least one modifier',
    };
  }

  // Get the provider's data
  const { url, getParams, getHeaders, getText, availableModifiersKeys } =
    providers[provider];

  // Check if the modifiers are valid
  const invalidModifiers = Object.keys(modifiers).filter(
    (modifier) => !availableModifiersKeys.includes(modifier)
  );

  if (invalidModifiers.length) {
    return {
      text,
      error: `The following modifiers are not supported: ${invalidModifiers.join(
        ', '
      )}`,
    };
  }

  // Fetch the improved text from provider's API
  try {
    const data = await ky
      .post(url, {
        json: getParams({ text, modifiers, context, type }),
        headers: {
          ...getHeaders({ apiKey }),
          'Content-Type': 'application/json', // add always the content-type header for json
        },
      })
      .json();

    // Get the improved text from the response
    improvedText = getText({ data });
  } catch (err) {
    console.log(err.response); // eslint-disable-line no-console
    error = err;
  }

  return {
    error,
    text: improvedText,
  };
}

export default {
  improveText,
};
