import { getClientIp } from '@supercharge/request-ip/dist/index.js';

import { errorCodes, generateError } from '#utils/appError.js';
import { notifyAdmins } from '#utils/notifier.js';
import { toLogDate } from '#utils/dates.js';
import Logger from '#utils/logger.js';

const nodeEnv = process.env.NODE_ENV || 'development';

const multerErrors = {
  LIMIT_PART_COUNT: 'TOO_MANY_PARTS',
  LIMIT_FILE_SIZE: 'FILE_TOO_BIG',
  LIMIT_FILE_COUNT: 'TOO_MANY_FILES',
  LIMIT_FIELD_KEY: 'FIELD_NAME_TOO_LONG',
  LIMIT_FIELD_VALUE: 'FIELD_VALUE_TOO_LONG',
  LIMIT_FIELD_COUNT: 'TOO_MANY_FIELDS',
  LIMIT_UNEXPECTED_FILE: 'UNEXPECTED_FIELD',
};

const handleCastErrorDB = (err) => {
  const message = `Invalid ${err.path}: ${err.value}`;
  return generateError(message, errorCodes.CAST_ERROR, 400);
};

const handleMulterError = (err) => {
  const fieldName = err.field;
  const message = `${err.message} for '${fieldName}' field.`;
  return generateError(message, errorCodes.VALIDATION_ERROR, 422, {
    [fieldName]: { type: multerErrors[err.code] || err.code },
  });
};

const handleDuplicateFieldsDB = (err) => {
  const fieldName = Object.keys(err.keyValue)[0];
  const message = `Duplicated value '${err.keyValue[fieldName]}' for '${fieldName}' field. Please use another value!`;
  return generateError(message, errorCodes.VALIDATION_ERROR, 422, {
    [fieldName]: { type: 'DUPLICATED', value: err.keyValue[fieldName] },
  });
};

const handleValidationErrorDB = (err) => {
  const errors = Object.keys(err.errors).reduce((acc, key) => {
    const error = err.errors[key];

    if (!error.properties) {
      acc[key] = {
        type: 'UNEXPECTED',
      };
      return acc;
    }

    const { type, message, value } = error.properties;
    acc[key] = {
      type: (type === 'user defined' ? message : type).toUpperCase(),
      expected: error.properties[type],
      value,
    };
    return acc;
  }, {});

  return generateError(
    'Validation Error',
    errorCodes.VALIDATION_ERROR,
    422,
    errors
  );
};

const handleJWTError = () =>
  generateError('Invalid token', errorCodes.TOKEN_NOT_VALID, 401);

const sendErrorDev = (err, req, res) =>
  res.status(err.statusCode).json({
    code: err.code,
    message: err.message,
    error: err,
  });

const notifyError = (err, req) => {
  const status = err.statusCode || 500;
  // TODO: Revise if we should notify on 409 errors, or if 422 is the correct code for validation.
  // Avoid notifying on 'not found (404)', 'validation (422)' or 'rate limit (429)' errors
  if (status > 404 && status !== 409 && status !== 422 && status !== 429) {
    // Notify admins of important errors
    const { method, originalUrl, headers, params, query, body, user } = req;
    const accountId = user ? user._id : null;

    // Prevent sending sensitive information
    delete body.password;
    delete body.oldPassword;
    delete body.newPassword;
    delete body.token;

    const data = {
      'Date': toLogDate(new Date()),
      'IP': getClientIp(req),
      'Account ID': accountId,
      'ClientToken': req.header('ClientToken'),
      'Headers': JSON.stringify(headers),
      'Parameters': JSON.stringify(params),
      'Query': JSON.stringify(query),
      'Body': JSON.stringify(body),
      'HTTP Method': method,
      'URL': `${originalUrl.split('?')[0]}`,
      'Error Code': err.code,
      'Error Status': status,
      'Error Stack': err.stack,
    };

    notifyAdmins({
      subject: `API - Unexpected Error on ${req.entity?.name}`,
      templateName: 'requestError',
      templateValues: { status, data },
    });
  }
};

const sendErrorProd = (err, req, res) => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    notifyError(err, req);

    return res.status(err.statusCode).json({
      code: err.code,
      message: err.message,
    });
  }

  // Code here is undefined, so we set it
  err.code = 'INTERNAL_SERVER_ERROR';
  notifyError(err, req);

  // Programming or other unknown error: don't leak error details
  // 1) Log error
  Logger.error('ERROR 💥', err);

  // 2) Send generic message
  return res.status(500).json({
    code: 'INTERNAL_SERVER_ERROR',
    message: 'Oops! Something went wrong...',
  });
};

export default (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;
  error.stack = err.stack;

  if (err.code === 11000) error = handleDuplicateFieldsDB(err);
  if (err.name === 'CastError') error = handleCastErrorDB(err);
  if (err.name === 'MulterError') error = handleMulterError(err);
  if (err.name === 'ValidationError') error = handleValidationErrorDB(err);
  if (['JsonWebTokenError', 'TokenExpiredError'].includes(err.name))
    error = handleJWTError();

  error.statusCode = error.statusCode || 500;

  res.error = error;

  Logger.error(err.stack);

  if (nodeEnv === 'production') {
    sendErrorProd(error, req, res);
  } else {
    sendErrorDev(error, req, res);
  }
};
