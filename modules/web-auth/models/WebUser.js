import mongoose from 'mongoose';
import validator from 'validator';

import SchemaFactory from '#utils/schemaFactory.js';

const webUserSchema = SchemaFactory({
  name: {
    type: String,
    trim: true,
    maxlength: 80,
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    validate: [validator.isEmail, 'INVALID_EMAIL'],
  },
  emailVerified: {
    type: Date,
  },
  termsAccepted: {
    type: Date,
  },
  lastLogin: {
    type: Date,
  },
  userCollection: {
    type: mongoose.Types.ObjectId,
    ref: 'WebUserCollection',
  },
  profile: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  preferences: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  image: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

webUserSchema.index(
  { email: 1, userCollection: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);

export default mongoose.model('WebUser', webUserSchema);
