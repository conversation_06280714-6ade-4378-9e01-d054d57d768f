import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const accountSchema = SchemaFactory({
  user: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'WebUser',
  },
  type: {
    type: String, // "oidc" | "oauth" | "email" | "credentials"
    required: true,
  },
  provider: {
    type: String,
    required: true,
  },
  providerAccountId: {
    type: String,
    required: true,
  },
  refreshToken: {
    type: String,
  },
  accessToken: {
    type: String,
  },
  accessTokenExpiresAt: {
    type: Number,
  },
  tokenType: {
    type: String,
  },
  scope: {
    type: String,
  },
  idToken: {
    type: String,
  },
  password: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  sessionState: {
    type: String,
  },
});

accountSchema.index({ user: 1, provider: 1 }, { unique: true });

export default mongoose.model('Account', accountSchema);
