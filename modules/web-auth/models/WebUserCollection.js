import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';
import { FormSchema } from '#utils/schemas.js';

// Schema for the sites attribute
const ProviderSchema = new mongoose.Schema({
  code: {
    type: String,
    enum: ['email', 'credentials', 'oauth'],
    required: true,
    default: 'email',
  },
  type: {
    type: String,
    required: true,
  },
  options: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
});

const webUserCollectionSchema = SchemaFactory({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  type: {
    type: String,
    enum: ['entity', 'network'],
    required: true,
    default: 'entity',
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },
  network: {
    type: mongoose.Types.ObjectId,
    ref: 'Network',
  },
  providers: {
    type: [ProviderSchema],
    default: [],
  },
  userProfile: {
    form: {
      type: FormSchema,
      default: {},
    },
    profileNameField: {
      type: String,
    },
  },
});

webUserCollectionSchema.index({ entity: 1, network: 1, type: 1 });

export default mongoose.model('WebUserCollection', webUserCollectionSchema);
