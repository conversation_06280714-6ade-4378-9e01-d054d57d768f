import siteServices from '#modules/web/services/siteServices.js';
import { errors } from '#utils/appError.js';
import { sendEmail } from '#utils/notifier.js';
import { t } from '#utils/translator.js';

import webUserServices from '../services/webUserServices.js';

export const createWebUser = async (req, res) => {
  const user = await webUserServices.createWebUser({
    ...req.body,
    userCollectionId: req.body.userCollection,
    entity: req.entity,
  });

  res.status(200).json(user);
};

const getWebUsers = async (req, res) => {
  const { query, entity } = req;
  const {
    limit,
    page,
    search,
    siteId,
    sort,
    sortDir,
    statuses,
    userCollectionIds,
  } = query;

  const { items, count } = await webUserServices.getWebUsers({
    entity,
    fields: [],
    limit,
    page,
    search,
    siteId,
    sort,
    sortDir,
    statuses,
    userCollectionIds,
  });

  res.status(200).json({ items, count });
};

const getWebUser = async (req, res) => {
  const { params, entity, query } = req;
  const { siteId } = query;

  const data = await webUserServices.getWebUserById({
    entity,
    id: params.id,
    siteId,
  });

  res.status(200).json(data);
};

const updateWebUser = async (req, res) => {
  const { params, body } = req;

  const data = await webUserServices.updateWebUser(params.id, body);

  res.status(200).json(data);
};

const updateWebUserProfile = async (req, res) => {
  const { params, body } = req;

  const data = await webUserServices.updateWebUser(params.id, {
    profile: body,
  });

  res.status(200).json(data);
};

const deleteWebUser = async (req, res) => {
  const result = await webUserServices.deleteWebUser(req.params.id);

  res.status(204).json(result);
};

export const disableWebUser = async (req, res) => {
  const { webUser } = await webUserServices.disableWebUser(req.params.id);

  res.status(200).json(webUser);
};

export const enableWebUser = async (req, res) => {
  const webUser = await webUserServices.enableWebUser(req.params.id);

  res.status(200).json(webUser);
};

export const notifyWebUserAccount = async (req, res) => {
  const { params, entity, body } = req;
  const { sites } = body;

  const user = await webUserServices.getWebUserById({ id: params.id, entity });

  if (!user) {
    throw new errors.not_found('User', params.id);
  }

  const siteLogins = await siteServices.getLoginUrlsForSites(sites);

  if (!siteLogins || siteLogins.length === 0) {
    throw new errors.params(['sites']);
  }

  const language = 'en'; // TODO: Add support for user language preference

  sendEmail({
    language,
    entity,
    to: user.email,
    subject: t(language, 'newWebUserAccountSubject', { entity: entity.name }),
    templateName: 'newWebUserAccount',
    templateValues: {
      title: t(language, 'newWebUserAccountTitle', { entity: entity.name }),
      subtitle:
        siteLogins.length > 1
          ? t(language, 'newWebUserAccountSubtitle_many')
          : t(language, 'newWebUserAccountSubtitle_one'),
      siteLogins: siteLogins.map((site) => ({
        ...site,
        loginUrlLabel: t(language, 'newWebUserAccountLogin', {
          site: site.siteName,
        }),
      })),
    },
  });

  res.status(204).json({});
};

export default {
  createWebUser,
  getWebUsers,
  getWebUser,
  updateWebUser,
  updateWebUserProfile,
  deleteWebUser,
  disableWebUser,
  enableWebUser,
  notifyWebUserAccount,
};
