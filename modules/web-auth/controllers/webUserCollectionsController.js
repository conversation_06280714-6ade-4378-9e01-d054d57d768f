import webUserCollectionServices from '../services/webUserCollectionServices/webUserCollectionServices.js';

export const createWebUserCollection = async (req, res) => {
  const data = await webUserCollectionServices.createWebUserCollection({
    ...req.body,
  });

  res.status(200).json(data);
};

const getWebUserCollections = async (req, res) => {
  const { query, entity, user } = req;
  const { statuses, limit, page, sort, sortDir, search } = query;

  const { items, count } =
    await webUserCollectionServices.getWebUserCollections({
      entity,
      fields: [],
      limit,
      sortDir,
      page,
      search,
      sort,
      statuses,
      user,
    });

  res.status(200).json({ items, count });
};

const getWebUserCollection = async (req, res) => {
  const { params, entity, user } = req;

  const data = await webUserCollectionServices.getWebUserCollectionById({
    entity,
    id: params.id,
    user,
  });

  res.status(200).json(data);
};

const getWebUserCollectionSites = async (req, res) => {
  const { params, entity, user } = req;

  const sites = await webUserCollectionServices.getWebUserCollectionSites({
    entity,
    id: params.id,
    user,
  });

  res.status(200).json(sites);
};

const updateWebUserCollection = async (req, res) => {
  const { params, body } = req;

  const data = await webUserCollectionServices.updateWebUserCollection(
    params.id,
    body
  );

  res.status(200).json(data);
};

const deleteWebUserCollection = async (req, res) => {
  await webUserCollectionServices.deleteWebUserCollection({
    id: req.params.id,
    entity: req.entity,
    user: req.user,
  });

  res.status(204).json({});
};

export const disableWebUserCollection = async (req, res) => {
  const data = await webUserCollectionServices.disableWebUserCollection({
    id: req.params.id,
    entity: req.entity,
    user: req.user,
  });

  res.status(200).json(data);
};

export const enableWebUserCollection = async (req, res) => {
  const data = await webUserCollectionServices.enableWebUserCollection({
    id: req.params.id,
    entity: req.entity,
    user: req.user,
  });

  res.status(200).json(data);
};

export default {
  createWebUserCollection,
  getWebUserCollections,
  getWebUserCollection,
  getWebUserCollectionSites,
  updateWebUserCollection,
  deleteWebUserCollection,
  disableWebUserCollection,
  enableWebUserCollection,
};
