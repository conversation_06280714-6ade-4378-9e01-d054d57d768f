import isEmpty from 'lodash/isEmpty.js';

import getListFilters from '#utils/api/list/filters.js';
import { errors } from '#utils/appError.js';

import accountServices from './accountServices.js';
import WebUser from '../models/WebUser.js';
import sessionServices from './sessionServices.js';
import webUserCollectionServices from './webUserCollectionServices/webUserCollectionServices.js';
import verificationTokenServices from './verificationTokenServices.js';

async function createWebUser({ name, email, userCollectionId, entity }) {
  if (!email || !userCollectionId) {
    throw errors.params(['email', 'userCollectionId']);
  }

  const webUserCollection =
    await webUserCollectionServices.getWebUserCollectionById({
      id: userCollectionId,
      entity,
    });

  if (!webUserCollection) {
    throw errors.not_found('WebUserCollection');
  }

  const webUser = await WebUser.create({
    name,
    email,
    userCollection: webUserCollection?._id,
  });

  return webUser;
}

async function getWebUsers({
  entity,
  limit = 10,
  page = 1,
  search = '',
  siteId,
  skip = 0,
  sort = 'name',
  sortDir = 'asc',
  statuses = [],
  userCollectionIds = [],
}) {
  const listFilters = getListFilters({
    statuses,
    limit,
    page,
    skip,
    sort,
    order: sortDir,
    sortFields: ['name'],
    search,
    searchFields: ['name', 'email'],
  });

  const webUserCollectionsForEntity =
    await webUserCollectionServices.getWebUserCollectionBySiteIdOrEntity({
      siteId,
      entity,
    });

  const filters = {
    $and: [
      {
        userCollection: {
          $in: webUserCollectionsForEntity.items.map((item) => item._id),
        },
      },
      ...(isEmpty(listFilters.statuses) ? [] : [listFilters.statuses]),
      ...(isEmpty(listFilters.search) ? [] : [listFilters.search]),
      ...(userCollectionIds.length
        ? [
            {
              userCollection: {
                $in: webUserCollectionsForEntity
                  .filter((uc) => userCollectionIds.includes(uc?._id))
                  .map((uc) => uc._id),
              },
            },
          ]
        : []),
    ],
  };

  const items = await WebUser.find(filters)
    .sort(listFilters.sort)
    .populate(['userCollection'])
    .skip(listFilters.pagination.skip)
    // NOTE: limit must be after skip!
    .limit(listFilters.pagination.limit);

  const count = await WebUser.find(filters).countDocuments();

  return {
    count,
    items,
  };
}

async function getWebUserById({ id, entity, siteId }) {
  if (!id) {
    throw errors.params(['id']);
  }

  const webUserCollectionsForEntity =
    await webUserCollectionServices.getWebUserCollectionBySiteIdOrEntity({
      siteId,
      entity,
    });

  const webUser = await WebUser.findOne({
    _id: id,
    userCollection: {
      $in: webUserCollectionsForEntity.items.map((item) => item._id),
    },
  }).populate(['userCollection']);

  if (!webUser) {
    throw errors.not_found('WebUser');
  }

  return webUser;
}

async function getWebUsersByCollectionId(collectionId) {
  if (!collectionId) {
    throw errors.params(['collectionId']);
  }

  const webUsers = await WebUser.find({
    userCollection: collectionId,
  });

  return webUsers;
}

async function updateWebUser(userId, user = {}) {
  if (!userId) {
    throw errors.params(['id']);
  }

  const webUser = await WebUser.findOneAndUpdate(
    {
      _id: userId,
    },
    user,
    {
      new: true,
      runValidators: true,
    }
  );

  if (!webUser) {
    throw errors.not_found('WebUser');
  }

  return webUser;
}

async function deleteWebUserSessions(user) {
  const { deletedCount: sessionsDeleted } =
    await sessionServices.deleteSessionsByUserId(user._id);
  const { deletedCount: verificationTokensDeleted } =
    await verificationTokenServices.deleteVerificationTokensByIdentifier(
      user.email
    );

  return { sessionsDeleted, verificationTokensDeleted };
}

async function deleteWebUser(id) {
  if (!id) {
    throw errors.params(['id']);
  }

  const webUser = await WebUser.findOneAndUpdate(
    {
      _id: id,
    },
    { deleted: true },
    {
      returnDocument: 'after',
      runValidators: true,
    }
  );

  if (!webUser) {
    throw errors.not_found('WebUser');
  }

  const { modifiedCount: accountsDeleted } =
    await accountServices.deleteAccountsByUserId(id);
  const { sessionsDeleted, verificationTokensDeleted } =
    await deleteWebUserSessions(webUser);

  return {
    webUser,
    accountsDeleted,
    sessionsDeleted,
    verificationTokensDeleted,
  };
}

async function hardDeleteUser({ userId, entity, siteId }) {
  const existingUser = await getWebUserById({ id: userId, entity, siteId });

  if (!existingUser) {
    throw errors.not_found();
  }

  // Hard delete user
  const data = await WebUser.findByIdAndDelete(existingUser._id);

  return data;
}

async function disableWebUser(id) {
  if (!id) {
    throw errors.params(['id']);
  }

  const webUser = await WebUser.findOneAndUpdate(
    {
      _id: id,
    },
    { enabled: false },
    {
      new: true,
      runValidators: true,
    }
  );

  if (!webUser) {
    throw errors.not_found('WebUser');
  }

  // Delete active sessions and verification tokens
  const { sessionsDeleted, verificationTokensDeleted } =
    await deleteWebUserSessions(webUser);

  return { webUser, sessionsDeleted, verificationTokensDeleted };
}

async function enableWebUser(id) {
  if (!id) {
    throw errors.params(['id']);
  }

  const webUser = await WebUser.findOneAndUpdate(
    {
      _id: id,
    },
    { enabled: true },
    {
      returnDocument: 'after',
    }
  );

  if (!webUser) {
    throw errors.not_found('WebUser');
  }

  return webUser;
}

export default {
  createWebUser,
  getWebUsers,
  getWebUserById,
  getWebUsersByCollectionId,
  updateWebUser,
  deleteWebUser,
  hardDeleteUser,
  disableWebUser,
  enableWebUser,
};
