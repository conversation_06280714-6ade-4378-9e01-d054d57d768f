import { errors } from '#utils/appError.js';

import Session from '../models/Session.js';

async function deleteSessionsByUserId(userId) {
  if (!userId) {
    throw errors.params(['userId']);
  }

  return await Session.deleteMany({
    user: userId,
  });
}

async function deleteSessionsByUserIds(userIds) {
  if (!userIds) {
    throw errors.params(['userIds']);
  }

  return await Session.deleteMany({
    user: { $in: userIds },
  });
}

export default { deleteSessionsByUserId, deleteSessionsByUserIds };
