import Joi from 'joi';

import { validate } from '#utils/validationMiddleware.js';

import webUserServices from '../services/webUserServices.js';

function getUserSchema(userCollection) {
  const { userProfile } = userCollection;

  const profileFields = userProfile?.form?.fields ?? [];
  const profileFieldsSchema = profileFields.reduce(
    (acc, { type, typeOption, required, name }) => {
      // Email field is required by default in the user details form (userSchema)
      if (type === 'EmailField') {
        return acc;
      }

      if (type === 'Checkbox') {
        if (required) {
          return {
            ...acc,
            [name]: Joi.boolean().empty('').required().valid(true),
          };
        }
        return { ...acc, [name]: Joi.boolean().empty('').default(false) };
      }

      const baseType =
        type === 'Input' && typeOption === 'number' ? 'number' : 'string';

      if (required) {
        return { ...acc, [name]: <PERSON><PERSON>[baseType]().required() };
      }

      return { ...acc, [name]: Joi[baseType]().allow('', null) };
    },
    {}
  );

  return Joi.object().keys(profileFieldsSchema);
}

export const validateUserProfile = async (req, res, next) => {
  const user = await webUserServices.getWebUserById({
    id: req.params.id,
    entity: req.entity,
  });

  const schema = getUserSchema(user?.userCollection);

  return validate(schema)(req, res, next);
};
