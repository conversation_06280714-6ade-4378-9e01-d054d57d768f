import _ from 'lodash';
import ky from 'ky';
import fs from 'fs';
import { extname } from 'path';
import mimeTypes from 'mime-types';
import { once } from 'events';
import { finished } from 'stream';
import { promisify } from 'util';

import {
  errorCodes,
  errors,
  generateError,
  generateMissingParamsError,
} from '#utils/appError.js';
import { fileTypes } from '#utils/files.js';
import factory from '#utils/handlerFactory.js';
import {
  getUpload,
  randomFilename,
  uploadFile,
  getDownloadPreSignedUrl,
  sanitizeForContentDisposition,
  getUploadPreSignedUrl,
} from '#utils/storage.js';
import { decodeUrl } from '#utils/helpers.js';
import Logger from '#utils/logger.js';
import Document from '../models/Document.js';

// The default version of stream.finished() is callback-based but can be turned into a Promise-based version via util.promisify()
const streamFinished = promisify(finished);

const allowedContentTypes = [
  ...fileTypes.anyDocument,
  ...fileTypes.anyCompressed,
  ...fileTypes.anyPresentation,
  ...fileTypes.anySpreadsheet,
  ...fileTypes.anyAudio,
  ...fileTypes.anyVideo,
  ...fileTypes.anyImage,
  ...fileTypes.anyFont,
  ...fileTypes.json,
];
const maxFileSize = process.env.MAX_DOCUMENT_FILE_SIZE || '200mb';
const maxFileCount = process.env.MAX_DOCUMENT_FILE_COUNT || 20;
const tempFolder = process.env.TEMP_FOLDER || 'zafir-temp';

// TODO: When replacing digital ocean with cloudflare r2, change the names of the variables to the new folders that will be created.
const bucketMap = {
  hope: process.env.BUCKET_DOCUMENTS || 'hope-documents',
  zafir: process.env.ZAFIR_DOCUMENTS || 'zafir-documents',
  fonts: process.env.ZAFIR_FONTS || 'zafir-fonts',
};
// const bucketDocuments = process.env.BUCKET_DOCUMENTS || 'hope-documents';

const cdnBaseURL = process.env.FILES_CDN;

export const getFile = (req, res) => {
  const { containerId, fileName } = req.params;

  res.status(307).redirect(`${cdnBaseURL}/${containerId}/${fileName}`);
};

const defaultUpload = getUpload({
  folder: tempFolder,
  maxFileSize,
  allowedContentTypes,
});

const multipleUpload = getUpload({
  folder: tempFolder,
  maxFileSize,
  allowedContentTypes,
  maxFileCount,
});

export const uploadDocument = (upload = defaultUpload, fieldName = 'file') =>
  upload.single(fieldName);

export const saveDocument =
  (fieldName = 'file') =>
  async (req, res) => {
    if (!req.file) throw generateMissingParamsError([fieldName]);

    const { entity, file, body } = req;
    const { path: filePath, mimetype, originalname, size } = file;
    const { folder = 'hope', privateUpload = false } = body || {};

    Logger.info('Saving file:', originalname, `to ${bucketMap[folder]}`);

    // If the filename contains special characters, sanitize it
    const filename = sanitizeForContentDisposition(originalname);

    // If folder is provided, use the bucketMap to get the bucket name
    const bucket = bucketMap[folder];

    // Upload the file
    const uploadInfo = await uploadFile(filePath, {
      bucket,
      key: entity.id,
      acl: privateUpload ? 'private' : 'public-read', // unless privateUpload is provided, uses public-read
      mime: mimetype,
      contentDisposition: `attachment; filename="${filename}"`,
    });

    // Delete the temp file
    try {
      fs.unlinkSync(filePath);
    } catch (error) {
      Logger.error('Error trying to delete temp file:', error);
    }

    // Calculate remotename and remote url
    const remoteName = uploadInfo.Key.split('/').pop();

    Logger.info('File saved:', originalname, `to ${bucketMap[folder]}`);

    res.status(200).json({
      containerId: entity.id,
      extension: extname(remoteName),
      name: remoteName,
      originalFilename: originalname
        .substring(0, originalname.lastIndexOf('.'))
        .trim(),
      size,
      mime: mimetype,
    });
  };

export const saveRemoteFile = (fileUrl, entity) =>
  new Promise((resolve, reject) => {
    const url = decodeUrl(fileUrl);

    ky.get(url)
      .then(async (resp) => {
        let originalFilename = url.split('/').pop().split(/\?|#/).shift();

        // Try to get the mime-type from the file name
        let fileExtension = originalFilename.split('.').pop();
        let fileMime = mimeTypes.lookup(fileExtension);

        // If it cannot be detected, try to obtain from headers
        if (
          !fileMime &&
          mimeTypes.contentType(resp.headers.get('content-type'))
        ) {
          fileMime = mimeTypes
            .contentType(resp.headers.get('content-type'))
            .split(';')
            .shift();

          // And detect the file extension from the mime-type
          fileExtension = mimeTypes.extension(fileMime);
        }

        const stream = resp.body;

        if (
          allowedContentTypes.length > 0 &&
          !allowedContentTypes.includes(fileMime)
        ) {
          stream.destroy();
          return reject(
            generateError(
              `File type "${fileMime}" is not allowed. Must be in [${allowedContentTypes.join(
                ', '
              )}].`,
              errorCodes.FILE_TYPE_NOT_ALLOWED,
              400
            )
          );
        }

        // Generate a random file name
        const filename = randomFilename();

        // Use the generated random filename, with the extension provided by fileType
        const filePath = `temp/${tempFolder}/${filename}.${fileExtension}`;

        // This is to reject the download if it exceeds the max file size defined
        let isFirstChunk = true;
        let tempOutput;
        let error;
        for await (const chunk of stream) {
          if (isFirstChunk) {
            isFirstChunk = false;

            try {
              tempOutput = fs.createWriteStream(filePath);
            } catch (err) {
              Logger.error('Error trying to read file info:', err);
              error = errors.file_download_error();
              stream.destroy();
            }
          }

          if (!error) {
            if (tempOutput) {
              if (!tempOutput.write(Buffer.from(chunk))) {
                // Handle backpressure
                await once(tempOutput, 'drain');
              }
            }

            if (fs.existsSync(filePath)) {
              const stats = fs.statSync(filePath);
              if (stats.size > maxFileSize) {
                // If the file size exceeds the allowed, end the stream
                error = errors.file_too_big();
                stream.destroy();
              }
            }
          }
        }

        if (tempOutput) {
          tempOutput.end();
          // Wait until done. Throws if there are errors.
          await streamFinished(tempOutput);
        }

        if (error) {
          // If an error ocurred delete the file and return the error
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
          return reject(error);
        }

        // Get file size
        const { size } = fs.statSync(filePath);

        // Upload the file
        const uploadInfo = await uploadFile(filePath, {
          bucket: bucketMap.hope,
          key: entity.id,
          mime: fileMime,
        });

        // Delete the temp file
        try {
          fs.unlinkSync(filePath);
        } catch (err) {
          Logger.error('Error trying to delete temp file:', err);
        }

        // Calculate remotename and remote url
        const remoteName = uploadInfo.Key.split('/').pop();

        // Try to obtain original filename
        originalFilename = originalFilename.substring(
          0,
          originalFilename.lastIndexOf('.')
        );

        if (originalFilename === '')
          originalFilename = remoteName.substring(
            0,
            remoteName.lastIndexOf('.')
          );

        return resolve({
          containerId: entity.id,
          extension: extname(remoteName),
          name: remoteName,
          originalFilename,
          size,
          mime: fileMime,
        });
      })
      .catch((err) => {
        Logger.error(
          'Error when trying to download the file from this url:',
          url,
          'Error:',
          err.message
        );
        return reject(errors.file_download_error());
      });
  });

export const filterProperties = (req, res, next) => {
  if (!req.user.isAdmin) {
    // Remove from body non-settable properties
    req.body = _.omit(req.body, ['entity']);
  }

  next();
};

export const createDocument = async (req, res) => {
  const data = await Document.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getAllDocuments = async (req, res) => {
  const data = await factory.getAll(Document, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const getDocument = async (req, res) => {
  const data = await factory.getOne(
    Document,
    req,
    {},
    { filterByEntity: true }
  );

  res.status(200).json(data);
};

export const updateDocument = async (req, res) => {
  const data = await factory.updateOne(Document, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const deleteDocument = async (req, res) => {
  const data = await factory.deleteOne(Document, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const restoreDocument = async (req, res) => {
  const data = await factory.restoreOne(Document, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disableDocument = async (req, res) => {
  const data = await factory.disableOne(Document, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enableDocument = async (req, res) => {
  const data = await factory.enableOne(Document, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const preSignedUrl = async (req, res) => {
  const { containerId, fileName, folder = 'hope' } = req.params || {};

  const url = await getDownloadPreSignedUrl({
    bucket: bucketMap[folder],
    key: `${containerId}/${fileName}`,
  });

  res.status(200).json({ url });
};

/*
 * MULTIPLE DOCUMENTS
 */
export const uploadMultipleDocuments = (
  options = { upload: multipleUpload, fieldName: 'files' }
) => {
  const { upload, fieldName } = {
    upload: multipleUpload,
    fieldName: 'files',
    ...options,
  };

  return upload.array(fieldName);
};

export const saveDocuments =
  (fieldName = 'files') =>
  async (req, res) => {
    if (!req.files) throw generateMissingParamsError([fieldName]);

    const { entity, files, body } = req;
    const { privateUpload } = body || {};
    const output = [];

    for (let index = 0; index < files.length; index += 1) {
      const file = files[index];

      const { path: filePath, mimetype, originalname } = file;
      const { size } = file;

      const filename = sanitizeForContentDisposition(originalname);

      // Upload the file
      const uploadInfo = await uploadFile(filePath, {
        bucket: bucketMap.hope,
        key: entity.id,
        acl: privateUpload ? 'private' : 'public-read',
        mime: mimetype,
        contentDisposition: `attachment; filename="${filename}"`,
      });

      // Delete the temp file
      try {
        fs.unlinkSync(filePath);
      } catch (error) {
        Logger.error('Error trying to delete temp file:', error);
      }

      // Calculate remotename and remote url
      const remoteName = uploadInfo.Key.split('/').pop();

      output.push({
        containerId: entity.id,
        extension: extname(remoteName),
        name: remoteName,
        acl: privateUpload ? 'private' : 'public-read', // unless privateUpload is provided, uses public-read
        originalFilename: originalname
          .substring(0, originalname.lastIndexOf('.'))
          .trim(),
        size,
        mime: mimetype,
      });
    }

    res.status(200).json(output);
  };

export const getUploadSignedUrl = async (req, res) => {
  const { fileName, expiresIn } = req.query || {};
  const folder = req.query?.folder?.trim() || 'hope'; // Default folder is 'hope' if coming as empty string or undefined

  if (!fileName) throw generateMissingParamsError(['fileName']);

  const ext = extname(fileName);
  const mime = mimeTypes.lookup(fileName);
  const newFileName = `${randomFilename()}${ext}`; // Filename it will be stored as.

  const { id: entityFolder } = req.entity;

  // The file will be saved under the entity folder, with a random filename and the original extension
  const fileKey = `${entityFolder}/${newFileName}`;

  const signedUrl = await getUploadPreSignedUrl({
    bucket: bucketMap[folder],
    key: fileKey,
    contentType: mimeTypes.lookup(fileName),
    expiresIn, // If provided, uses the expiresIn provided otherwise makes it 5 minutes.
  });

  res.status(200).json({ signedUrl, entityFolder, newFileName, mime });
};

export default {
  getFile,
  uploadDocument,
  saveDocument,
  uploadMultipleDocuments,
  saveDocuments,
  filterProperties,
  createDocument,
  getAllDocuments,
  getDocument,
  updateDocument,
  deleteDocument,
  restoreDocument,
  disableDocument,
  enableDocument,
  preSignedUrl,
  getUploadSignedUrl,
};
