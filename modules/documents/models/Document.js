import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

/**
 * @typedef {object} DocumentFile An object representing a document file
 * @property {String} name The hashed name of the document (e.g. 'abc123.pdf')
 * @property {String} containerId The container ID is the id of the entity where the document was uploaded from  (e.g. '123456')
 * @property {String} extension The extension of the document (e.g. 'pdf')
 * @property {String} mime The mime type of the document (e.g. 'application/pdf')
 * @property {Number} size The size of the document in bytes (e.g. '123456')
 * @property {String} originalFilename  The original filename of the document (e.g. 'my-article.pdf')
 */

/**
 * @typedef {object} Document An object representing a document
 * @property {DocumentFile} file The file of the document
 * @property {String} title The title of the document
 * @property {String} description The description of the document
 * @property {String} author The author of the document
 * @property {String} copyright The copyright of the document
 * @property {String[]} tags The tags of the document
 * @property {String[]} importIDs The import IDs of the document
 * @property {String} entity The entity ID of the document
 */

const documentSchema = SchemaFactory({
  file: {
    type: mongoose.SchemaTypes.Mixed,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  description: String,
  author: String,
  copyright: String,
  tags: [String],
  importIDs: [String],
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
});

documentSchema.index({ title: 1 });
documentSchema.index({ description: 1 });
documentSchema.index({ author: 1 });
documentSchema.index({ tags: 1 });
documentSchema.index({ entity: 1 });

export default mongoose.model('Document', documentSchema);
