import express from 'express';

import {
  authorizeRequest,
  protect,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import documentController from './controllers/documentController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router.post(
  '/upload',
  logRequest({ module: 'documents', action: 'UPLOAD_DOCUMENT' }),
  documentController.uploadDocument(),
  documentController.saveDocument()
);

router.post(
  '/upload-multiple',
  logRequest({ module: 'images', action: 'UPLOAD_MULTIPLE_DOCUMENTS' }),
  documentController.uploadMultipleDocuments(),
  documentController.saveDocuments()
);

router.get(
  '/download/:containerId/:fileName{/:customFilename}',
  documentController.getFile
);

// Upload signed url
router.get(
  '/upload/signedUrl',
  logRequest({ module: 'documents', action: 'GET_UPLOAD_SIGNED_URL' }),
  documentController.getUploadSignedUrl
);

// Pre signed URL for downloading
router.get(
  '/pre-signed-url/:containerId/:fileName{/:folder}',
  documentController.preSignedUrl
);

router
  .route('/')
  .get(documentController.getAllDocuments)
  .post(
    logRequest({ module: 'documents', action: 'CREATE_DOCUMENT' }),
    documentController.filterProperties,
    documentController.createDocument
  );

router
  .route('/:id')
  .get(documentController.getDocument)
  .patch(
    logRequest({ module: 'documents', action: 'UPDATE_DOCUMENT' }),
    documentController.filterProperties,
    documentController.updateDocument
  )
  .delete(
    logRequest({ module: 'documents', action: 'DELETE_DOCUMENT' }),
    documentController.deleteDocument
  );

router
  .route('/:id/restore')
  .patch(
    logRequest({ module: 'documents', action: 'RESTORE_DOCUMENT' }),
    documentController.restoreDocument
  );

router
  .route('/:id/disable')
  .patch(
    logRequest({ module: 'documents', action: 'DISABLE_DOCUMENT' }),
    documentController.disableDocument
  );

router
  .route('/:id/enable')
  .patch(
    logRequest({ module: 'documents', action: 'ENABLE_DOCUMENT' }),
    documentController.enableDocument
  );

export default router;
