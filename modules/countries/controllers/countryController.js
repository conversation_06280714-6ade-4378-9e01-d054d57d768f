import countryServices from '../services/countryServices.js';

export const getCountries = async (req, res) => {
  const { query } = req;
  const { statuses, limit, page, sort, search, locales, exceptions } = query;

  const data = await countryServices.getCountries({
    statuses,
    limit,
    page,
    sort,
    search,
    locales,
    exceptions,
  });

  res.status(200).json(data);
};

export const createCountry = async (req, res) => {
  const country = await countryServices.createCountry(req.body);

  res.status(200).json(country);
};

export const getCountry = async (req, res) => {
  const country = await countryServices.getCountryById(req.params.id);

  res.status(200).json(country);
};

export const getCountryByCode = async (req, res) => {
  const country = await countryServices.getCountryByCode(req.params.code);

  res.status(200).json(country);
};

export const updateCountry = async (req, res) => {
  const updatedCountry = await countryServices.updateCountry({
    countryId: req.params.id,
    data: req.body,
  });

  res.status(200).json(updatedCountry);
};

export const disableCountry = async (req, res) => {
  const updatedCountry = await countryServices.disableCountry({
    countryId: req.params.id,
  });

  res.status(200).json(updatedCountry);
};

export const enableCountry = async (req, res) => {
  const updatedCountry = await countryServices.enableCountry({
    countryId: req.params.id,
  });

  res.status(200).json(updatedCountry);
};

export const deleteCountry = async (req, res) => {
  await countryServices.deleteCountry(req.params.id);

  res.status(204).json({});
};

export default {
  getCountries,
  createCountry,
  getCountry,
  getCountryByCode,
  updateCountry,
  disableCountry,
  enableCountry,
  deleteCountry,
};
