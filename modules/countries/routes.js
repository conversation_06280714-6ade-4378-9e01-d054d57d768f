import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import listSchema from '#utils/api/list/schema.js';
import { validate } from '#utils/validationMiddleware.js';

import countryController from './controllers/countryController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(validate(listSchema, 'query'), countryController.getCountries)
  .post(
    restrictTo({
      module: 'countries',
      permissions: ['create'],
    }),
    logRequest({
      module: 'countries',
      action: 'CREATE_COUNTRY',
    }),
    countryController.createCountry
  );

router.route('/code/:code').get(countryController.getCountryByCode);

router
  .route('/:id')
  .get(countryController.getCountry)
  .patch(
    restrictTo({
      module: 'countries',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({
      module: 'countries',
      action: 'UPDATE_COUNTRY',
    }),
    countryController.updateCountry
  )
  .delete(
    restrictTo({
      module: 'countries',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({
      module: 'countries',
      action: 'DELETE_COUNTRY',
    }),
    countryController.deleteCountry
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'countries',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'countries',
    action: 'DISABLE_COUNTRY',
  }),
  countryController.disableCountry
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'countries',
    permissions: ['delete'],
    paramId: 'id',
  }),
  logRequest({
    module: 'countries',
    action: 'ENABLE_COUNTRY',
  }),
  countryController.enableCountry
);

export default router;
