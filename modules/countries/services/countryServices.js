import getCollationFilter from '#utils/api/collation/filter.js';
import getListFilters from '#utils/api/list/filters.js';
import { errors } from '#utils/appError.js';

import Country from '../models/Country.js';

/**
 * Returns a list of countries
 * @param {Object} params - The params object
 * @param {String[]} params.statuses - List of statuses to filter
 * @param {String[]} params.codes - List of codes to filter
 * @param {String} params.search - Search string
 * @param {Number} params.limit - Number of items to return
 * @param {Number} params.page - Page number
 * @param {String} params.sort - Sort field
 * @param {Number} params.skip - Number of items to skip
 * @param {String} params.language - Collation language
 * @returns {Promise<{ items: Country[], count: Number }>} The response object
 */
export async function getCountries({
  statuses = [],
  codes = [],
  exceptions = [],
  search = '',
  limit = 10,
  page = 1,
  sort = 'name',
  skip = 0,
  language = 'en',
} = {}) {
  // Calculate sort field for name in current language (e.g. name.en)
  const nameSort = `name.${language || 'en'}`;

  const filters = getListFilters({
    statuses,
    limit,
    page,
    skip,
    sort: sort === 'name' ? nameSort : sort,
    sortFields: [nameSort, 'code'],
    search,
    searchFields: [nameSort, 'nativeName', 'code'],
  });

  const matchFiters = {
    $and: [filters.statuses, filters.search],
  };

  if (codes?.length && exceptions?.length) {
    matchFiters.code = { $in: codes, $nin: exceptions };
  } else if (codes?.length) {
    matchFiters.code = { $in: codes };
  } else if (exceptions?.length) {
    matchFiters.code = { $nin: exceptions };
  }

  const collationFilters = getCollationFilter({ language });

  const itemsQuery = Country.find(matchFiters)
    .sort(filters.sort)
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit)
    .collation(collationFilters);

  const items = await itemsQuery;

  const count = await Country.find(matchFiters).countDocuments();

  return { items, count };
}

async function getCountryById(countryId) {
  const country = await Country.findOne({
    _id: countryId,
  });

  return country;
}

async function getCountryByCode(code) {
  const country = await Country.findOne({
    code,
    enabled: true,
    deleted: false,
  });

  if (!country) {
    throw errors.not_found('Country', code);
  }

  return country;
}

async function createCountry(data) {
  // Check for duplicate codes first
  const existingCountry = await Country.findOne({
    code: data.code,
  });

  if (existingCountry) {
    throw errors.already_exists();
  }

  const country = await Country.create({
    ...data,
  });

  return country;
}

async function updateCountry({ countryId, data }) {
  if (!countryId) {
    throw errors.not_found('country');
  }

  // Check for duplicate codes first
  // const existingCountry = await Country.findOne({
  //   code: data.code,
  // });

  // if (existingCountry) {
  //   throw errors.already_exists();
  // }

  // Update data formatting
  if (data.code) data.code = data.code.toUpperCase();
  if (data.phone?.startsWith('+')) data.phone = data.phone.substring(1);

  // Save changes
  const updatedCountry = await Country.findByIdAndUpdate(
    { _id: countryId },
    data,
    { new: true, runValidators: true }
  );

  return updatedCountry;
}

async function disableCountry({ countryId }) {
  if (!countryId) {
    throw errors.not_found('country');
  }

  const updatedCountry = await Country.findByIdAndUpdate(
    { _id: countryId },
    { enabled: false },
    { new: true, runValidators: true }
  );

  return updatedCountry;
}

async function enableCountry({ countryId }) {
  if (!countryId) {
    throw errors.not_found('country');
  }

  const updatedCountry = await Country.findByIdAndUpdate(
    { _id: countryId },
    { enabled: true },
    { new: true, runValidators: true }
  );

  return updatedCountry;
}

async function deleteCountry(countryId) {
  if (!countryId) {
    throw errors.not_found('country', countryId);
  }

  const existingCountry = await Country.findOne({
    _id: countryId,
  });

  if (!existingCountry) {
    throw errors.not_found('country', countryId);
  }

  await Country.deleteOne({ _id: existingCountry._id });
}

export default {
  getCountries,
  getCountryById,
  getCountryByCode,
  createCountry,
  updateCountry,
  disableCountry,
  enableCountry,
  deleteCountry,
};
