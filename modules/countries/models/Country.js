import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const countrySchema = SchemaFactory({
  code: {
    type: String,
    required: true,
    trim: true,
  },
  name: {
    type: mongoose.SchemaTypes.Mixed,
    default: { en: '' },
    set: function (value) {
      // This is a mongoose setter to trim all string values inside the object
      const trimmedValue = {};

      // If it's a string, just trim it
      if (typeof value === 'string') {
        return value.trim();
      }

      for (const key in value) {
        if (typeof value[key] === 'string') {
          trimmedValue[key] = value[key].trim();
        } else {
          trimmedValue[key] = value[key];
        }
      }

      return trimmedValue;
    },
  },
  nativeName: {
    type: String,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
});

countrySchema.index(
  { code: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);
countrySchema.index({ name: 1 });
countrySchema.index({ phone: 1 });

export default mongoose.model('Country', countrySchema);
