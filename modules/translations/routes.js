import express from 'express';

import {
  authorizeRequest,
  protect,
} from '#modules/users/controllers/authController.js';
import translationController from './controllers/translationController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router.route('/translate').post(translationController.translate);

router
  .route('/supported-languages')
  .get(translationController.getSupportedLanguages);

router
  .route('/supported-models/:provider')
  .get(translationController.getSupportedModels);

export default router;
