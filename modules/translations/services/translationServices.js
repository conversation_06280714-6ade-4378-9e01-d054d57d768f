import {
  supportedSourceLanguages,
  supportedTargetLanguages,
} from './providers/deepl/translationLanguages.js';
import {
  getProviderTranslationFn,
  getProviderAvailableModels,
} from './providers/index.js';

/**
 * Translate text using the specified translation provider
 * @param {Object} params The params object
 * @param {Object} params.entity The entity object
 * @param {string} params.text The text to translate
 * @param {boolean} params.type Type of text to translate (json or text)
 * @param {string} params.sourceLang The source language
 * @param {string} params.targetLang The target language
 * @returns {Promise<{text: string, error?: string}>} The translation object
 */
export async function translate({
  entity,
  text,
  type,
  sourceLang,
  targetLang,
} = {}) {
  const { config } = entity;
  const { automaticTranslations } = config || {};
  const {
    apiKey,
    enabled,
    provider = 'openai',
    model,
    richTextByNode = false,
  } = automaticTranslations || {};

  if (!enabled) {
    return {
      text,
      error: 'Automatic translations are not enabled for this entity',
    };
  }

  if (!text || !sourceLang || !targetLang) {
    return {
      text,
      error: 'Automatic translation requires text, source and target',
    };
  }

  // Obtain the translation provider function based on the provider name
  const getTranslatedText = getProviderTranslationFn(provider);

  // Fetch the translation from provider
  const { translatedText, error } = await getTranslatedText({
    text,
    type,
    sourceLang,
    targetLang,
    model,
    byNode: richTextByNode,
    apiKey,
  });

  if (error) {
    return {
      text, // Return the original text if there is an error
      error,
    };
  }

  return {
    text: translatedText,
  };
}

/**
 * Provides the supported  languages array
 * @returns {{target: string[], source: string[]}} The supported languages object
 */
export async function getSupportedLanguages() {
  return {
    target: supportedTargetLanguages,
    source: supportedSourceLanguages,
  };
}

/**
 * @typedef {Object} AITranslationModel
 * @property {string} name The model name
 * @property {string} description The model description
 */

/**
 * Get the available models for a given translation provider
 * @param {String} provider The translation provider name
 * @returns {AITranslationModel[]} List of available models for the provider
 */
export function getAvailableModels(provider) {
  const models = getProviderAvailableModels(provider);

  // Return an array of models with their names and other fields
  return Object.entries(models).map(
    ([name, { description, cost, quality }]) => ({
      name,
      description,
      cost,
      quality,
    })
  );
}

export default {
  translate,
  getSupportedLanguages,
  getAvailableModels,
};
