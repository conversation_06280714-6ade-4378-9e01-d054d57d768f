// Languages that DeepL supports as source languages (see Request Parameters > source_lang in https://www.deepl.com/docs-api/translate-text/multiple-sentences/)
const supportedSourceLanguages = [
  'BG', // Bulgarian
  'CS', // Czech
  'DA', // Danish
  'DE', // German
  'EL', // Greek
  'EN', // English
  'ES', // Spanish
  'ET', // Estonian
  'FI', // Finnish
  'FR', // French
  'HU', // Hungarian
  'ID', // Indonesian
  'IT', // Italian
  'JA', // Japanese
  'KO', // Korean
  'LT', // Lithuanian
  'LV', // Latvian
  'NB', // Norwegian (Bokmål)
  'NL', // Dutch
  'PL', // Polish
  'PT', // Portuguese (all Portuguese varieties mixed)
  'RO', // Romanian
  'RU', // Russian
  'SK', // Slovak
  'SL', // Slovenian
  'SV', // Swedish
  'TR', // Turkish
  'UK', // Ukrainian
  'ZH', // Chinese
];

// Languages that DeepL supports as target languages (see Request Parameters > target_lang in  https://www.deepl.com/docs-api/translate-text/multiple-sentences/)
const supportedTargetLanguages = [
  'BG', // Bulgarian
  'CS', // Czech
  'DA', // Danish
  'DE', // German
  'EL', // Greek
  'EN', // English (unspecified variant for backward compatibility; please select EN-GB or EN-US instead)
  'EN-GB', // English (British)
  'EN-US', // English (American)
  'ES', // Spanish
  'ET', // Estonian
  'FI', // Finnish
  'FR', // French
  'HU', // Hungarian
  'ID', // Indonesian
  'IT', // Italian
  'JA', // Japanese
  'KO', // Korean
  'LT', // Lithuanian
  'LV', // Latvian
  'NB', // Norwegian (Bokmål)
  'NL', // Dutch
  'PL', // Polish
  'PT', // Portuguese (unspecified variant for backward compatibility; please select PT-BR or PT-PT instead)
  'PT-BR', // Portuguese (Brazilian)
  'PT-PT', // Portuguese (all Portuguese varieties excluding Brazilian Portuguese)
  'RO', // Romanian
  'RU', // Russian
  'SK', // Slovak
  'SL', // Slovenian
  'SV', // Swedish
  'TR', // Turkish
  'UK', // Ukrainian
  'ZH', // Chinese (simplified)
];

export { supportedSourceLanguages, supportedTargetLanguages };
