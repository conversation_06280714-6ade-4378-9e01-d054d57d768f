import { getDeepLTranslation } from './deepl/index.js';
import { getOpenAITranslation, availableOpenModels } from './openai/index.js';

/**
 * Available translation providers
 */
const translationProviders = {
  deepl: { translate: getDeepLTranslation, models: {} },
  openai: { translate: getOpenAITranslation, models: availableOpenModels },
};

/**
 * Return a translation provider function based on the provider name
 * @param {'deepl'|'openai'} provider The provider name
 * @return {function} The translation provider function
 */
export function getProviderTranslationFn(provider) {
  return (
    translationProviders[provider]?.translate ??
    translationProviders.openai.translate
  );
}

/**
 * Get the list of available translation providers
 * @returns {Array} List of available translation providers
 */
export function getAvailableTranslationProviders() {
  return Object.keys(translationProviders);
}

/**
 * Get a list of available models for a given translation provider
 * @param {String} provider The translation provider name
 * @returns {Object} List of available models for the provider
 */
export function getProviderAvailableModels(provider) {
  return (
    translationProviders[provider]?.models || translationProviders.openai.models
  );
}
