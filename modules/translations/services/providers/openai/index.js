import getOpenAIRichTextTranslation from './richText.js';
import getOpenAISimpleTextTranslation from './simpleText.js';
import { openAIModels } from './request.js';

const promptTypes = {
  json: getOpenAIRichTextTranslation,
  text: getOpenAISimpleTextTranslation,
};

/**
 * Get the translation of a text using OpenAI
 * @param {Object} params The parameters object
 * @param {String} params.text The text to translate
 * @param {String} params.sourceLang The source language (e.g. 'en')
 * @param {String} params.targetLang The target language (e.g. 'es')
 * @param {String} params.type The type of translation ('text' or 'json')
 * @param {String} params.model The OpenAI model to use for the translation
 * @param {String} params.apiKey The OpenAI API key
 * @param {Boolean} params.byNode Whether to translate the document by node or as a whole
 * @returns {Promise<{text: string, error?: string}>} Object with the translated text or an error
 */
export async function getOpenAITranslation({
  text,
  sourceLang,
  targetLang,
  type = 'text',
  model,
  apiKey,
  byNode,
}) {
  // Get the prompt request function based on the type
  const promptRequest = promptTypes[type];

  // Check if the prompt request is valid
  if (!promptRequest) {
    return {
      text,
      error: `Invalid translation type provided. Please provide a valid type (${Object.keys(
        promptTypes
      )
        .join(', ')
        .map((t) => `"${t}"`)}`,
    };
  }

  // Fetch the translation from provider
  return await promptRequest({
    sourceLang,
    targetLang,
    text,
    apiKey,
    model,
    byNode: byNode && type === 'json',
  });
}

/**
 * Expose the available models for OpenAI
 **/
export const availableOpenModels = openAIModels;
