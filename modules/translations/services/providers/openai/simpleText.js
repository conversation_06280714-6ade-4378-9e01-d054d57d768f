import getOpenAIRequest from './request.js';

export default async function getOpenAISimpleTextTranslation({
  sourceLang,
  targetLang,
  text,
  apiKey,
  model,
}) {
  return await getOpenAIRequest({
    prompt: `Translate the following text string from locale "${sourceLang}" to locale "${targetLang}", in a formal but friendly language. Don't include any comments or notes in the response nor extra quotes arround the text, just the translated text. The text to translate is the following:\n "${text}"`,
    apiKey,
    model,
  });
}
