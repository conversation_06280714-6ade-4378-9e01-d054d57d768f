import getOpenAIRequest from './request.js';

/**
 * @typedef {import('./request.js').OpenAIResponse} OpenAIResponse
 */

/**
 * Get the translation of a rich text document using OpenAI
 * @param {Object} params The parameters object
 * @param {String} params.sourceLang The source language (e.g. 'en')
 * @param {String} params.targetLang The target language (e.g. 'es')
 * @param {String} params.text The rich text document to translate, as a JSON string
 * @param {String} params.apiKey The Open
 * @param {String} params.model The OpenAI model to use for the translation
 * @param {Boolean} params.byNode Whether to translate the document by node or as a whole
 * @returns {Promise<OpenAIResponse>} Object with the translated text or an error
 */
export default async function getOpenAIRichTextTranslation({
  sourceLang,
  targetLang,
  text,
  apiKey,
  model,
  byNode = false,
}) {
  // If the translation is requested by node, translate the document by node
  if (byNode) {
    return await translateRichTextByNode({
      text,
      sourceLang,
      targetLang,
      apiKey,
      model,
    });
  }

  // Otherwise translate the whole document at once
  return await getOpenAIRequest({
    prompt: `You are an expert translator. Translate a JSON object from locale "${sourceLang}" to locale "${targetLang}", in a formal but friendly language. Don't translate object keys, and only translate values of entries where the key is named "text" or "caption", and avoid translating "copyright" values. IMPORTANT: Return only a valid JSON object as output, and do not wrap the code with a markdown code format. The JSON object to translate is the following:
      "${text}".`,
    params: {
      response_format: { type: 'json_object' }, // Ensure the response is a JSON object
    },
    apiKey,
    model,
  });
}

// Basic TipTap document structure
const baseDoc = {
  type: 'doc',
  content: [],
};

/**
 * Translate a rich text document by node
 * @param {Object} params The parameters object
 * @param {String} params.sourceLang The source language (e.g. 'en')
 * @param {String} params.targetLang The target language (e.g. 'es')
 * @param {String} params.text The rich text document to translate, as a JSON string
 * @param {String} params.apiKey The OpenAI API key
 * @param {String} params.model The OpenAI model to use for the translation
 * @returns {Promise<OpenAIResponse>} The translated text
 */
async function translateRichTextByNode({
  text,
  sourceLang,
  targetLang,
  apiKey,
  model,
}) {
  const doc = JSON.parse(text);

  // Translate each node in the document (e.g. paragraphs, headings, lists, etc.)
  // Use Promise.all to translate all nodes concurrently.
  // NOTE: Even when this makes multiple requests to the OpenAI API, spliting the document into nodes and processing them sequentially, is faster than making a single request to translate the whole document.
  const translatedNodes = await Promise.all(
    doc.content.map(async (node) => {
      const { translatedText, error } = await getOpenAIRequest({
        prompt: `You are an expert translator. You will get a JSON object to translate, provided bellow as "JSON_OBJECT". It's part of a larger object, provided bellow as "CONTEXT". Translate the JSON object from locale "${sourceLang}" to locale "${targetLang}", in a formal but friendly language. Don't translate object keys, and only translate values of entries where the key is named "text" or "caption", and avoid translating "copyright" values. IMPORTANT: Return only a valid JSON object as output, and do not wrap the code with a markdown code format.

          JSON_OBJECT: "${JSON.stringify(node)}"

          CONTEXT: "${JSON.stringify(text)}"`,
        params: {
          response_format: { type: 'json_object' }, // Ensure the response is a JSON object
        },
        apiKey,
        model,
      });

      // skip node if there was an error
      if (error) {
        return node;
      }

      return JSON.parse(translatedText);
    })
  );

  const translatedDoc = {
    ...baseDoc,
    content: translatedNodes,
  };

  return {
    translatedText: JSON.stringify(translatedDoc),
  };
}
