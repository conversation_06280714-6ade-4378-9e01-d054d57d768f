import translationServices from '../services/translationServices.js';

export const translate = async (req, res) => {
  const { body, entity } = req;
  const { text, type, source, target } = body;

  const data = await translationServices.translate({
    entity,
    text,
    type,
    sourceLang: source,
    targetLang: target,
  });

  res.status(200).json(data);
};

export const getSupportedLanguages = async (req, res) => {
  const data = translationServices.getSupportedLanguages();
  res.status(200).json(data);
};

export const getSupportedModels = async (req, res) => {
  const { provider } = req.params;

  const data = translationServices.getAvailableModels(provider);
  res.status(200).json(data);
};

export default {
  translate,
  getSupportedLanguages,
  getSupportedModels,
};
