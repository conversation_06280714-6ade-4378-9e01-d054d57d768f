import express from 'express';

import {
  authorizeRequest,
  protect,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import imageController from './controllers/imageController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router.post(
  '/upload',
  logRequest({ module: 'images', action: 'UPLOAD_IMAGE' }),
  imageController.uploadImage(),
  imageController.cropAndSaveImage()
);

router.post(
  '/upload-multiple',
  logRequest({ module: 'images', action: 'UPLOAD_MULTIPLE_IMAGES' }),
  imageController.uploadMultipleImages(),
  imageController.saveImages()
);

router.post(
  '/upload-remote',
  logRequest({ module: 'images', action: 'UPLOAD_REMOTE_IMAGE' }),
  imageController.uploadRemoteImage
);

router
  .route('/')
  .get(imageController.getAllImages)
  .post(
    logRequest({ module: 'images', action: 'CREATE_IMAGE' }),
    imageController.filterProperties,
    imageController.createImage
  );

router
  .route('/:id')
  .get(imageController.getImage)
  .patch(
    logRequest({ module: 'images', action: 'UPDATE_IMAGE' }),
    imageController.filterProperties,
    imageController.updateImage
  )
  .delete(
    logRequest({ module: 'images', action: 'DELETE_IMAGE' }),
    imageController.deleteImage
  );

router
  .route('/:id/restore')
  .patch(
    logRequest({ module: 'images', action: 'RESTORE_IMAGE' }),
    imageController.restoreImage
  );

router
  .route('/:id/disable')
  .patch(
    logRequest({ module: 'images', action: 'DISABLE_IMAGE' }),
    imageController.disableImage
  );

router
  .route('/:id/enable')
  .patch(
    logRequest({ module: 'images', action: 'ENABLE_IMAGE' }),
    imageController.enableImage
  );

export default router;
