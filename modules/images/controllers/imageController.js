import _ from 'lodash';
import ky from 'ky';
import fs from 'fs';
import { extname } from 'path';
import mimeTypes from 'mime-types';
import { once } from 'events';
import { finished } from 'stream';
import { promisify } from 'util';

import {
  errorCodes,
  errors,
  generateError,
  generateMissingParamsError,
} from '#utils/appError.js';
import {
  cropImage,
  encodeImageToBlurhash,
  fileTypes,
  getCropValues,
  getImageSizeData,
  resizeImage,
} from '#utils/files.js';
import factory from '#utils/handlerFactory.js';
import {
  getUpload,
  randomFilename,
  removeFile,
  uploadFile,
} from '#utils/storage.js';
import Logger from '#utils/logger.js';
import Image from '../models/Image.js';

import { getOriginalFileName } from '../services/imageServices.js';

// The default version of stream.finished() is callback-based but can be turned into a Promise-based version via util.promisify()
const streamFinished = promisify(finished);

const allowedContentTypes = fileTypes.anyImage;
const maxFileCount = process.env.MAX_IMAGE_FILE_COUNT || 50;
const maxFileSize = process.env.MAX_IMAGE_FILE_SIZE || '15mb';
const bucketImages = process.env.BUCKET_IMAGES || 'hope-images';

const defaultUpload = getUpload({
  folder: bucketImages,
  maxFileSize,
  allowedContentTypes,
});

const multipleUpload = getUpload({
  folder: bucketImages,
  maxFileSize,
  allowedContentTypes,
  maxFileCount,
});

export const saveRemoteImage = (
  imageUrl,
  entity,
  getOriginalFileNameFn = getOriginalFileName
) =>
  new Promise((resolve, reject) => {
    const url = new URL(imageUrl);
    const urlString = url.toString();

    let tempOutput;
    let filePath;
    let error;

    ky.get(urlString)
      .then(async (res) => {
        let originalFilename = urlString.split('/').pop().split(/\?|#/).shift();

        // Try to get the mime-type from the file name
        let fileExtension = originalFilename.split('.').pop();
        let fileMime = mimeTypes.lookup(fileExtension);

        // If it cannot be detected, try to obtain from headers
        if (
          !fileMime &&
          mimeTypes.contentType(res.headers.get('content-type'))
        ) {
          fileMime = mimeTypes
            .contentType(res.headers.get('content-type'))
            .split(';')
            .shift();

          // And detect the file extension from the mime-type
          fileExtension = mimeTypes.extension(fileMime);
        }

        const stream = res.body;
        let isFirstChunk = true;

        if (
          allowedContentTypes.length > 0 &&
          !allowedContentTypes.includes(fileMime)
        ) {
          stream.destroy();
          return reject(
            generateError(
              `File type "${fileMime}" is not allowed. Must be in [${allowedContentTypes.join(
                ', '
              )}].`,
              errorCodes.FILE_TYPE_NOT_ALLOWED,
              400
            )
          );
        }

        // Generate a random file name
        const filename = randomFilename();

        // This is to reject the download if it exceeds the max file size defined
        for await (const chunk of stream) {
          if (isFirstChunk) {
            isFirstChunk = false;

            try {
              // Use the generated random filename, with the extension provided by fileType
              filePath = `temp/${bucketImages}/${filename}.${fileExtension}`;

              tempOutput = fs.createWriteStream(filePath);
            } catch (err) {
              Logger.error('Error trying to read file info:', err);
              error = errors.file_download_error();
              stream.destroy();
            }
          }

          if (!error) {
            if (tempOutput) {
              if (!tempOutput.write(Buffer.from(chunk))) {
                // Handle backpressure
                await once(tempOutput, 'drain');
              }
            }

            if (fs.existsSync(filePath)) {
              const stats = fs.statSync(filePath);
              if (stats.size > maxFileSize) {
                // If the file size exceeds the allowed, end the stream
                error = errors.file_too_big();
                stream.destroy();
              }
            }
          }
        }

        if (tempOutput) {
          tempOutput.end();
          // Wait until done. Throws if there are errors.
          await streamFinished(tempOutput);
        }

        if (error) {
          // If an error occurred delete the file and return the error
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
          return reject(error);
        }
        // Get image width and height
        let { width, height } = await getImageSizeData(filePath);

        // Get file size
        let { size } = fs.statSync(filePath);
        const resolution = width * height;

        // If image is bigger than 16.5 megapixels, resize it
        if (resolution > 16500000) {
          // Resizing to fit in 16 megapixels (5333x3000)
          await resizeImage(filePath, filePath, 5333, 3000);

          // Obtain new image size and dimensions
          const { width: newWidth, height: newHeight } =
            await getImageSizeData(filePath);
          width = newWidth;
          height = newHeight;
          // eslint-disable-next-line
          size = fs.statSync(filePath).size;
        }

        // Upload the file
        const uploadInfo = await uploadFile(filePath, {
          bucket: bucketImages,
          key: entity.id,
          mime: fileMime,
        });

        const blurhash = await encodeImageToBlurhash(filePath);

        // Delete the temp file
        try {
          fs.unlinkSync(filePath);
        } catch (err) {
          Logger.error('Error trying to delete temp file:', err);
        }

        // Calculate remoteName and remote url
        const remoteName = uploadInfo.Key.split('/').pop();

        // Try to obtain original filename
        originalFilename = getOriginalFileNameFn(originalFilename);

        if (originalFilename === '')
          originalFilename = remoteName.substring(
            0,
            remoteName.lastIndexOf('.')
          );

        return resolve({
          containerId: entity.id,
          extension: extname(remoteName),
          name: remoteName,
          originalFilename,
          size,
          mime: fileMime,
          width,
          height,
          blurhash,
        });
      })
      .catch((err) => {
        Logger.error(
          'Error when trying to download the file from this url:',
          imageUrl,
          'Error:',
          err.message
        );
        return reject(errors.file_download_error());
      });
  });

export const getUploader = (options = { maxFileSize, allowedContentTypes }) =>
  getUpload({
    ...options,
    folder: bucketImages,
  });

export const uploadImage = (
  options = { upload: defaultUpload, fieldName: 'file' }
) => {
  const { upload, fieldName } = {
    upload: defaultUpload,
    fieldName: 'file',
    ...options,
  };

  return upload.single(fieldName);
};

export const cropAndSaveImage =
  (fieldName = 'file', cropFieldName = 'crop') =>
  async (req, res) => {
    if (!req.file) throw generateMissingParamsError([fieldName]);

    const { entity, file } = req;
    const { path: filePath, mimetype, originalname } = file;
    let { size } = file;
    let crop;

    if (cropFieldName && req.body[cropFieldName]) {
      try {
        crop = JSON.parse(req.body[cropFieldName]);
      } catch (e) {
        Logger.error('Could not parse crop values:', e);
      }
    }

    // Get image width and height
    let { width, height } = await getImageSizeData(filePath);

    // Sharp doesn't support cropping gifs
    if (!mimetype.includes('gif') && crop) {
      const { x, y, cropWidth, cropHeight } = getCropValues(
        crop,
        width,
        height
      );

      await cropImage(filePath, filePath, cropWidth, cropHeight, x, y);

      width = cropWidth;
      height = cropHeight;
      // eslint-disable-next-line
      size = fs.statSync(filePath).size;
    }

    const resolution = width * height;
    // If image is bigger than 16.5 megapixels, resize it
    if (resolution > 16500000) {
      // Resizing to fit in 16 megapixels (5333x3000)
      await resizeImage(filePath, filePath, 5333, 3000);

      // Obtain new image size and dimensions
      const { width: newWidth, height: newHeight } =
        await getImageSizeData(filePath);
      width = newWidth;
      height = newHeight;
      // eslint-disable-next-line
      size = fs.statSync(filePath).size;
    }

    // Upload the file
    const uploadInfo = await uploadFile(filePath, {
      bucket: bucketImages,
      key: entity.id,
      mime: mimetype,
    });

    const blurhash = await encodeImageToBlurhash(filePath);

    // Delete the temp file
    try {
      fs.unlinkSync(filePath);
    } catch (error) {
      Logger.error('Error trying to delete temp file:', error);
    }

    // Calculate remoteName and remote url
    const remoteName = uploadInfo.Key.split('/').pop();

    res.status(200).json({
      containerId: entity.id,
      extension: extname(remoteName),
      name: remoteName,
      originalFilename: originalname
        .substring(0, originalname.lastIndexOf('.'))
        .trim(),
      size,
      mime: mimetype,
      width,
      height,
      blurhash,
    });
  };

export const uploadMultipleImages = (
  options = { upload: multipleUpload, fieldName: 'files' }
) => {
  const { upload, fieldName } = {
    upload: multipleUpload,
    fieldName: 'files',
    ...options,
  };

  return upload.array(fieldName);
};

export const saveImages =
  (fieldName = 'files') =>
  async (req, res) => {
    if (!req.files) throw generateMissingParamsError([fieldName]);

    const { entity, files } = req;
    const output = [];

    for (let index = 0; index < files.length; index += 1) {
      const file = files[index];

      const { path: filePath, mimetype, originalname } = file;
      const { size } = file;
      const { width, height } = await getImageSizeData(filePath);

      // Upload the file
      const uploadInfo = await uploadFile(filePath, {
        bucket: bucketImages,
        key: entity.id,
        mime: mimetype,
      });

      const blurhash = await encodeImageToBlurhash(filePath);

      // Delete the temp file
      try {
        fs.unlinkSync(filePath);
      } catch (error) {
        Logger.error('Error trying to delete temp file:', error);
      }

      // Calculate remoteName and remote url
      const remoteName = uploadInfo.Key.split('/').pop();

      output.push({
        containerId: entity.id,
        extension: extname(remoteName),
        name: remoteName,
        originalFilename: originalname
          .substring(0, originalname.lastIndexOf('.'))
          .trim(),
        size,
        mime: mimetype,
        width,
        height,
        blurhash,
      });
    }

    res.status(200).json(output);
  };

export const uploadRemoteImage = async (req, res) => {
  if (!req.body.url) throw generateMissingParamsError(['url']);

  const fileData = await saveRemoteImage(req.body.url, req.entity);

  res.status(200).json(fileData);
};

export const deleteImageFile = (imagePath) =>
  removeFile(bucketImages, imagePath);

export const filterProperties = (req, res, next) => {
  if (!req.user.isAdmin) {
    // Remove from body non-settable properties
    req.body = _.omit(req.body, ['entity']);
  }

  next();
};

export const createImage = async (req, res) => {
  const data = await Image.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getAllImages = async (req, res) => {
  const data = await factory.getAll(Image, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const getImage = async (req, res) => {
  const data = await factory.getOne(Image, req, {}, { filterByEntity: true });

  res.status(200).json(data);
};

export const updateImage = async (req, res) => {
  const data = await factory.updateOne(Image, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const deleteImage = async (req, res) => {
  const data = await factory.deleteOne(Image, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const restoreImage = async (req, res) => {
  const data = await factory.restoreOne(Image, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const disableImage = async (req, res) => {
  const data = await factory.disableOne(Image, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const enableImage = async (req, res) => {
  const data = await factory.enableOne(Image, req, { filterByEntity: true });

  res.status(200).json(data);
};

export default {
  getUploader,
  uploadImage,
  uploadMultipleImages,
  uploadRemoteImage,
  cropAndSaveImage,
  saveImages,
  saveRemoteImage,
  deleteImageFile,
  filterProperties,
  createImage,
  getAllImages,
  getImage,
  updateImage,
  deleteImage,
  restoreImage,
  disableImage,
  enableImage,
};
