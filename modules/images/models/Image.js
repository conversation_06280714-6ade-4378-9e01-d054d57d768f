import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';
/**
 * @typedef {object} ImageFile An object representing an image file
 * @property {String} name The hashed name of the image (e.g. 'abc123.jpg')
 * @property {String} containerId The container ID is the id of the entity where the image was uploaded from  (e.g. '123456')
 * @property {String} extension The extension of the image (e.g. 'jpg')
 * @property {String} mime The mime type of the image (e.g. 'image/jpeg')
 * @property {Number} size The size of the image in bytes (e.g. '123456')
 * @property {String} originalFilename  The original filename of the image (e.g. 'cover.jpg')
 * @property {Number} width The width of the image in pixels (e.g. 800)
 * @property {Number} height The height of the image in pixels (e.g. 600)
 * @property {String} blurhash A compact representation of a placeholder for the image. See https://blurha.sh (e.g. 'L5H2EC=PM+yV0g-mq.wG9c010J}I')
 */

/**
 * @typedef {object} Image An object representing an image
 * @property {ImageFile} file The file of the image
 * @property {String} caption The caption of the image
 * @property {String} description The description of the image
 * @property {String} author The author of the image
 * @property {Date} takenAt The date the image was taken
 * @property {String} location The location where the image was taken
 * @property {String} source The source of the image
 * @property {String[]} tags The tags of the image
 * @property {String[]} importIDs The import IDs of the image
 * @property {String} entity The entity ID of the image
 */
const imageSchema = SchemaFactory({
  file: {
    type: mongoose.SchemaTypes.Mixed,
    required: true,
  },
  caption: {
    type: String,
    required: true,
  },
  description: String,
  author: String,
  takenAt: Date,
  location: String,
  source: String,
  tags: [String],
  importIDs: [String],
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
});

imageSchema.index({ caption: 1 });
imageSchema.index({ description: 1 });
imageSchema.index({ author: 1 });
imageSchema.index({ takenAt: 1 });
imageSchema.index({ tags: 1 });
imageSchema.index({ entity: 1 });

export default mongoose.model('Image', imageSchema);
