import {
  getJetstreamEpisodeProgress,
  getMediaUploadUrl,
} from '#utils/jetstream.js';
import Logger from '#utils/logger.js';
import getCanonicalEpisodeUrl from '../helpers/getCanonicalEpisodeUrl.js';

import { POPULATE_EPISODE } from '../helpers/populateEpisode.js';
import episodesServices from '../services/episodesServices.js';

export const getAllEpisodes = async (req, res) => {
  const { showId, channelId } = req.params;
  const {
    categories,
    ids,
    ignoreIds,
    limit,
    page,
    search,
    seasons,
    sort,
    sortOrder,
    statuses,
  } = req.query;

  const { items, count, error } = await episodesServices.getEpisodes({
    categories,
    channelId,
    ids,
    ignoreIds,
    limit,
    page,
    search,
    seasons,
    showId,
    sort,
    sortOrder,
    statuses,
  });

  if (error) {
    throw error;
  }

  res.status(200).json({ count, items });
};

export const getEpisode = async (req, res) => {
  const language = req.query.language ?? req.user.preferences.language;
  const { fields } = req.query;

  const { episode, error } = await episodesServices.getEpisodeById(
    req.params.episodeId,
    {
      fields,
      populate: [
        ...POPULATE_EPISODE,
        {
          path: 'categories',
          match: { deleted: false, enabled: true },
          select: `title.${language} title.en`,
        },
        {
          path: 'segments',
          match: { deleted: false, enabled: true },
          select: fields,
        },
      ],
    }
  );

  if (error) {
    throw error;
  }

  const leanEpisode = episode.toObject();

  const url = await getCanonicalEpisodeUrl(leanEpisode._id);
  if (url) {
    leanEpisode.canonicalUrl = url;
  }

  res.status(200).json({
    ...leanEpisode,
    categories:
      leanEpisode.categories?.map((c) => ({
        id: c._id,
        title: c.title[language] || c.title.en,
      })) || [],
    isTranslation: !!leanEpisode.translationOf,
  });
};

export const createEpisode = async (req, res) => {
  const { episode, error } = await episodesServices.createEpisode({
    showId: req.params.showId,
    body: req.body,
    entity: req.entity,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(episode);
};

export const getJetstreamProgress = async (req, res) => {
  const { episodeId } = req.params;

  try {
    const progress = await getJetstreamEpisodeProgress(req, episodeId);

    return res.status(200).json(progress);
  } catch (error) {
    // If this point is reached, the episode is not being processed, and an error should be thrown.
    Logger.error('Could not get progress of processed jetstream media', {
      error,
    });
    return res.status(200).json({ status: 'ERROR', percentage: 0 });
  }
};

export const updateEpisode = async (req, res) => {
  const { entity } = req;

  const { episode, error } = episodesServices.updateEpisode({
    episodeId: req.params.episodeId,
    body: req.body,
    entity,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(episode);
};

export const moveEpisode = async (req, res) => {
  const { season, show } = req.body;

  const { episode, error } = await episodesServices.moveEpisode({
    episodeId: req.params.episodeId,
    targetSeasonId: season,
    targetShowId: show,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(episode);
};

export const deleteEpisode = async (req, res) => {
  const { entity, params } = req;
  const { episodeId } = params;

  const { episode, error } = episodesServices.deleteEpisode({
    episodeId,
    entity,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(episode);
};

export const restoreEpisode = async (req, res) => {
  const { episode, error } = episodesServices.restoreEpisode({
    episodeId: req.params.episodeId,
    entity: req.entity,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(episode);
};

export const disableEpisode = async (req, res) => {
  const { entity, params } = req;
  const { episodeId } = params;

  const { episode, error } = episodesServices.disableEpisode({
    episodeId,
    entity,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(episode);
};

export const enableEpisode = async (req, res) => {
  const { episode, error } = episodesServices.enableEpisode({
    episodeId: req.params.episodeId,
    entity: req.entity,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(episode);
};

export const getJetstreamSignedUrl = async (req, res) => {
  const { fileType } = req.query;

  const data = await getMediaUploadUrl({
    fileType,
    origin: req.headers.origin,
  });

  res.status(200).json(data);
};

export const getEpisodeTranslations = async (req, res) => {
  const { entity, query } = req;
  const { episodeId } = req.params;

  const episodeTranslations = await episodesServices.getEpisodeTranslations({
    episodeId,
    entity,
    query,
  });

  res.status(200).json(episodeTranslations);
};

export const addEpisodeTranslation = async (req, res) => {
  const { entity, params } = req;
  const { episodeId, language } = params;

  const { episode, error } = await episodesServices.addEpisodeTranslation(
    episodeId,
    language,
    entity
  );

  if (error) {
    throw error;
  }

  res.status(200).json(episode);
};

export const getLinkedContent = async (req, res) => {
  const { entity, params, query } = req;
  const { episodeId } = params;
  const { type, sort, order } = query;

  const { linkedItems, error } = await episodesServices.getEpisodeLinkedItems({
    episodeId,
    entity,
    sort,
    order,
    type,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(linkedItems);
};

export const addLinkedContent = async (req, res) => {
  const { params, body } = req;
  const { episodeId } = params;

  const { type, defaultItem } = body;

  let updatedEpisode = null;

  if (type === 'article') {
    const { error, episode } = await episodesServices.addLinkedArticle({
      episodeId,
      articleId: body.articleId,
      defaultItem,
    });

    if (error) {
      throw error;
    }

    updatedEpisode = episode;
  }

  res.status(200).json(updatedEpisode);
};

export const updateLinkedContent = async (req, res) => {
  const { params, body } = req;
  const { episodeId, type, linkedItemId } = params;

  let updatedEpisode = null;

  if (type === 'article') {
    const { error, episode } = await episodesServices.updateLinkedArticle({
      episodeId,
      linkedItemId,
      ...body,
    });

    if (error) {
      throw error;
    }

    updatedEpisode = episode;
  }

  res.status(200).json(updatedEpisode);
};

export const deleteLinkedContent = async (req, res) => {
  const { params } = req;
  const { episodeId, type, linkedItemId } = params;

  let updatedEpisode = null;

  if (type === 'article') {
    const { error, episode } = await episodesServices.deleteLinkedArticle({
      episodeId,
      linkedItemId,
    });

    if (error) {
      throw error;
    }

    updatedEpisode = episode;
  }

  res.status(200).json(updatedEpisode);
};

export default {
  getAllEpisodes,
  getEpisode,
  createEpisode,
  updateEpisode,
  moveEpisode,
  deleteEpisode,
  restoreEpisode,
  disableEpisode,
  enableEpisode,
  getJetstreamSignedUrl,
  getJetstreamProgress,
  getEpisodeTranslations,
  addEpisodeTranslation,
  getLinkedContent,
  addLinkedContent,
  updateLinkedContent,
  deleteLinkedContent,
};
