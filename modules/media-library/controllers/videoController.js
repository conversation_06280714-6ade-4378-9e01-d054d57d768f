// import {errors} from '#utils/appError.js';

import { isEmpty } from '#utils/arrays.js';
import factory from '#utils/handlerFactory.js';
import { areEqualIDs } from '#utils/helpers.js';
import { slugify } from '#utils/strings.js';

import { createOrUpdateMediaLinks } from '../helpers/createOrUpdateMediaLinks.js';
import Channel from '../models/Channel.js';
import Episode from '../models/Episode.js';
import Show from '../models/Show.js';
import Video from '../models/Video.js';

export const getAllVideos = async (req, res) => {
  const { showId, episodeId } = req.params;

  let model;
  let paramId;

  if (showId) {
    model = Show;
    paramId = 'showId';
  } else if (episodeId) {
    model = Episode;
    paramId = 'episodeId';
  }

  const { videos } = await factory.getOne(model, req, {
    paramId,
    populate: [
      {
        path: 'videos',
        select: 'slug title enabled deleted',
        match: { deleted: false },
      },
    ],
  });

  res.status(200).json(videos);
};

export const getVideo = async (req, res) => {
  const { fields, populate } = req.query;

  const options = {
    paramId: 'videoId',
    fields: fields,
    populate: populate ? JSON.parse(populate) : [],
  };

  const data = await factory.getOne(Video, req, options);

  res.status(200).json(data);
};

export const createVideo = async (req, res) => {
  const { showId, episodeId } = req.params;

  let episode;
  let show;

  if (episodeId) {
    episode = await factory.getOne(Episode, req, {
      paramId: 'episodeId',
      fields: 'id slug title show videos',
    });
    show = await Show.findById(episode.show, 'id slug title videos channel');
  } else if (showId) {
    show = await factory.getOne(Show, req, {
      paramId: 'showId',
      fields: 'id slug title videos channel',
    });
  }

  const channel = await Channel.findById(show.channel, 'vimeo');

  // Create media links (and title/image for YouTube and Jetstream)
  if (req.body.mediaLinks) {
    const { body, error: mediaLinksError } = await createOrUpdateMediaLinks({
      body: req.body,
      channel,
      entity: req.entity,
    });

    if (mediaLinksError) {
      throw mediaLinksError;
    }

    req.body = body;

    // Merge with properties of schema
    req.body.mediaLinks = {
      ...Video.schema.obj.mediaLinks.default,
      ...req.body.mediaLinks,
    };
  }

  // Ensure video has a valid slug within its siblings (or create one from its title)
  const slug = await Video.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    show
  );

  // Create the video
  const video = await Video.create({
    ...req.body,
    slug,
    show: show.id,
    ...(episode && { episode: episode.id }),
  });

  // Add video to episode
  if (episodeId) {
    episode.videos = [...episode.videos, video.id];
    await episode.save();
  }

  // Add video to show
  else if (showId) {
    show.videos = [...show.videos, video.id];
    await show.save();
  }

  res.status(200).json(video);
};

export const updateVideo = async (req, res) => {
  const video = await factory.getOne(Video, req, { paramId: 'videoId' });
  const show = await Show.findById(video.show, 'channel').populate({
    path: 'channel',
    select: 'vimeo',
  });

  // Update media links (and title/image for YouTube and Jetstream)
  if (req.body.mediaLinks) {
    const { body, error: mediaLinksError } = await createOrUpdateMediaLinks({
      body: req.body,
      channel: show.channel,
      episode: video,
      entity: req.entity,
    });

    if (mediaLinksError) {
      throw mediaLinksError;
    }

    req.body = body;

    // Merge with properties of schema and video
    req.body.mediaLinks = {
      ...Video.schema.obj.mediaLinks.default,
      ...video.mediaLinks,
      ...req.body.mediaLinks,
    };
  }

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Video.getAvailableSlug(
        slugify(req.body.slug),
        video.show,
        video._id
      )
    : video.slug;

  // Update video's data
  const updatedVideo = await Video.findByIdAndUpdate(
    video._id,
    {
      ...req.body,
      slug,
      show: video.show, // prevents moving a video to different show
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedVideo);
};

export const deleteVideo = async (req, res) => {
  const deletedVideo = await factory.deleteOne(Video, req, {
    paramId: 'videoId',
  });

  // Remove video from show
  const show = await Show.findById(deletedVideo.show, 'videos');
  if (show && !isEmpty(show.videos)) {
    show.videos = show.videos.filter(
      (video) => !areEqualIDs(video._id, deletedVideo._id)
    );
    await show.save();
  }

  // Remove video from episode
  if (deletedVideo.episode) {
    const episode = await Episode.findById(deletedVideo.episode, 'videos');
    if (episode && !isEmpty(episode.videos)) {
      episode.videos = episode.videos.filter(
        (video) => !areEqualIDs(video._id, deletedVideo._id)
      );
      await episode.save();
    }
  }

  res.status(200).json(deletedVideo);
};

export const restoreVideo = async (req, res) => {
  const restoredVideo = await factory.restoreOne(Video, req, {
    paramId: 'videoId',
  });

  // Add video to episode
  if (restoredVideo.episode) {
    const episode = await Episode.findById(restoredVideo.episode, 'videos');
    if (episode && !isEmpty(episode.videos)) {
      episode.videos = [
        ...episode.videos.filter(
          (video) => !areEqualIDs(video._id, restoredVideo._id)
        ),
        restoredVideo._id,
      ];
      await episode.save();

      // Disable restored video
      restoredVideo.enabled = false;
      await restoredVideo.save();
    }
  }

  res.status(200).json(restoredVideo);
};

export const disableVideo = async (req, res) => {
  const data = await factory.disableOne(Video, req, { paramId: 'videoId' });

  res.status(200).json(data);
};

export const enableVideo = async (req, res) => {
  const data = await factory.enableOne(Video, req, { paramId: 'videoId' });

  res.status(200).json(data);
};

export default {
  getAllVideos,
  getVideo,
  createVideo,
  updateVideo,
  deleteVideo,
  restoreVideo,
  disableVideo,
  enableVideo,
};
