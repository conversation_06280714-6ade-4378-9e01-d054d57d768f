import { areEqualIDs } from '#utils/helpers.js';
import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';
import { isEmpty } from '#utils/arrays.js';

import Show from '../models/Show.js';
import VideoCategory from '../models/VideoCategory.js';

export const getAllVideoCategories = async (req, res) => {
  const { videoCategories } = await factory.getOne(Show, req, {
    paramId: 'showId',
    populate: [
      {
        path: 'videoCategories',
        match: { deleted: false },
      },
    ],
    fields: ['videoCategories'],
  });

  res.status(200).json({
    count: videoCategories.length,
    items: videoCategories,
  });
};

export const getVideoCategory = async (req, res) => {
  const data = await factory.getOne(VideoCategory, req, {
    paramId: 'videoCategoryId',
  });

  res.status(200).json(data);
};

export const createVideoCategory = async (req, res) => {
  const show = await factory.getOne(Show, req, { paramId: 'showId' });

  // Ensure video category has a valid slug within its siblings (or create one from its title)
  const slug = await VideoCategory.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    show
  );

  const videoCategory = await VideoCategory.create({
    ...req.body,
    slug,
    channel: show.channel,
    show,
  });

  // Add video category to show
  show.videoCategories = [...show.videoCategories, videoCategory.id];
  await show.save();

  res.status(200).json(videoCategory);
};

export const updateVideoCategory = async (req, res) => {
  const videoCategory = await factory.getOne(VideoCategory, req, {
    paramId: 'videoCategoryId',
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await VideoCategory.getAvailableSlug(
        slugify(req.body.slug),
        videoCategory.show,
        videoCategory._id
      )
    : videoCategory.slug;

  // Update video categorie's data
  const updatedVideoCategory = await VideoCategory.findByIdAndUpdate(
    videoCategory._id,
    {
      ...req.body,
      slug,
      show: videoCategory.show, // prevents moving a video category to different show
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedVideoCategory);
};

export const deleteVideoCategory = async (req, res) => {
  const deletedVideoCategory = await factory.deleteOne(VideoCategory, req, {
    paramId: 'videoCategoryId',
  });

  // Remove video category from show
  const show = await Show.findById(
    deletedVideoCategory.show,
    'videoCategories'
  );
  if (show && !isEmpty(show.videoCategories)) {
    show.videoCategories = show.videoCategories.filter(
      (videoCategory) =>
        !areEqualIDs(videoCategory._id, deletedVideoCategory._id)
    );
    await show.save();
  }

  res.status(200).json(deletedVideoCategory);
};

export const restoreVideoCategory = async (req, res) => {
  const data = await factory.restoreOne(VideoCategory, req, {
    paramId: 'videoCategoryId',
  });

  // TODO: add video category to show

  res.status(200).json(data);
};

export const disableVideoCategory = async (req, res) => {
  const data = await factory.disableOne(VideoCategory, req, {
    paramId: 'videoCategoryId',
  });

  res.status(200).json(data);
};

export const enableVideoCategory = async (req, res) => {
  const data = await factory.enableOne(VideoCategory, req, {
    paramId: 'videoCategoryId',
  });

  res.status(200).json(data);
};

export default {
  getAllVideoCategories,
  getVideoCategory,
  createVideoCategory,
  updateVideoCategory,
  deleteVideoCategory,
  restoreVideoCategory,
  disableVideoCategory,
  enableVideoCategory,
};
