import Person from '#modules/persons/models/Person.js';
import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';

import Channel from '../models/Channel.js';

export const getAllPersons = async (req, res) => {
  const channel = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: true,
  });

  const data = await factory.getAll(Person, req, {
    filter: { channel: channel._id },
  });

  res.status(200).json(data);
};

export const getPerson = async (req, res) => {
  const data = await factory.getOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export const createPerson = async (req, res) => {
  const channel = await factory.getOne(Channel, req, {
    paramId: 'channelId',
  });

  const { prefix, firstName, middleName, lastName, suffix } = req.body;
  const fullName =
    `${prefix} ${firstName} ${middleName} ${lastName} ${suffix}`.replace(
      /undefined/g,
      ''
    );

  // Ensure person has a valid slug within its siblings (or create one from its name)
  const slug = await Person.getAvailableSlug(
    slugify(req.body.slug || fullName),
    channel
  );

  // Create the person
  const person = await Person.create({
    ...req.body,
    channel,
    slug,
  });

  res.status(200).json(person);
};

export const updatePerson = async (req, res) => {
  const person = await factory.getOne(Person, req, {
    paramId: 'personId',
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Person.getAvailableSlug(
        slugify(req.body.slug),
        person.channel,
        person._id
      )
    : person.slug;

  // Update person's data
  const updatedPerson = await Person.findByIdAndUpdate(
    person._id,
    {
      ...req.body,
      slug,
      channel: person.channel, // prevents moving a person to different channel
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedPerson);
};

export const deletePerson = async (req, res) => {
  const data = await factory.deleteOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export const restorePerson = async (req, res) => {
  const data = await factory.restoreOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export const disablePerson = async (req, res) => {
  const data = await factory.disableOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export const enablePerson = async (req, res) => {
  const data = await factory.enableOne(Person, req, {
    paramId: 'personId',
  });

  res.status(200).json(data);
};

export default {
  getAllPersons,
  getPerson,
  createPerson,
  updatePerson,
  deletePerson,
  restorePerson,
  disablePerson,
  enablePerson,
};
