// import {errors} from '#utils/appError.js';
import deepmerge from 'deepmerge';

import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';
import { errors } from '#utils/appError.js';

import Channel from '../models/Channel.js';
import Show from '../models/Show.js';
import showsServices from '../services/showsServices.js';

export const getAllShows = async (req, res) => {
  const language = req.query.language ?? req.user.preferences.language;
  const channel = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: true,
  });

  const data = await factory.getAll(Show, req, {
    filter: {
      // NOTE: keep nested $and/$or because $or gets overwritten when shows are filtered by status in backend!
      $and: [
        {
          // Exclude translations
          translationOf: { $exists: false },
          $or: [
            { channel: channel._id },
            { [`channels.${channel.id}.sharingEnabled`]: true },
          ],
        },
      ],
    },
    populate: [
      {
        path: 'categories',
        match: { deleted: false, enabled: true },
        select: `title.${language} title.en`,
      },
    ],
  });

  // Gets the count of episodes for each show
  const showIds = data.items.map((show) => show._id);
  const episodesCount = await Show.aggregate([
    {
      $match: {
        _id: { $in: showIds },
        deleted: false,
      },
    },
    {
      $lookup: {
        from: 'episodes',
        let: { showId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$show', '$$showId'] },
              deleted: false,
              translationOf: { $exists: false },
            },
          },
          { $project: { _id: 1 } },
        ],
        as: 'episodes',
      },
    },
    {
      $project: {
        _id: 1,
        episodesCount: { $size: '$episodes' },
      },
    },
  ]);

  res.status(200).json({
    count: data.count,
    items: data.items.map((show) => ({
      ...show.toObject(),
      categories: show.toObject().categories.map((c) => ({
        id: c.id,
        title: c.title[language] || c.title.en,
      })),
      episodesCount:
        episodesCount.find((e) => e._id.equals(show._id))?.episodesCount || 0,
    })),
  });
};

export const getShow = async (req, res) => {
  const language = req.query.language ?? req.user.preferences.language;
  const data = await factory.getOne(Show, req, {
    paramId: 'showId',
    populate: [
      {
        path: 'categories',
        match: { deleted: false, enabled: true },
        select: `title.${language} title.en`,
      },
      {
        path: 'channel',
        select: `title rss translations`,
      },
      {
        path: 'seasons',
        match: { deleted: false },
        select: 'enabled title number groupName slug documents links',
      },
      {
        path: 'videoCategories',
        match: { deleted: false },
        select: 'enabled title slug abstract body',
      },
    ],
  });

  if (!data) {
    throw errors.not_found('show');
  }

  const show = data.toObject();

  res.status(200).json({
    ...show,
    categories: show.categories.map((c) => ({
      id: c.id,
      title: c.title[language] || c.title.en,
    })),
    isTranslation: !!show.translationOf,
  });
};

export const createShow = async (req, res) => {
  const channel = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: true,
  });

  if (!req.body.title) throw errors.params(['title']);

  // Ensure show has a valid slug within its siblings (or create one from its title)
  const slug = await Show.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    channel
  );

  // Create the show
  const show = await Show.create({
    ...req.body,
    language: req.body.language ? req.body.language : channel.mainLanguage,
    images: deepmerge(Show.schema.obj.images.default, req.body.images || {}),
    backgroundImages: deepmerge(
      Show.schema.obj.backgroundImages.default,
      req.body.backgroundImages || {}
    ),
    colors: deepmerge(Show.schema.obj.colors.default, req.body.colors || {}),
    channel,
    slug,
    importIDs: [
      {
        type: 'schedule',
        recordID: slugify(req.body.title),
      },
    ],
  });

  res.status(200).json(show);
};

export const updateShow = async (req, res) => {
  const show = await factory.getOne(Show, req, { paramId: 'showId' });

  // If show's title changed, add a schedule importID
  if (req.body.title && req.body.title !== show.title) {
    show.importIDs = [
      ...(show.importIDs || []),
      {
        type: 'schedule',
        recordID: slugify(req.body.title),
      },
    ];

    await show.save();
  }

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Show.getAvailableSlug(
        slugify(req.body.slug),
        show.channel,
        show._id
      )
    : show.slug;

  // Update show's data
  const updatedShow = await Show.findByIdAndUpdate(
    show._id,
    {
      ...req.body,
      slug,
      channel: show.channel, // prevents moving a show to different channel
      images: deepmerge.all([
        Show.schema.obj.images.default,
        show.images,
        req.body.images || {},
      ]),
      backgroundImages: deepmerge.all([
        Show.schema.obj.backgroundImages.default,
        show.backgroundImages,
        req.body.backgroundImages || {},
      ]),
      colors: deepmerge.all([
        Show.schema.obj.colors.default,
        show.colors,
        req.body.colors || {},
      ]),
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedShow);
};

export const deleteShow = async (req, res) => {
  const { entity, params } = req;
  const { showId } = params;

  const { show, error } = await showsServices.deleteShow({ entity, showId });

  if (error) {
    throw error;
  }

  res.status(200).json(show);
};

export const restoreShow = async (req, res) => {
  const data = await factory.restoreOne(Show, req, { paramId: 'showId' });

  res.status(200).json(data);
};

export const disableShow = async (req, res) => {
  const { entity, params, query } = req;
  const { showId } = params;
  const { channel } = query;

  const { show, error } = await showsServices.disableShow({
    channelId: channel,
    entity,
    showId,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(show);
};

export const enableShow = async (req, res) => {
  let data;

  // Enable show for the provided channel
  const channelId = req.query.channel;
  if (channelId) {
    const show = await factory.getOne(Show, req, { paramId: 'showId' });
    const channel = await Channel.findById(show.channel);
    if (channel && show.channels[channelId]) {
      show.channels = {
        ...show.channels,
        [channelId]: {
          ...show.channels[channelId],
          enabled: true,
        },
      };
      data = await show.save();
    }
  }

  // Enable show
  else {
    data = await factory.enableOne(Show, req, { paramId: 'showId' });
  }

  res.status(200).json(data);
};

export const getShowTranslations = async (req, res) => {
  const { entity, query } = req;
  const { showId } = req.params;

  const showTranslations = await showsServices.getShowTranslations({
    showId,
    entity,
    query,
  });

  res.status(200).json(showTranslations);
};

export const addShowTranslation = async (req, res) => {
  const { entity, params } = req;
  const { showId, language } = params;

  const showTranslations = await showsServices.addShowTranslation(
    showId,
    language,
    entity
  );

  res.status(200).json(showTranslations);
};

export default {
  getAllShows,
  getShow,
  createShow,
  updateShow,
  deleteShow,
  restoreShow,
  disableShow,
  enableShow,
  getShowTranslations,
  addShowTranslation,
};
