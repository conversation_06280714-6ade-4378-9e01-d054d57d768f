import { areEqualIDs } from '#utils/helpers.js';
import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';
import { isEmpty } from '#utils/arrays.js';

import Show from '../models/Show.js';
import Season from '../models/Season.js';

export const getAllSeasons = async (req, res) => {
  const { seasons } = await factory.getOne(Show, req, {
    paramId: 'showId',
    populate: [
      {
        path: 'seasons',
        match: { deleted: false },
      },
    ],
    fields: ['seasons'],
  });

  res.status(200).json({
    count: seasons.length,
    items: seasons,
  });
};

export const getSeason = async (req, res) => {
  const data = await factory.getOne(Season, req, {
    paramId: 'seasonId',
  });

  res.status(200).json(data);
};

export const createSeason = async (req, res) => {
  const show = await factory.getOne(Show, req, { paramId: 'showId' });

  // Ensure season has a valid slug within its siblings (or create one from its title)
  const slug = await Season.getAvailableSlug(
    slugify(
      req.body.slug ||
        (req.body.groupName
          ? `${req.body.groupName} ${req.body.title}`
          : req.body.title)
    ),
    show
  );

  const season = await Season.create({
    ...req.body,
    slug,
    channel: show.channel,
    show,
  });

  // Add season to show
  show.seasons = [season.id, ...show.seasons];
  await show.save();

  res.status(200).json(season);
};

export const updateSeason = async (req, res) => {
  const season = await factory.getOne(Season, req, { paramId: 'seasonId' });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Season.getAvailableSlug(
        slugify(req.body.slug),
        season.show,
        season._id
      )
    : season.slug;

  // Update season's data
  const updatedSeason = await Season.findByIdAndUpdate(
    season._id,
    {
      ...req.body,
      slug,
      channel: season.channel, // prevents moving a season to different channel
      show: season.show, // prevents moving a season to different show
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedSeason);
};

export const deleteSeason = async (req, res) => {
  const deletedSeason = await factory.deleteOne(Season, req, {
    paramId: 'seasonId',
  });

  // Remove season from show
  const show = await Show.findById(deletedSeason.show, 'seasons');
  if (show && !isEmpty(show.seasons)) {
    show.seasons = show.seasons.filter(
      (season) => !areEqualIDs(season._id, deletedSeason._id)
    );
    await show.save();
  }

  res.status(200).json(deletedSeason);
};

export const restoreSeason = async (req, res) => {
  const data = await factory.restoreOne(Season, req, { paramId: 'seasonId' });

  // TODO: add season to show

  res.status(200).json(data);
};

export const disableSeason = async (req, res) => {
  const data = await factory.disableOne(Season, req, { paramId: 'seasonId' });

  res.status(200).json(data);
};

export const enableSeason = async (req, res) => {
  const data = await factory.enableOne(Season, req, { paramId: 'seasonId' });

  res.status(200).json(data);
};

export default {
  getAllSeasons,
  getSeason,
  createSeason,
  updateSeason,
  deleteSeason,
  restoreSeason,
  disableSeason,
  enableSeason,
};
