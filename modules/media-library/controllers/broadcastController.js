import fs from 'fs';

import { generateMissingParamsError } from '#utils/appError.js';
import factory from '#utils/handlerFactory.js';
import { fileTypes } from '#utils/files.js';
import { getUpload } from '#utils/storage.js';
import Logger from '#utils/logger.js';

import Channel from '../models/Channel.js';
import Broadcast from '../models/Broadcast.js';
import Episode from '../models/Episode.js';
import { scheduleImport } from '../scripts/scheduleImport.js';

const allowedContentTypes = [...fileTypes.xml];
const maxFileSize = '100mb';
const folder = 'hope-schedule';

const defaultUpload = getUpload({
  folder,
  maxFileSize,
  allowedContentTypes,
});

export const uploadSchedule = (upload = defaultUpload, fieldName = 'file') =>
  upload.single(fieldName);

export const importSchedule =
  (fieldName = 'file') =>
  async (req, res) => {
    if (!req.file) throw generateMissingParamsError([fieldName]);

    const { file } = req;
    const { path: filePath } = file;

    // Import schedule
    const result = await scheduleImport({
      channelId: req.params.channelId,
      filePath,
    });

    // Delete the temp file
    try {
      fs.unlinkSync(filePath);
    } catch (error) {
      Logger.error('Error trying to delete temp file:', error);
    }

    res.status(200).json(result);
  };

export const getAllBroadcasts = async (req, res) => {
  const channel = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: true,
  });

  const data = await factory.getAll(Broadcast, req, {
    filter: { channel: channel._id },
    populate: [
      {
        path: 'episode',
        select: 'title show',

        populate: {
          path: 'show',
          select: 'title',
        },
      },
    ],
  });

  res.status(200).json({
    count: data.count,
    items: data.items,
  });
};

export const getBroadcast = async (req, res) => {
  const data = await factory.getOne(Broadcast, req, {
    paramId: 'broadcastId',
    populate: [
      {
        path: 'episode',
        select: 'title show',

        populate: {
          path: 'show',
          select: 'title',
        },
      },
    ],
  });

  res.status(200).json(data);
};

export const createBroadcast = async (req, res) => {
  const { channelId } = req.params;

  const { episode: episodeId } = req.body;

  const channel = await Channel.findOne({
    _id: channelId,
    deleted: false,
  });

  const episode = episodeId
    ? await Episode.findOne({ _id: episodeId, deleted: false }).populate({
        path: 'show',
        select: 'title ',
      })
    : undefined;

  const broadcast = await Broadcast.create({
    ...req.body,
    channel: channel._id,
    episode,
  });

  res.status(200).json(broadcast);
};

export const updateBroadcast = async (req, res) => {
  const broadcast = await factory.getOne(Broadcast, req, {
    paramId: 'broadcastId',
  });

  // Update broadcast's data
  const updatedBroadcast = await Broadcast.findByIdAndUpdate(
    broadcast._id,
    {
      ...req.body,
      channel: broadcast.channel, // prevents moving a broadcast to different channel
    },
    {
      new: true,
      runValidators: true,
      populate: [
        {
          path: 'episode',
          select: 'title show',

          populate: {
            path: 'show',
            select: 'title',
          },
        },
      ],
    }
  );

  res.status(200).json(updatedBroadcast);
};

export const deleteBroadcast = async (req, res) => {
  const broadcast = await factory.getOne(Broadcast, req, {
    paramId: 'broadcastId',
  });

  if (broadcast) {
    await Broadcast.deleteOne({ _id: broadcast._id });
  }

  res.status(204).json({});
};

export default {
  getAllBroadcasts,
  getBroadcast,
  uploadSchedule,
  importSchedule,
  createBroadcast,
  updateBroadcast,
  deleteBroadcast,
};
