import { areEqualIDs } from '#utils/helpers.js';
import factory from '#utils/handlerFactory.js';
import { isEmpty } from '#utils/arrays.js';
import { slugify } from '#utils/strings.js';

import Channel from '../models/Channel.js';
import Collection from '../models/Collection.js';
import getEpisodeCountByMediaType from '../helpers/getEpisodeCountByMediaType.js';

export const getAllCollections = async (req, res) => {
  const mediaType = req.query.mediaType ?? 'video';

  const { channelId } = req.params;
  // To optimize the loading of collections, we need to know how many episodes are in each show for the specified media type, be it audio or video.
  const showsWithMediaType = await getEpisodeCountByMediaType(
    channelId,
    mediaType
  );

  const { collections } = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: true,
    // only manually added content
    populate: [
      {
        path: 'collections',
        match: { deleted: false },
        options: {
          populate: [
            {
              path: 'episodes',
              match: {
                enabled: true,
                deleted: false,
              },
              select: 'id image title',
            },
            {
              path: 'shows',
              match: { enabled: true, deleted: false },
              select: 'id images.default title',
            },
          ],
        },
      },
    ],
    fields: ['collections'],
  });

  const collectionList = await Promise.all(
    collections.map(async (collection) => {
      // filter out shows that don't have episodes
      collection = { ...collection.toObject() };
      collection.shows = collection.shows.filter((show) =>
        showsWithMediaType.some((s) => s._id.equals(show._id))
      );
      return await Collection.loadItems(collection, showsWithMediaType);
    })
  );

  res.status(200).json({
    count: collectionList.length,
    items: collectionList,
  });
};

export const getCollection = async (req, res) => {
  const language = req.query.language ?? req.user.preferences.language;
  const { mediaType } = req.query;
  const { collectionId } = req.params;
  const _collection = await Collection.findById(collectionId).lean();

  const showsWithMediaType = mediaType
    ? await getEpisodeCountByMediaType(_collection.channel, mediaType)
    : undefined;

  const data = await factory.getOne(Collection, req, {
    paramId: 'collectionId',
    populate: [
      {
        path: 'categories',
        select: `title.${language} title.en`,
      },
      {
        path: 'shows',
        match: { deleted: false },
        select: 'id images.default title',
      },
      {
        path: 'episodes',
        match: { deleted: false },
        select: 'id image title',
      },
    ],
  });

  let collection = await Collection.loadItems(
    { ...data.toObject() },
    showsWithMediaType
  );

  collection = {
    ...collection,
    categories: collection.categories.map((c) => ({
      id: c.id,
      title: c.title[language] || c.title.en,
    })),
  };

  res.status(200).json(collection);
};

export const createCollection = async (req, res) => {
  const channel = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: true,
  });

  // Ensure collection has a valid slug within its siblings (or create one from its title)
  const slug = await Collection.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    channel
  );

  // req.body.position = await Collection.findNextPosition(channel);

  // Create the collection
  const collection = await Collection.create({
    ...req.body,
    channel,
    slug,
  });

  // Add collection to channel
  channel.collections = [...channel.collections, collection.id];
  await channel.save();

  res.status(200).json(collection);
};

export const updateCollection = async (req, res) => {
  const collection = await factory.getOne(Collection, req, {
    paramId: 'collectionId',
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Collection.getAvailableSlug(
        slugify(req.body.slug),
        collection.channel,
        collection._id
      )
    : collection.slug;

  // Update collection's data
  const updatedCollection = await Collection.findByIdAndUpdate(
    collection._id,
    {
      ...req.body,
      slug,
      channel: collection.channel, // prevents moving a collection to different channel
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedCollection);
};

export const deleteCollection = async (req, res) => {
  const deletedCollection = await factory.deleteOne(Collection, req, {
    paramId: 'collectionId',
  });

  // Remove collection from channel
  const channel = await Channel.findById(
    deletedCollection.channel,
    'collections'
  );
  if (channel && !isEmpty(channel.collections)) {
    channel.collections = channel.collections.filter(
      (collection) => !areEqualIDs(collection._id, deletedCollection._id)
    );
    await channel.save();
  }

  res.status(200).json(deletedCollection);
};

export const restoreCollection = async (req, res) => {
  const data = await factory.restoreOne(Collection, req, {
    paramId: 'collectionId',
  });

  res.status(200).json(data);
};

export const disableCollection = async (req, res) => {
  const data = await factory.disableOne(Collection, req, {
    paramId: 'collectionId',
  });

  res.status(200).json(data);
};

export const enableCollection = async (req, res) => {
  const data = await factory.enableOne(Collection, req, {
    paramId: 'collectionId',
  });

  res.status(200).json(data);
};

export const updateCollectionSort = async (req, res) => {
  const channel = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: false,
  });

  // Update channel data
  const updatedChannel = await Channel.findByIdAndUpdate(
    channel._id,
    {
      // TODO: add validation to ensure that the data being modified is only the collections array
      ...req.body,
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedChannel);
};

export default {
  getAllCollections,
  getCollection,
  createCollection,
  updateCollection,
  deleteCollection,
  restoreCollection,
  disableCollection,
  enableCollection,
  updateCollectionSort,
};
