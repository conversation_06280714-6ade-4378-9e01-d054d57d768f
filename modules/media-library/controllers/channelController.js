import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';

import Channel from '../models/Channel.js';
import channelServices from '../services/channelServices.js';

export const getAllChannels = async (req, res) => {
  let data = { count: 0, items: [] };

  if (
    !req.user.isAdmin &&
    !req.user.hasPermission({ module: 'channels', permission: 'read' })
  ) {
    res.status(200).json(data);
    return;
  }

  const restrictedRecords = req.user.getRestrictedRecords({
    module: 'channels',
    permission: 'read',
  });

  data = await factory.getAll(Channel, req, {
    filterByEntity: req.query.filterByEntity === 'true',
    filter: {
      ...(restrictedRecords && {
        _id: { $in: restrictedRecords },
      }),
    },
    populate: [
      {
        path: 'entity',
        select: 'name config.backendDomains',
      },
    ],
  });

  res.status(200).json(data);
};

export const getChannel = async (req, res) => {
  const data = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const createChannel = async (req, res) => {
  // Ensure channel has a valid slug within its siblings (or create one from  its title)
  const slug = await Channel.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    req.entity
  );

  // Set mapPoint as GeoJSON format
  if (req.body.mapPoint) {
    formatGeoJSON(req, 'mapPoint');
  }

  // Create channel
  const channel = await Channel.create({
    ...req.body,
    siteTitle: req.body.siteTitle ? req.body.siteTitle : req.body.title,
    entity: req.entity._id,
    slug,
  });

  res.status(200).json(channel);
};

export const updateChannel = async (req, res) => {
  const channel = await factory.getOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: false,
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Channel.getAvailableSlug(
        slugify(req.body.slug),
        channel.entity,
        channel._id
      )
    : channel.slug;

  // Set mapPoint as GeoJSON format
  if (req.body.mapPoint) {
    formatGeoJSON(req, 'mapPoint');
  }

  // Update channel data
  const updatedChannel = await Channel.findByIdAndUpdate(
    channel._id,
    {
      ...req.body,
      slug,
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedChannel);
};

export const deleteChannel = async (req, res) => {
  const data = await factory.deleteOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const restoreChannel = async (req, res) => {
  const data = await factory.restoreOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const disableChannel = async (req, res) => {
  const data = await factory.disableOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

export const enableChannel = async (req, res) => {
  const data = await factory.enableOne(Channel, req, {
    paramId: 'channelId',
    filterByEntity: false,
  });

  res.status(200).json(data);
};

function formatGeoJSON(req, fieldName) {
  const { coordinates, bearing, pitch, zoom } = req.body[fieldName];
  const [longitude, latitude] = coordinates;

  if (longitude && latitude) {
    req.body[fieldName] = {
      type: 'Point',
      coordinates,
      ...(bearing && { bearing }),
      ...(pitch && { pitch }),
      ...(zoom && { zoom }),
    };
  } else {
    delete req.body[fieldName];
  }
}

export const getShareChannels = async (req, res) => {
  const { channelId } = req.params;
  const { limit } = req.query;

  const data = await channelServices.getShareChannels(channelId, { limit });

  res.status(200).json(data);
};

export default {
  getAllChannels,
  getChannel,
  createChannel,
  updateChannel,
  deleteChannel,
  restoreChannel,
  disableChannel,
  enableChannel,
  getShareChannels,
};
