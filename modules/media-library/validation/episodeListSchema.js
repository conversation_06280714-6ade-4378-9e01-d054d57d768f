import Joi from 'joi';

import { plainListSchema } from '#utils/api/list/schema.js';

export const episodeListSchema = Joi.object().keys({
  ...plainListSchema,
  sort: Joi.string().default('createdAt').optional(),
  sortOrder: Joi.string().default('desc').optional(),
  categories: Joi.array().items(Joi.string()).optional(),
  seasons: Joi.array().items(Joi.string()).optional(),
  ids: Joi.array().items(Joi.string()).optional(),
  ignoreIds: Joi.array().items(Joi.string()).optional(),
});
