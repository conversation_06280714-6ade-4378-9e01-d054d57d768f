import Joi from 'joi';

import { validate } from '#utils/validationMiddleware.js';

import { episodeLinkedArticleContentSchema } from './episodeLinkedArticleContentSchema.js';

export const validateUpdateEpisodeLinkedItem = async (req, res, next) => {
  const { type } = req.params;

  const linkedItemTypeSchema = Joi.string().valid('article').required();

  if (type === 'article') {
    const linkedArticleSchema = Joi.object().keys({
      ...episodeLinkedArticleContentSchema,
    });

    return validate(linkedArticleSchema, 'body')(req, res, next);
  }

  // Fallback to checking the params
  const paramsSchema = Joi.object().keys({
    ...linkedItemTypeSchema,
    episodeId: Joi.string().required(),
    linkedItemId: Joi.string().required(),
  });

  return validate(paramsSchema, 'params')(req, res, next);
};
