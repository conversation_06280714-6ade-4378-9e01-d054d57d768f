import Joi from 'joi';

import { validate } from '#utils/validationMiddleware.js';
import { episodeLinkedArticleContentSchema } from './episodeLinkedArticleContentSchema.js';

export const validateAddEpisodeLinkedItem = async (req, res, next) => {
  const { type } = req.body;

  const linkedItemTypeSchema = {
    type: Joi.string().valid('article').required(),
  };

  if (type === 'article') {
    const linkedArticleSchema = Joi.object().keys({
      ...linkedItemTypeSchema,
      ...episodeLinkedArticleContentSchema,
    });

    return validate(linkedArticleSchema, 'body')(req, res, next);
  }

  const schema = Joi.object().keys({
    ...linkedItemTypeSchema,
  });

  return validate(schema, 'body')(req, res, next);
};
