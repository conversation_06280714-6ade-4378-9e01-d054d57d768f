import Joi from 'joi';

export const updateEpisodesDataSourceSchema = {
  settings: Joi.object({
    detailPage: Joi.string().required(),
    channel: Joi.string().required(),
    show: Joi.string().optional().allow(null, ''),
    season: Joi.string().optional().allow(null, ''),
    segmentType: Joi.string().allow(
      null,
      '',
      'any',
      'onlyWithSegments',
      'noSegments',
      'onlySegments'
    ),
    mediaType: Joi.string().allow(null, '', 'video', 'audio'),
    categories: Joi.array().items(Joi.string()).optional().allow(null),
  }).required(),
};
