import fs from 'fs';
import { S3 } from '@aws-sdk/client-s3';
import { readFile } from 'fs/promises';
import set from 'lodash/set.js';
import get from 'lodash/get.js';

import dotenv from 'dotenv';

import { DateTime } from 'luxon';

import slateToTiptap from '#modules/articles/tasks/acl-import/helpers/slateToTiptap/index.js';
import { toJsonObjectId } from '#utils/api/mongoose/id.js';
import Logger from '#utils/logger.js';

import showsMap from './input/showsMap.js';
import getFieldTranslation from './helpers/getFieldTranslation.js';
import getMediaLinks from './helpers/getMediaLinks.js';
import { findImage, convertImageData } from './helpers/images.js';

import {
  VERBOSE,
  DRY_RUN,
  IMPORT_IMAGES,
  INPUT_FOLDER,
  INPUT_FILE,
  INPUT_IMAGES_FILE,
  OUTPUT_FOLDER,
  OUTPUT_SHOWS_FILE,
  OUTPUT_EPISODES_FILE,
  OUTPUT_VIDEOS_FILE,
} from './constants.js';

import { parseDate } from './helpers/dates.js';

dotenv.config({ path: new URL('.env', import.meta.url) });

const {
  LOCAL_ENTITY_ID,
  LOCAL_CHANNEL_ID,
  REMOTE_SITE_ID,
  S3_SOURCE_ENDPOINT_URL,
  S3_SOURCE_SECRET_ACCESS_KEY,
  S3_SOURCE_ACCESS_KEY_ID,
  S3_SOURCE_BUCKET_NAME,
  S3_TARGET_ENDPOINT_URL,
  S3_TARGET_SECRET_ACCESS_KEY,
  S3_TARGET_ACCESS_KEY_ID,
  S3_TARGET_BUCKET_NAME,
} = process.env;

let records = [];
let imageRecords = [];

let missingShowCount = 0;
let segmentsCount = 0;
let translationsCount = 0;

const missingShowVideosIds = [];
const episodesMap = {};
const duplicatedTranslationsInSameDay = {};
const loneTranslations = {};
const episodeTranslationsDatesMap = {};
const videosMap = {};
const images = [];

// Read the input file
try {
  records = JSON.parse(
    await readFile(new URL(`${INPUT_FOLDER}/${INPUT_FILE}`, import.meta.url))
  );
} catch {
  Logger.error('Error reading the input file', `${INPUT_FOLDER}/${INPUT_FILE}`);
  Logger.info(
    `Hint: Export the videos collection from the database and save it as ${INPUT_FILE} in the ${INPUT_FOLDER} folder.`
  );
}

// Read the input file
try {
  imageRecords = JSON.parse(
    await readFile(
      new URL(`${INPUT_FOLDER}/${INPUT_IMAGES_FILE}`, import.meta.url)
    )
  );
} catch {
  Logger.error(
    'Error reading the images input file',
    `${INPUT_FOLDER}/${INPUT_IMAGES_FILE}`
  );
  Logger.info(
    `Hint: Export the images collection from the database and save it as ${INPUT_IMAGES_FILE} in the ${INPUT_FOLDER} folder.`
  );
}

// Loop through the records
for (const record of records) {
  const {
    _id: { $oid: id },
    data,
    sites,
    belongsTo,
    createdAt,
    updatedAt,
  } = record || {};

  const { title, description, body, slug, video, image } = data || {};

  // Check if video has a show (or belongs to one)
  const showId = data.show ?? belongsTo?.show;

  // Get the segmentOf from the belongsTo object
  const segmentOf = belongsTo?.parent;

  // Get the show from the shows map
  const show = showsMap[showId];

  if (show) {
    const { language } = show;

    const isTranslation = show.translationOf && language !== 'en';
    const originalShowId = show.translationOf ?? showId;

    // Get the title in the language
    const videoTitle = getFieldTranslation(title, language)?.replaceAll(
      '–',
      '-'
    );

    const { startDate } = sites[REMOTE_SITE_ID] ?? {};

    const publishDateValue = startDate?.$date ?? startDate;

    const publishDateTime = publishDateValue
      ? DateTime.fromISO(publishDateValue)
      : null;

    let videoDate = publishDateTime;

    // If the show is ANN Videos and is not a segment
    if (originalShowId === '5e674d2279e84b31f64d097e' && !segmentOf) {
      // Extract the date from the title
      const titleParts = videoTitle.split('-');
      const date = titleParts[1]?.trim(); // The date is the second (or last) part of the title in ANN Videos

      // Obtain a parsed date from the date extracted from the title, and based on the language
      videoDate = parseDate(date, language, id);
    }

    // If there is a date and the video is not a segment
    if (videoDate && !segmentOf) {
      // Format the date as 'yyyy-MM-dd'
      const formatedDate = videoDate.toFormat('yyyy-MM-dd');

      // Get the existing value for the show, date, and language (if any)
      const prevValue = get(episodeTranslationsDatesMap, [
        originalShowId,
        formatedDate,
        language,
      ]);

      // Add or update the value in the map
      set(
        episodeTranslationsDatesMap,
        [originalShowId, formatedDate, language],
        [...(prevValue ?? []), id]
      );
    }

    // Increment the translations count if the video is a translation
    if (isTranslation) {
      translationsCount += 1;
    }

    // Find the image data
    const imageData = findImage(image?.id, imageRecords);

    // If its found
    if (imageData) {
      // Add the image to the images array (to download it later)
      images.push(imageData);
    } else {
      // Or log a warning to inform the user
      Logger.warning('Image not found for ACL v1 video:', id);
    }

    if (!video) {
      Logger.error('Video not found:', id);
      continue;
    }

    // Create the new video object
    const newVideo = {
      _id: toJsonObjectId(id),
      title: videoTitle,
      abstract: getFieldTranslation(description, language),
      body: await slateToTiptap({
        content: getFieldTranslation(body, language),
        locale: language,
      }),
      slug: getFieldTranslation(slug, language),
      show: toJsonObjectId(showId),
      mediaLinks: getMediaLinks(video),
      image: imageData ? convertImageData(imageData, LOCAL_ENTITY_ID) : null,
      entity: toJsonObjectId(LOCAL_ENTITY_ID),
      channel: toJsonObjectId(LOCAL_CHANNEL_ID),
      language,
      enabled: true,
      deleted: false,
      videoOnDemand: true,
      episodesDownloadable: false,
      createdAt,
      updatedAt,
      importIDs: [
        {
          type: 'acl-v1',
          recordID: id,
          enabled: true,
        },
      ],
    };

    // If the video is a segment, add it as a Video
    if (segmentOf) {
      // Add the segment to the videos map (with the episode id set)
      videosMap[id] = { ...newVideo, episode: toJsonObjectId(segmentOf) };

      // Increment the segments count for stats
      segmentsCount += 1;

      // Skip the rest of the loop
      continue;
    }

    // Add the video to the episodes map
    episodesMap[id] = { ...videosMap[id], ...newVideo };
  } else {
    // If the show is not found, add the video to the missing shows list
    missingShowVideosIds.push(id);

    // Increment the missing show count
    missingShowCount += 1;

    if (VERBOSE) {
      Logger.warning('Missing show in video', id); // TODO: inform the ANN team which videos are missing shows
    }
  }
}

const translations = Object.keys(episodeTranslationsDatesMap).reduce(
  (acc, showId) => {
    // Add the show id to the accumulator as it is
    acc[showId] = episodeTranslationsDatesMap[showId];

    // Sort the dates in ascending order
    const sortedDates = Object.keys(acc[showId]).sort();

    // Loop through the dates
    for (const day of sortedDates) {
      const currentDay = acc[showId][day];
      const languages = Object.keys(currentDay);

      // Check if the current day has only one language and it's not English
      if (languages.length === 1 && languages[0] !== 'en') {
        set(loneTranslations, [showId, day], currentDay);
      }

      // Check if the current day has no english translation
      if (!languages.includes('en')) {
        //current day has no english translation

        //  get the previous day
        const prevDate = sortedDates[sortedDates.indexOf(day) - 1];
        const prevDay = acc[showId][prevDate];

        //  get the next day
        const nextDate = sortedDates[sortedDates.indexOf(day) + 1];
        const nextDay = acc[showId][nextDate];

        Logger.warning('Original in english missing in current date', {
          prevDate,
          prevDay,
          day,
          currentDay,
          nextDate,
          nextDay,
        });
      }

      // Detect if there are more than one show's episode in the language
      for (const [language, episodes] of Object.entries(currentDay)) {
        // Skip the English language
        if (language === 'en') continue;

        // Get the first episode id (should be only one episode id in the array, and if there are more it will be logged below to be manually checked and fixed)
        const episodeId = episodes[0];

        // Get the episode from the episodes map
        const episode = episodesMap[episodeId];

        // Check if the episode is not found
        if (!episode) {
          Logger.error('Episode not found:', episodeId);
          continue;
        }

        // Get the original english episode
        const originalEpisodeId = currentDay.en?.[0];

        if (!originalEpisodeId) {
          Logger.error('Original episode not found for:', episodeId);
          continue;
        }

        // Update the episode as a translation
        episode.translationOf = toJsonObjectId(originalEpisodeId);

        // If there are more than one episode in the same language on the same day
        if (episodes.length > 1) {
          set(duplicatedTranslationsInSameDay, [showId, day, language], {
            episodes,
            links: episodes.map(
              (id) =>
                `https://manage.adventistcontent.org/contents/videos/${id}/details`
            ),
          });

          if (VERBOSE) {
            Logger.warning(
              `More than one episode in the same language (${language}) on the same day: ${day} - ${episodes}`
            );
          }
        }
      }
    }

    // Update the accumulator with the sorted dates
    acc[showId] = sortedDates.reduce((sortAcc, date) => {
      const day = episodeTranslationsDatesMap[showId][date];

      const languages = Object.keys(day);

      // Skip if there is only one language and it's English
      if (languages.length === 1 && languages[0] === 'en') {
        return sortAcc;
      }

      // Add the day with the languages to the accumulator, including the episode id and title for each language
      sortAcc[date] = Object.entries(day).reduce(
        (langAcc, [lang, episodes]) => {
          langAcc[lang] = {
            id: episodes[0],
            title: episodesMap[episodes[0]].title,
          };
          return langAcc;
        },
        {}
      );
      return sortAcc;
    }, {});

    // Return the accumulator
    return acc;
  },
  {}
);

if (!DRY_RUN) {
  fs.writeFileSync(
    `${OUTPUT_FOLDER}/translations.json`,
    JSON.stringify(translations, null, 2)
  );

  // Convert the shows map to an array of shows
  const shows = Object.entries(showsMap).map(([key, show]) => ({
    ...show,
    _id: toJsonObjectId(key),
    enabled: true,
    deleted: false,
    entity: toJsonObjectId(LOCAL_ENTITY_ID),
    channel: toJsonObjectId(LOCAL_CHANNEL_ID),
    translationOf: toJsonObjectId(show.translationOf), // If the show is a translation, set it as the translation of the original show
    importIDs: [
      {
        type: 'acl-v1',
        recordID: key,
        enabled: true,
      },
    ],
  }));

  // Write the shows to the output file
  fs.writeFileSync(
    `${OUTPUT_FOLDER}/${OUTPUT_SHOWS_FILE}`,
    JSON.stringify(shows, null, 4)
  );

  // Convert the videos map to an array of episodes
  const episodes = Object.values(episodesMap);

  // Write the episodes to the output file
  fs.writeFileSync(
    `${OUTPUT_FOLDER}/${OUTPUT_EPISODES_FILE}`,
    JSON.stringify(episodes, null, 2)
  );

  // Convert the segments map to an array of videos
  const videos = Object.values(videosMap);

  // Write the videos to the output file
  fs.writeFileSync(
    `${OUTPUT_FOLDER}/${OUTPUT_VIDEOS_FILE}`,
    JSON.stringify(videos, null, 2)
  );
}

// Initialize the S3 clients:
// - sourceS3: The S3 client for the source (ACL v1)
const sourceS3 = new S3({
  endpoint: S3_SOURCE_ENDPOINT_URL,
  region: 'us-east-1', // The region is hardcoded as AWS S3 does not support custom endpoints withouth a region
  credentials: {
    secretAccessKey: S3_SOURCE_SECRET_ACCESS_KEY,
    accessKeyId: S3_SOURCE_ACCESS_KEY_ID,
  },
});

// - targetS3: The S3 client for the target (AWE)
const targetS3 = new S3({
  endpoint: S3_TARGET_ENDPOINT_URL,
  region: 'us-east-1', // The region is hardcoded as AWS S3 does not support custom endpoints withouth a region
  credentials: {
    secretAccessKey: S3_TARGET_SECRET_ACCESS_KEY,
    accessKeyId: S3_TARGET_ACCESS_KEY_ID,
  },
});

// If enabled, download the images from the source space / bucket and upload them to the target space / bucket
if (IMPORT_IMAGES) {
  // Loop through the images
  for (const image of images) {
    const { file } = image;

    if (!file) {
      Logger.error('Image file not found:', image);
      continue;
    }

    const sourceSpace = S3_SOURCE_BUCKET_NAME;
    const sourceKey = `${file.containerId}/${file.name}`;

    const targetSpace = S3_TARGET_BUCKET_NAME;
    const targetKey = `${LOCAL_ENTITY_ID}/${file.name}`;
    const contentType = file.type;

    // Check if the file already exists in the target space / bucket
    targetS3.getObject(
      {
        Bucket: targetSpace,
        Key: targetKey,
      },
      (_checkErr, existingData) => {
        // If the file already exists, skip it
        if (existingData) {
          return;
        }

        // Read the file from the source space / bucket
        sourceS3.getObject(
          {
            Bucket: sourceSpace,
            Key: sourceKey,
          },
          (getErr, data) => {
            if (getErr) {
              return Logger.error(
                `Error getting object ${sourceKey} from bucket ${sourceSpace}`
              );
            }

            // Upload the file to the destination space / bucket
            targetS3.putObject(
              {
                Bucket: targetSpace,
                Key: targetKey,
                Body: data.Body,
                ContentType: contentType,
                ACL: 'public-read',
                // ContentDisposition: `attachment; filename="${desiredFilename}"`,
              },
              (error) => {
                if (error) return Logger.error(error);
              }
            );
          }
        );
      }
    );
  }
}

// Show the results
Logger.info({
  showsCount: Object.keys(showsMap).length,
  totalVideos: records.length,
  translationsCount,
  segmentsCount,
});

if (missingShowCount) {
  Logger.warning('Total missing shows:', missingShowCount);
  Logger.warning('- Missing shows ids:', missingShowVideosIds);
}

if (Object.keys(duplicatedTranslationsInSameDay).length) {
  Logger.warning(
    'Duplicated translations in the same day:',
    JSON.stringify(duplicatedTranslationsInSameDay, null, 2)
  );
}

if (Object.keys(loneTranslations).length) {
  Logger.warning(
    'Lone translations:',
    Object.keys(loneTranslations).reduce((acc, showId) => {
      const showDatesCount = Object.keys(loneTranslations[showId]).length;

      acc += showDatesCount;

      return acc;
    }, 0)
  );
  Logger.warning(JSON.stringify(loneTranslations, null, 2));
}
