/**
 * Given an object with locale as keys and a translation object as values, return the translation for the current language, or the default language if the current language is not found.
 * @param {Object} obj - Object with locale as keys and a translation object as values
 * @param {String} language - The target language (e.g. 'en', 'es', 'fr', 'pt')
 * @param {String} defaultLanguage - The default/fallback language (default 'en')
 * @returns {string|object} - The translation value
 */
export default function getFieldTranslation(obj, lang, defaultLang = 'en') {
  if (!obj) {
    return undefined;
  }

  // Build a map of languages as keys with their respective locales as values
  const localesWithValue = getKeysWithValue(obj).reduce((acc, key) => {
    const language = key.split('-')[0].toLowerCase(); // e.g. 'en-US' -> 'en'
    acc[language] = key;
    return acc;
  }, {});

  // get the current locale
  const currentLocale = localesWithValue[lang];

  if (currentLocale) {
    return obj[currentLocale];
  }

  // get the default locale, if it exists
  const defaultLocale = localesWithValue[defaultLang];

  return obj[defaultLocale];
}

// Helper function to get the keys with a truthy value
export function getKeysWithValue(obj) {
  if (typeof obj !== 'object') {
    return [];
  }
  return Object.keys(obj).filter((key) => obj[key]);
}
