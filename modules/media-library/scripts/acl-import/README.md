# ACL Videos Import scripts

This folder contains scripts to import videos from ACL v1 into the AWE's Media Library.

## Export data from ACL v1 database

Use MongoDB Compass to export the data from the ACL v1 database. The data to export is the following:

### Videos

Fiter by `contentTypeId` (the one for `videos`) and `ownerId` (the entity that owns the video), and not `deleted`:

```js
{ contentTypeId: ObjectId('5e492404a4022206271ce121'), ownerId: ObjectId('5df200974469ba078555eb4c'), deleted: false }
```

### Images

Filter by `ownerId` (the entity that owns the image), and not `deleted`:

```js
{ ownerId: ObjectId('5df200974469ba078555eb4c'), deleted: false }
```

Save the files in the `/input` folder as `videos.json` and `images.json` respectively.

## Import `Videos` data into AWE's Media Library

Once the data is exported and with the files in the rigth folder, you can generate. First, you need to install the dependencies:

````bash

Then run the following command:

```bash
cd modules/media-library/scripts/acl-import
node migrate.js
````

This will generate some files under the `/output` folder. Then you can use these files to import the data into the AWE's database directly

## Import images

Images metadata will be imported directly into the espisode records. The files will be copied to the AWE's Media Library via the S3 API. Image files copy can be prevented by setting the `IMPORT_IMAGES` constant to `false` in the `constants.js` file.
