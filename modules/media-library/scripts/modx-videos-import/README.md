# Modx Videos Import Script for Media Library

This script is used to import several records from Adventist Mission MODX instance into AWE.

## Usage

```bash
cd modules/media-library/scripts/modx-videos-import
node migrate.js
```

> Important: Make sure all the required environment variables and constants are set before running the script (.env file and constants.js).

- This will generate some JSON files under the `output` directory:
  - `output/db` files that can be used to import the data into AWE's database directly.
  - `output/reports` files that contain the reports of the import process (only if `DEBUG` env var is enabled).
- Once the script runs, the generated JSON files in `output/db` can be used to import the data into AWE's database.
  - You may need to delete previous imported / existing data in AWE's to avoid duplicates. Be careful with this step, and applying some filters to the data to be deleted is recommended (e.g. by channel or by show).
- Images will be uploaded automatically to the AWE CDN (unless `IMPORT_IMAGES` is set to `false` in `constants.js`).

### Show's Seasons

For the seasons, a file is created with the name `seasons.json` that contains the seasons of the shows. This file is used to import the seasons into AWE's database directly.
But also the shows' `seasons` field need to be updated with the correct array of seasons. For that a file is created with the name `shows-seasons.json` that contains the shows with the seasons that they have. These arrays need to be updated manually in every show record in AWE's database.

## Maintenance

- Read the code and the comments to understand how the script works in detail. Extend/improve the comments if needed, to make the script more understandable. Contact the original author if you have any questions.
- The script is designed to be run multiple times without any issues.
- Take a look at 'constants.js' file to see the configuration of the script. There you can configure some parameters to help you with the debugging process.
- If you are focusing on debugin only data, you should set the `IMPORT_IMAGES` constant to `false` in `constants.js`, to avoid uploading images to the CDN.
- Also, you may want to disable the `AI_CATEGORIZATION` constant to avoid using the AI service to categorize the videos, as this can be a time (and money) consuming process.
- Cache files: The script uses cache files to avoid making the same requests to the API multiple times. If you want to force the script to make the requests again, you can delete the cache files in the `cache` directory. The script will generate them again when needed.
  - `cache/videos-list.json` this is the main cache file that contains the videos from the API feed provided from MODX.
  - `cache/categories.json` contains the categories with their IDs.
  - `cache/images.json` contains the images with their URLs as keys, and the image file data and debug info as values.
  - `cache/episode-categories.json` contains the previouly AI-generated categories of the episodes with their ID in modx as keys (and some debug info).
