import dotenv from 'dotenv';
import { exit } from 'process';
import { readFile } from 'fs/promises';

import { newMongoDBUUid } from '#utils/api/mongoose/id.js';
import { saveToJsonFile } from '#utils/json.js';
import { logError, logInfo, logWarning } from '#utils/logger.js';
import { getS3Client } from '#utils/storage.js';

import { getEntitiesMap } from './helpers/entities.js';
import {
  fixEpisodeId,
  fixEpisodeLanguage,
  parseEpisode,
  saveEpisodes,
} from './helpers/episodes.js';
import { loadImagesMap, saveImages } from './helpers/images.js';
import {
  shows,
  showsTypeMap,
  channelId,
  loadShowSeasonsMap,
} from './helpers/shows.js';

import {
  API_VIDEOS_LIST_URL,
  VERBOSE,
  USE_CACHE,
  DRY_RUN,
  IMPORT_IMAGES,
  CACHE_FOLDER,
  OUTPUT_FOLDER,
} from './constants.js';
import {
  categoriesMap,
  loadEpisodeCategoriesMap,
  saveCategories,
} from './helpers/categories.js';
import { loadCachedFile } from './helpers/fileMetadata.js';
import { loadPeopleMap, savePeople } from './helpers/persons.js';

// Folders
const DB_FOLDER = `${OUTPUT_FOLDER}/db`;
const REPORTS_FOLDER = `${OUTPUT_FOLDER}/reports`;

// Load environment variables
const envPath = new URL('.env', import.meta.url);

dotenv.config({ path: envPath });

const {
  MODE, // 'production' or 'staging'
  DEBUG,
  AWE_ENTITY_ID,
  BUCKET_ENDPOINT,
  BUCKET_SECRET,
  BUCKET_KEY_ID,
  BUCKET_IMAGES,
} = process.env;

// Check if the script should produce also debug information (default: undefined)
const DEBUG_MODE = DEBUG === 'true';

// Initalize a custom S3 client to upload the images to the CDN
const customS3Client = getS3Client({
  endpoint: BUCKET_ENDPOINT,
  secretAccessKey: BUCKET_SECRET,
  accessKeyId: BUCKET_KEY_ID,
});

// Initialize maps and arrays to store the data during the migration process

// - entities map
const organizationsMap = getEntitiesMap({ environment: MODE });

// - the images map from the cache folder (if exists), otherwise an empty object
const imagesMap = loadImagesMap({ tempFolder: CACHE_FOLDER });

// - the file sizes map (if exists), otherwise empty objects
const fileSizesMap = loadCachedFile({
  tempFolder: CACHE_FOLDER,
  fileName: 'file-sizes',
});

// - shows seasons map
const showsSeasonsMap = loadShowSeasonsMap({ tempFolder: CACHE_FOLDER });

// - episodes map
const episodesMap = {};

// - episode translations map
const episodesTranslationsMap = {};

// - episode categories map
const episodeCategoriesMap = loadEpisodeCategoriesMap({
  tempFolder: CACHE_FOLDER,
});

// - people map
const peopleMap = loadPeopleMap({
  tempFolder: CACHE_FOLDER,
});

// - debug collector map (for debugging purposes)
const debugCollectorMap = {};

// - broken files debug collector map (for debugging purposes)
const brokenFilesMap = { images: {}, downloads: {}, streams: {} };

try {
  await migrate();
} catch (error) {
  logError(`Migration failed`, error);
}

/**
 * Migrate the episodes of a specific domain from MODX to MongoDB
 * @returns {Promise<void>}
 */
async function migrate({
  useCache = USE_CACHE,
  cacheFolder = CACHE_FOLDER,
} = {}) {
  const contextCacheFile = `${cacheFolder}/videos-list.json`;

  let rawResponse = {};

  // In case we want to use the cache,
  if (useCache) {
    try {
      // Read resources from cache folder
      const cacheEntries = await readFile(contextCacheFile, 'utf8');

      // If there are cache entries there,
      if (cacheEntries) {
        logInfo(`A cache for videos list was found. Reading from it...`);

        // parse them to the rawEntries array
        rawResponse = JSON.parse(cacheEntries || '{}');
      }
    } catch {
      logWarning(
        `No cache file found for videos list. Fetching from the API...`
      );
    }
  }

  if (Object.keys(rawResponse).length === 0) {
    // Get all videos from API
    const response = await fetch(API_VIDEOS_LIST_URL);
    rawResponse = (await response.json()) ?? {};

    if (useCache) {
      // Save the resources to cache folder for future use
      saveToJsonFile({
        data: rawResponse,
        fileName: 'videos-list',
        folder: cacheFolder,
      });
    }
  }

  const seasonsMap = {};

  for (const [showName, showData] of Object.entries(shows)) {
    const rawShowEpisodes = rawResponse[showName] || [];

    // Get the show's seasons map if it exists, otherwise an empty object
    const showSeasonsMap = showsSeasonsMap[showData.id] || {};

    // For each episode in the raw show episodes array,
    for (const rawEpisode of rawShowEpisodes) {
      // Extract the episode id from the raw episode
      // Used to check if the episode already exists in the map
      const { id } = rawEpisode || {};

      // Generate a new episode id (as ObjectId)
      const episodeId = newMongoDBUUid();

      // Skip the episode if it already exists in the map
      if (episodesMap[id] && episodesMap[id]?.title === rawEpisode.title) {
        logInfo(
          `Episode with an id ${id} and title (${rawEpisode.title}) already exists. Skipping...`
        );
        continue;
      }

      // Parse the raw episode entry to get the episode and its related data.
      const episode = await parseEpisode({
        episodeId,
        showId: showData.id,
        channelId,
        rawEpisode,
        imagesMap,
        seasonsMap: showSeasonsMap,
        organizationsMap,
        categoriesMap,
        episodeCategoriesMap,
        peopleMap,
        debugCollectorMap,
        brokenFilesMap,
        fileSizesMap,
      });

      // Skip the episode if it is not valid
      if (!episode) {
        continue;
      }

      // Add the episode to the map
      episodesMap[id] = episode;
    }

    // Save the show's seasons map to the cache map
    showsSeasonsMap[showData.id] = showSeasonsMap;

    // Add the show's seasons map to the global seasons map
    seasonsMap[showData.id] = showSeasonsMap;
  }

  // Handle episodes translations
  const episodesTranslations = rawResponse.translations || [];

  for (const rawEpisodeTranslation of episodesTranslations) {
    const { id, type, lang } = rawEpisodeTranslation || {};

    const showId = showsTypeMap[type];

    // Generate a new episode id (as ObjectId)
    const episodeId = newMongoDBUUid();

    // Skip the episode if it already exists in the map
    if (
      episodesMap[id] &&
      episodesMap[id]?.title === rawEpisodeTranslation.title
    ) {
      logInfo(
        `Episode with an id ${id} and title (${rawEpisodeTranslation.title}) already exists. Skipping...`
      );
      continue;
    }

    // Fix the episode id in case it contains some know issues
    const fixedEpisodeId = fixEpisodeId(id);

    // Calculate the original translation id, remove last 5 characters from the id, and add the '_en' suffix of the original.
    const translationOf = fixedEpisodeId.slice(0, -5).concat('_en');

    // Get the original episode from the episodes map
    const originalEpisode = episodesMap[translationOf];

    // If the original episode does not exist, log an error and skip the episode
    if (!originalEpisode) {
      logError(
        `Episode with an id ${id} (fixed to ${fixedEpisodeId}) and title (${rawEpisodeTranslation.title}) has no original translation. Skipping...`
      );
      continue;
    }

    // If the original episode does not have a translation in the map, add an empty array
    if (!episodesTranslationsMap[originalEpisode.id]) {
      episodesTranslationsMap[originalEpisode.id] = [];
    }

    // Add the translation to the original episode translations array
    episodesTranslationsMap[originalEpisode.id].push(fixEpisodeLanguage(lang));

    // Parse the raw episode entry to get the episode and its related data.
    const translatedEpisode = await parseEpisode({
      episodeId,
      showId,
      channelId,
      rawEpisode: rawEpisodeTranslation,
      imagesMap,
      fileSizesMap,
      brokenFilesMap,
      translationOf: originalEpisode.id,
    });

    // Skip the episode if it is not valid
    if (!translatedEpisode) {
      continue;
    }

    // Add the episode to the map
    episodesMap[episodeId] = translatedEpisode;
  }

  // Generate a flat array of seasons for the JSON output
  const seasons = Object.values(seasonsMap).reduce(
    (acc, showSeasons) => [
      ...acc, // Add the previous seasons
      ...Object.values(showSeasons), // Add the show's seasons
    ],
    []
  );

  // Save the seasons to the output folder
  saveToJsonFile({
    data: seasons,
    fileName: 'seasons',
    folder: DB_FOLDER,
  });

  // Generate arrays of shows seasons to update the shows with the seasons ids
  const showsSeasons = Object.entries(seasonsMap).reduce(
    (acc, [showId, showSeasons]) => ({
      ...acc,
      [showId]: Object.values(showSeasons).map(({ _id }) => _id),
    }),
    {}
  );

  // Save the shows seasons to the output folder
  saveToJsonFile({
    data: showsSeasons,
    fileName: 'shows-seasons',
    folder: DB_FOLDER,
  });
}

// Save the images in the cache folder, upload them to the CDN (within the entity folder), and update the images map with file objects
await saveImages({
  imagesMap,
  entityId: AWE_ENTITY_ID,
  tempFolder: CACHE_FOLDER,
  verbose: VERBOSE,
  customS3Client,
  bucket: BUCKET_IMAGES,
  dryRun: DRY_RUN || !IMPORT_IMAGES,
  brokenFilesMap,
});

// Save the categories to the output folder (this should be done only once)
saveCategories({ tempFolder: DB_FOLDER });

// Save the episodes to the output folder
saveEpisodes({
  episodesMap,
  imagesMap,
  episodesTranslationsMap,
  entityId: AWE_ENTITY_ID,
  outputFolder: DB_FOLDER,
  brokenFilesMap,
});

// Save the collected People to the output folder
savePeople({
  peopleMap,
  entityId: AWE_ENTITY_ID,
  channelId,
  outputFolder: DB_FOLDER,
});

// Save the episode categories to the cache folder
saveToJsonFile({
  data: episodeCategoriesMap,
  fileName: 'episode-categories',
  folder: CACHE_FOLDER,
});

// Save the people map into the cache folder
saveToJsonFile({
  data: peopleMap,
  fileName: 'people',
  folder: CACHE_FOLDER,
});

// Save the filesizes to the cache folder
saveToJsonFile({
  data: fileSizesMap,
  fileName: 'file-sizes',
  folder: CACHE_FOLDER,
});

// For debugging purposes:
if (DEBUG_MODE) {
  // Save the debug collector to the output folder
  saveToJsonFile({
    data: debugCollectorMap,
    fileName: 'debug-collector',
    folder: REPORTS_FOLDER,
  });
  // Save the broken files debug collector to the output folder
  saveToJsonFile({
    data: brokenFilesMap,
    fileName: 'broken-files-map',
    folder: REPORTS_FOLDER,
  });
  // Save the episodes translations map to the output folder
  saveToJsonFile({
    data: episodesTranslationsMap,
    fileName: 'episodesTranslationsMap',
    folder: REPORTS_FOLDER,
  });
}

logInfo('Migration completed');

// Exit the process (required for the script to end in Node v20)
exit(0);
