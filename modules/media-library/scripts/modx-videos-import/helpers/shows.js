import { logError } from '#utils/logger.js';
import fs from 'fs';

export const shows = {
  episodes: {
    id: '672a86e14254816bad4917dc',
    type: 'episode',
  },
  interviews: {
    id: '672a87564254816bad491de7',
    type: 'interview',
  },
  weekly: {
    id: '6710487bb90c7a6050ed456a',
    type: 'weekly',
  },
  monthly: {
    id: '677d6983612627a581f2b29b',
    type: 'monthly',
  },
};

export const channelId = '66bb690994db0292f4d2618d';

export const showsTypeMap = Object.values(shows).reduce(
  (acc, { id, type }) => ({
    ...acc,
    [type]: id,
  }),
  {}
);

/**
 * Load the shows seasons map from the cache Folder
 * @param {Object} params The parameters object
 * @param {String} params.tempFolder The folder that contains the seasons cache file
 * @returns {Object} The seasons map
 */
export function loadShowSeasonsMap({ tempFolder }) {
  // Read the season cache file if it exists
  const showsSeasonsCacheFile = `${tempFolder}/seasons.json`;
  if (fs.existsSync(showsSeasonsCacheFile)) {
    try {
      const showsSeasonsCache = fs.readFileSync(showsSeasonsCacheFile, 'utf8');
      return JSON.parse(showsSeasonsCache);
    } catch (error) {
      logError('Failed to read season cache file', error);
    }
  }

  // Return
  return {};
}
