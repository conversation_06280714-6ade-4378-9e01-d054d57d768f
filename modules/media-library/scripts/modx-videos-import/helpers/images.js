import fs from 'fs';
import https from 'https';

import { logError, logInfo } from '#utils/logger.js';
import { saveRemoteImage } from '#modules/images/services/imageServices.js';

/**
 * @typedef {{[url:string]: import('#utils/images.js').ImageFile}} ModxImportImageMap The images map from the MODX import
 */

/**
 * Download an image from a URL and save it to a file
 * @param {String} url The URL to the image
 * @param {String} targetPath The path to save the image
 * @returns {Promise<string>} The path to the saved image
 */
export function downloadImage({ url, targetPath }) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      if (res.statusCode === 200) {
        res
          .pipe(fs.createWriteStream(targetPath))
          .on('error', reject)
          .once('close', () => resolve(targetPath));
      } else {
        // Consume response data to free up memory
        res.resume();

        reject(
          new Error(
            `${url}: Request Failed With a Status Code: ${res.statusCode}`
          )
        );
      }
    });
  });
}

/**
 * Load the images map from the cache Folder
 * @param {Object} params The parameters object
 * @param {String} params.tempFolder The folder that contains the images cache file
 * @returns {ModxImportImageMap} The images map
 */
export function loadImagesMap({ tempFolder }) {
  const imagesCacheFile = `${tempFolder}/images.json`;

  // Read the images cache file if it exists
  if (fs.existsSync(imagesCacheFile)) {
    try {
      const imagesCache = fs.readFileSync(imagesCacheFile, 'utf8');
      return JSON.parse(imagesCache);
    } catch (error) {
      logError('Failed to read images cache file', error);
    }
  }

  return {};
}

/**
 * Save the images in the cache folder, upload them to the CDN and update the images map with file objects.
 * @param {Object} params The parameters object
 * @param {Object} params.imagesMap The map to save the data of the images (key: URL, value: file object)
 * @param {String} params.tempFolder The folder to save the images
 * @param {String} params.entityId The entity id to save the images to
 * @param {Boolean} params.dryRun Whether to skip saving the images or not
 * @param {Object} params.customS3Client The custom S3 client to use for uploading the images
 * @param {String} params.bucket The bucket to save the images to
 * @param {Boolean} params.verbose Whether to log the progress or not
 * @param {Object} params.brokenFilesMap The map to save the broken files
 * @returns {Promise<void>}
 */
export async function saveImages({
  imagesMap,
  tempFolder,
  entityId,
  dryRun = false,
  customS3Client = null,
  bucket = 'hope-images',
  verbose = false,
  brokenFilesMap = {},
}) {
  if (!tempFolder) {
    logError('Missing tempFolder to save images');
    return { error: 'Missing tempFolder to save images' };
  }

  // Calculate the total images to save
  let totalImages = Object.keys(imagesMap || {}).length;

  // If there are no images to save, return an error
  if (totalImages === 0) {
    logInfo('No images to save');
    return { error: 'No images to save' };
  }

  if (!entityId) {
    logError('Missing entityId to save images');
    return { error: 'Missing entityId to save images' };
  }

  // Create the temp folder if it doesn't exist yet
  if (!fs.existsSync(tempFolder)) {
    fs.mkdirSync(tempFolder, { recursive: true });
  }

  // The path to save the images cache file
  const imagesCacheFile = `${tempFolder}/images.json`;

  // The path to save the images cache file
  const tempImagesFolder = `${tempFolder}/images`;

  // Create the images folder if it doesn't exist yet
  if (!fs.existsSync(tempImagesFolder)) {
    fs.mkdirSync(tempImagesFolder, { recursive: true });
  }

  logInfo(`Saving ${totalImages} images to ${tempImagesFolder}`);

  fs.writeFileSync(imagesCacheFile, JSON.stringify(imagesMap, null, 2));

  // 1. Download the images to the cache folder and upload them to the CDN
  const imageUrls = Object.keys(imagesMap);
  const batchSize = 50;

  if (dryRun) {
    logInfo(`- Dry run: Skipping saving images.`);
  }

  for (let i = 0; i < imageUrls.length; i += batchSize) {
    const batchUrls = imageUrls.slice(i, i + batchSize);
    const batchPromises = batchUrls.map(async (imageUrl) => {
      // If the image is already saved (cached), skip it
      if (imagesMap[imageUrl]?.file) {
        // Skip the image if it's already saved
        return;
      }

      let imageFile = null;
      let imageError = null;

      // Avoid saving the images if it's a dry run,
      if (dryRun) {
        // Store the image as a dry-run
        imageFile = { type: 'dry-run', url: imageUrl };
      } else {
        try {
          // Save the image to the CDN
          const { file, error } = await saveRemoteImage({
            imageUrl,
            entityId,
            tempFolder: tempImagesFolder,
            customS3Client,
            bucket,
            verbose,
          });

          if (error) {
            imageError = error;
          } else {
            imageFile = file;
          }
        } catch (error) {
          imageError = error;
        }
      }

      // Get the debugId from the images map for this image
      const { debugId, originalUrl, episodeId, oldEpisodeId } =
        imagesMap[imageUrl];

      // If there is an error or the file is missing, log it
      if (imageError || !imageFile) {
        if (verbose) {
          logError(`Failed to save image ${imageUrl}`, {
            error: imageError,
            file: imageFile,
          });
        }

        if (brokenFilesMap?.images) {
          // Add the image to the images with errors map
          brokenFilesMap.images[imageUrl] = {
            error: imageError,
            debugId,
            episodeId,
            oldEpisodeId,
            originalUrl,
          };
        }
      } else {
        // Update the images map with the file object (overrides the prev value including debugId with the file object as it is not required anymore)
        imagesMap[imageUrl] = {
          file: imageFile,
          episodeId,
          oldEpisodeId,
          originalUrl,
        };
      }
    });

    await Promise.all(batchPromises);
  }

  totalImages = Object.keys(imagesMap || {}).length;

  // 3. Save the images map to the cache folder as a JSON file for later use as index
  try {
    fs.writeFileSync(imagesCacheFile, JSON.stringify(imagesMap, null, 2));
    logInfo(`Images map saved to ${imagesCacheFile}`);
  } catch (error) {
    logError('Failed to save images', error);
  }
}
