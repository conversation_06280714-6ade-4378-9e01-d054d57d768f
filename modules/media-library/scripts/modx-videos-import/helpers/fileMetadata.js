import fs from 'fs';
import { logError } from '#utils/logger.js';

/**
 * Loads a cached JSON file from the specified temp folder and returns its content.
 *
 * @param {Object} params The function parameters.
 * @param {String} params.tempFolder Path to the temporary folder containing the cached file.
 * @param {String} params.fileName Name of the file (without the .json extension).
 * @returns {Object} Parsed JSON object from the cached file, or an empty object if the file isn't found or can't be read.
 */
export function loadCachedFile({ tempFolder, fileName }) {
  const map = `${tempFolder}/${fileName}.json`;

  // Read the images cache file if it exists
  if (fs.existsSync(map)) {
    try {
      const cachedMap = fs.readFileSync(map, 'utf8');
      return JSON.parse(cachedMap);
    } catch (error) {
      logError('Failed to read images cache file', error);
    }
  }

  return {};
}

/**
 * Asynchronously checks if the provided resource exists by sending a HEAD request to its URL.
 *
 * @param {Object} params The parameters object.
 * @param {String} params.url The URL of the resource to check.
 * @returns {Promise<{ fileExists: Boolean, fileSize: Number }>} An object containing the fileExists flag and the fileSize.
 *
 * Logs errors and updates a debug collector map if the request fails or the response status is not 200.
 */
export async function checkIfFileExist({ url }) {
  const output = {
    fileExists: false,
    fileSize: 0,
  };

  try {
    const response = await fetch(url, { method: 'HEAD' });

    const { status, headers } = response || {};

    // Get the file size from the 'content-length' header
    const fileSize = Number(headers?.get('content-length') || 0);

    // If the file size is greater than 0, update the output
    if (fileSize > 0) {
      output.fileSize = fileSize;
    }

    // If the status is 200, consider that the file exists
    if (status === 200) {
      output.fileExists = true;
    }

    return output;
  } catch {
    return output;
  }
}
