import fs from 'fs';

import { logError, logInfo } from '#utils/logger.js';
import { slugify } from '#utils/strings.js';
import { toMongoDateObject } from '#utils/dates.js';
import { toJsonObjectId } from '#utils/api/mongoose/id.js';
import { getJSONOpenAIRequest } from '#modules/ai/helpers/openai.js';

// AWE's Global categories (with their respective production IDs)
export const globalCategoriesMap = {
  'Bible': '613a1516e5fc600a8720d0d5',
  'Education': '613a153be5fc600a8720d0ed',
  'Culture': '613a1528e5fc600a8720d0e1',
  'Documentary': '613a1533e5fc600a8720d0e7',
  'History': '613a1d3de5fc600a8720d110',
  'Youth': '613a1d83e5fc600a8720d13a',
  'Health': '613a1d33e5fc600a8720d10a',
  'People': '613a1d65e5fc600a8720d128',
  'Politics': '613a1d70e5fc600a8720d12e',
  'Children': '613a1521e5fc600a8720d0db',
  'Faith': '613a154ce5fc600a8720d0f9',
  'Family': '613a1554e5fc600a8720d0ff',
  'Music': '613a1d49e5fc600a8720d116',
  'Nature': '613a1d52e5fc600a8720d11c',
  'Adventists': '613a1501e5fc600a8720d0cf',
  'Events': '613a1543e5fc600a8720d0f3',
  'News': '613a1d5de5fc600a8720d122',
  'Sermon': '613a1d79e5fc600a8720d134',
  'Adventist Mission': '65e8f17fd9988b19076a7ed3',
  'Adventist History': '65e8f193d9988b19076a7f89',
  'Adventist Review': '65e8f3aad9988b19076a9c7d',
  'Religious Liberty': '66276273ab49f441c17cba28',
  'Breaking': '66276273ab49f441c17cba2d',
  'Business Meetings': '66276273ab49f441c17cba30',
  'Commentary': '66276274ab49f441c17cba33',
  'Crisis': '66276274ab49f441c17cba36',
  'GC Release': '66276274ab49f441c17cba3b',
  'Government': '66276274ab49f441c17cba3e',
  'Healthcare': '66276274ab49f441c17cba41',
  'Humanitarian': '66276274ab49f441c17cba46',
  'Leadership': '66276274ab49f441c17cba49',
  'Media': '66276274ab49f441c17cba4c',
  'Ministries': '66276274ab49f441c17cba4f',
  'Mission': '66276274ab49f441c17cba52',
  'Obituary': '66276274ab49f441c17cba57',
  'Organization': '66276274ab49f441c17cba5a',
  'Press Release': '66276274ab49f441c17cba5d',
  'Profiles': '66276274ab49f441c17cba60',
  'Science': '66276274ab49f441c17cba63',
  'Service': '66276274ab49f441c17cba66',
  'Technology': '66276274ab49f441c17cba69',
  'Testimony': '66276274ab49f441c17cba6c',
  'Theology': '66276274ab49f441c17cba6f',
  'Viewpoints': '66276274ab49f441c17cba72',
  'I Will Go': '66276274ab49f441c17cba75',
  'Human Interest': '66276274ab49f441c17cba7e',
};

// List of categories provided by GC's Adventist Mission department (with their respective production IDs)
export const adventistMissionCategoriesMap = {
  '10/40 Window': '678820f65617eb77e0ee4d95',
  '13th Sabbath Offering': '678820f65617eb77e0ee4d96',
  'Adventist Volunteer Service': '678820f65617eb77e0ee4d97',
  'Annual Sacrifice Offering': '678820f65617eb77e0ee4d98',
  'Center for Adventist-Buddhist Relations': '678820f65617eb77e0ee4d99',
  'Center for Adventist-Jewish Relations': '678820f65617eb77e0ee4d9a',
  'Center for Adventist-Muslim Relations': '678820f65617eb77e0ee4d9b',
  'Center for Secular and Post-Christian Mission': '678820f65617eb77e0ee4d9c',
  'Center for South Asian Religions': '678820f65617eb77e0ee4d9d',
  'Center for Urban Mission': '678820f65617eb77e0ee4d9e',
  "Children's Story": '678820f65617eb77e0ee4d9f',
  'Church Planting': '678820f65617eb77e0ee4da0',
  'Conversion Story': '678820f65617eb77e0ee4da1',
  'Discipling': '678820f65617eb77e0ee4da2',
  'Division Overview': '678820f65617eb77e0ee4da3',
  'DMA - Deferred Medical Appoint': '678820f65617eb77e0ee4da4',
  'GC Session Offering': '678820f65617eb77e0ee4da5',
  'Global Mission Center': '678820f65617eb77e0ee4da6',
  'Global Mission pioneer': '678820f65617eb77e0ee4da7',
  'Global Mission Project': '678820f65617eb77e0ee4da8',
  'Global Mission-Funded center of influence': '678820f65617eb77e0ee4da9',
  'ISE - Inter-Service Employee': '678820f65617eb77e0ee4daa',
  'Life Hope Center': '678820f65617eb77e0ee4dab',
  'Mission Offering': '678820f65617eb77e0ee4dac',
  'Mission To The Cities': '678820f65617eb77e0ee4dad',
  'Music Video': '678820f65617eb77e0ee4dae',
  'Past 13th Sabbath Offering Project': '678820f65617eb77e0ee4db0',
  'Post-Christian': '678820f65617eb77e0ee4db1',
  'Secular': '678820f65617eb77e0ee4db2',
  'Small Opportunity Projects': '678820f65617eb77e0ee4db3',
  'Small Projects': '678820f65617eb77e0ee4db4',
  'Special Feature': '678820f65617eb77e0ee4db5',
  'Special Needs': '678820f65617eb77e0ee4db6',
  'Tentmaking': '678820f65617eb77e0ee4db7',
  'Total Employment': '678820f65617eb77e0ee4db8',
  'UCI -  Urban Center of Influence': '678820f65617eb77e0ee4db9',
  'Unreached People Group': '678820f65617eb77e0ee4dba',
  'Unusual Opportunities': '678820f65617eb77e0ee4dbb',
  'Urban Mission': '678820f65617eb77e0ee4dbc',
  'Veiled location': '678820f65617eb77e0ee4dbd',
  'Vivid Faith': '678820f65617eb77e0ee4dbe',
  'Waldensian Students': '678820f65617eb77e0ee4dbf',
};

// Merge the global and Adventist Mission categories maps
export const categoriesMap = {
  ...globalCategoriesMap,
  ...adventistMissionCategoriesMap,
};

// Create an array with all the categories names:
export const categoriesNames = Object.keys(categoriesMap);

/**
 * Load the episodeCategories map from the cache Folder
 * @param {Object} params The parameters object
 * @param {String} params.tempFolder The folder that contains the episodeCategories cache file
 * @returns {ModxImportImageMap} The episodeCategories map
 */
export function loadEpisodeCategoriesMap({ tempFolder }) {
  const episodeCategoriesCacheFile = `${tempFolder}/episode-categories.json`;

  // Read the episodeCategories cache file if it exists
  if (fs.existsSync(episodeCategoriesCacheFile)) {
    try {
      const episodeCategoriesCache = fs.readFileSync(
        episodeCategoriesCacheFile,
        'utf8'
      );
      return JSON.parse(episodeCategoriesCache);
    } catch (error) {
      logError('Failed to read episodeCategories cache file', error);
    }
  }

  return {};
}

/**
 * Categorize the video episode using OpenAI
 * @param {Object} params The parameters object
 * @param {String} params.title The title of the episode
 * @param {String} params.description The description of the episode
 * @param {String[]} params.tags The fixed tags of the episode
 * @param {String} params.transcript The transcript of the episode
 * @returns {Promise<String[]>} The suggested categories for the episode
 */
export async function categorizeEpisodeWithAI({
  title,
  description,
  tags,
  transcript,
}) {
  // Extract the categories from the title, abstract and tags using OpenAI
  const content = await getJSONOpenAIRequest({
    prompt: `Categorize the following video episode based on its title, abstract, and optionally transcript and tags, from a list of available categories. Return a JSON array. Here are the provided values:
        Title: "${title}"
        Abstract: "${description}"
        Tags: "${tags ? tags?.join(', ') : ''}"
        Available Categories: "${categoriesNames.join(', ')}"
        Transcript: "${transcript}"
        `,
  });

  // Get the categories from the parsed content or return an empty array
  return content?.categories || [];
}

/**
 * Save the categories to a JSON to be imported into a MongoDB
 * @param {Object} params The parameters object
 * @param {String} params.tempFolder The folder to save the categories JSON file
 * @returns {void} Nothing, as it saves the categories to a file
 */
export function saveCategories({ tempFolder }) {
  // Get the current date
  const now = toMongoDateObject(new Date().toISOString());

  // Create the categories array
  // NOTE: only the Adventist Mission categories!
  const categories = Object.entries(adventistMissionCategoriesMap).map(
    ([category, id]) => ({
      _id: toJsonObjectId(id),
      title: { en: category },
      name: slugify(category), // Slugified version of the category in English
      type: 'entity',
      entity: toJsonObjectId(process.env.AWE_ENTITY_ID),
      importIDs: [
        {
          type: 'modx-videos-import',
          id: category,
        },
      ],
      enabled: true,
      deleted: false,
      createdAt: now,
      updatedAt: now,
    })
  );

  const categoriesFile = `${tempFolder}/categories.json`;

  try {
    fs.writeFileSync(categoriesFile, JSON.stringify(categories, null, 2));
    logInfo('Categories saved to file', categoriesFile);
  } catch (error) {
    logError('Failed to save categories to file', error);
  }
}
/**
 * Get categories ids from the categories map based on category names
 * @param {Object} params The parameters object
 * @param {String[]} params.categories The categories names
 * @returns {String[]} The categories ids
 */
export function getCategoriesIds({ categories }) {
  return categories
    .map((category) => categoriesMap[category]) // Get the category id from the categories map
    .filter(Boolean) // Remove undefined values
    .map((id) => toJsonObjectId(id)); // Convert the ids to ObjectId objects
}
