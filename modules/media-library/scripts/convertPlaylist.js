import { DateTime, Duration } from 'luxon';

/**
 * @Example of an import
 * <?xml version="1.0" encoding="utf-8" standalone="yes" ?>
 * <broadcasts>
 *     <broadcast>
 *         <date>2010-01-30</date>
 *         <time>0:30</time>
 *         <duration>00:30:00</duration>
 *         <series><![CDATA[The show 1 title]]></series>
 *         <program><![CDATA[The program 1 title]]></program>
 *         <programuid>CC-001</programuid>
 *         <language>LANGUAGE</language>
 *     </broadcast>
 *     <broadcast>
 *         <date>2010-01-30</date>
 *         <time>1:00</time>
 *         <duration>01:00:00</duration>
 *         <series><![CDATA[The show n title]]></series>
 *         <program><![CDATA[The program n title]]></program>
 *         <programuid>CC-00n</programuid>
 *         <language>LANGUAGE</language>
 *     </broadcast>
 * </broadcasts>
 */

export function convertMcrsPlaylist(parsedXML) {
  // Our docs have broadcasts as the top level of the xml
  // Under it is broadcast (singular). Each broadcast has other elements inside
  // Such as date, time, duration, series, program, programuid, and language

  // We will return an array of broadcasts
  const broadcast = []; // Here we insert a broadcast element for each item

  const programs = parsedXML.mcrs_playlist.program;
  // console.log(`Program found in file  -  ${program}`);

  //Date that is set by the first program, then in the
  // iteration scope gets modified for each program if there was a change in day.
  let prevDate = '';

  //each broadcast gets built and then added to the broadcast array
  //In an MCRlist file, the program is the series, and the item is the program (episode)
  programs.forEach((program) => {
    //each program is an object inside the broadcast array
    // console.log(program.block.item);
    const series = program['@_name']; //series name in xml
    const items = !Array.isArray(program.block.item)
      ? [program.block.item]
      : program.block.item; //

    // items is an array of objects
    // We can access the first object by doing items[0] and get the name and guid
    // This name will represent this specific item and program (broadcast).
    const itemProgramName = items[0]['@_name'] || undefined;
    const itemProgramGuid = items[0].guid || undefined;

    // We declare a variable to hold the duration of the program, if there is more than 1 item in the program,
    // we will add the duration of each item to get the total duration of the program.
    let programDuration;
    let programDate;
    let programTime;

    // We iterate through each item in the program to retreive the duration, and name
    items.forEach((item) => {
      const duration = item['@_out']; //duration has the format

      const luxonDuration = Duration.fromObject({
        hours: duration.split(':')[0],
        minutes: duration.split(':')[1],
        seconds: duration.split(':')[2],
        milliseconds: duration.split(':')[3],
      });

      let date = '';
      let time = '';
      if (item['@_start']) {
        const start = item['@_start'];
        //check if start is a year
        if (start.charAt(4) === '-') {
          time = start.substring(11, 16);
          date = start.substring(0, 10);

          //we set
          const iterationDate = DateTime.fromISO(start, { zone: 'utc' });
          // console.log(iterationDate);

          // We set the previous date for the next program to use.
          prevDate = iterationDate.plus(luxonDuration);
        } else {
          date = prevDate.toISO().substring(0, 10);
          time = prevDate.toISO().substring(11, 16);

          prevDate = prevDate.plus(luxonDuration);
        }
      } else {
        // We set the date from the prevDate, and set the time accordingly.
        date = prevDate.toISO().substring(0, 10);
        time = prevDate.toISO().substring(11, 16);

        prevDate = prevDate.plus(luxonDuration);
      }

      //if there is no programDuration yet, we set it to the duration of the first item, and the date and time
      if (!programDuration) {
        programDuration = luxonDuration;
        programDate = date;
        programTime = time;
      } else {
        //if there is a programDuration, we add the duration of the current item to the programDuration
        programDuration = programDuration.plus(luxonDuration);
      }
    });

    // We format the duration to be in the format HH:MM:SS
    const formattedDuration = `${
      programDuration.hours
    }:${programDuration.minutes
      .toString()
      .padStart(2, '0')}:${programDuration.seconds
      .toString()
      .padStart(2, '0')}`;

    // We create a broadcast object to push to the broadcast array
    const broadcastItem = {
      date: programDate,
      time: programTime,
      duration: formattedDuration,
      series: series,
      program: itemProgramName,
      programuid: itemProgramGuid,
    };

    // Push the broadcast object only if itemProgramName and itemProgramGuid are not empty
    if (itemProgramName && itemProgramGuid) broadcast.push(broadcastItem);
  });

  //<!— The first <item> of <program> informs about the program start and the end of earlier program. [Can be in format with date and time 2022-12-09T04:00:00Z  ] —>

  return broadcast; // we return the broadcast array
}

export default { convertMcrsPlaylist };
