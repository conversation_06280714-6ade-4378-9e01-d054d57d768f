import { DateTime } from 'luxon';
import fs from 'fs';
import ftp from 'basic-ftp';
import { XMLParser } from 'fast-xml-parser';

import Logger from '#utils/logger.js';
import { notifyAdmins, sendEmail } from '#utils/notifier.js';
import { t } from '#utils/translator.js';
import { toLocaleStringFullWithSeconds, toLogDate } from '#utils/dates.js';

import Broadcast from '../models/Broadcast.js';
import Episode from '../models/Episode.js';
import { convertMcrsPlaylist } from './convertPlaylist.js';
import Channel from '../models/Channel.js';

const { FTP_HOST } = process.env;
const { FTP_USER } = process.env;
const { FTP_PASS } = process.env;

const tempFolder = 'temp/hope-schedule';

/**
 * Imports a schedule from an XML file
 * @param {Object} options - The options object
 * @param {String} options.channelId - The channel ID
 * @param {String} options.filePath - The file path
 * @returns {Object} The result object
 */
export async function scheduleImport({ channelId = '', filePath = '' } = {}) {
  let deleted = 0;
  let found = 0;
  let notFound = 0;
  let firstDate;
  let lastDate;

  if (!channelId || !filePath) {
    Logger.error('No channelId or filePath provided!');
  } else {
    const parser = new XMLParser();

    try {
      const xmlData = fs.readFileSync(filePath);

      const parsedXML = parser.parse(xmlData);

      // Here we detect if the xml comes with the tag cinergy air control
      if (parsedXML.mcrs_playlist) {
        const parseOptions = {
          attributeNamePrefix: '@_',
          ignoreAttributes: false,
        };
        const attributeParser = new XMLParser(parseOptions); //we need the attributes
        const reparsedXML = attributeParser.parse(xmlData);
        // console.log(reparsedXML);

        const broadcast = convertMcrsPlaylist(reparsedXML);
        parsedXML.broadcasts = { broadcast: broadcast }; // returns the broadcast array converted to our format
      }

      // Then we continue the import as usual
      if (
        parsedXML.broadcasts &&
        Array.isArray(parsedXML.broadcasts.broadcast)
      ) {
        const broadcasts = parsedXML.broadcasts.broadcast;
        const firstBroadcast = broadcasts[0];
        const lastBroadcast = broadcasts[broadcasts.length - 1];

        // Start date of first broadcast
        firstDate = DateTime.fromISO(firstBroadcast.date, {
          zone: 'utc',
        })
          .set({
            hour: firstBroadcast.time.split(':')[0],
            minute: firstBroadcast.time.split(':')[1],
          })
          .toJSDate();
        // End date of last broadcast
        lastDate = DateTime.fromISO(lastBroadcast.date, { zone: 'utc' })
          .set({
            hour: lastBroadcast.time.split(':')[0],
            minute: lastBroadcast.time.split(':')[1],
          })
          .plus({
            hours: lastBroadcast.duration.split(':')[0],
            minutes: lastBroadcast.duration.split(':')[1],
            seconds: lastBroadcast.duration.split(':')[2],
          })
          .toJSDate();

        const res = await Broadcast.deleteMany({
          channel: channelId,
          startsAt: { $gte: firstDate },
          endsAt: { $lte: lastDate },
        });

        deleted = res.deletedCount;

        // Include entity channels to check for existing episodes
        const channel = await Channel.findById(channelId);
        const { includeEntityChannels } = channel.scheduleImport;
        let entityChannelIds = [];
        if (includeEntityChannels) {
          const entityId = channel.entity;
          const entityChannels = await Channel.find({ entity: entityId });
          entityChannelIds = entityChannels.map((c) => c._id);
        }

        Logger.info(`Attempting to import ${broadcasts.length} broadcasts`);

        for (let index = 0; index < broadcasts.length; index += 1) {
          Logger.info(`Importing ${index + 1} of ${broadcasts.length}`);

          const broadcast = broadcasts[index];

          const { date, time, duration, programuid, program, series } =
            broadcast;
          const [startHour, startMinute] = time.split(':');
          const [hours, minutes, seconds] = duration.split(':');

          const startsAt = DateTime.fromISO(date, { zone: 'utc' }).set({
            hour: startHour,
            minute: startMinute,
          });

          const endsAt = startsAt.plus({ hours, minutes, seconds });

          const baseBroadcast = {
            startsAt: startsAt.toJSDate(),
            endsAt: endsAt.toJSDate(),
            scheduleID: programuid,
            channel: channelId,
          };

          const episode = await Episode.findOne({
            channel: includeEntityChannels
              ? { $in: entityChannelIds }
              : channelId,
            scheduleIDs: {
              $elemMatch: {
                scheduleID: programuid,
                enabled: true,
              },
            },
          });

          if (episode) {
            // !NOTE: This uses `updateOne` instead of `create` since we run this on two identical server instances concurrently. This attempts to avoid duplicates using the `upsert: true` option
            await Broadcast.updateOne(
              baseBroadcast,
              {
                ...baseBroadcast,
                episode,
              },
              { upsert: true }
            );
            found += 1;
          } else {
            // !NOTE: This uses `updateOne` instead of `create` since we run this on two identical server instances concurrently. This attempts to avoid duplicates using the `upsert: true` option
            await Broadcast.updateOne(
              baseBroadcast,
              {
                ...baseBroadcast,
                episodeTitle: program,
                showTitle: series,
              },
              { upsert: true }
            );
            notFound += 1;
          }
        }

        Logger.success(
          `Imported broadcasts between ${firstDate} and ${lastDate}`
        );
        Logger.success(`Deleted: ${deleted}`);
        Logger.success(`Added: ${found + notFound}`);
        Logger.success(`Found: ${found}`);
        Logger.success(`Not Found: ${notFound}`);
      } else {
        Logger.warning('No broadcasts found in the provided file');
      }
    } catch (error) {
      Logger.error('Error', error);
    }
  }

  return {
    firstDate,
    lastDate,
    deleted,
    found,
    notFound,
    total: found + notFound,
  };
}

/**
 * Imports a schedule from an XML file via FTP
 * @param {Object} options - The options object
 * @param {Object} options.channel - The channel object
 * @returns {Promise<void>}
 */
export async function ftpScheduleImport({ channel = {} } = {}) {
  const { entity, title } = channel;

  Logger.info(`Attempting FTP schedule import for ${title}`);

  if (
    channel.scheduleImport &&
    channel.scheduleImport.enabled &&
    channel.scheduleImport.ftpLocation
  ) {
    const { ftpLocation, feedbackEmail } = channel.scheduleImport;
    const language = entity.language || 'en';

    const client = new ftp.Client();

    try {
      await client.access({
        host: FTP_HOST,
        user: FTP_USER,
        password: FTP_PASS,
      });

      await client.ensureDir(ftpLocation);

      const files = await client.list();

      const file = files[0];

      if (file && file.name.includes('.xml')) {
        Logger.success('XML file found. Running import...');

        await client.downloadToDir(`${tempFolder}/${ftpLocation}`);

        // Import schedule
        const result = await scheduleImport({
          channelId: channel.id,
          filePath: `${tempFolder}/${ftpLocation}/${file.name}`,
        });

        const { firstDate, lastDate, deleted, found, notFound, total } = result;

        sendEmail({
          language,
          entity,
          to: `<EMAIL>, ${feedbackEmail || ''}`,
          subject: t(language, 'scheduleImportTitle', {
            channel: title,
          }),
          templateName: 'scheduleImport',
          templateValues: {
            title: t(language, 'scheduleImportTitle', {
              channel: title,
            }),
            nothingImported: !firstDate || !lastDate,
            nothingImportedText: t(language, 'scheduleImportNoImport'),
            firstDate: t(language, 'scheduleImportFirstDate', {
              firstDate: toLocaleStringFullWithSeconds(firstDate, language),
            }),
            lastDate: t(language, 'scheduleImportLastDate', {
              lastDate: toLocaleStringFullWithSeconds(lastDate, language),
            }),
            deleted: t(language, 'scheduleImportDeleted', { deleted }),
            found: t(language, 'scheduleImportFound', { found }),
            notFound: t(language, 'scheduleImportNotFound', { notFound }),
            total: t(language, 'scheduleImportTotal', { total }),
          },
        });

        // Delete files from ftp
        await client.clearWorkingDir();
      } else {
        Logger.warning('No XML file found for schedule import.');
      }

      // Close ftp connection
      client.close();
    } catch (error) {
      Logger.error('Error trying to run FTP schedule import', error);

      notifyAdmins({
        subject: 'API - FTP schedule import error!',
        templateName: 'requestError',
        templateValues: {
          status: 500,
          data: {
            Date: toLogDate(new Date()),
            Channel: title,
            Error: JSON.stringify(error),
          },
        },
      });
    }
  } else {
    Logger.warning(`No FTP schedule import configured for ${title}`);
  }
}
