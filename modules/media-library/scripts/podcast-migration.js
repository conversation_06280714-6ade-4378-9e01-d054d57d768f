import Logger from '#utils/logger.js';
import {
  createJestreamEpisode,
  createJetstreamMedia,
  generateJetstreamCode,
  getMediaUploadUrl,
  uploadFileToJetstream,
} from '#utils/jetstream.js';
import { downloadFile } from '#utils/storage.js';
import Episode from '../models/Episode.js';
import Show from '../models/Show.js';

const accessToken = process.env.JETSTREAM_GRAPHQL_ACCESS_TOKEN;
const apiUrl = process.env.JETSTREAM_GRAPHQL_API_URL;

export async function migratePodcasts(req, res) {
  const { organizationId, showId } = req.body;

  if (!showId || !organizationId) {
    throw new Error('Organization ID of Jetstream missing');
  }
  // If the show of the episode  doesn't have a brandId, we can't migrate it, skip and go to the next episode.
  const show = await Show.findById(showId, 'jetstream').lean();
  if (!show.jetstream.brandId) {
    throw new Error('Brand ID of Jetstream missing');
  }

  const episodes = await Episode.find({
    'mediaLinks.mp3.link': {
      $regex: /^https:\/\/videos\.hopechannel\.de/,
    },
    'show': showId,
    // Also that it doesn't have a new link, so we don't migrate it twice.
    '$or': [
      { 'mediaLinks.mp3.newLink': { $exists: false } },
      { 'mediaLinks.mp3.newLink': '' },
      { 'mediaLinks.mp3.newLink': null },
    ],
  }).lean();

  const page = 1; // Change this value if everything went well.

  const limit = 200;

  Logger.info(
    'Total episodes to migrate:',
    episodes.length,
    'Starting at:',
    (page - 1) * limit,
    'Until:',
    page * limit
  );

  const failedEpisodes = [];

  const episodesArray = episodes
    .slice((page - 1) * limit, page * limit)
    .map(async (episode, index) => {
      try {
        // We get the typo3 url we want to download from
        const previousUrl = episode.mediaLinks.mp3.link;

        // We download the file
        const tempFilename = `${episode._id}.mp3`; // We use the episode id as filename.
        const filePath = await downloadFile(previousUrl, tempFilename); // We download to a local temp folder.

        // We download to a local temp folder.

        // Step 1. We get the signed url from jetstream.
        const signedUrl = await getMediaUploadUrl();
        if (!signedUrl) {
          throw new Error('Could not get signed url from jetstream');
        }

        // Step 2. We upload the file to the new url.
        const uploadResponse = await uploadFileToJetstream(filePath, signedUrl);

        if (!uploadResponse) {
          throw new Error('Upload to jetstream failed');
        }

        // Media url that is used to create the jetstream media. (For step 3)
        // Public URL
        const mediaUrl = `https://storage.googleapis.com/${uploadResponse.bucket}/${uploadResponse.name}`;

        // Code to be used in jetstream, it was required, they could generate this automatically in the future.
        const headers = {
          'content-type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        };
        const code = await generateJetstreamCode({ headers, apiUrl });

        // We create the episode in jetstream under the brand of the show, returns the id of the episode.
        const jetstreamEpisode = await createJestreamEpisode({
          brandId: show.jetstream.brandId.includes('hcb:')
            ? show.jetstream.brandId
            : `hcb:${show.jetstream.brandId}`,
          code,
          organizationId,
          title: episode.title,
        });
        const jetstreamEpisodeId = jetstreamEpisode?.createEpisode?.id || null;

        if (!jetstreamEpisodeId) {
          throw new Error('Could not create episode in jetstream');
        }

        // console.log('jetstreamEpisodeId', jetstreamEpisodeId);

        // Step 3: We create the jetstream media.
        const media = await createJetstreamMedia({
          contentType: 'AUDIO',
          title: episode.title,
          originalFileName: episode._id,
          filesize: uploadResponse.size,
          mediaUrl,
          organizationId,
          code,
          attachToEpisodeId: jetstreamEpisodeId || null, // If episode was created, we attach the media to it.
        }); // Media returned has id, title, and mediaUrl.

        Logger.info('Media created in Jetstream', media);
        // We get the aac url from the master url.
        // Example of (master) https://eu.jstre.am/audios/jsa:5OYPtYS87q9/master/64c0d81a67cbf973044f5de6.mp3
        // Example of aac url https://eu.jstre.am/audios/jsa:5OYPtYS87q9/aac/64c0d81a67cbf973044f5de6.m4a
        const aacAudioUrl = media.mediaUrl
          .replace('master', 'aac')
          .replace('.mp3', '.m4a');
        Logger.info('New link for the episode:', aacAudioUrl);

        // // We update the episode with the new url.
        // await Episode.findByIdAndUpdate(
        //   episode._id,
        //   {
        //     $set: {
        //       'mediaLinks.mp3.newLink': aacAudioUrl,
        //       'mediaLinks.mp3.oldLink': previousUrl, // We save the old link in case we need it later.
        //       'mediaLinks.mp3.migratedOn': new Date(),
        //       'mediaLinks.mp3.filesize': uploadResponse.size,
        //       'mediaLinks.mp3.contentType': 'AUDIO',
        //     },
        //   }
        //   // { new: true } // The new updated document will be returned
        // ).lean();

        Logger.info(
          `Episode ${index + 1}: ${episode.title} - has been migrated`
        );

        // Should we return the updated episode? Or just say say that it was migrated correctly?
        return `Migrated correctly - ${episode._id} - ${episode.title}`;
      } catch (error) {
        Logger.error(error);
        failedEpisodes.push(episode);
        // return `Error migrating - ${episode._id} - ${episode.title} - ${error}`;
        return `${episode.title} - ${episode.mediaLinks.mp3.link}`;
      }
    });

  const result = await Promise.all(episodesArray); // We use promise all to wait for all episodes to be migrated.
  Logger.info('Status of each episode', result);

  const failedIds = failedEpisodes.map((episode) => episode._id);
  Logger.info('Episodes that failed to migrate', failedIds);

  return result;
}

async function isAudioUrl(url) {
  const controller = new AbortController();
  const { signal } = controller;

  const response = await fetch(url, {
    method: 'GET',
    signal,
  });

  const isAudio = response.headers.get('content-type').includes('audio');

  controller.abort();

  return isAudio;
}

export async function cleanMigratedPodcasts({ showId }) {
  // Finds episodes that have been migrated but not turned on to the new link.
  const episodes = await Episode.find({
    'mediaLinks.mp3.link': {
      $regex: /^https:\/\/videos\.hopechannel\.de/,
    },
    'mediaLinks.mp3.newLink': { $exists: true, $ne: '' },
    'show': showId,
  }).lean();

  const show = await Show.findById(showId, 'title').lean();

  Logger.info(
    'Episodes to be cleaned:',
    episodes.length,
    'for show:',
    show.title
  );

  const failedEpisodes = [];

  const episodesArray = episodes.slice(0 - 50).map(async (episode, index) => {
    try {
      // Test if the episode is migrated correctly.
      const isAudio = await isAudioUrl(episode.mediaLinks.mp3.newLink);
      if (!isAudio) {
        throw new Error(
          'New link is not an audio file. It probably is processing or has an error'
        );
      }

      // We update the link to the new link.
      await Episode.findByIdAndUpdate(
        episode._id,
        {
          $set: {
            'mediaLinks.mp3.link': episode.mediaLinks.mp3.newLink, // OldLink still exists, we can use it to revert, if needed.
          },

          // We remove the "newLink" now that it has been updated.
          $unset: {
            'mediaLinks.mp3.newLink': '',
          },
        }
        // { new: true } // The new updated document will be returned
      ).lean();

      Logger.info(`Episode ${index + 1}: ${episode.title} - has been cleaned`);

      // Should we return the updated episode? Or just say say that it was migrated correctly?
      return `Cleaned correctly - ${episode._id} - ${episode.title}`;
    } catch (error) {
      Logger.error(error);
      failedEpisodes.push(episode);
      return `Error cleaning - ${episode._id} - ${error}`;
    }
  });

  const result = await Promise.all(episodesArray); // We use promise all to wait for all episodes to be migrated.
  Logger.info('Status of each episode', result);

  Logger.info('Episodes that failed to clean', failedEpisodes);

  return { result };
}

export default {
  cleanMigratedPodcasts,
  migratePodcasts,
};

// Extra code for other files, like episode controller, etc.

// export const migratePodcasts = async (req, res) => {
//   const data = await podcastServices.migratePodcasts(req, res);

//   res.status(200).json(data);
// };

// export const cleanMigratedPodcasts = async (req, res) => {
//   const { showId } = req.body;

//   if (!showId) {
//     res.status(400).json({ error: 'Show ID is required' });
//   }

//   const { result, error } = await podcastServices.cleanMigratedPodcasts({
//     showId,
//   });

//   if (error) {
//     res.status(500).json({ error });
//   }

//   res.status(200).json(result);
// };
