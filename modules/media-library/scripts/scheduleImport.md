# Schedule import

## Enabling FTP schedule import for a channel

> All necessary credentials are in KeePass, under CMS->Hope Platform.

### 1. Create the folder where the files will be uploaded

- Connect with an FTP client using the production credentials.
- Create the folder for the channel.

### 2. Create the FTP user for client to upload the files

- Log in to the FTP backend.
- Go to `USER` section and under `schedule` go to `Login as schedule` (the option appears when you hover the row).
- Now, under the `WEB` section, click to edit `schedule.hopeplatform.org`.
- Click on `Add one more FTP Account`, located at the end of the page.
- Provide a `username`, `password` and `path`.
  > **IMPORTANT**: Make sure the path starts with `/production`, followed by the name of the folder you just created. For example, if the folder is `hope-channel-deutsch`, the path should be `/production/hope-channel-deutsch`.
- Click on save.

### 3. Add the schedule import configuration to the channel

The client will need to go to the channel edition page, under the `Advanced` tab, go to the `Schedule import` section and add:

- FTP location: the name of the folder created in the FTP server. **Just the folder, not the complete path!** For instance, `hope-channel-deutsch`
- Feedback email: comma separated list of emails where they wish to receive the import results.
- Import enabled: the import will run only if this is checked.
