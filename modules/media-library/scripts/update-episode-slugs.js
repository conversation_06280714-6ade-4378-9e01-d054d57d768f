/* eslint-disable no-console */
import { slugify } from '#utils/strings.js';

import Channel from '#modules/media-library/models/Channel.js';
import Episode from '#modules/media-library/models/Episode.js';

async function execute(task) {
  // Get task settings
  const channelId = task.settings.channel;

  // Get episodes
  const episodes = await Episode.find({
    channel: channelId,
    deleted: false,
  })
    .select('title show slug')
    .sort('-createdAt');

  // Get channel
  const channel = await Channel.findById(channelId, 'title');

  console.log(channel.title.toUpperCase());
  console.log('-------------------------------');
  console.log(`${episodes.length} episodes:`);

  // Loop through episodes
  let i = 1;
  let updatedSlugs = 0;
  for (const episode of episodes) {
    // Check if slug has changed
    const slugifiedTitle = slugify(episode.title);
    if (slugifiedTitle === episode.slug) continue;

    // Create new slug and ensure new slug doesn't exist
    const newSlug = await Episode.getAvailableSlug(
      slugifiedTitle,
      episode.show,
      episode._id
    );

    // Save new slug
    console.log(episode.id);
    console.log(episode.title);
    console.log(`${episode.slug} (episode)`);
    console.log(`${slugifiedTitle} (slugify)`);
    console.log(`${newSlug} (new + unique)`);
    console.log();
    await Episode.updateOne({ _id: episode.id }, { slug: newSlug });
    updatedSlugs += 1;

    // Print progress
    if (i % 100 === 0) console.log(i);

    i += 1;
  }

  console.log('-------------------------------');
  console.log(`${updatedSlugs} slugs updated.`);
  console.log();

  return {};
}

async function getTitle(task) {
  const { settings } = task;
  const { channel } = settings;
  return `Update episode slugs (${channel})`;
}

export default {
  execute,
  getTitle,
};
