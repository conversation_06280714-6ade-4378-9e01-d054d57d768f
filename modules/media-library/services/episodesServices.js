import mongoose from 'mongoose';

import Page from '#modules/web/models/Page.js';
import { getDynamicRecordPagesByResource } from '#modules/web/services/webServices.js';

import { getTranslationDoneField } from '#utils/aggregations.js';
import getListFilters from '#utils/api/list/filters.js';
import { getRecordById } from '#utils/api/model/getRecordById.js';
import { isValidObjectId, toObjectId } from '#utils/api/mongoose/id.js';
import { errors } from '#utils/appError.js';
import { isEmpty } from '#utils/arrays.js';
import { eventEmitter } from '#utils/eventEmitter.js';
import { areEqualIDs } from '#utils/helpers.js';
import { createJetstreamUploadedEpisode } from '#utils/jetstream.js';
import Logger from '#utils/logger.js';
import { slugify } from '#utils/strings.js';

import { episodeEvents } from '../events/episodeEvents.js';
import { createOrUpdateMediaLinks } from '../helpers/createOrUpdateMediaLinks.js';
import { POPULATE_EPISODE } from '../helpers/populateEpisode.js';
import Episode from '../models/Episode.js';
import channelServices from './channelServices.js';
import seasonServices from './seasonServices.js';
import showsServices from './showsServices.js';

// Check if "expected" translations are done for a episode:
// - Define expected translation fields
const expectedTranslationFields = [
  { name: 'title', type: 'string' },
  { name: 'slug', type: 'string' },
  { name: 'body', type: 'object' },
  //   { name: 'abstract', type: 'string' }, // ASK: Is the abstract expected for an episode to be considered translated?
];

// - set translationDone field using an conditional aggregation
const translationDone = getTranslationDoneField(expectedTranslationFields);

/**
 * Get episodes
 * @param {Object} options - Options object
 * @param {String[]} options.categories - The categories to filter by
 * @param {String} options.channelId - The channel id to filter by
 * @param {String[]} options.ids - The ids to filter by
 * @param {String[]} options.ignoreIds - The ids to ignore
 * @param {Number} options.limit - The limit of items to return
 * @param {Number} options.page - The page number
 * @param {String} options.search - The search query
 * @param {String[]} options.seasons - The seasons to filter by
 * @param {String} options.showId - The show id to filter by
 * @param {String} options.sort - The field to sort by
 * @param {String} options.sortOrder - The sort order
 * @param {String[]} options.statuses - The statuses to filter by
 * @returns {Object} - The items and count or an error
 */
async function getEpisodes({
  categories = [],
  channelId,
  ids = [],
  ignoreIds = [],
  limit = 25,
  page = 1,
  search,
  seasons = [],
  showId,
  sort,
  sortOrder,
  statuses,
}) {
  const filters = getListFilters({
    limit,
    order: sortOrder,
    page,
    search,
    searchFields: ['title'],
    sort,
    statuses,
  });

  const matchFiters = {
    $and: [filters.statuses, filters.search],
    // Exclude translations
    translationOf: { $exists: false },
  };

  // categories
  if (!isEmpty(categories)) {
    matchFiters.categories = {
      $in: categories
        .filter((category) => isValidObjectId(category)) // Only valid ObjectIds
        .map((category) => new mongoose.Types.ObjectId(category)), // Convert to ObjectId
    };
  }

  // seasons
  if (!isEmpty(seasons)) {
    matchFiters.season = {
      $in: seasons.map((id) => new mongoose.Types.ObjectId(id)),
    };
  }

  // ids
  if (!isEmpty(ids)) {
    matchFiters._id = { $in: ids.map((id) => new mongoose.Types.ObjectId(id)) };
  }

  // ignore ids
  if (!isEmpty(ignoreIds)) {
    matchFiters._id = {
      $nin: ignoreIds.map((id) => new mongoose.Types.ObjectId(id)),
    };
  }

  if (channelId) {
    const { channel, error: channelError } =
      await channelServices.getChannelById(channelId);
    if (channelError) {
      return { error: channelError };
    }
    matchFiters.channel = channel._id;
  } else if (showId) {
    const { show, error: showError } = await showsServices.getShowById(showId);
    if (showError) {
      return { error: showError };
    }
    matchFiters.show = show._id;
  }

  const items = await Episode.find(matchFiters)
    .sort(filters.sort)
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit)
    .populate([
      {
        path: 'channel',
        select: 'title translations',
      },
      {
        path: 'show',
        select: 'title channel',
      },
      {
        path: 'season',
        select: 'title groupName',
      },
    ]);

  const count = await Episode.find(matchFiters).countDocuments();

  return {
    count,
    items,
  };
}

/**
 * Get an episode by id
 * @param {String} episodeId - The id of the episode
 * @param {Object} options - Options object
 * @param {String[]} options.fields - Fields to select
 * @param {Object[]} options.populate - Fields to populate
 * @param {Boolean} options.lean - If the query should be lean
 * @returns {Object} - The episode or an error
 */
async function getEpisodeById(episodeId, options = {}) {
  const { record, error } = await getRecordById(Episode, episodeId, options);
  return { episode: record, error };
}

/**
 * Create an episode
 * @param {Object} options - Options object
 * @param {String} options.showId - The id of the show
 * @param {Object} options.body - The episode data
 * @param {Object} options.entity - The entity data
 * @returns {Object} - The episode or an error
 */
async function createEpisode({ showId, body, entity }) {
  const { show, error: showError } = await showsServices.getShowById(showId);

  if (showError) {
    return { error: showError };
  }

  const { channel, error: channelError } = await channelServices.getChannelById(
    show.channel,
    {
      fields: ['vimeo', 'jetstream'],
    }
  );

  if (channelError) {
    return { error: channelError };
  }

  // Create media links (and title/image for YouTube and Jetstream)
  if (!body.googleResponse && body.mediaLinks) {
    const { body: updatedBody, error: mediaLinksError } =
      await createOrUpdateMediaLinks({ body, channel, entity });

    if (mediaLinksError) {
      return { error: mediaLinksError };
    }

    body = updatedBody || {};

    // Merge with properties of schema
    body.mediaLinks = {
      ...Episode.schema.obj.mediaLinks.default,
      ...(updatedBody?.mediaLinks || {}),
    };
  }

  // Ensure episode has a slug or title
  if (!body.slug && !body.title) {
    return { error: errors.video_not_found() };
  }

  // Ensure episode has a valid slug within its siblings (or create one from its title)
  const slug = await Episode.getAvailableSlug(
    slugify(body.slug || body.title),
    show
  );

  // Remove what is not part of the episode
  const { googleResponse, ...rest } = body; // Google response is used for Jetstream uploads

  // Create the episode
  const episode = await Episode.create({
    ...rest,
    language: body.language ? body.language : show.language,
    slug,
    show: show._id,
    channel: show.channel,
    processed: !googleResponse || true,
    translationsStats: {
      total: 0,
      languages: [],
    },
  });

  // For Jetstream uploads, create the episode and media
  if (googleResponse) {
    const { error } = await createJetstreamUploadedEpisode({
      episode: episode.toObject(), // oObject() is used to convert the mongoose object.
      show,
      channel,
      googleResponse,
    });
    if (error) {
      Logger.error('Error creating Jetstream episode', error);
      return { error };
    }
  }

  // To check processed status we have two tasks that would run separately. One for updating progress every 15 seconds if user is in episode page in the backend, and another that runs in the background in the API every 5 minutes or so.
  // Whichever finishes first will update the episode with the processed status.

  return { episode };
}

/**
 * Update an episode
 * @param {Object} options - Options object
 * @param {String} options.episodeId - The id of the episode
 * @param {Object} options.body - The episode data
 * @param {Object} options.entity - The entity data
 * @returns {Object} - The updated episode or an error
 */
async function updateEpisode({ episodeId, body, entity }) {
  const { episode, error: episodeError } = await getEpisodeById(episodeId);
  if (episodeError) {
    return { error: episodeError };
  }

  let showId = episode.show;

  // If we're updating a translation, we need to get the showId from the original episode's show
  if (body.translationOf) {
    const { episode: originalEpisode, error: originalEpisodeError } =
      await getEpisodeById(body.translationOf);
    if (originalEpisodeError) {
      return { error: originalEpisodeError };
    }
    showId = originalEpisode.show;
  }

  const { show, error: showError } = await showsServices.getShowById(showId, {
    fields: ['jetstream'],
  });

  if (showError) {
    return { error: showError };
  }
  // Get channel data for media links and language
  const { channel } = await channelServices.getChannelById(episode.channel, {
    fields: ['vimeo', 'mainLanguage'],
  });

  // TODO: Check if episode is a jetstream upload, and if it is still processing.
  // Update media links (and title/image for YouTube, Vimeo and Jetstream)
  try {
    if (!body.googleResponse && body.mediaLinks) {
      const { body: updatedBody, error: mediaLinksError } =
        await createOrUpdateMediaLinks({ body, episode, channel, entity });

      if (mediaLinksError) {
        // TODO: Make this error available in the backend to show in the UI
        return { error: mediaLinksError };
      }

      body = updatedBody || {};

      // Merge with properties of schema and episode
      body.mediaLinks = {
        ...Episode.schema.obj.mediaLinks.default,
        ...episode.mediaLinks,
        ...body?.mediaLinks,
      };
    }
  } catch (error) {
    Logger.error('Error updating media links', error);
    return { error };
  }

  // Ensures new slug doesn't exists
  const slug = body.slug
    ? await Episode.getAvailableSlug(
        slugify(body.slug),
        episode.show,
        episode._id
      )
    : episode.slug;

  // Remove what is not part of the episode
  const { googleResponse, ...rest } = body; // Google response is used for Jetstream uploads

  const updatedData = {
    ...rest, // ...body without mode, googleResponse, and mediaLinks
    // Ensure a language is set:
    language:
      body.language ?? // 1. Form language
      episode.language ?? // 2. Previous language
      show.language ?? // 3. Show language
      channel.mainLanguage ?? // 4. Channel's main language
      entity.language ?? // 5. Entity language
      'en', // 6. Fallback to English
    slug,
    show: episode.show, // prevents moving an episode to different show
    channel: episode.channel, // prevents moving an episode to different channel
  };

  // Remove mediaLinks from body if googleResponse is present, so it won't be updated
  if (googleResponse) {
    delete body.mediaLinks;
  } else {
    updatedData.mediaLinks = body.mediaLinks;
  }

  let updatedEpisode;

  // Update episode's data
  try {
    updatedEpisode = await Episode.findByIdAndUpdate(episode._id, updatedData, {
      new: true,
      runValidators: true,
    }).populate(POPULATE_EPISODE);

    await updateEpisodeTranslationStats(
      episode.translationOf ? episode.translationOf : episode._id,
      entity
    );
  } catch (error) {
    Logger.error('Error updating episode', error);
    return { error };
  }

  if (googleResponse) {
    const { error } = await createJetstreamUploadedEpisode({
      episode: updatedEpisode.toObject(), // toObject() is used to convert the mongoose object.
      show,
      channel,
      googleResponse,
    });

    if (error) {
      Logger.error('Error updating Jetstream episode', error);
      return { error };
    }
  }

  return { episode: updatedEpisode };
}

/**
 * Get episode translations
 * @param {Object} options - Options object
 * @param {String} options.episodeId - The id of the episode
 * @param {Object} options.entity - The entity data
 * @param {Object} options.query - The query data
 * @returns {Object} - The episode translations
 */
export async function getEpisodeTranslations({ episodeId, entity, query }) {
  // We get the language from the locale and if the query includes sort by language we use the language field
  const { sort, enabled } = query;

  const filter = {
    translationOf: new mongoose.Types.ObjectId(episodeId),
    deleted: false,
  };

  if (enabled !== undefined) {
    filter.enabled = enabled;
  }

  const episodeTranslations = await Episode.aggregate()
    .match(filter)

    // Populate entity data
    .lookup({
      from: 'entities',
      localField: 'entity',
      foreignField: '_id',
      as: 'entity',
    })
    .unwind({
      path: '$entity',
      preserveNullAndEmptyArrays: true,
    })
    .addFields({
      entity: {
        _id: '$entity._id',
        id: '$entity._id',
        name: '$entity.name',
        slug: '$entity.slug',
        language: '$entity.language',
        logo: '$entity.logo',
        type: '$entity.type',
      },
    })

    .lookup({
      from: 'languages',
      localField: 'language',
      foreignField: 'locale',
      as: 'languageInfo',
    })
    .unwind('$languageInfo')

    // Sort by language or id
    .sort(
      sort && sort === 'language' ? { 'languageInfo.name.en': 1 } : { _id: 1 }
    )
    // Add other computed fields:
    .addFields({
      // Include "id" field (alias for "_id")
      id: '$_id',

      // Check if all expected translation fields are set
      translationDone,

      // Check if translation belongs to the current entity
      ownTranslation: {
        $eq: ['$entity._id', entity?._id],
      },
    });

  return episodeTranslations;
}

/**
 * Add an episode translation
 * @param {String} episodeId - The id of the episode
 * @param {String} language - The language of the translation
 * @param {Object} entity - The entity data
 * @returns {Object} - The episode translation or an error
 */
export async function addEpisodeTranslation(episodeId, language, entity) {
  // Fetch source episode
  const sourceEpisode = await Episode.findById(episodeId);

  // Check if episode doesn't exists
  if (!sourceEpisode) {
    return { error: errors.not_found('Episode', episodeId) };
  }

  // Check if episode already has a translation in the given language
  const existingEpisode = await Episode.findOne({
    translationOf: episodeId,
    language,
    deleted: false,
  });

  if (existingEpisode) {
    return { error: errors.already_exists() };
  }

  // Create the episode translation
  const episodeTranslation = await Episode.create({
    entity: sourceEpisode.entity || entity._id,
    language,
    translationOf: sourceEpisode._id,
  });

  return { episode: episodeTranslation };
}

/**
 * Update episode translation stats
 * @param {String} originalEpisodeId - The id of the original episode
 * @param {Object} entity - The entity data
 * @returns {Promise<void>} - The updated episode or an error
 */
export async function updateEpisodeTranslationStats(originalEpisodeId, entity) {
  const translations = await getEpisodeTranslations({
    episodeId: originalEpisodeId,
    entity,
    query: { enabled: true },
  });

  const translationsStats = translations.reduce(
    (acc, translation) => {
      // Global translations stats
      acc.total += 1;

      if (!acc.languages.includes(translation.language)) {
        acc.languages.push(translation.language);
      }

      return acc;
    },
    {
      total: 0,
      languages: [],
    }
  );

  // - Update original source article with new translation stats
  await Episode.updateOne(
    { _id: originalEpisodeId, translationOf: null }, // only an original episode (not translations)
    {
      translationsStats,
    }
  );
}

/**
 * Move an episode to another season or show
 * @param {Object} options - Options object
 * @param {String} options.episodeId - The id of the episode
 * @param {String} options.targetSeasonId - The id of the target season
 * @param {String} options.targetShowId - The id of the current show
 * @returns {Object} - The moved episode or an error
 */
async function moveEpisode({ episodeId, targetSeasonId, targetShowId }) {
  const { episode, error } = await getEpisodeById(episodeId);

  if (error) {
    return { error };
  }

  let { slug } = episode;
  let showId = episode.show;
  let seasonId = targetSeasonId || episode.season;

  // Moving episode to another show
  if (targetShowId && !areEqualIDs(targetShowId, episode.show)) {
    const { show: newShow, error: showError } = await showsServices.getShowById(
      targetShowId,
      {
        populate: [
          {
            path: 'seasons',
            match: { deleted: false },
            select: '_id',
          },
        ],
      }
    );

    if (showError) {
      return { error: showError };
    }

    // Set new show
    showId = newShow._id;

    // Ensures slug doesn't exist in new show
    slug = await Episode.getAvailableSlug(slug, newShow._id, episode._id);

    // If new show has seasons
    if (!isEmpty(newShow.seasons)) {
      const latestSeason = newShow.seasons[0];

      // Check passed season
      if (targetSeasonId) {
        const { season: newSeason, error: seasonError } =
          await seasonServices.getSeasonById(targetSeasonId);

        if (seasonError) {
          return { error: seasonError };
        }

        // Use first season of show if the new season doesn't belong to new show
        if (!areEqualIDs(newSeason.show, newShow._id)) {
          seasonId = latestSeason._id;
        }
      }

      // Use first season of show if no season was passed
      else {
        seasonId = latestSeason._id;
      }
    } else {
      // New show has no seasons
      seasonId = null;
    }
  }

  // Update episode's data
  const updatedEpisode = await Episode.findByIdAndUpdate(
    episode._id,
    {
      show: showId,
      season: seasonId,
      slug,
      channel: episode.channel, // prevents moving an episode to different channel
    },
    {
      new: true,
      runValidators: true,
    }
  ).populate(POPULATE_EPISODE);

  return { episode: updatedEpisode };
}

/**
 * Delete an episode
 * @param {Object} options - Options object
 * @param {String} options.episodeId - The id of the episode
 * @param {Object} options.entity - The entity data
 * @returns {Object} - The deleted episode or an error
 */
async function deleteEpisode({ episodeId, entity }) {
  const updatedEpisode = await Episode.findByIdAndUpdate(
    episodeId,
    { deleted: true },
    {
      new: true,
      runValidators: true,
    }
  );

  if (!updatedEpisode) {
    return { error: errors.not_found('Episode', episodeId) };
  }

  await updateEpisodeTranslationStats(
    updatedEpisode.translationOf ?? updatedEpisode._id,
    entity
  );

  eventEmitter.emit(episodeEvents.DELETE_EPISODE, {
    episode: updatedEpisode,
    entity,
    language: updatedEpisode.translationOf ? updatedEpisode.language : null,
  });

  return { episode: updatedEpisode };
}

/**
 * Restore an episode
 * @param {Object} options - Options object
 * @param {String} options.episodeId - The id of the episode
 * @param {Object} options.entity - The entity data
 * @returns {Object} - The restored episode or an error
 */
async function restoreEpisode({ episodeId, entity }) {
  const updatedEpisode = await Episode.findByIdAndUpdate(
    episodeId,
    { deleted: false },
    {
      new: true,
      runValidators: true,
    }
  );

  if (!updatedEpisode) {
    return { error: errors.not_found('Episode', episodeId) };
  }

  await updateEpisodeTranslationStats(
    updatedEpisode.translationOf ?? updatedEpisode._id,
    entity
  );

  return { episode: updatedEpisode };
}

/**
 * Disable an episode
 * @param {Object} options - Options object
 * @param {String} options.episodeId - The id of the episode
 * @param {Object} options.entity - The entity data
 * @returns {Object} - The disabled episode or an error
 */
async function disableEpisode({ episodeId, entity }) {
  const updatedEpisode = await Episode.findByIdAndUpdate(
    episodeId,
    { enabled: false },
    {
      new: true,
      runValidators: true,
    }
  );

  if (!updatedEpisode) {
    return { error: errors.not_found('Episode', episodeId) };
  }

  await updateEpisodeTranslationStats(
    updatedEpisode.translationOf ?? updatedEpisode._id,
    entity
  );

  eventEmitter.emit(episodeEvents.DISABLE_EPISODE, {
    episode: updatedEpisode,
    entity,
    language: updatedEpisode.translationOf ? updatedEpisode.language : null,
  });

  return { episode: updatedEpisode };
}

/**
 * Enable an episode
 * @param {Object} options - Options object
 * @param {String} options.episodeId - The id of the episode
 * @param {Object} options.entity - The entity data
 * @returns {Object} - The enabled episode or an error
 */
async function enableEpisode({ episodeId, entity }) {
  const updatedEpisode = await Episode.findByIdAndUpdate(
    episodeId,
    { enabled: true },
    {
      new: true,
      runValidators: true,
    }
  );

  if (!updatedEpisode) {
    return { error: errors.not_found('Episode', episodeId) };
  }

  await updateEpisodeTranslationStats(
    updatedEpisode.translationOf ?? updatedEpisode._id,
    entity
  );

  return { episode: updatedEpisode };
}

async function getEpisodeLinkedItems({
  episodeId,
  entity,
  sort = 'title',
  order,
  type,
}) {
  const includeArticles = !type || Boolean(type?.includes('article'));

  const { episode, error } = await getEpisodeById(episodeId, {
    populate: [
      ...(includeArticles
        ? [
            {
              path: 'linkedArticles',
              populate: {
                path: 'article',
                model: 'Article',
                select: 'title slug image entity',
                match: { deleted: false, entity: entity?._id },
              },
            },
          ]
        : []),
    ],
  });

  if (!episode || error) {
    return { error: errors.not_found('Episode') };
  }

  return {
    linkedItems: {
      article: includeArticles
        ? (episode.linkedArticles?.sort((a, b) =>
            order === 'asc'
              ? a.article[sort] > b.article[sort]
                ? 1
                : -1
              : a.article[sort] < b.article[sort]
                ? 1
                : -1
          ) ?? [])
        : [],
    },
  };
}

async function addLinkedArticle({ articleId, episodeId, defaultItem }) {
  try {
    const { episode, error } = await getEpisodeById(episodeId);

    if (error) {
      return { error };
    }

    if (!episode) {
      return { error: errors.not_found('Episode', episodeId) };
    }

    const existingArticleIndex = episode.linkedArticles?.findIndex(
      (a) => a.article.toString() === articleId
    );

    if (existingArticleIndex > -1) {
      return { error: errors.already_exists('Article', articleId) };
    }

    // If the new episode is set as default, remove the default flag from the other linked episodes
    if (defaultItem) {
      episode.linkedArticles?.forEach((a) => {
        a.defaultItem = false;
      });
    }

    episode.linkedArticles?.push({
      article: toObjectId(articleId),
      defaultItem,
    });

    await episode.save();

    return { episode };
  } catch (error) {
    return { error };
  }
}

export async function updateLinkedArticle({
  articleId,
  defaultItem,
  episodeId,
  linkedItemId,
}) {
  try {
    const { episode, error } = await getEpisodeById(episodeId, {
      fields: ['linkedArticles'],
    });

    if (error) {
      return { error };
    }

    if (!episode) {
      return { error: errors.not_found('Episode', episodeId) };
    }

    const linkedArticle = episode.linkedArticles?.id(toObjectId(linkedItemId));

    if (!linkedArticle) {
      return { error: errors.not_found('Linked article', linkedItemId) };
    }

    linkedArticle.article = toObjectId(articleId);
    linkedArticle.defaultItem = defaultItem;

    if (defaultItem) {
      episode.linkedArticles?.forEach((a) => {
        if (a._id.toString() !== linkedItemId) {
          a.defaultItem = false;
        }
      });
    }

    await episode.save();

    return { episode };
  } catch (error) {
    return { error };
  }
}

async function deleteLinkedArticle({ episodeId, linkedItemId }) {
  try {
    const { episode, error } = await getEpisodeById(episodeId, {
      fields: ['linkedArticles'],
    });

    if (error) {
      return { error };
    }

    if (!episode) {
      return { error: errors.not_found('Episode', episodeId) };
    }

    await episode.linkedArticles?.id(toObjectId(linkedItemId)).deleteOne();

    if (episode.linkedArticles?.length === 1) {
      episode.linkedArticles[0].defaultItem = true;
    }

    await episode.save();

    return { episode };
  } catch (error) {
    return { error };
  }
}

export async function getEpisodeDetailPages({ siteId, detailPageId }) {
  const anchorShowPages = await getDynamicRecordPagesByResource(
    siteId,
    'Episode'
  );
  const episodePage = await Page.findById(detailPageId);

  return { anchorShowPages, episodePage };
}

// NOT USED
export async function addEpisodeUrl({
  addOrigin = false,
  episode = null,
  site,
  anchorShowPages = {},
  episodePage = null,
  paramName = 'url',
}) {
  if (!episode) return null;

  const detailPage = anchorShowPages[episode._id]
    ? anchorShowPages[episode._id]
    : episodePage;

  if (!detailPage) return episode;

  let origin = '';
  if (addOrigin) {
    origin = site ? `https://${site.domain}` : '';
  }

  // console.log('333');
  // console.log(
  //   `${origin}${
  //     detailPage?.dynamic && episode.slug
  //       ? `${detailPage?.path
  //           .replace('[Show]', episode.show?.slug)
  //           .replace('[Season]', episode.season?.slug)
  //           .replace('[Episode]', episode.slug)}`
  //       : detailPage?.path
  //   }`
  // );

  return {
    ...episode,
    [paramName]: `${origin}${
      detailPage?.dynamic && episode.slug
        ? `${detailPage?.path
            .replace('[Show]', episode.show?.slug)
            .replace('[Season]', episode.season?.slug)
            .replace('[Episode]', episode.slug)}`
        : detailPage?.path
    }`,
  };
}

export default {
  getEpisodes,
  getEpisodeById,
  createEpisode,
  updateEpisode,
  moveEpisode,
  getEpisodeTranslations,
  addEpisodeTranslation,
  updateEpisodeTranslationStats,
  deleteEpisode,
  restoreEpisode,
  disableEpisode,
  enableEpisode,
  getEpisodeLinkedItems,
  addLinkedArticle,
  updateLinkedArticle,
  deleteLinkedArticle,
  getEpisodeDetailPages,
  addEpisodeUrl,
};
