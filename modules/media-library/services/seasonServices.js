import { getRecordById } from '#utils/api/model/getRecordById.js';

import Season from '../models/Season.js';

/**
 * Get a season by id
 * @param {String} seasonId - The id of the season
 * @param {Object} options - Options object
 * @param {String[]} options.fields - Fields to select
 * @param {Object[]} options.populate - Fields to populate
 * @param {Object[]} options.lean - Return plain object
 * @returns {Object} - The season or an error
 */
async function getSeasonById(seasonId, options = {}) {
  const { record, error } = await getRecordById(Season, seasonId, options);
  return { season: record, error };
}

export default {
  getSeasonById,
};
