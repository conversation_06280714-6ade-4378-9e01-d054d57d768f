## Collections and content types.

- Collections can be filtered by video or audio when listed. This is useful to differentiate which page blocks to use. Video episodes can use the episode player block, for which the collection lists need to point to a page with that block.While audio episodes can use the audio player blocks.

This media type setting is set in the collection list block, by selecting between video and audio. Video is the default, and so far everything pointed to video.

NOTE: The player order was used before, with audio and mp3 being added there to enable showing audio episodes in collection lists. But now this depends on the media formats available and the media type setting in the collection list block.

### How is this filtered in the frontend API

The media type of the collection list block is passed to the collections resource. This then selects between the available media formats of the media type, and returns the collections that have media of that type.

### How is this filtered in the backend API

In the collection controller when a media type is passed, the shows of the channel are then counted for how many episodes of that media type they have. If they have at least one, they are included in the list. This list is then used to only return the shows that have episodes of that media type. And also the collection should have at least one episode in at least one show.
Since collections can also be of episodes (not just shows) directly, the check for that is done directly, checking if at least one episode is of the media type before adding the collection to the displayed list.
