/**
 * Get Episode resource meta
 * @param {Object} options
 * @param {Object} options.resource - The Episode resource
 * @returns {Object}
 */
export function getEpisodeResourceMeta({ resource }) {
  if (!resource) {
    return null;
  }

  const {
    categories,
    channel,
    createdAt,
    displayDate,
    guests,
    hosts,
    location,
    season,
    show,
    tags,
    videoOnDemand,
    videoOnDemandStartsAt,
  } = resource;

  return {
    categories,
    channel,
    guests,
    hosts,
    location: location
      ? {
          ...(location.coordinates
            ? { coordinates: location.coordinates }
            : {}),
          ...(location.context?.country
            ? { country: location.context?.country }
            : {}),
          ...(location.context?.place
            ? { place: location.context?.place }
            : {}),
          ...(location.nameOverride || location.placeName
            ? { placeName: location.nameOverride || location.placeName }
            : {}),
          ...(location.context?.region
            ? { region: location.context?.region }
            : {}),
        }
      : {},
    publishedAt: videoOnDemand
      ? displayDate || videoOnDemandStartsAt || createdAt
      : createdAt,
    season,
    show,
    tags,
  };
}
