/**
 * Get Show resource meta
 * @param {Object} options
 * @param {Object} options.resource - The Show resource
 * @returns {Object}
 */
export function getShowResourceMeta({ resource }) {
  if (!resource) {
    return null;
  }

  const {
    categories,
    channel,
    createdAt,
    hosts,
    participants,
    seasons,
    tags,
    videoOnDemand,
    videoOnDemandStartsAt,
  } = resource;

  return {
    categories,
    channel,
    hosts,
    participants,
    publishedAt: videoOnDemand ? videoOnDemandStartsAt || createdAt : createdAt,
    seasons,
    tags,
  };
}
