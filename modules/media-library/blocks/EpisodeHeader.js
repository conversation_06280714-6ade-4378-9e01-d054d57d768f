import { stringArrayToString } from '#utils/strings.js';

/**
 * EpisodeHeader block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export function EpisodeHeader() {
  return {
    name: 'EpisodeHeader',
    getSearchableContent,
  };
}

/**
 * Get searchable content for EpisodeHeader
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node, language, site }) {
  const { showAbstract, episode } = node?.props ?? {};

  const { abstract, publishedAt, title } = episode || {};

  const publishedAtDate = new Date(publishedAt);

  const publishedDateDisplay = Number.isNaN(publishedAtDate.getDate())
    ? null
    : new Intl.DateTimeFormat(language, {
        dateStyle: 'full',
        timeZone: site.timezone,
      }).format(publishedAtDate);

  return stringArrayToString(
    [title, showAbstract ? abstract : null, publishedDateDisplay],
    '\n'
  );
}
