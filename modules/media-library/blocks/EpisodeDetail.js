import { convertRichTextToPlainText } from '#utils/richText.js';
import { stringArrayToString } from '#utils/strings.js';

/**
 * EpisodeDetail block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export function EpisodeDetail() {
  return {
    name: 'EpisodeDetail',
    getSearchableContent,
  };
}

/**
 * Get searchable content for EpisodeDetail
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { showAbstract, showBody, showTitle, episodeDetail } =
    node?.props ?? {};

  const { abstract, body, title } = episodeDetail || {};

  return stringArrayToString(
    [
      showTitle ? title : null,
      showAbstract ? abstract : null,
      showBody ? convertRichTextToPlainText(body) : null,
    ],
    '\n'
  );
}
