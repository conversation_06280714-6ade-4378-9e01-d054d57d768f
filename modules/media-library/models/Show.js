import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const showSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    // required: true, // Not required for translations
    trim: true,
  },
  abstract: {
    type: String,
    trim: true,
    default: '',
  },
  subtitle: {
    type: String,
    trim: true,
  },
  body: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      type: 'doc',
      content: [],
    },
  },
  images: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      default: null,
      banner: null,
      bannerMobile: null,
      poster: null,
      podcast: null,
    },
  },
  backgroundImages: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      default: null,
      phone: null,
      tablet: null,
      tv: null,
    },
  },
  logo: {
    type: mongoose.SchemaTypes.Mixed,
  },
  colors: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      page: {
        background: null,
        title: null,
        text: null,
        button: {
          background: null,
          backgroundHover: null,
          text: null,
          textHover: null,
        },
      },
      box: {
        background: null,
        title: null,
        text: null,
        line: null,
        button: {
          background: null,
          backgroundHover: null,
          text: null,
          textHover: null,
        },
      },
    },
  },
  language: {
    type: String,
    default: '',
  },

  /*
   * Indicates if the recored is a translation of a show
   */
  translationOf: {
    type: mongoose.Types.ObjectId,
    ref: 'Show',
  },

  /*
   * Stats about the shows's translations used to track the progress of the translations
   * @schema: { total: number, done: number }
   * @example: { total: 3, done: 2 }
   */
  translationsStats: {
    type: mongoose.SchemaTypes.Mixed,
  },

  contactEmail: {
    type: String,
    default: '',
  },
  categories: {
    type: [mongoose.Types.ObjectId],
    ref: 'Category',
    default: [],
  },
  tags: {
    type: [String],
    default: [],
  },
  episodeSorting: {
    type: String,
    default: 'desc',
    enum: {
      values: ['', 'desc', 'asc'],
      message: 'INVALID_ENUM',
    },
  },
  episodesDownloadable: {
    type: Boolean,
    default: true,
  },
  autoplayEpisodes: {
    type: Boolean,
    default: false,
  },
  hosts: {
    type: [mongoose.Types.ObjectId],
    ref: 'Person',
    default: [],
  },
  participantsTitle: {
    type: String,
    trim: true,
    default: '',
  },
  participants: {
    type: [mongoose.Types.ObjectId],
    ref: 'Person',
    default: [],
  },
  documents: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
  links: {
    type: [
      {
        enabled: {
          type: Boolean,
          default: true,
        },
        title: {
          type: String,
          required: true,
          trim: true,
        },
        url: {
          type: String,
          trim: true,
        },
      },
    ],
    default: [],
  },
  podcasts: {
    type: [
      {
        enabled: {
          type: Boolean,
          default: true,
        },
        title: {
          type: String,
          required: true,
          trim: true,
        },
        url: {
          type: String,
          trim: true,
        },
      },
    ],
    default: [],
  },
  seasons: {
    type: [mongoose.Types.ObjectId],
    ref: 'Season',
    default: [],
  },
  importIDs: {
    type: [
      {
        enabled: {
          type: Boolean,
          default: true,
        },
        type: {
          type: String,
          required: true,
        },
        accountID: {
          type: String,
          trim: true,
        },
        recordID: {
          type: String,
          trim: true,
        },
      },
    ],
    default: [],
  },
  scheduleIDs: {
    type: [
      {
        enabled: {
          type: Boolean,
          default: true,
        },
        scheduleID: {
          type: String,
          trim: true,
        },
      },
    ],
    default: [],
  },
  videoOnDemand: {
    type: Boolean,
    default: true,
  },
  videoOnDemandStartsAt: {
    type: Date,
    default: null,
  },
  videoOnDemandEndsAt: {
    type: Date,
    default: null,
  },
  rss: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      enabled: true,
      mediaFormat: '',
      type: 'episodic',
      categories: [],
      subcategories: [],
      author: '',
      owner: '',
      ownerEmail: '',
      copyright: '',
      episodeImages: false,
    },
  },
  relatedShows: {
    type: [mongoose.Types.ObjectId],
    ref: 'Show',
  },
  videosTitle: {
    type: String,
    trim: true,
    default: '',
  },
  videoCategories: {
    type: [mongoose.Types.ObjectId],
    ref: 'VideoCategory',
    default: [],
  },
  videos: {
    type: [mongoose.Types.ObjectId],
    ref: 'Video',
    default: [],
  },
  trailer: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  previewVideo: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },

  /*
   * Used in case the record is a translation of a show, to reference to owner entity of the translation.
   */
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },

  channel: {
    type: mongoose.Types.ObjectId,
    // required: true, // Not required for translations
    ref: 'Channel',
  },
  channels: {
    type: mongoose.SchemaTypes.Mixed,
  },
  canonicalSite: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
  },
  canonicalSitePage: {
    type: mongoose.Types.ObjectId,
    ref: 'Page',
  },
  jetstream: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      brandId: null,
    },
  },
});

// NOTE: Index disabled due to TYPO3 import having non-unique show titles
// showSchema.index(
//   { title: 1, channel: 1 },
//   {
//     unique: true,
//     partialFilterExpression: { deleted: false },
//   }
// );
showSchema.index({ slug: 1 });
showSchema.index({ title: 1 });
showSchema.index({ language: 1 });
showSchema.index({ categories: 1 });
showSchema.index({ tags: 1 });
showSchema.index({ importIDs: 1 });
showSchema.index({ scheduleIDs: 1 });
showSchema.index({ videoOnDemand: 1 });
showSchema.index({ videoOnDemandStartsAt: 1 });
showSchema.index({ videoOnDemandEndsAt: 1 });
showSchema.index({ channel: 1 });
showSchema.index({ 'channels.$**': 1 });

// FE: Index used for loading enabled shows
showSchema.index({
  translationOf: 1,
  enabled: 1,
  deleted: 1,
  videoOnDemand: 1,
  channel: 1,
});

showSchema.statics.getAvailableSlug = async function (
  slug,
  channel,
  showId = null
) {
  const query = { slug, channel, deleted: false };

  if (showId) {
    query._id = { $ne: showId };
  }

  const existingShow = await this.findOne(query);

  return existingShow ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Show', showSchema);
