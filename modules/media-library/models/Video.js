import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const videoSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  abstract: {
    type: String,
    trim: true,
    default: '',
  },
  body: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      type: 'doc',
      content: [],
    },
  },
  image: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  videoCategories: {
    type: [mongoose.Types.ObjectId],
    ref: 'VideoCategory',
    default: [],
  },
  tags: {
    type: [String],
    default: [],
  },
  mediaLinks: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      'youtube': null,
      'jetstream': null,
      'vimeo': null,
      'mp4-hd': null,
      'mp4-sd': null,
      'mp3': null,
      'hls': null,
      'bif': null,
    },
  },
  show: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Show',
  },
  episode: {
    type: mongoose.Types.ObjectId,
    ref: 'Episode',
    default: null,
  },
  relatedVideos: {
    type: [mongoose.Types.ObjectId],
    ref: 'Video',
    default: [],
  },
  language: {
    type: String,
  },
  importIDs: {
    type: [String],
    default: [],
  },
});

videoSchema.index({ slug: 1 });
videoSchema.index({ title: 1 });
videoSchema.index({ tags: 1 });
videoSchema.index({ show: 1 });
videoSchema.index({ episode: 1 });
videoSchema.index({ importIDs: 1 });

// Virtuals
// videoSchema.virtual('episode', {
//   ref: 'Episode',
//   foreignField: 'videos',
//   localField: '_id',
// });

videoSchema.statics.getAvailableSlug = async function (
  slug,
  show,
  videoId = null
) {
  const query = { slug, show, deleted: false };

  if (videoId) {
    query._id = { $ne: videoId };
  }

  const existingVideo = await this.findOne(query);

  return existingVideo ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Video', videoSchema);
