import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const LinkedArticleSchema = new mongoose.Schema({
  article: {
    type: mongoose.Types.ObjectId,
    ref: 'Article',
  },
  defaultItem: {
    type: Boolean,
    default: false,
  },
});

const episodeSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  number: {
    type: Number,
    default: null,
  },
  title: {
    type: String,
    // required: true, // Not required for translations
    trim: true,
  },
  abstract: {
    type: String,
    trim: true,
    default: '',
  },
  body: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      type: 'doc',
      content: [],
    },
  },
  image: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  images: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  language: {
    type: String,
    required: true,
  },
  /*
   * Indicates if the recored is a translation of a episode
   */
  translationOf: {
    type: mongoose.Types.ObjectId,
    ref: 'Episode',
  },

  /*
   * Stats about the episode's translations used to track the progress of the translations
   * @schema: { total: number, languages: [string]}
   * @example: { total: 3, languages: ['de', 'es', 'pt-br']}
   */
  translationsStats: {
    total: { type: Number, default: 0 },
    languages: [{ type: String }], // [ 'de', 'es', 'pt-br', 'fr', 'sw' ] etc.
  },

  categories: {
    type: [mongoose.Types.ObjectId],
    ref: 'Category',
    default: [],
  },
  tags: {
    type: [String],
    default: [],
  },
  downloadable: {
    type: Boolean,
    default: true,
  },
  hosts: {
    type: [mongoose.Types.ObjectId],
    ref: 'Person',
    default: [],
  },
  guests: {
    type: [mongoose.Types.ObjectId],
    ref: 'Person',
    default: [],
  },
  documents: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
  links: {
    type: [
      {
        enabled: {
          type: Boolean,
          default: true,
        },
        title: {
          type: String,
          required: true,
          trim: true,
        },
        url: {
          type: String,
          trim: true,
        },
      },
    ],
    default: [],
  },
  duration: {
    type: Number,
    default: 0,
  },
  mediaLinks: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      'youtube': null,
      'vimeo': null,
      'jetstream': null,
      'jetstream-audio': null,
      'mp4-hd': null,
      'mp4-sd': null,
      /**
       * @deprecated prefer more generic `audio` together with mime type
       */
      'mp3': null,
      'hls': null,
      'bif': null,
      'audio': null,
    },
  },
  subtitles: {
    type: [
      {
        language: String,
        label: String,
        url: String,
      },
    ],
    default: [],
  },
  plays: {
    type: Number,
    default: 0,
  },
  views: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      youtube: 0,
      vimeo: 0,
      jetstream: 0,
    },
  },
  rating: {
    type: Number,
    default: 0,
  },
  downloads: {
    type: Number,
    default: 0,
  },
  importIDs: {
    type: [
      {
        enabled: {
          type: Boolean,
          default: true,
        },
        type: {
          type: String,
          required: true,
        },
        accountID: {
          type: String,
          trim: true,
        },
        recordID: {
          type: String,
          trim: true,
        },
      },
    ],
    default: [],
  },
  scheduleIDs: {
    type: [
      {
        enabled: {
          type: Boolean,
          default: true,
        },
        scheduleID: {
          type: String,
          trim: true,
        },
      },
    ],
    default: [],
  },
  firstAirDate: {
    type: Date,
    default: null,
  },
  videoOnDemand: {
    type: Boolean,
    default: true,
  },
  videoOnDemandStartsAt: {
    type: Date,
    default: null,
  },
  videoOnDemandEndsAt: {
    type: Date,
    default: null,
  },
  displayDate: {
    type: Date,
    default: null,
  },
  videosTitle: {
    type: String,
    trim: true,
    default: '',
  },
  videos: {
    type: [mongoose.Types.ObjectId],
    ref: 'Video',
    default: [],
  },
  channel: {
    type: mongoose.Types.ObjectId,
    // required: true, // Not required for translations
    ref: 'Channel',
  },
  show: {
    type: mongoose.Types.ObjectId,
    // required: true, // Not required for translations
    ref: 'Show',
  },
  /*
   * Entity: Used in case the record is a translation of a show, to reference to owner entity of the translation.
   */
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },
  season: {
    type: mongoose.Types.ObjectId,
    ref: 'Season',
    default: null,
  },
  processed: {
    type: Boolean,
    default: true, // In case of uploaded to jetstream they will be set to false on upload and then to true when processed by the jetstream processing task.
  },
  timesProcessed: {
    type: Number,
    default: 0,
  },
  tempLinks: {
    //mixed
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },

  // Episode location (name, placeName, coordinates)
  location: {
    name: String, // short name. e.g. "Viale"
    nameOverride: String, // short name override. e.g. "Viale City"
    context: {
      address: String, // e.g. "25 de Mayo 807"
      place: String, // e.g. "Viale"
      region: String, // e.g. "Entre Ríos"
      country: String, // e.g. "Argentina"
    },
    placeName: String, // long name. e.g. "Viale, Entre Ríos, Argentina"
    coordinates: [Number, Number], // geoppoint with [lat, long]. E.g. [-31.8680222,-60.007516]
    boundingBox: [Number, Number, Number, Number], // e.g. [-31.8680222,-60.007516,-31.8680222,-60.007516]
    bearing: Number, // value between 0 and 360 (default 0 is looking north, and 90 is looking east, etc.)
    pitch: Number, // value between 0 and 60 (default 0 is looking straight down at the map, and 60 is looking at the horizon)
    zoom: Number, // value between 0 and 20
  },

  // Organizations (entities) that are referenced in or related to the episode
  organizations: {
    type: [mongoose.Types.ObjectId],
    ref: 'Entity',
  },

  /*
   * A list of related segments to the episode
   */
  segments: {
    type: [mongoose.Types.ObjectId],
    ref: 'Episode',
    default: [],
  },

  // Articles that are linked to the episode
  linkedArticles: {
    type: [LinkedArticleSchema],
  },
});

episodeSchema.index({ slug: 1 });
episodeSchema.index({ number: 1 });
episodeSchema.index({ title: 1 });
episodeSchema.index({ language: 1 });
episodeSchema.index({ categories: 1 });
episodeSchema.index({ segments: 1 });
episodeSchema.index({ tags: 1 });
episodeSchema.index({ plays: 1 });
episodeSchema.index({ rating: 1 });
episodeSchema.index({ importIDs: 1 });
episodeSchema.index({ scheduleIDs: 1 });
episodeSchema.index({ firstAirDate: 1 });
episodeSchema.index({ videoOnDemand: 1 });
episodeSchema.index({ videoOnDemandStartsAt: 1 });
episodeSchema.index({ videoOnDemandEndsAt: 1 });
episodeSchema.index({ channel: 1 });
episodeSchema.index({ show: 1 });
episodeSchema.index({ season: 1 });
episodeSchema.index({ processed: 1 });
episodeSchema.index({ 'views.$**': 1 });
episodeSchema.index({ 'mediaLinks.$**': 1 });

// FE: Index used for filtering episodes in loadCollectionItems()
episodeSchema.index({
  enabled: 1,
  deleted: 1,
  videoOnDemand: 1,
  translationOf: 1,
  language: 1,
  show: 1,
  season: 1,
});

episodeSchema.statics.getAvailableSlug = async function (
  slug,
  show,
  episodeId = null
) {
  const query = { slug, show, deleted: false };

  if (episodeId) {
    query._id = { $ne: episodeId };
  }

  const existingEpisode = await this.findOne(query);

  return existingEpisode ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Episode', episodeSchema);
