import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const channelSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  logo: {
    type: mongoose.SchemaTypes.Mixed,
  },
  mainLanguage: {
    type: String,
  },
  additionalLanguages: {
    type: [String],
    default: [],
  },
  contactEmail: {
    type: String,
    default: '',
  },
  countries: {
    type: [String],
    default: [],
  },
  hls: {
    type: String,
    trim: true,
  },
  jetstreamId: {
    type: String,
    trim: true,
  },
  appSettings: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      videoOnDemand: false,
      collections: false,
      livestream: false,
      schedule: false,
    },
  },
  pageIDs: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      siteID: null,
      pages: {
        showDetailPageID: null,
        episodeDetailPageID: null,
        livestreamPageID: null,
      },
    },
  },
  youtube: {
    type: {
      allowPrivateVideos: Boolean,
    },
    default: {},
  },

  vimeo: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      clientID: null,
      clientSecret: null,
      accessToken: null,
    },
  },
  jetstream: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      organizationId: null,
      enableUpload: false,
    },
  },
  mediaSettings: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      enableAudioUpload: false,
      enableDisplayDate: false,
    },
  },
  playerOrder: {
    type: [String],
    default: ['youtube', 'jetstream', 'vimeo'],
  },
  videoMediaFormats: {
    type: [String],
    default: ['youtube', 'jetstream', 'vimeo'],
  },
  audioMediaFormats: {
    type: [String],
    default: ['mp3', 'audio'],
  },
  rss: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      mediaFormat: '',
      categories: [],
      subcategories: [],
      author: '',
      owner: '',
      ownerEmail: '',
      copyright: '',
    },
  },
  translations: {
    enabled: {
      type: Boolean,
      default: false,
    },
  },
  podcastFormat: {
    type: String,
    default: 'mp4-hd',
  },
  scheduleImport: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  collections: {
    type: [mongoose.Types.ObjectId],
    ref: 'Collection',
    default: [],
  },
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
  importIDs: {
    type: [String],
    default: [],
  },
  siteUrl: {
    type: String,
    default: null,
  },
  hideInMap: {
    type: Boolean,
  },
  mapPoint: {
    type: {
      type: String,
      enum: ['Point'],
    },
    coordinates: {
      type: [Number],
    },
    bearing: {
      type: Number,
    },
    pitch: {
      type: Number,
    },
    zoom: {
      type: Number,
    },
  },
  mapRegion: {
    type: String,
    default: null,
  },
  mapTitle: {
    type: String,
    default: null,
  },
  mapOverrides: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
});

channelSchema.index(
  { title: 1, entity: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);
channelSchema.index({ slug: 1 });
channelSchema.index({ title: 1 });
channelSchema.index({ mainLanguage: 1 });
channelSchema.index({ additionalLanguages: 1 });
channelSchema.index({ countries: 1 });
channelSchema.index({ entity: 1 });
channelSchema.index({ importIDs: 1 });
channelSchema.index({ mapPoint: '2dsphere' });

channelSchema.statics.getAvailableSlug = async function (
  slug,
  entity,
  channelId = null
) {
  const query = { slug, entity, deleted: false };

  if (channelId) {
    query._id = { $ne: channelId };
  }

  const existingChannel = await this.findOne(query);

  return existingChannel ? uniquifySlug(slug) : slug;
};

channelSchema.statics.getFormats = async function (channelId, mediaType) {
  const channel = await this.findById(channelId).select(
    `${mediaType}MediaFormats`
  );

  if (!channel) {
    throw new Error('Channel not found');
  }

  return mediaType === 'video'
    ? channel.videoMediaFormats
    : channel.audioMediaFormats;
};

export default mongoose.model('Channel', channelSchema);
