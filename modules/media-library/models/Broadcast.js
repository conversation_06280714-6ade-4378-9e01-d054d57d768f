import mongoose from 'mongoose';

const broadcastSchema = new mongoose.Schema(
  {
    startsAt: {
      type: Date,
      required: true,
    },
    endsAt: {
      type: Date,
      required: true,
    },
    showTitle: {
      type: String,
    },
    episodeTitle: {
      type: String,
    },
    scheduleID: {
      type: String,
    },
    episode: {
      type: mongoose.Types.ObjectId,
      ref: 'Episode',
    },
    channel: {
      type: mongoose.Types.ObjectId,
      required: true,
      ref: 'Channel',
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    minimize: false,
  }
);

broadcastSchema.index({ createdAt: 1 });
broadcastSchema.index({ updatedAt: 1 });
broadcastSchema.index({ startsAt: 1 });
broadcastSchema.index({ endsAt: 1 });
broadcastSchema.index({ showTitle: 1 });
broadcastSchema.index({ episodeTitle: 1 });
broadcastSchema.index({ episode: 1 });
broadcastSchema.index({ channel: 1 });

export default mongoose.model('Broadcast', broadcastSchema);
