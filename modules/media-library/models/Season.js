import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const seasonSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  number: {
    type: Number,
    default: null,
  },
  groupName: {
    type: String,
    default: '',
  },
  documents: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
  links: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
  importID: {
    type: String,
  },
  channel: {
    type: mongoose.Types.ObjectId,
    ref: 'Channel',
  },
  show: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Show',
  },
});

seasonSchema.index({ slug: 1 });
seasonSchema.index({ title: 1 });
seasonSchema.index({ number: 1 });
seasonSchema.index({ groupName: 1 });
seasonSchema.index({ importID: 1 });
seasonSchema.index({ show: 1 });
seasonSchema.index({ channel: 1 });

seasonSchema.statics.getAvailableSlug = async function (
  slug,
  show,
  seasonId = null
) {
  const query = { slug, show, deleted: false };

  if (seasonId) {
    query._id = { $ne: seasonId };
  }

  const existingSeason = await this.findOne(query);

  return existingSeason ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Season', seasonSchema);
