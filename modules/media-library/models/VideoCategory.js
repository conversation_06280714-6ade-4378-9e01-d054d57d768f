import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const videoCategorySchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  abstract: {
    type: String,
    trim: true,
    default: '',
  },
  body: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      type: 'doc',
      content: [],
    },
  },
  importID: {
    type: String,
  },
  show: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Show',
  },
});

videoCategorySchema.index({ slug: 1 });
videoCategorySchema.index({ title: 1 });
videoCategorySchema.index({ show: 1 });

videoCategorySchema.statics.getAvailableSlug = async function (
  slug,
  show,
  videoCategoryId = null
) {
  const query = { slug, show, deleted: false };

  if (videoCategoryId) {
    query._id = { $ne: videoCategoryId };
  }

  const existingVideoCategory = await this.findOne(query);

  return existingVideoCategory ? uniquifySlug(slug) : slug;
};

export default mongoose.model('VideoCategory', videoCategorySchema);
