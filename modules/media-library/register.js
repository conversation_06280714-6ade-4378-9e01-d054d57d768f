import { mediaLibraryDataSourceRegistration } from '#modules/media-library/data-sources/mediaLibraryDataSourceRegistration.js';

import { mediaLibraryBlocksRegistration } from './blocks/blocksRegistration.js';
import { modelResourcesRegistration } from './resources/modelResourcesRegistration.js';
import mediaLibraryRouter from './routes.js';
import mediaLibraryTasks from './tasks/index.js';

export default function mediaLibrary() {
  return {
    routes: {
      '/media-library': mediaLibraryRouter,
    },

    tasks: mediaLibraryTasks,
    dataSources: mediaLibraryDataSourceRegistration,
    blocks: mediaLibraryBlocksRegistration,
    modelResources: modelResourcesRegistration,
  };
}
