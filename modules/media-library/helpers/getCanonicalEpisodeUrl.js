import siteServices from '#modules/web/services/siteServices.js';
import episodesServices from '../services/episodesServices.js';

export default async function getCanonicalEpisodeUrl(episodeId) {
  const { episode } = await episodesServices.getEpisodeById(episodeId, {
    fields: ['slug'],
    populate: [
      {
        path: 'channel',
        select: ['pageIDs'],
      },
      {
        path: 'show',
        select: ['slug'],
      },
      {
        path: 'season',
        select: ['slug'],
      },
    ],
  });

  const { channel, show } = episode;

  if (
    !channel?.pageIDs?.siteID ||
    !channel?.pageIDs?.pages?.episodeDetailPageID
  ) {
    return null;
  }

  const { siteID, pages } = channel.pageIDs;

  const { anchorShowPages, episodePage } =
    await episodesServices.getEpisodeDetailPages({
      siteId: siteID,
      detailPageId: pages.episodeDetailPageID,
    });

  const detailPage = anchorShowPages[episode._id]
    ? anchorShowPages[episode._id]
    : episodePage;

  if (!detailPage) return null;

  const { site } = await siteServices.getSiteById(channel?.pageIDs?.siteID);

  if (!site?.domain) return null;

  return `${site.domain.includes('localhost') ? 'http://' : 'https://'}${site.domain}${
    detailPage?.dynamic && episode.slug
      ? `${detailPage?.path
          .replace('[Show]', show?.slug)
          .replace('[Season]', episode.season?.slug)
          .replace('[Episode]', episode.slug)}`
      : detailPage?.path
  }`;
}
