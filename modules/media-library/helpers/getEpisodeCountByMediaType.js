import mongoose from 'mongoose';
import Channel from '../models/Channel.js';
import Show from '../models/Show.js';

export default async function getEpisodeCountByMediaType(channelId, mediaType) {
  if (!channelId) {
    throw new Error('Channel ID is required');
  }

  const channelFormats = await Channel.getFormats(channelId, mediaType);
  if (!channelFormats) {
    return [];
  }

  const pipeline = [
    {
      $match: {
        $or: [
          { channel: new mongoose.Types.ObjectId(channelId) },
          {
            [`channels.${channelId}.sharingEnabled`]: true,
            [`channels.${channelId}.enabled`]: true,
          },
        ],
        deleted: false,
        enabled: true,
      },
    },
    {
      $lookup: {
        from: 'episodes',
        let: { showId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$show', '$$showId'] },
                  {
                    $or: channelFormats.map((format) => ({
                      // Have to use $gt to compare with null
                      $gt: [
                        {
                          $ifNull: [
                            {
                              // Have to use getField to access nested fields
                              $getField: {
                                field: format,
                                input: '$mediaLinks',
                              },
                            },
                            null,
                          ],
                        },
                        null,
                      ],
                    })),
                  },
                ],
              },
            },
          },
        ],
        as: 'episodes',
      },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        episodeCount: { $size: '$episodes' },
      },
    },
    {
      $match: {
        episodeCount: { $gt: 0 },
      },
    },
  ];

  return await Show.aggregate(pipeline);
}
