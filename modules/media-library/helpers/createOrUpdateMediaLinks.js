import _ from 'lodash';

import imageController from '#modules/images/controllers/imageController.js';
import { errors } from '#utils/appError.js';
import jetstream from '#utils/jetstream.js';
import mpeg from '#utils/mpeg.js';
import vimeo from '#utils/vimeo.js';
import youtube from '#utils/youtube.js';

export async function createOrUpdateMediaLinks({
  body: bodyParam = {},
  episode,
  channel,
  entity,
}) {
  const body = { ...bodyParam };
  const mediaLinks = {};

  for (const [mediaFormat, mediaLink] of Object.entries(body.mediaLinks)) {
    const newValue = mediaLink?.file ?? mediaLink?.link ?? null;
    let currentValue =
      episode && episode.mediaLinks && episode.mediaLinks[mediaFormat]
        ? (episode.mediaLinks[mediaFormat].file ??
          episode.mediaLinks[mediaFormat].link)
        : null;

    // Skip if no new value
    if (!mediaLink) {
      mediaLinks[mediaFormat] = null;
      continue;
    }

    switch (mediaFormat) {
      case 'youtube': {
        const youtubeId = youtube.getYouTubeId(newValue);
        if (youtubeId !== currentValue) {
          const youtubeVideo = await youtube.getMediaLink(youtubeId);
          mediaLinks[mediaFormat] = youtubeVideo;

          if (newValue && !youtubeVideo) {
            return errors.video_not_found();
          }

          if (youtubeVideo) {
            // Set title
            if (!body.title && !(episode && episode.title)) {
              body.title = youtubeVideo.title;
            }

            // Set image
            if (!body.image && !(episode && episode.image)) {
              const {
                maxres,
                high,
                standard,
                medium,
                default: small,
              } = youtubeVideo.thumbnails;

              const imageUrl =
                (maxres || high || standard || medium || small)?.url || null;

              if (imageUrl) {
                body.image = await imageController.saveRemoteImage(
                  imageUrl,
                  entity
                );
              }
            }

            // Set duration
            if (!body.duration) {
              body.duration = youtubeVideo.duration;
            }
          }
        }
        break;
      }
      case 'vimeo': {
        vimeo.initClient(channel);
        const vimeoId = vimeo.getVimeoId(newValue);
        if (vimeoId !== currentValue) {
          const vimeoVideo = await vimeo.getVideo(vimeoId);
          const vimeoMediaLink = await vimeo.getMediaLink(vimeoVideo);
          mediaLinks[mediaFormat] = vimeoMediaLink;

          if (newValue && !vimeoVideo) {
            return errors.video_not_found();
          }

          if (vimeoVideo) {
            // Set title
            if (!body.title && !(episode && episode.title)) {
              body.title = vimeoVideo.name;
            }

            // Set image
            if (!body.image && !(episode && episode.image)) {
              const imageUrl = vimeoVideo.pictures.base_link;
              if (imageUrl) {
                body.image = await imageController.saveRemoteImage(
                  imageUrl,
                  entity
                );
              }
            }

            // Set duration
            if (!body.duration) {
              body.duration = vimeoVideo.duration;
            }

            // Set media links (MP4-HD, MP4-SD, HLS)
            if (vimeoVideo.files) {
              if (!body.mediaLinks['mp4-hd']) {
                const videoFile = vimeo.getVideoFile(vimeoVideo, 'hd', '720p');
                if (videoFile) {
                  mediaLinks['mp4-hd'] = mpeg.getMediaLink({
                    url: videoFile.link,
                    type: 'mp4',
                    fileSize: videoFile.size,
                  });
                }
              }

              if (!body.mediaLinks['mp4-sd']) {
                const videoFile = vimeo.getVideoFile(vimeoVideo, 'sd', '360p');
                if (videoFile) {
                  mediaLinks['mp4-sd'] = mpeg.getMediaLink({
                    url: videoFile.link,
                    type: 'mp4',
                    fileSize: videoFile.size,
                  });
                }
              }

              if (!body.mediaLinks.hls) {
                const videoFile = vimeo.getVideoFile(
                  vimeoVideo,
                  'hls',
                  'adaptive'
                );
                if (videoFile) {
                  mediaLinks.hls = {
                    link: videoFile.link,
                    aspectRatio: '16:9',
                    fileSize: 0,
                  };
                }
              }
            }

            // Set downloadable flag
            if (!body.downloadable) {
              body.downloadable = !!vimeoVideo.privacy?.download;
            }
          }
        }
        break;
      }
      // To be specific this is the video variant, the audio jetstream variant is handled in the jetstream-audio case
      case 'jetstream': {
        const jetstreamId = jetstream.getJetstreamId(newValue);

        // Add a jsv: prefix where it is missing
        currentValue = jetstream.getJetstreamId(currentValue);

        if (jetstreamId !== currentValue) {
          const jetstreamVideo = await jetstream.getVideo(jetstreamId);
          const jetstreamMediaLink = jetstream.getMediaLink(jetstreamVideo);
          mediaLinks[mediaFormat] = jetstreamMediaLink;

          if (newValue && !jetstreamVideo) {
            return errors.video_not_found();
          }

          if (jetstreamVideo) {
            // Set title
            if (!body.title && !(episode && episode.title)) {
              body.title = jetstreamVideo.title || '';
            }
            // Set image
            if (
              !body.image &&
              !(episode && episode.image) &&
              jetstreamVideo.primaryThumbnail?.largeUrl
            ) {
              try {
                body.image = await imageController.saveRemoteImage(
                  jetstreamVideo.primaryThumbnail.largeUrl,
                  entity
                );
              } catch {
                // We don't save the image if it fails
              }
            }

            // Set duration
            if (!body.duration) {
              body.duration = jetstream.getDuration(jetstreamVideo);
            }

            // Set media links (MP4-HD, MP4-SD, MP3, AAC, HLS, BIF)
            if (jetstreamVideo.renditions) {
              if (!body.mediaLinks['mp4-hd']) {
                const rendition = jetstream.getRendition(
                  jetstreamVideo,
                  'mp4-720p'
                );
                if (rendition)
                  mediaLinks['mp4-hd'] = mpeg.getMediaLink({
                    url: rendition.shortUrl,
                    type: 'mp4',
                    fileSize: rendition.filesize,
                  });
              }

              if (!body.mediaLinks['mp4-sd']) {
                const rendition = jetstream.getRendition(
                  jetstreamVideo,
                  'mp4-360p'
                );
                if (rendition)
                  mediaLinks['mp4-sd'] = mpeg.getMediaLink({
                    url: rendition.shortUrl,
                    type: 'mp4',
                    fileSize: rendition.filesize,
                  });
              }

              if (!body.mediaLinks.mp3) {
                const renditionAAC = jetstream.getRendition(
                  jetstreamVideo,
                  'aac'
                );
                const renditionMp3 = jetstream.getRendition(
                  jetstreamVideo,
                  'mp3'
                );
                const rendition = renditionAAC ?? renditionMp3;
                if (rendition)
                  mediaLinks.mp3 = mpeg.getMediaLink({
                    url: rendition.shortUrl,
                    type: 'mp3',
                    fileSize: rendition.filesize,
                  });
              }

              if (!body.mediaLinks.hls) {
                const rendition = jetstream.getRendition(jetstreamVideo, 'hls');
                if (rendition)
                  mediaLinks.hls = {
                    link: rendition.shortUrl,
                    aspectRatio: '16:9',
                    fileSize: 0,
                    duration: 0,
                  };
              }

              if (!body.mediaLinks.bif) {
                const rendition = jetstream.getRendition(jetstreamVideo, 'bif');
                if (rendition)
                  mediaLinks.bif = {
                    link: rendition.shortUrl,
                    aspectRatio: '16:9',
                    fileSize: 0,
                    duration: 0,
                  };
              }
            }
          }
        }
        break;
      }
      case 'mp4-hd':
      case 'mp4-sd': {
        if (newValue !== currentValue) {
          const mpegVideo = mpeg.getMediaLink({
            url: newValue,
            type: 'mp4',
          });
          mediaLinks[mediaFormat] = mpegVideo || null;
        }
        break;
      }
      case 'mp3': {
        if (newValue !== currentValue) {
          const mpegMP3 = mpeg.getMediaLink({
            url: newValue,
            type: 'mp3',
          });
          mediaLinks[mediaFormat] = mpegMP3 || null;
        }
        break;
      }
      case 'bif':
      case 'hls': {
        if (newValue !== currentValue) {
          const newMediaLink = {
            link: newValue, // TODO: check if new value is a valid URL
            aspectRatio: '16:9',
            fileSize: 0,
            duration: 0,
          };
          mediaLinks[mediaFormat] = newValue ? newMediaLink : null;
        }
        break;
      }
      case 'audio': {
        if (!_.isEqual(newValue, currentValue)) {
          const audioFile = {
            file: newValue,
            mime: newValue?.mime,
            type: newValue?.extension?.slice(1),
          };
          mediaLinks[mediaFormat] = newValue ? audioFile : null;
        }
        break;
      }
      case 'jetstream-audio': {
        // For this case we need to check if the id valid, like done in jetstream video.
        const jetstreamId = jetstream.getJetstreamId(newValue, 'audio');
        currentValue = jetstream.getJetstreamId(currentValue, 'audio');
        const jetstreamAudio = await jetstream.getMedia(jetstreamId, 'audio');
        const jetstreamAudioMediaLink = jetstream.getMediaLink(jetstreamAudio);

        if (newValue && !jetstreamAudio) {
          return errors.audio_not_found();
        }

        if (jetstreamAudio) {
          mediaLinks[mediaFormat] = jetstreamAudioMediaLink; // TODO: Do like in jetstream video and get other fields to then display

          // If the value changed, we need to get the new mp3 (optimized in aac format) link
          if (jetstreamId !== currentValue && jetstreamId) {
            if (!body.title && !(episode && episode.title)) {
              body.title = jetstreamAudio.title || '';
            }

            // Set image
            if (
              !body.image &&
              !(episode && episode.image) &&
              jetstreamAudio.primaryThumbnail?.largeUrl
            ) {
              body.image = await imageController.saveRemoteImage(
                jetstreamAudio.primaryThumbnail.largeUrl,
                entity
              );
            }
            if (!body.mediaLinks.mp3) {
              const renditionAAC = jetstream.getRendition(
                jetstreamAudio,
                'aac'
              );
              const renditionMp3 = jetstream.getRendition(
                jetstreamAudio,
                'mp3'
              );
              const rendition = renditionAAC ?? renditionMp3;
              if (rendition)
                mediaLinks.mp3 = mpeg.getMediaLink({
                  url: rendition.shortUrl,
                  type: 'mp3',
                  fileSize: rendition.filesize,
                });
            }
          }
        }

        break;
      }
      default: {
        break;
      }
    }
  }
  body.mediaLinks = mediaLinks || {};

  return { body };
}
