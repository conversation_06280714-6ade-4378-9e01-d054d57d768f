import jetstream from '#utils/jetstream.js';
import mpeg from '#utils/mpeg.js';
import Logger from '#utils/logger.js';

import imageController from '#modules/images/controllers/imageController.js';

import Channel from '#modules/media-library/models/Channel.js';
import Episode from '#modules/media-library/models/Episode.js';

export default async function runJetstreamUpdater(task) {
  // Get task settings
  const {
    channel: channelId,
    updateDuration = false,
    updateMediaLinks = true,
    updateImages = false,
  } = task.settings;

  const channel = await (
    await Channel.findById(channelId, 'title jetstream entity')
  ).populate({ path: 'entity', select: 'title' });
  if (!channel.jetstream?.organizationId) {
    return { error: 'Organization ID of Jetstream missing' };
  }

  Logger.info(channel.title.toUpperCase());
  Logger.info('-------------------------------');

  let episodesUpdated = 0;
  let jetstreamVideosMissing = 0;

  const pageSize = 20;
  let pageNumber = 1;
  let offset = 0;

  const filter = {
    // '_id': '63b58243a8e035fb1eb9e5c2',
    // 'title': 'Do We Really Understand Religious Freedom?',
    // 'show': '61975e1fd9c698836400304e',
    'channel': channelId,
    'deleted': false,
    'videoOnDemand': true,
    // 'enabled': true,
    // '$expr': {
    //   $and: [
    //     { $lte: [{ $ifNull: ['$videoOnDemandStartsAt', '$$NOW'] }, '$$NOW'] },
    //     { $gte: [{ $ifNull: ['$videoOnDemandEndsAt', '$$NOW'] }, '$$NOW'] },
    //   ],
    // },
    'mediaLinks.jetstream': { $ne: null },
  };

  // Get total episodes
  const totalEpisodes = await Episode.countDocuments(filter);
  Logger.info(`Total episodes: ${totalEpisodes}`);
  Logger.info('');

  while (offset <= totalEpisodes) {
    // Print progress
    Logger.info(`Page ${pageNumber}`);

    // Get episodes
    const episodes = await Episode.find(filter)
      .populate({ path: 'show', select: 'title' })
      .select('title show duration mediaLinks image createdAt')
      .sort({ show: 1, createdAt: 1, _id: 1 })
      .skip(offset)
      .limit(pageSize);

    // Increase page number and offset
    pageNumber += 1;
    offset += pageSize;

    // Continue if no episodes were found
    if (episodes.length === 0) {
      continue;
    }

    // Get videos from Jetstream GraphQL API
    const jetstreamIds = episodes.map((e) =>
      jetstream.getJetstreamId(e.mediaLinks.jetstream.link)
    );

    const results = await jetstream.getVideos({
      organizationId: channel.jetstream?.organizationId,
      pageSize,
      videoIds: jetstreamIds,
    });

    // Debug info
    // Logger.info(`Hope Platform: ${jetstreamIds.join(', ')}`);
    // Logger.info(
    //   `Jetstream: ${
    //     results?.videos
    //       ? results.videos.map((video) => video.id).join(', ')
    //       : 'n/a'
    //   }`
    // );

    if (results) {
      const { videos: jetstreamVideos } = results;

      // Loop through episodes
      for (const episode of episodes) {
        const modifiedFields = [];
        const jetstreamId = jetstream.getJetstreamId(
          episode.mediaLinks.jetstream.link
        );
        const jetstreamVideo = jetstreamVideos.find(
          (v) => v.id === jetstreamId
        );
        if (jetstreamVideo) {
          // Update duration
          if (updateDuration) {
            const jetstreamDuration = jetstream.getDuration(jetstreamVideo);
            if (
              jetstreamDuration > 0 &&
              jetstreamDuration !== episode.duration
            ) {
              episode.duration = jetstreamDuration;
              modifiedFields.push('duration');
            }
          }

          // Update media links
          if (updateMediaLinks) {
            if (jetstreamVideo.renditions) {
              let rendition = null;
              let episodeLink = null;
              let renditionLink = null;
              const { mediaLinks } = episode;
              const updatedMediaLinks = {};

              // Jetstream
              episode.mediaLinks.jetstream =
                jetstream.getMediaLink(jetstreamVideo);

              // MP4 HD
              rendition = jetstream.getRendition(jetstreamVideo, 'mp4-720p');
              renditionLink = rendition?.shortUrl || null;
              episodeLink = mediaLinks['mp4-hd']?.link || null;
              if (episodeLink !== renditionLink) {
                updatedMediaLinks['mp4-hd'] = renditionLink
                  ? mpeg.getMediaLink({
                      url: renditionLink,
                      type: 'mp4',
                      fileSize: rendition.filesize,
                    })
                  : null;
                modifiedFields.push('mp4-hd');
              }

              // MP4 SD
              rendition = jetstream.getRendition(jetstreamVideo, 'mp4-360p');
              renditionLink = rendition?.shortUrl || null;
              episodeLink = mediaLinks['mp4-sd']?.link || null;
              if (episodeLink !== renditionLink) {
                updatedMediaLinks['mp4-sd'] = renditionLink
                  ? mpeg.getMediaLink({
                      url: renditionLink,
                      type: 'mp4',
                      fileSize: rendition.filesize,
                    })
                  : null;
                modifiedFields.push('mp4-sd');
              }

              // Audio
              const renditionAAC = jetstream.getRendition(
                jetstreamVideo,
                'aac'
              );
              const renditionMp3 = jetstream.getRendition(
                jetstreamVideo,
                'mp3'
              );
              rendition = renditionAAC ?? renditionMp3;
              renditionLink = rendition?.shortUrl || null;
              episodeLink = mediaLinks.mp3?.link || null;
              if (episodeLink !== renditionLink) {
                updatedMediaLinks.mp3 = renditionLink
                  ? mpeg.getMediaLink({
                      url: renditionLink,
                      type: 'mp3',
                      fileSize: rendition.filesize,
                    })
                  : null;
                modifiedFields.push('mp3');
              }

              // HLS
              rendition = jetstream.getRendition(jetstreamVideo, 'hls');
              renditionLink = rendition?.shortUrl || null;
              episodeLink = mediaLinks.hls?.link || null;
              if (episodeLink !== renditionLink) {
                updatedMediaLinks.hls = renditionLink
                  ? {
                      link: renditionLink,
                      aspectRatio: '16:9',
                      fileSize: 0,
                      duration: 0,
                    }
                  : null;
                modifiedFields.push('hls');
              }

              // BIF
              rendition = jetstream.getRendition(jetstreamVideo, 'bif');
              renditionLink = rendition?.shortUrl || null;
              episodeLink = mediaLinks.bif?.link || null;
              if (episodeLink !== renditionLink) {
                updatedMediaLinks.bif = renditionLink
                  ? {
                      link: renditionLink,
                      aspectRatio: '16:9',
                      fileSize: 0,
                      duration: 0,
                    }
                  : null;
                modifiedFields.push('bif');
              }
              if (Object.keys(updatedMediaLinks).length > 0) {
                episode.mediaLinks = {
                  ...episode.mediaLinks, // keep YouTube and Vimeo links
                  ...updatedMediaLinks, // overwrite Jetstream renditions
                };
              }
            }
          }

          // Update image
          if (updateImages) {
            if (jetstreamVideo.primaryThumbnail?.largeUrl) {
              episode.image = await imageController.saveRemoteImage(
                jetstreamVideo.primaryThumbnail.largeUrl,
                channel.entity
              );
              modifiedFields.push('image');
            }
          }

          if (modifiedFields.length > 0) {
            await episode.save();
            Logger.success(
              `Updated episode "${episode.show.title}: ${
                episode.title
              }" (${jetstreamId}, ${modifiedFields.join(', ')})`
            );
            episodesUpdated += 1;
          }
        } else {
          Logger.warning(
            `Jetstream video missing for "${episode.show.title}: ${episode.title}" (${jetstreamId})`
          );
          jetstreamVideosMissing += 1;
        }
      }
    }
  }

  return {
    pages: pageNumber,
    episodesUpdated,
    jetstreamVideosMissing,
  };
}
