import Channel from '#modules/media-library/models/Channel.js';

export default async function getJetstreamUpdaterMeta({ task }) {
  if (!task) {
    throw new Error('Task is required');
  }
  const { settings } = task;

  const channel = settings?.channel
    ? await Channel.findById(settings.channel).select('title')
    : null;

  return {
    module: 'media-library',
    name: 'jetstream-updater',
    title: channel?.title,
  };
}
