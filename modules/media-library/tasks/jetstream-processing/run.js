import { getJetstreamEpisodeProgress } from '#utils/jetstream.js';
import Logger from '#utils/logger.js';

import Channel from '#modules/media-library/models/Channel.js';
import Episode from '#modules/media-library/models/Episode.js';

export default async function runJetstreamProcessing(task) {
  // Get task settings
  const { channel: channelId } = task.settings;

  const channel = await (
    await Channel.findById(channelId, 'title jetstream entity')
  ).populate({ path: 'entity', select: 'title' });
  if (!channel.jetstream?.organizationId) {
    return { error: 'Organization ID of Jetstream missing' };
  }

  Logger.info('-------------------------------');

  let episodesUpdated = 0;
  let jetstreamMediaFailedProcessing = 0;

  const pageSize = 20;
  let pageNumber = 1;
  let offset = 0;

  const filter = {
    channel: channelId,
    deleted: false,
    processed: false,
  };

  // Get total episodes
  const totalEpisodes = await Episode.countDocuments(filter);
  Logger.info(`Total episodes: ${totalEpisodes}`);
  Logger.info('');

  while (offset <= totalEpisodes) {
    // Print progress
    Logger.info(`Page ${pageNumber}`);

    // Get episodes
    const episodes = await Episode.find(filter)
      .populate({ path: 'show', select: 'title' })
      .select(
        'title downloadable show duration mediaLinks tempLinks processed timesProcessed image createdAt'
      )
      .sort({ show: 1, createdAt: 1, _id: 1 })
      .skip(offset)
      .limit(pageSize);

    // Increase page number and offset
    pageNumber += 1;
    offset += pageSize;

    // Continue if no episodes were found
    if (episodes.length === 0) {
      continue;
    }

    // For each episode we get the progress (it gets updated it if done), if not done then we update here the timesProcessed field of the episode, incrementing it by 1.
    for (const episode of episodes) {
      // We simulate the req object. This object will then be correctly updated, then if episode is done processing we will update the episode in getJetstreamEpisodeProgress.
      const req = {
        body: {
          title: episode.title,
          duration: episode.duration,
          mediaLinks: episode.mediaLinks,
          image: episode.image,
          downloadable: episode.downloadable,
        },
        entity: channel.entity,
      };

      // This function updates the episode if it is done processing, and returns the progress with the status and percentage.
      const progress = await getJetstreamEpisodeProgress(req, episode.id);
      Logger.info(
        `Episode "${episode.show.title}: ${episode.title}" (${episode.id}) progress: ${progress.status} (${progress.percentage}%)`
      );

      // Episode still not done
      if (progress.status !== 'DONE') {
        // we set timesProcessed to 0 if it isn't a numnber
        if (typeof episode.timesProcessed !== 'number') {
          episode.timesProcessed = 0;
        }

        if (episode.timesProcessed >= 3) {
          Logger.error(
            `Episode "${episode.show.title}: ${episode.title}" (${episode.id}) failed to process after 3 attempts`
          );
          episode.processed = true;
          episode.timesProcessed += 1;
          // episode.timesProcessed = 0; // We will use timesProcessed to show the error
          jetstreamMediaFailedProcessing += 1;
          await episode.save();
        } else {
          // Episode still not done, we increment timesProcessed by 1
          episode.timesProcessed += 1;
          await episode.save(); // Save the timesProcessed
          Logger.info(
            `Episode "${episode.show.title}: ${episode.title}" (${episode.id}) processing attempt ${episode.timesProcessed}`
          );
        }
      } else {
        // It is updated in getJetstreamEpisodeProgress if it is done processing.
        episodesUpdated += 1;
        episode.timesProcessed = 0;
        await episode.save(); // Reset timesProcessed on success
      }
    }
  }

  return {
    episodesUpdated,
    jetstreamMediaFailedProcessing,
  };
}
