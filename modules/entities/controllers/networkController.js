import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';

import Network from '../models/Network.js';

export const getAllNetworks = async (req, res) => {
  const data = await factory.getAll(Network, req, {
    sort: ['title'],
  });

  res.status(200).json(data);
};

// Bring all networks, of which only bring the title and slug
export const getAllPublicNetworks = async (req, res) => {
  const data = await factory.getAll(Network, req, {
    select: 'title slug id',
    sort: ['title'],
  });

  res.status(200).json(data);
};

export const getNetwork = async (req, res) => {
  const data = await factory.getOne(Network, req, {
    paramId: 'networkId',
  });

  res.status(200).json(data);
};

export const createNetwork = async (req, res) => {
  // Ensure network has a valid slug within its siblings (or create one from its name)
  const slug = await Network.getAvailableSlug(
    slugify(req.body.slug || req.body.title)
  );

  // Create the network
  const network = await Network.create({
    ...req.body,
    slug,
  });

  res.status(200).json(network);
};

export const updateNetwork = async (req, res) => {
  const network = await factory.getOne(Network, req, {
    paramId: 'networkId',
  });

  // Ensures new slug doesn't exists
  const slug = req.body.slug
    ? await Network.getAvailableSlug(slugify(req.body.slug), network._id)
    : network.slug;

  // Update network's data
  const updatedNetwork = await Network.findByIdAndUpdate(
    network._id,
    {
      ...req.body,
      slug,
    },
    {
      new: true,
      runValidators: true,
    }
  );

  res.status(200).json(updatedNetwork);
};

export const deleteNetwork = async (req, res) => {
  const data = await factory.deleteOne(Network, req, {
    paramId: 'networkId',
  });

  res.status(200).json(data);
};

export const restoreNetwork = async (req, res) => {
  const data = await factory.restoreOne(Network, req, {
    paramId: 'networkId',
  });

  res.status(200).json(data);
};

export const disableNetwork = async (req, res) => {
  const data = await factory.disableOne(Network, req, {
    paramId: 'networkId',
  });

  res.status(200).json(data);
};

export const enableNetwork = async (req, res) => {
  const data = await factory.enableOne(Network, req, {
    paramId: 'networkId',
  });

  res.status(200).json(data);
};

export default {
  getAllNetworks,
  getAllPublicNetworks,
  getNetwork,
  createNetwork,
  updateNetwork,
  deleteNetwork,
  restoreNetwork,
  disableNetwork,
  enableNetwork,
};
