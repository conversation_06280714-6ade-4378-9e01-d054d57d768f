import { errors } from '#utils/appError.js';
import { isEmpty } from '#utils/arrays.js';
import factory from '#utils/handlerFactory.js';
import { areEqualIDs } from '#utils/helpers.js';

import personController from '#modules/persons/controllers/personController.js';
import personRoleController from '#modules/persons/controllers/personRoleController.js';
import Person from '#modules/persons/models/Person.js';
import PersonRole from '#modules/persons/models/PersonRole.js';
import Entity from '../models/Entity.js';
import Service from '../models/EntityService.js';
import serviceServices from '../services/serviceServices.js';

export const getAllServices = async (req, res) => {
  const entity = await factory.getOne(Entity, req, {
    id: req.query.entityId,
    populate: [
      {
        path: 'services',
        match: { deleted: false },
        populate: [
          personRoleController.populateRole('contactPersons'),
          personRoleController.populateRole('team'),
        ],
      },
    ],
    fields: ['services'],
  });

  const services =
    entity?.services?.map((service) => {
      service = service.toObject();
      return {
        ...service,
        ...personController.mergeReferencedPersons(service, 'contactPersons'),
        ...personController.mergeReferencedPersons(service, 'team'),
      };
    }) || [];

  res.status(200).json({
    count: services.length,
    items: services,
  });
};

export const getService = async (req, res) => {
  const data = await serviceServices.getServiceById(req.params.serviceId);

  res.status(200).json(data);
};

export const createService = async (req, res) => {
  const { entityId, ...serviceData } = req.body;
  const service = await serviceServices.createService(entityId, serviceData);

  res.status(200).json(service);
};

export const updateService = async (req, res) => {
  const updatedService = await serviceServices.updateService(
    req.params.serviceId,
    req.body
  );

  res.status(200).json(updatedService);
};

export const deleteService = async (req, res) => {
  const deletedService = await factory.deleteOne(Service, req, {
    paramId: 'serviceId',
  });

  // Remove service from entity
  const entity = await Entity.findById(deletedService.entity, 'services');
  if (entity && !isEmpty(entity.services)) {
    entity.services = entity.services.filter(
      (service) => !areEqualIDs(service._id, deletedService._id)
    );
    await entity.save();
  }

  res.status(200).json(deletedService);
};

export const restoreService = async (req, res) => {
  const data = await factory.restoreOne(Service, req, { paramId: 'serviceId' });

  // TODO: add service to entity

  res.status(200).json(data);
};

export const disableService = async (req, res) => {
  const data = await factory.disableOne(Service, req, { paramId: 'serviceId' });

  res.status(200).json(data);
};

export const enableService = async (req, res) => {
  const data = await factory.enableOne(Service, req, { paramId: 'serviceId' });

  res.status(200).json(data);
};

export const addContactPerson = async (req, res) => {
  const service = await factory.getOne(Service, req, { paramId: 'serviceId' });

  const {
    entity,
    person,
    role,
    title,
    email,
    phone,
    personRoleId,
    existingRole,
  } = req.body;

  let personRole = await PersonRole.findById(personRoleId);

  if (!personRole && existingRole === 'existing') {
    throw errors.not_found('PersonRole', personRoleId);
  }

  const personRecord = await Person.findOne({
    _id: person,
    deleted: false,
  });

  if (personRecord && existingRole !== 'existing') {
    // Create role
    personRole = await PersonRole.create({
      person: personRecord.id,
      entity,
      role,
      title,
      email,
      phone,
    });
  }

  if (personRole) {
    // Add contact person to service
    service.contactPersons = Array.from(
      new Set([...service.contactPersons, personRole._id])
    );
    await service.save();
  }

  res.status(200).json(service);
};

export const disableContactPerson = async (req, res) => {
  const service = await factory.getOne(Service, req, { paramId: 'serviceId' });
  await PersonRole.findByIdAndUpdate(req.body.personRoleId, {
    enabled: false,
  });
  res.status(200).json(service);
};

export const enableContactPerson = async (req, res) => {
  const service = await factory.getOne(Service, req, { paramId: 'serviceId' });
  await PersonRole.findByIdAndUpdate(req.body.personRoleId, { enabled: true });
  res.status(200).json(service);
};

export const removeContactPerson = async (req, res) => {
  const service = await factory.getOne(Service, req, { paramId: 'serviceId' });

  // Remove contact person from service
  service.contactPersons = service.contactPersons.filter(
    (personRoleId) => !areEqualIDs(personRoleId, req.body.personRoleId)
  );
  await service.save();

  res.status(200).json(service);
};

export const updateContactPerson = async (req, res) => {
  const service = await factory.getOne(Service, req, { paramId: 'serviceId' });
  await PersonRole.findByIdAndUpdate(req.body._id, req.body);
  res.status(200).json(service);
};

export default {
  getAllServices,
  getService,
  createService,
  updateService,
  deleteService,
  restoreService,
  disableService,
  enableService,
  addContactPerson,
  updateContactPerson,
  disableContactPerson,
  enableContactPerson,
  removeContactPerson,
};
