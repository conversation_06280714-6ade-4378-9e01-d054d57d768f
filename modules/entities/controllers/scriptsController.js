import factory from '#utils/handlerFactory.js';

import coursesImport from '#modules/courses/scripts/import/courses-import.js';
import adventistenDeImport from '../scripts/adventisten-de/typo3-import.js';

import Entity from '../models/Entity.js';

export const importAdventistenDe = async (req, res) => {
  const entity = await factory.getOne(Entity, req, { paramId: 'entityId' });
  const results = await adventistenDeImport.importData(entity);
  res.status(results.error ? 400 : 200).json(results);
};

export const importCourses = async (req, res) => {
  const entity = await factory.getOne(Entity, req, { paramId: 'entityId' });
  const sourceEntityId = req.query['source-entity'];
  const providerId = req.query.provider;
  const results = await coursesImport.importData(
    sourceEntityId,
    entity,
    providerId
  );

  if (results.error) {
    return res.status(results.status || 500).json(results);
  }
  res.status(200).json(results);
};

export default {
  importAdventistenDe,
  importCourses,
};
