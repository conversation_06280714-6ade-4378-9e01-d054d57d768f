import personRoleService from '#modules/persons/services/personRoleService.js';
import { getBackendURL } from '#modules/users/utils/ancestors.js';
import { errors } from '#utils/appError.js';
import factory from '#utils/handlerFactory.js';
import { areEqualIDs } from '#utils/helpers.js';

import entityTypes from '../data/entityTypes.js';
import { filterAllowedAncestors } from '../helpers/filterAllowedAncestors.js';
import getEntitiesPermissions from '../helpers/getEntitiesPermissions.js';
import {
  ancestorsPopulate,
  getInheritedAttribute,
} from '../helpers/getEntityAncestorAttribute.js';
import { getEntityLocales } from '../helpers/getEntityLocales.js';
import Entity from '../models/Entity.js';
import entityServices, {
  contactPersonsPopulate,
} from '../services/entityServices.js';

export const setEntityId = (req, res, next) => {
  req.params.id = req.entity._id;

  next();
};

export const currentEntity = async (req, res) => {
  const { entity, user, query } = req;
  const { isAdmin } = user || {};
  const { includeNetwork, fields } = query || {};

  const { entity: entityData, error } = await entityServices.getCurrentEntity({
    entity,
    isAdmin,
    includeNetwork,
    fields,
  });

  if (error) {
    return res.status(500).json({ error });
  }

  res.status(200).json(entityData);
};

/**
 * Get entities for current user within current entity's scope
 * @param {Object} req
 * @param {Object} res
 * @returns {{ items: Entity[], count: Number }} The response
 */
export const getEntities = async (req, res) => {
  const { user } = req;

  // Get user entities permissions
  const { entitiesIds, withSubentities } = getEntitiesPermissions(user);
  // const { automatedEntitiesIds, withSubentities } = getEntitiesPermissions(user);

  // Convert req.query.filter to object in order to manipulate it
  const { $or, ...restFilter } = JSON.parse(req.query.filter || '{}');

  // Wrap $or filter in an $and filter to ensure it's not overwritten
  const filter = { ...restFilter, $and: [{ $or }] };

  const { excludedIds, publicMode = false } = req.query || {};

  // Filter entities if user is not admin, and public mode is false
  if (!user.isAdmin && !publicMode) {
    const entityFilter = {
      // Get entities that user has direct access to, or subentities of entities that user has direct access to
      $or: [
        // 1. Entities that user has direct access to and are not excluded
        { _id: { $in: entitiesIds, $nin: excludedIds } },

        // 2. Subentities that user has access to (under entities with direct access)
        ...withSubentities.map(({ entityId, type }) => {
          const subEntitiesFilter = {
            // Subentities of the entity to which the user has direct access
            ancestors: { $in: [entityId] },
          };

          // Filter by subentities type if defined (otherwise, get all subentities)
          if (type && type !== 'all') subEntitiesFilter.type = type;

          return subEntitiesFilter;
        }),
      ],
    };

    // Add entity filter to $and filter
    filter.$and.push(entityFilter);
  }

  // Convert filter back to string to be used in req.query
  req.query.filter = JSON.stringify(filter);

  // Get entities (and count)
  const { items, count } = await factory.getAll(Entity, req, {
    fields: publicMode ? 'name shortName type' : undefined, // Only get name, shortName, and type in public mode
  });

  const newItems = [];
  const entityIds = items.map((item) => item._id);
  const entities = await Entity.find(
    { _id: { $in: entityIds }, deleted: false },
    'parent'
  ).lean();
  for (const item of items) {
    const newItem = item.toObject();
    const subentityCount = entities.filter((entity) =>
      areEqualIDs(entity.parent, item._id)
    ).length;
    newItems.push({ ...newItem, hasSubentities: subentityCount > 0 });
  }

  res.status(200).json({ items: newItems, count });
};

/**
 * Get public entity
 */
export const getPublicEntity = async (req, res) => {
  // Get entity name, type, and id
  const data = await Entity.findById(req.params.id, 'name type _id id').lean();

  // Return entity name, type, and id
  res.status(200).json(data);
};

/**
 * Get entity by id
 */
export const getEntity = async (req, res) => {
  const { user } = req;
  const { includeAncestors, includeContactPersons } = req.query;

  let data = await entityServices.getEntityById(req.params.id, {
    populate: [
      ...(includeAncestors ? [ancestorsPopulate] : []),
      ...(includeContactPersons ? [contactPersonsPopulate] : []),
      { path: 'network' },
    ],
  });

  data = data.toObject();

  // Remove ancestors that are higher than the user entity
  if (includeAncestors && !user.isAdmin) {
    data.ancestors = filterAllowedAncestors(data, req.entity);
  }

  // Only some config fields are visible only for non-admins
  const { defaultDesign, defaultRegion, currencies } = data.config;

  data.config = {
    ...(user?.isAdmin ? data.config : {}), // Add all other config fields for admins
    defaultDesign,
    defaultRegion,
    currencies,
  };

  // Inherited values will be shown in the select components for each.
  data.config.inheritedDefaultDesign = getInheritedAttribute({
    entity: data,
    attributeName: 'config.defaultDesign',
    networkAttributeName: 'defaultDesign',
    excludeSelf: true,
  });

  data.config.inheritedDefaultRegion = getInheritedAttribute({
    entity: data,
    attributeName: 'config.defaultRegion',
    networkAttributeName: 'defaultRegion',
    excludeSelf: true,
  });

  data.config.inheritedCurrencies = getInheritedAttribute({
    entity: data,
    attributeName: 'config.currencies',
    networkAttributeName: 'currencies',
    excludeSelf: true,
  });
  data.network = getInheritedAttribute({
    entity: data,
    attributeName: 'network._id',
  });

  data.inheritedNetwork = getInheritedAttribute({
    entity: data,
    attributeName: 'network._id',
    excludeSelf: true,
  });

  data.languages = getEntityLocales(data);

  res.status(200).json(data);
};

/**
 * Create entity
 */
export const createEntity = async (req, res) => {
  if (!req.user.isAdmin && !req.body.parent) {
    req.body.parent = req.entity._id; // Set parent to current entity
  }
  const data = await entityServices.createEntity(req.body);

  data.languages = getEntityLocales(data.toObject ? data.toObject() : data);

  res.status(200).json(data);
};

/**
 * Update entity
 */
export const updateEntity = async (req, res) => {
  const updatedEntity = await entityServices.updateEntity(
    req.params.id,
    req.body,
    {
      populate: [
        ancestorsPopulate,
        contactPersonsPopulate,
        { path: 'network' },
      ],
    }
  );

  updatedEntity.languages = getEntityLocales(
    updatedEntity.toObject ? updatedEntity.toObject() : updatedEntity
  );

  res.status(200).json(updatedEntity);
};

/**
 * Delete entity
 */
export const deleteEntity = async (req, res) => {
  const data = await factory.deleteOne(Entity, req);
  res.status(200).json(data);
};

/**
 * Restore entity
 */
export const restoreEntity = async (req, res) => {
  const data = await factory.restoreOne(Entity, req);

  data.languages = getEntityLocales(data.toObject ? data.toObject() : data);

  res.status(200).json(data);
};

/**
 * Disable entity
 */
export const disableEntity = async (req, res) => {
  const data = await factory.disableOne(Entity, req);

  data.languages = getEntityLocales(data.toObject ? data.toObject() : data);

  res.status(200).json(data);
};

/**
 * Enable entity
 */
export const enableEntity = async (req, res) => {
  const data = await factory.enableOne(Entity, req);

  data.languages = getEntityLocales(data.toObject ? data.toObject() : data);

  res.status(200).json(data);
};

/**
 * Create subentity
 */
export const createSubentity = async (req, res) => {
  req.body.parent = req.params.id;

  createEntity(req, res);
};

/**
 * Get subentities
 */
export const getSubentities = async (req, res) => {
  const data = await factory.getAll(Entity, req, {
    filter: { parent: req.params.id },
  });
  res.status(200).json(data);
};

/**
 * Get all entity types
 */
export const getEntityTypes = async (req, res) => {
  const items = Object.entries(entityTypes).reduce(
    (acc, [name, { services, icon, orgMastCode }]) => {
      acc.push({
        name,
        services,
        icon,
        orgMastCode,
      });

      return acc;
    },
    []
  );

  return res.status(200).json({
    items,
    count: items.length,
  });
};

/**
 * Get a specific entity type
 */
export const getEntityType = async (req, res) => {
  const { type } = req.params;
  const entityType = entityTypes[type] || {};
  const { services, icon, orgMastCode } = entityType;

  if (!entityType) {
    return res.status(404).json({ message: 'Entity type not found' });
  }

  return res.status(200).json({
    name: type,
    services,
    icon,
    orgMastCode,
  });
};

const getEntityLanguages = async (req, res) => {
  const { query, params, user } = req;
  const { search, locales, exceptions } = query;

  const { items, count } = await entityServices.getLanguages({
    entityId: params.id,
    exceptions,
    language: user?.preferences?.language || 'en',
    locales,
    search,
  });

  res.status(200).json({ items, count });
};

/**
 * Adds a contact person to the entity
 */
export const addContactPerson = async (req, res) => {
  const { id } = req.params;
  const {
    avatar,
    email,
    existingRole,
    personId,
    personRoleId,
    phone,
    role,
    title,
  } = req.body;

  const entity = await entityServices.getEntityById(id);

  let personRole;

  if (existingRole === 'existing') {
    // Get person role
    personRole = await personRoleService.getPersonRoleById({
      personRoleId,
      entityId: entity._id,
    });
  } else {
    // Create person role
    personRole = await personRoleService.createPersonRole({
      entityId: entity._id,
      personId,
      personRoleData: {
        avatar,
        email,
        phone,
        role,
        title,
      },
    });
  }

  if (!personRole) {
    throw errors.params('PersonRole');
  }

  await entityServices.addContactPerson({
    entity,
    personRoleId: personRole._id,
  });

  return res.status(200).json(personRole);
};

export const getBackendUrl = async (req, res) => {
  const { id } = req.params;
  const entity = await entityServices.getEntityById(id);
  const backendUrl = await getBackendURL(entity);

  return res.status(200).json({ backendUrl });
};

export default {
  setEntityId,
  currentEntity,
  getEntities,
  getEntity,
  getPublicEntity,
  createEntity,
  updateEntity,
  deleteEntity,
  restoreEntity,
  disableEntity,
  enableEntity,
  createSubentity,
  getSubentities,
  getEntityTypes,
  getEntityType,
  getEntityLanguages,
  addContactPerson,
  getBackendUrl,
};
