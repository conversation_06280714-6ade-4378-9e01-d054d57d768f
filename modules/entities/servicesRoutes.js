import express from 'express';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';

import serviceController from './controllers/serviceController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(serviceController.getAllServices)
  .post(
    restrictTo({
      module: 'services',
      permissions: ['create'],
    }),
    logRequest({ module: 'entities', action: 'CREATE_SERVICE' }),
    serviceController.createService
  );

router
  .route('/:serviceId')
  .get(serviceController.getService)
  .patch(
    restrictTo({
      module: 'services',
      permissions: ['update'],
      paramId: 'serviceId',
    }),
    logRequest({ module: 'entities', action: 'UPDATE_SERVICE' }),
    serviceController.updateService
  )
  .delete(
    restrictTo({
      module: 'services',
      permissions: ['delete'],
      paramId: 'serviceId',
    }),
    logRequest({ module: 'entities', action: 'DELETE_SERVICE' }),
    serviceController.deleteService
  );

router.route('/:serviceId/restore').patch(
  restrictTo({
    module: 'services',
    permissions: ['delete'],
    paramId: 'serviceId',
  }),
  logRequest({ module: 'entities', action: 'RESTORE_SERVICE' }),
  serviceController.restoreService
);

router.route('/:serviceId/disable').patch(
  restrictTo({
    module: 'services',
    permissions: ['update'],
    paramId: 'serviceId',
  }),
  logRequest({ module: 'entities', action: 'DISABLE_SERVICE' }),
  serviceController.disableService
);

router.route('/:serviceId/enable').patch(
  restrictTo({
    module: 'services',
    permissions: ['update'],
    paramId: 'serviceId',
  }),
  logRequest({ module: 'entities', action: 'ENABLE_SERVICE' }),
  serviceController.enableService
);

router.route('/:serviceId/add-contact-persons').post(
  restrictTo({
    module: 'services',
    permissions: ['update'],
    paramId: 'serviceId',
  }),
  logRequest({ module: 'entities', action: 'ADD_CONTACT_PERSON' }),
  serviceController.addContactPerson
);

router.route('/:serviceId/remove-contact-person').patch(
  restrictTo({
    module: 'services',
    permissions: ['update'],
    paramId: 'serviceId',
  }),
  logRequest({ module: 'entities', action: 'REMOVE_CONTACT_PERSON' }),
  serviceController.removeContactPerson
);

router.route('/:serviceId/disable-contact-person').patch(
  restrictTo({
    module: 'services',
    permissions: ['update'],
    paramId: 'serviceId',
  }),
  logRequest({ module: 'entities', action: 'DISABLE_CONTACT_PERSON' }),
  serviceController.disableContactPerson
);

router.route('/:serviceId/enable-contact-person').patch(
  restrictTo({
    module: 'services',
    permissions: ['update'],
    paramId: 'serviceId',
  }),
  logRequest({ module: 'entities', action: 'ENABLE_CONTACT_PERSON' }),
  serviceController.enableContactPerson
);

router.route('/:serviceId/update-contact-person').patch(
  restrictTo({
    module: 'services',
    permissions: ['update'],
    paramId: 'serviceId',
  }),
  logRequest({ module: 'entities', action: 'UPDATE_CONTACT_PERSON' }),
  serviceController.updateContactPerson
);

export default router;
