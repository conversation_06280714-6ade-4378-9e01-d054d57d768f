import entitiesRouter from './entitiesRoutes.js';
import networksRoutes from './networksRoutes.js';
import servicesRoutes from './servicesRoutes.js';
import entitiesTasks from './tasks/index.js';

import { entitiesBlocksRegistration } from './blocks/entitiesBlocksRegistration.js';

export default function entities() {
  return {
    routes: {
      '/entities/services': servicesRoutes,
      '/entities/networks': networksRoutes,
      '/entities': entitiesRouter,
    },

    tasks: entitiesTasks,
    blocks: entitiesBlocksRegistration,
  };
}
