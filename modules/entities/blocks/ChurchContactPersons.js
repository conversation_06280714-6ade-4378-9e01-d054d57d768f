import { getSearchableContactInfo } from './helpers/getSearchableContactInfo.js';

/**
 * ChurchContactPersons block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export function ChurchContactPersons() {
  return {
    name: 'ChurchContactPersons',
    getSearchableContent,
  };
}

/**
 * Get searchable content for ChurchContactPersons
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { Entity, entityServices } = node?.props ?? {};

  return getSearchableContactInfo({
    Entity,
    entityServices: entityServices.items,
  });
}
