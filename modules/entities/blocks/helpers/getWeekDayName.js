import range from 'lodash/range.js';

const weekdayMap = {
  su: 0,
  mo: 1,
  tu: 2,
  we: 3,
  th: 4,
  fr: 5,
  sa: 6,
};

/**
 * Get the name of the week day in the given language
 * @param {keyof typeof weekdayMap} weekday - The week day
 * @param {String} language - The language
 * @returns {String}
 */
export function getWeekDayName(weekday, language = 'en') {
  const weekdays = range(4, 11, 1).map((day) =>
    new Date(1970, 0, day).toLocaleString(language, {
      weekday: 'long',
    })
  );

  return weekdays[weekdayMap[weekday]];
}
