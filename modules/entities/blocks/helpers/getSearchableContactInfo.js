import { convertRichTextToPlainText } from '#utils/richText.js';
import { getSearchablePersonName } from '#utils/search/getSearchablePersonName.js';
import { stringArrayToString } from '#utils/strings.js';

/**
 * Get searchable contact information
 * @param {Object} options
 * @param {Object} options.Entity - The entity object
 * @param {Array} options.entityServices - The entity services array
 * @returns {String}
 */
export function getSearchableContactInfo({ Entity, entityServices = [] }) {
  const allContactPersons = entityServices.reduce((acc, { contactPersons }) => {
    if (!contactPersons) {
      return acc;
    }

    const serviceContactPersons = contactPersons.reduce(
      (contactPersonsAcc, contactPerson) => {
        // Check if the contact person is already in the "allContactPersons" list
        if (acc.find(({ id: accId }) => accId === contactPerson.id)) {
          return contactPersonsAcc;
        }
        return [...contactPersonsAcc, contactPerson];
      },
      []
    );

    return [...acc, ...serviceContactPersons];
  }, Entity?.contactPersons ?? []);

  return allContactPersons
    .map((person) => {
      const { body, email, phone, mobile } = person || {};

      const name = getSearchablePersonName(person);
      const description = convertRichTextToPlainText(body);

      return stringArrayToString(
        [name, description, email, phone, mobile],
        '\n'
      );
    })
    .join('\n\n');
}
