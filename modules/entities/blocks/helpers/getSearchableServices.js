import { stringArrayToString } from '#utils/strings.js';
import { getWeekDayName } from './getWeekDayName.js';

/**
 * Get searchable services
 * @param {Object} options
 * @param {Array} options.entityServices - The entity services array
 * @param {String} options.language - The language associated with the block
 * @returns {String}
 */
export function getSearchableServices({
  entityServices = [],
  language = 'en',
}) {
  return entityServices
    .map(({ title, dateString, startsAt, weekday }) =>
      stringArrayToString([
        title,
        dateString,
        startsAt,
        getWeekDayName(weekday, language),
      ])
    )
    .join('\n\n');
}
