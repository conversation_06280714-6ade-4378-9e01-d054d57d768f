import { stringArrayToString } from '#utils/strings.js';
import { getWeekDayName } from './helpers/getWeekDayName.js';

/**
 * ChurchServices block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export function ChurchServices() {
  return {
    name: 'ChurchServices',
    getSearchableContent,
  };
}

/**
 * Get searchable content for ChurchServices
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node, language }) {
  const { churchServices } = node?.props ?? {};

  if (!churchServices?.items) {
    return '';
  }

  return churchServices.items.map(({ title, startsAt, weekday, website }) =>
    stringArrayToString([
      title,
      startsAt,
      getWeekDayName(weekday, language),
      website,
    ])
  );
}
