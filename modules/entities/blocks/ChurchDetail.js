import { getSearchableAddress } from '#utils/search/getSearchableAddress.js';
import { stringArrayToString } from '#utils/strings.js';

import { getSearchableContactInfo } from './helpers/getSearchableContactInfo.js';
import { getSearchableServices } from './helpers/getSearchableServices.js';

/**
 * ChurchDetail block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export function ChurchDetail() {
  return {
    name: 'ChurchDetail',
    getSearchableContent,
  };
}

/**
 * Get searchable content for ChurchDetail
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ language, node }) {
  const {
    Entity,
    entityServices,
    showContactInfo,
    showDescription,
    showServices,
  } = node?.props ?? {};

  const contentItems = [];

  // Add the church entity content
  const { name, description, siteUrl, address } = Entity || {};

  contentItems.push(
    stringArrayToString(
      [
        name,
        showDescription ? description : null,
        siteUrl,
        getSearchableAddress(address),
      ],
      '\n\n'
    )
  );

  if (showContactInfo) {
    const contactInfo = getSearchableContactInfo({
      Entity,
      entityServices: entityServices.items,
    });
    contentItems.push(contactInfo);
  }

  if (showServices) {
    const services = getSearchableServices({
      entityServices: entityServices.items,
      language,
    });
    contentItems.push(services);
  }

  return contentItems.join('\n\n');
}
