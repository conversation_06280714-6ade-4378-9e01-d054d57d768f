import express from 'express';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { validate } from '#utils/validationMiddleware.js';

import entityController from './controllers/entityController.js';
import scriptsController from './controllers/scriptsController.js';
import restrictAccessToEntity from './helpers/restrictAccessToEntity.js';
import entityTypesListSchema from './validations/entityTypesSchema.js';
import { validateEntityContactPersons } from './validations/entityContactPersonsSchema.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Any user, logged-in or not, should be able to read the current entity without permissions
router.get('/current', entityController.currentEntity);

// Require user for all routes after this middleware
router.use(protect);

router.patch(
  '/current',
  restrictAccessToEntity({ permission: 'update', isCurrentEntity: true }),
  logRequest({ module: 'entities', action: 'UPDATE_CURRENT_ENTITY' }),
  entityController.setEntityId,
  entityController.updateEntity
);

router
  .route('/')
  .get(entityController.getEntities)
  .post(
    restrictAccessToEntity({ permission: 'create', subentities: true }),
    logRequest({ module: 'entities', action: 'CREATE_ENTITY' }),
    entityController.createEntity
  );

// NOTE: This types/* and public/* routes need to be before the /:id route to be matched first.
router
  .route('/types')
  .get(
    validate(entityTypesListSchema, 'query'),
    entityController.getEntityTypes
  );
router.route('/types/:type').get(entityController.getEntityType);

// This route exposes public entity data
router.get('/public/:id', entityController.getPublicEntity);

router
  .route('/:id')
  .get(
    restrictAccessToEntity({ permission: 'read', paramId: 'id' }),
    entityController.getEntity
  )
  .patch(
    restrictAccessToEntity({ permission: 'update', paramId: 'id' }),
    logRequest({ module: 'entities', action: 'UPDATE_ENTITY' }),
    entityController.updateEntity
  )
  .delete(
    restrictAccessToEntity({ permission: 'delete', paramId: 'id' }),
    logRequest({ module: 'entities', action: 'DELETE_ENTITY' }),
    entityController.deleteEntity
  );

router
  .route('/:id/contactPersons')
  .post(
    restrictAccessToEntity({ permission: 'update', paramId: 'id' }),
    validateEntityContactPersons,
    entityController.addContactPerson
  );

router
  .route('/:id/languages')
  .get(
    restrictAccessToEntity({ permission: 'read', paramId: 'id' }),
    entityController.getEntityLanguages
  );

router
  .route('/:id/restore')
  .patch(
    restrictAccessToEntity({ permission: 'delete', paramId: 'id' }),
    logRequest({ module: 'entities', action: 'RESTORE_ENTITY' }),
    entityController.restoreEntity
  );

router
  .route('/:id/disable')
  .patch(
    restrictAccessToEntity({ permission: 'update', paramId: 'id' }),
    logRequest({ module: 'entities', action: 'DISABLE_ENTITY' }),
    entityController.disableEntity
  );

router
  .route('/:id/enable')
  .patch(
    restrictAccessToEntity({ permission: 'update', paramId: 'id' }),
    logRequest({ module: 'entities', action: 'ENABLE_ENTITY' }),
    entityController.enableEntity
  );

router
  .route('/:id/subentities')
  .get(
    restrictAccessToEntity({ permission: 'read', subentities: true }),
    entityController.getSubentities
  )
  .post(
    restrictAccessToEntity({ permission: 'create', subentities: true }),
    entityController.createSubentity
  );

router
  .route('/:id/backendURL')
  .get(
    restrictAccessToEntity({ permission: 'read', paramId: 'id' }),
    entityController.getBackendUrl
  );

//#region Scripts
// -----------------------------------------------
router.route('/:entityId/scripts/adventisten-de-import').post(
  restrictTo(), // admins only
  logRequest({ module: 'entities', action: 'ADVENTISTEN_DE_IMPORT' }),
  scriptsController.importAdventistenDe
);
router.route('/:entityId/scripts/courses-import').post(
  restrictTo(), // admins only
  logRequest({ module: 'courses', action: 'COURSES_IMPORT' }),
  scriptsController.importCourses
);
//#endregion

export default router;
