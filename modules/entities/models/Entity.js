import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';
import { uniquifySlug } from '#utils/strings.js';

/**
 * @typedef {Object} EntityCurrency A currency associated with an entity.
 * @property {String} code Currency code (e.g., "EUR", "USD").
 * @property {String} label Label for the currency.
 * @property {Boolean} isDefault Indicates if this is the default currency for the entity.
 */
const CurrencySchema = new mongoose.Schema({
  code: { type: String, required: true },
  label: { type: String },
  isDefault: { type: Boolean },
});

/**
 * @typedef {Object} EntityPaymentMethod
 * @property {String} name Name of the payment method.
 * @property {String} type Type of the payment method.
 * @property {String} enabled Indicates if the payment method is enabled.
 * @property {Boolean} primary Indicates if this is the primary payment method.
 * @property {mongoose.SchemaTypes.Mixed} config Configuration for the payment method.
 */

const PaymentMethodSchema = new mongoose.Schema({
  name: { type: String },
  type: { type: String, required: true },
  enabled: String,
  primary: Boolean,
  config: mongoose.SchemaTypes.Mixed,
});

/**
 * @typedef {Object} EntityConfigAIProvider
 * @property {String} apiKey API key for the AI provider.
 * @property {Boolean} enabled Indicates if the AI provider is enabled.
 */

/**
 * @typedef {Object} EntityConfigAI
 * @property {Boolean} enabled Indicates if AI features are enabled for the entity.
 * @property {Object} providers Configuration for AI providers.
 * @property {EntityConfigAIProvider} openai Configuration for OpenAI provider.
 */

/**
 * @typedef {Object} EntityConfigAutomaticTranslations
 * @property {Boolean} enabled Indicates if automatic translations are enabled.
 * @property {String} provider Provider for automatic translations.
 * @property {String} apiKey API key for the translation provider.
 * @property {String} model Model to use for translations.
 * @property {Boolean} richTextByNode Indicates if rich text should be processed by node.
 */

/**
 * @typedef {Object} EntityConfigBackendDomain
 * @property {String} domain Domain for the backend.
 * @property {Boolean} isPrimary Indicates if this is the primary domain for the entity.
 */

/**
 * @typedef {Object} EntityConfigAutomatedSite
 * @property {Boolean} enabled Indicates if the automated site feature is enabled.
 * @property {String} domain Domain for the automated site.
 * @property {String} site Site identifier for the automated site.
 */

/**
 * @typedef {Object} EntityConfigEmail
 * @property {String} from Email address to use as the sender.
 * @property {String} host SMTP host for sending emails.
 * @property {String} user Username for SMTP authentication.
 * @property {String} password Password for SMTP authentication.
 * @property {Number} port Port for SMTP connection.
 * @property {Object} appearance Appearance settings for emails.
 * @property {String} appearance.footerText Footer text for emails.
 * @property {mongoose.SchemaTypes.Mixed} appearance.logo Logo for emails.
 * @property {Object} appearance.colors Color settings for emails.
 * @property {String} appearance.colors.primary Primary color for emails.
 * @property {String} appearance.colors.secondary Secondary color for emails.
 * @property {String} appearance.colors.neutral Neutral color for emails.
 */

/**
 * @typedef {Object} EntityConfig
 * @property {EntityConfigAI} ai AI configuration for the entity.
 * @property {EntityConfigAutomaticTranslations} automaticTranslations Configuration for automatic translations.
 * @property {String} defaultDesign Default design for the entity.
 * @property {String} defaultRegion Default region for the entity.
 * @property {EntityCurrency[]} currencies List of currencies associated with the entity.
 * @property {EntityConfigBackendDomain[]} backendDomains List of backend domains for the entity.
 * @property {String} automatedDomain Automated domain for the entity.
 * @property {EntityConfigAutomatedSite} automatedSite Configuration for automated site.
 * @property {EntityConfigEmail} email Email configuration for the entity.
 */

/**
 * @typedef {Object} EntityAmenities
 * @property {Object} accessibility Accessibility features of the entity.
 * @property {Boolean} accessibility.stepFreeEntrance Indicates if the entrance is step-free.
 * @property {Boolean} accessibility.accessibleParkingSpots Indicates if there are accessible parking spots.
 * @property {Boolean} accessibility.signLanguageInterpreter Indicates if a sign language interpreter is available.
 * @property {Object} services Services provided by the entity.
 * @property {Boolean} services.parkingSpots Indicates if parking spots are available.
 * @property {Boolean} services.wifi Indicates if Wi-Fi is available.
 * @property {Boolean} services.nursingRoom Indicates if a nursing room is available.
 * @property {Boolean} services.projector Indicates if a projector is available.
 * @property {Boolean} services.library Indicates if a library is available.
 * @property {String[]} services.translations List of languages for which translations are available.
 */

/**
 * @typedef {Object} EntityLocation
 * @property {String} type Type of the location (e.g., "Point").
 * @property {String} placeName Name of the place.
 * @property {Number[]} boundingBox Bounding box coordinates for the location.
 * @property {Number[]} coordinates Coordinates of the location in [longitude, latitude] format.
 */

/**
 * @typedef {Object} Entity
 * @property {String} slug Unique slug for the entity.
 * @property {mongoose.Types.ObjectId} parent Parent entity ID.
 * @property {mongoose.Types.ObjectId[]} ancestors List of ancestor entity IDs.
 * @property {String} name Local name of the entity.
 * @property {String} officialName Official name of the entity, usually in English.
 * @property {String} shortName Short name of the entity.
 * @property {String} code Code of the entity, usually imported from OrgMast data.
 * @property {String} description Description of the entity.
 * @property {String} type Type of the entity.
 * @property {String[]} additionalTypes Additional types of the entity.
 * @property {mongoose.SchemaTypes.Mixed} logo Logo of the entity.
 * @property {String} language Main language of the entity.
 * @property {String[]} additionalLanguages Additional languages of the entity.
 * @property {mongoose.SchemaTypes.Mixed} image Image of the entity.
 * @property {Object} address Address of the entity.
 * @property {Object} mailAddress Mailing address of the entity.
 * @property {Number} membershipCount Count of memberships associated with the entity.
 * @property {EntityAmenities} amenities Amenities provided by the entity.
 * @property {String} email Email address of the entity.
 * @property {String} emailName Name to use in the email address.
 * @property {String} phone Phone number of the entity.
 * @property {String} fax Fax number of the entity.
 * @property {EntityLocation} location Location information of the entity.
 * @property {String} siteURL URL of the entity's website.
 * @property {EntityPaymentMethod[]} paymentMethods List of payment methods associated with the entity.
 * @property {EntityConfig} config Configuration settings for the entity.
 * @property {mongoose.Types.ObjectId[]} services List of services provided by the entity.
 * @property {mongoose.Types.ObjectId[]} contactPersons List of contact persons associated with the entity.
 * @property {mongoose.Types.ObjectId} network Network ID associated with the entity.
 * @property {mongoose.SchemaTypes.Mixed[]} importIDs List of import IDs associated with the entity.
 * @property {Boolean} excludeFromFinder Indicates if the entity should be excluded from the finder.
 * @property {Boolean} deleted Indicates if the entity is deleted.
 * @property {Date} createdAt Creation date of the entity.
 * @property {Date} updatedAt Last update date of the entity.
 * @property {mongoose.Types.ObjectId} _id Unique identifier for the entity.
 */

const entitySchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  parent: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
  },
  ancestors: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'Entity',
    },
  ],
  // Local name of the entity (one that can be changed from the backend).
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  // Official entity name, in English (usually imported from OrgMast data).
  // It should be modified obly in OrgMast, and imported here, and never changed by our backend.
  officialName: {
    type: String,
    trim: true,
  },
  // Short name of the entity.
  shortName: {
    type: String,
    trim: true,
    maxlength: 20,
  },
  // Code of the entity (usually imported from OrgMast data).
  code: {
    type: String,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  type: {
    type: String,
    trim: true,
  },
  additionalTypes: {
    type: [String],
  },
  logo: {
    type: mongoose.SchemaTypes.Mixed,
  },
  /**
   * The main language of the entity.
   */
  language: {
    type: String,
    trim: true,
  },
  /**
   * Additional languages of the entity.
   * This should not contain the main language.
   */
  additionalLanguages: {
    type: [String],
    default: [],
  },
  image: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  address: {
    street: String,
    additionalAddress: String,
    zip: String,
    city: String,
    state: String,
    country: String,
  },
  mailAddress: {
    street: String,
    additionalAddress: String,
    zip: String,
    city: String,
    state: String,
    country: String,
  },
  membershipCount: {
    type: Number,
  },
  amenities: {
    accessibility: {
      stepFreeEntrance: Boolean,
      accessibleParkingSpots: Boolean,
      signLanguageInterpreter: Boolean,
    },
    services: {
      parkingSpots: Boolean,
      wifi: Boolean,
      nursingRoom: Boolean,
      projector: Boolean,
      library: Boolean,
      translations: [String],
    },
  },
  email: {
    type: String,
    trim: true,
  },
  emailName: {
    type: String,
    trim: true,
  },
  phone: {
    type: String,
    trim: true,
  },
  fax: {
    type: String,
    trim: true,
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
    },
    placeName: {
      type: String,
    },
    boundingBox: {
      type: [Number],
    },
    coordinates: {
      type: [Number],
    },
  },
  siteURL: {
    type: String,
    trim: true,
  },
  paymentMethods: [PaymentMethodSchema],
  config: {
    ai: {
      enabled: Boolean,
      providers: {
        openai: {
          apiKey: String,
          enabled: Boolean,
        },
      },
    },
    automaticTranslations: {
      enabled: Boolean,
      provider: String,
      apiKey: String,
      model: String,
      richTextByNode: Boolean,
    },
    defaultDesign: String,
    defaultRegion: String,
    currencies: [CurrencySchema],
    backendDomains: [
      {
        domain: { type: String, trim: true },
        isPrimary: {
          type: Boolean,
          default: false,
        },
      },
    ],
    automatedDomain: {
      type: String,
    },
    automatedSite: {
      enabled: Boolean,
      domain: {
        type: String,
        trim: true,
      },
      site: String,
    },
    email: {
      from: String,
      host: String,
      user: String,
      password: String,
      port: Number,
      appearance: {
        footerText: String,
        logo: mongoose.SchemaTypes.Mixed,
        colors: {
          primary: String,
          secondary: String,
          neutral: String,
        },
      },
    },
  },
  services: {
    type: [mongoose.Types.ObjectId],
    ref: 'EntityService',
    default: [],
  },
  contactPersons: {
    type: [mongoose.Types.ObjectId],
    ref: 'PersonRole',
    default: [],
  },
  network: {
    type: mongoose.Types.ObjectId,
    ref: 'Network',
  },
  importIDs: {
    type: [mongoose.SchemaTypes.Mixed],
  },
  excludeFromFinder: {
    type: Boolean,
    default: false,
  },
});

entitySchema.index(
  { name: 1, type: 1, parent: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);
entitySchema.index({ location: '2dsphere' });
entitySchema.index({ type: 1 });
entitySchema.index({ parent: 1 });
entitySchema.index({ network: 1 });

entitySchema.statics.getAvailableSlug = async function (slug, entityId = null) {
  const query = { slug, deleted: false };

  if (entityId) {
    query._id = { $ne: entityId };
  }

  const existingEntity = await this.findOne(query);

  return existingEntity ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Entity', entitySchema);
