import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const serviceSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  type: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  language: {
    type: String,
  },
  dateString: {
    type: String,
    trim: true,
  },
  startsAt: {
    type: String,
    trim: true,
  },
  endsAt: {
    type: String,
    trim: true,
  },
  weekday: {
    type: String,
    trim: true,
  },
  // frequency: { // daily, weekly, monthly, yearly
  //   type: String,
  //   trim: true,
  // },
  // repetition: { // 1st, 2nd, 3rd, 4th, ...
  //   type: String,
  //   trim: true,
  // },
  location: {
    type: String,
    trim: true,
  },
  image: {
    type: mongoose.SchemaTypes.Mixed,
    default: null,
  },
  contactPersons: {
    type: [mongoose.Types.ObjectId],
    ref: 'PersonRole',
    default: [],
  },
  team: {
    type: [mongoose.Types.ObjectId],
    ref: 'PersonRole',
    default: [],
  },
  categories: {
    type: [mongoose.Types.ObjectId],
    ref: 'Category',
    default: [],
  },
  website: {
    type: String,
  },
  entity: {
    type: mongoose.Types.ObjectId,
    required: true,
    ref: 'Entity',
  },
  importIDs: {
    type: [mongoose.SchemaTypes.Mixed],
    default: [],
  },
});

serviceSchema.index(
  { title: 1, entity: 1, weekday: 1, startsAt: 1, type: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);
serviceSchema.index({ slug: 1 });
serviceSchema.index({ type: 1 });
serviceSchema.index({ title: 1 });
serviceSchema.index({ language: 1 });
serviceSchema.index({ contactPersons: 1 });
serviceSchema.index({ team: 1 });
serviceSchema.index({ categories: 1 });
serviceSchema.index({ entity: 1 });

serviceSchema.statics.getAvailableSlug = async function (
  slug,
  entity,
  serviceId = null
) {
  const query = { slug, entity, deleted: false };

  if (serviceId) {
    query._id = { $ne: serviceId };
  }

  const existingService = await this.findOne(query);

  return existingService ? uniquifySlug(slug) : slug;
};

export default mongoose.model('EntityService', serviceSchema);
