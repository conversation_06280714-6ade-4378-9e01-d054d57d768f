import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const networkSchema = SchemaFactory({
  slug: {
    type: String,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  automatedDomain: {
    type: String,
    trim: true,
  },
  // NOTE: The reason why we have the zone identifier here is because we want to change this quickly if a customer decides they want to use another zone. Zone identifier also is used by the automated site form to indicate to the endpoint that DNS should be allowed, if the environment variables are also set.
  zoneIdentifier: {
    type: String,
    trim: true,
  },
  cnameContent: {
    type: String,
    trim: true,
  },
  senderEmail: {
    type: String,
    trim: true,
  },
  defaultDesign: {
    type: String,
  },
  defaultRegion: {
    type: String,
  },
  backendName: {
    type: String,
    trim: true,
  },
  favicon: {
    type: mongoose.SchemaTypes.Mixed,
  },
  logoFull: {
    type: mongoose.SchemaTypes.Mixed,
  },
  logoIcon: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

networkSchema.index(
  { title: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);
networkSchema.index({ slug: 1 });

networkSchema.statics.getAvailableSlug = async function (
  slug,
  networkId = null
) {
  const query = { slug, deleted: false };

  if (networkId) {
    query._id = { $ne: networkId };
  }

  const existingNetwork = await this.findOne(query);

  return existingNetwork ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Network', networkSchema);
