import Entity from '../models/Entity.js';

/**
 * Get an entity ancestors list, startsAt the parentId and goes up to the root entity.
 * @param {String} parentId The ID of the parent entity to start from.
 * @param {Array} list The list to accumulate ancestor IDs. Defaults to an empty array.
 * @returns {Promise<Array>}
 */
export default async function getAncestorsList(parentId, list = []) {
  if (!parentId) return list;

  /**
   * @type {import('../models/Entity.js').Entity}
   */
  const parentEntity = await Entity.findById(parentId);

  if (parentEntity) {
    // Add parent to list
    list.push(parentEntity.id);

    // Get recursively parent of parent
    if (parentEntity.parent) {
      await getAncestorsList(parentEntity.parent, list);
    }
  }

  return list;
}
