/**
 * Returns the permissions of the user for the current entity and its subentities.
 *
 * @param {Object} user - The user object
 * @returns `Object` The permissions object
 */
export default function getEntitiesPermissions(user) {
  const { groups = [] } = user || {};

  const withSubentities = [];

  const permissions = groups.reduce((acc, group) => {
    const { entity = {}, subentities = {} } = group.permissions;

    // Get the entity id for group records ids (when group has no entity specified use the group entity)
    const entityId = entity?.records?.[0]?.toString() || group.entity;

    // if subentities's type is defined, set it. Otherwise, use 'all'
    const subentitiesType = subentities.type || 'all';

    // We build an object like the acc outputs, with the type as key. This is used to compare with prevSubentities
    const subentitiesPermissions = { [subentitiesType]: subentities };

    // check if group can read subentities
    const canReadSubentities = subentities.read;

    // if the group can read subentities, add the entity to the withSubentities array
    if (canReadSubentities) {
      withSubentities.push({ entityId, type: subentitiesType });
    }

    // if the entity is already in the accumulator, merge the permissions
    if (acc[entityId]) {
      // merge permissions from previous groups with the current one
      acc[entityId].update = acc[entityId].update || entity.update;

      // merge subentities permissions from previous groups with the current one
      const prevSubentities = acc[entityId].subentities;

      // subentitiesPermissions and prevSubentities may look like this:
      // subentitiesPermissions: { all: { read: true, create: true, update: true, delete: true } }

      acc[entityId].subentities = {
        ...acc[entityId].subentities,
        [subentitiesType]: {
          read:
            prevSubentities[subentitiesType]?.read ||
            subentitiesPermissions[subentitiesType]?.read ||
            false,
          create:
            prevSubentities[subentitiesType]?.create ||
            subentitiesPermissions[subentitiesType]?.create ||
            false,
          update:
            prevSubentities[subentitiesType]?.update ||
            subentitiesPermissions[subentitiesType]?.update ||
            false,
          delete:
            prevSubentities[subentitiesType]?.delete ||
            subentitiesPermissions[subentitiesType]?.delete ||
            false,
        },
      };
      // if the entity is not in the accumulator, add it (if it is not undefined)
    } else if (entityId !== undefined) {
      // This line is there to prevent a bug where the entity id is undefined if it was deleted
      acc[entityId] = {
        read: true, // always allow to read the entity (both for a selected or the current entity)
        update: entity.update || false, // allowed to update the entity if the group has the update permission

        // set subentities permissions
        subentities: {
          // Storinf prevents overriding permissions for different subentities types.
          [subentitiesType]: {
            read: subentitiesPermissions[subentitiesType]?.read || false,
            create: subentitiesPermissions[subentitiesType]?.create || false,
            update: subentitiesPermissions[subentitiesType]?.update || false,
            delete: subentitiesPermissions[subentitiesType]?.delete || false,
          },
        },
      };
    }

    return acc;
  }, {});

  // If the user's own entity is not in the permissions object, add it
  const userEntityId = user.entity?._id.toString();
  if (userEntityId && !permissions[userEntityId]) {
    permissions[userEntityId] = {
      // always allow to read the current entity
      read: true,
      // but can't update it
      update: false,
      // nor its subentities
      subentities: {
        all: {
          read: false,
          create: false,
          update: false,
          delete: false,
        },
      },
    };
  }

  // get the ids of the entities that the user has direct access to
  const entitiesIds = Object.keys(permissions);

  // add the current entity id to the entitiesIds array
  if (user) {
    entitiesIds.push(user.entity?._id?.toString());
  }

  return {
    permissions,
    entitiesIds,
    withSubentities,
  };
}
