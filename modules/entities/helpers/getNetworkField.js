import _ from 'lodash';
import Network from '../models/Network.js';
import Entity from '../models/Entity.js';

export async function getNetworkField(entity, fieldName) {
  if (!entity) return null;

  const { network: networkId } = entity.network
    ? entity
    : await Entity.findById(entity._id, 'network');

  const network = await Network.findById(networkId, fieldName);
  const value = _.get(network, fieldName);
  if (!_.isEmpty(value)) return value;
}
