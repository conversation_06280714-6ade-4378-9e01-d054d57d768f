import { addDomain, removeDomain } from '#utils/domains/index.js';

/**
 * Update the backend domains
 * @param {Object} config
 * @param {Object} config.entity
 * @param {Object} config.prevEntity
 * @param {Object} config.user
 * @returns {Promise}
 */

const { BACKEND_APP_NAME } = process.env;

export async function updateBackendDomains({
  newDomains,
  prevDomains = [],
} = {}) {
  for (const domain of newDomains) {
    // If a domain was added and it's not localhost.
    if (!prevDomains.includes(domain) && !domain?.startsWith('localhost')) {
      await addDomain({
        providerName: 'digitalOcean',
        domain,
        appName: BACKEND_APP_NAME,
      });
    }
  }

  // If a domain was removed and it's not localhost.
  for (const domain of prevDomains) {
    if (!newDomains.includes(domain) && !domain?.startsWith('localhost')) {
      await removeDomain({
        providerName: 'digitalOcean', // For now this is only available for DigitalOcean
        domain,
        appName: BACKEND_APP_NAME,
      });
    }
  }
}
