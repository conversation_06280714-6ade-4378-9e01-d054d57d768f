import Joi from 'joi';

import { personRoleSchema } from '#modules/persons/validation/personRoleValidation.js';
import { validate } from '#utils/validationMiddleware.js';

export const validateEntityContactPersons = async (req, res, next) => {
  const baseSchema = {
    existingRole: Joi.string().required().valid('new', 'existing'),
    entityId: Joi.string().required(),
  };

  if (req.body.existingRole === 'new') {
    const schema = Joi.object().keys({
      ...baseSchema,
      ...personRoleSchema,
    });
    return validate(schema)(req, res, next);
  }

  const schema = Joi.object().keys({
    ...baseSchema,
    personRoleId: Joi.string().required(),
  });

  return validate(schema)(req, res, next);
};
