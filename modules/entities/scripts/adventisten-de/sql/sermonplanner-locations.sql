SELECT locations.uid AS locations_uid, locations.title AS locations_title,
administrations.uid AS administrations_uid, administrations.title AS administrations_title
FROM tx_amslocations_locations locations
LEFT JOIN tx_amslocations_locations_fk_administration_mm mm
	ON mm.uid_local = locations.uid
LEFT JOIN tx_amslocations_administrations administrations
	ON administrations.uid = mm.uid_foreign
WHERE locations.deleted <> 1
ORDER BY locations.title