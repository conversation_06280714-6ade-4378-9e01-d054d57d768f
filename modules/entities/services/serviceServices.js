import { isValidObjectId } from '#utils/api/mongoose/id.js';
import { errors } from '#utils/appError.js';
import { slugify } from '#utils/strings.js';

import EntityService from '../models/EntityService.js';
import entityServices from './entityServices.js';

async function getServiceById(serviceId) {
  const validId = isValidObjectId(serviceId);
  if (!validId) {
    throw errors.not_found();
  }

  const service = await EntityService.findById(serviceId).populate([
    {
      path: 'contactPersons',
      select: 'title',
      match: { deleted: false },
      populate: [
        {
          path: 'person',
        },
      ],
    },
  ]);

  if (!service) {
    throw errors.not_found('EntityService', serviceId);
  }

  return service;
}

async function getServiceByImportId(type, importId) {
  return await EntityService.findOne({
    'deleted': false,
    'importIDs.type': type,
    'importIDs.recordID': importId,
  });
}

async function getImportedServiceIds({ entityId, type }) {
  const services = await EntityService.find({
    'deleted': false,
    'entity': entityId,
    'importIDs.type': type,
  })
    .select('importIDs.recordID')
    .lean();

  return services;
}

async function createService(entityId, serviceData) {
  const entity = await entityServices.getEntityById(entityId);

  // Ensure service has a valid slug within its siblings (or create one from its title)
  const slug = await EntityService.getAvailableSlug(
    slugify(serviceData.slug || serviceData.title),
    entity
  );

  const service = await EntityService.create({
    ...serviceData,
    slug,
    language: serviceData.language ? serviceData.language : entity.language,
    entity,
  });

  // Add service to entity
  entity.services = [...entity.services, service.id];
  await entity.save();

  return service;
}

async function updateService(serviceId, serviceData) {
  const service = await getServiceById(serviceId);

  // Ensures new slug doesn't exists
  const slug = serviceData.slug
    ? await EntityService.getAvailableSlug(
        slugify(serviceData.slug),
        service.entity,
        service._id
      )
    : service.slug;

  // Update service's data
  const updatedService = await EntityService.findByIdAndUpdate(
    service._id,
    {
      ...serviceData,
      slug,
      entity: service.entity, // prevents moving a service to different entity
    },
    {
      new: true,
      runValidators: true,
    }
  );

  return updatedService;
}

async function deleteServicesByIds({ serviceIds, entityId }) {
  const entity = await entityServices.getEntityById(entityId);

  const { nModified } = await EntityService.updateMany(
    { _id: { $in: serviceIds } },
    { deleted: true }
  );

  const plainServiceIds = serviceIds.map((id) => id.toString());
  // Add service to entity
  entity.services = entity.services.filter(
    (service) => !plainServiceIds.includes(service.toString())
  );
  await entity.save();

  return nModified;
}

export default {
  getServiceById,
  getServiceByImportId,
  getImportedServiceIds,
  createService,
  updateService,
  deleteServicesByIds,
};
