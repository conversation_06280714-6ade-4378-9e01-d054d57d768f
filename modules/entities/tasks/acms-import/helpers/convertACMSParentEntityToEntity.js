export function convertACMSParentEntityToEntity({
  networkId,
  targetEntityId,
  acmsParentEntity,
}) {
  return {
    parent: targetEntityId,
    network: networkId,
    name: acmsParentEntity.Name,
    code: acmsParentEntity.Abbreviation,
    type: 'conference', // entity.ChurchType === 0?
    importIDs: {
      type: 'acms',
      recordID: acmsParentEntity.Id,
      code: acmsParentEntity.Code,
    },
  };
}
