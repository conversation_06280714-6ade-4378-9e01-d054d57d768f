import { saveRemoteImage } from '#modules/images/controllers/imageController.js';
import Logger from '#utils/logger.js';

async function importImage(targetEntity, acmsPerson, existingPerson) {
  if (!acmsPerson?.MainPicture?.Url) {
    Logger.info(
      `No image found for ${acmsPerson?.Name} ${acmsPerson?.LastName}`
    );
    return null;
  }

  try {
    if (
      existingPerson?.avatar?.originalFilename === acmsPerson?.MainPicture?.Id
    ) {
      Logger.info(
        `Skipping existing image import for ${acmsPerson?.Name} ${acmsPerson?.LastName}`
      );
      return existingPerson?.avatar;
    }

    return await saveRemoteImage(
      acmsPerson.MainPicture.Url,
      targetEntity,
      () => acmsPerson?.MainPicture?.Id
    );
  } catch (error) {
    Logger.error(
      `Error when trying to save image ${acmsPerson?.MainPicture?.Id}`,
      error
    );
  }
}

export async function convertACMSPersonToPerson(
  targetEntity,
  acmsPerson,
  existingPerson
) {
  const image = await importImage(targetEntity, acmsPerson, existingPerson);

  const person = {
    firstName: acmsPerson.Name,
    lastName: acmsPerson.LastName,
    email: acmsPerson.Email,
    phone: acmsPerson.HomePhone,
    mobile: acmsPerson.MobilePhone,
    importIDs: {
      type: 'acms',
      recordID: acmsPerson.Id,
    },
    avatar: image,
    image,
  };

  return person;
}
