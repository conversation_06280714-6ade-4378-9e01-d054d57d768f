import { DateTime } from 'luxon';

function getTimeStringFromACMSServiceTime(acmsServiceTime) {
  const time = acmsServiceTime.split('T')[1].substring(0, 5);
  return time;
}

function parseServiceType(acmsServiceName) {
  if (!acmsServiceName) {
    return null;
  }

  if (acmsServiceName.search(/divine service/i) > -1) {
    return 'divineService';
  }

  if (acmsServiceName.search(/sabbath school/i) > -1) {
    return 'sabbathSchool';
  }

  if (acmsServiceName.search(/youth meeting/i) > -1) {
    return 'youthGroup';
  }

  if (acmsServiceName.search(/pathfinder/i) > -1) {
    return 'pathfinderGroup';
  }

  return null;
}

export function convertACMSServiceToEntityService(
  acmsService,
  language = 'en'
) {
  const weekdays = {
    0: 'su',
    1: 'mo',
    2: 'tu',
    3: 'we',
    4: 'th',
    5: 'fr',
    6: 'sa',
  };

  // TODO: What is an ACMS service ScheduleType?

  // Example of a service with a WeekDay
  // {
  //     "Id": "3f9b159a-9171-4de4-a324-cffc3338e0d9",
  //     "Name": "Sabbath School",
  //     "Time": "2018-01-08T10:00:00",
  //     "WeekDay": 6,
  //     "ScheduleType": 0
  // }

  const service = {
    title: acmsService.Name,
    type: parseServiceType(acmsService.Name),
    weekday: weekdays[acmsService.WeekDay] ?? null,
    startsAt: getTimeStringFromACMSServiceTime(acmsService.Time),
    dateString: acmsService.Notes ? acmsService.Notes : null,
    importIDs: [
      {
        type: 'acms',
        recordID: acmsService.Id,
      },
    ],
  };

  // Example of a service with a StartDate and EndDate
  //   {
  //     "Id": "d1010e88-eb97-40e2-823b-b134013a3ada",
  //     "Name": "Adventurer and Pathfinder Club ",
  //     "Time": "2024-03-15T14:00:00",
  //     "WeekDay": -1,
  //     "StartDate": "2024-04-20T00:00:00",
  //     "EndDate": "2024-04-20T00:00:00",
  //     "ScheduleType": 1
  // }
  if (acmsService.StartDate && acmsService.EndDate) {
    const startDateWeekDay = DateTime.fromISO(acmsService.StartDate).weekday;
    const endDateWeekDay = DateTime.fromISO(acmsService.EndDate).weekday;

    if (startDateWeekDay === endDateWeekDay) {
      service.weekday = weekdays[startDateWeekDay];
    }

    if (acmsService.StartDate === acmsService.EndDate) {
      service.startsAt = DateTime.fromISO(acmsService.StartDate).toLocaleString(
        DateTime.DATETIME_SHORT,
        { locale: language }
      );
    }

    if (acmsService.StartDate !== acmsService.EndDate) {
      service.startsAt = DateTime.fromISO(acmsService.StartDate).toLocaleString(
        DateTime.DATETIME_SHORT,
        { locale: language }
      );
      service.endsAt = DateTime.fromISO(acmsService.EndDate).toLocaleString(
        DateTime.DATETIME_SHORT,
        { locale: language }
      );
    }
  }

  return service;
}
