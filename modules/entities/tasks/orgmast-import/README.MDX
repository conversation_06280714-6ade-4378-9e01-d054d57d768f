# OrgMast API Importer documentation

This is a task is responsible for importing data from the OrgMast API into AWE's entities records.

## Configuration

The task accepts the following environment variables:

- `ORGMAST_API_URL`: The URL of the OrgMast API. The default value is `https://orgmast.adventist.org/OrgMastAPI/api`.

## Usage

The task can be run using by adding the task in Admin area of AWE and running it.

If necessary it can be configured to run on a schedule.

## OrgMast API Documentation

## Usefull links

- OrgMast Swagger UI: https://orgmast.adventist.org/OrgMastAPI/swagger/ui
- Yearbook page of an Entity: https://www.adventistyearbook.org/entity?EntityID=13730 (where `13730` is the Entity ID for Hope Media Europe)
