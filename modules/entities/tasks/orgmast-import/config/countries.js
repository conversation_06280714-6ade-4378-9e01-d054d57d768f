export default [
  {
    code: 'AD',
    name: 'Andorra',
    native: 'Andorra',
    phone: '376',
    languages: ['ca'],
  },
  {
    code: 'AE',
    name: 'United Arab Emirates',
    native: 'دولة الإمارات العربية المتحدة',
    phone: '971',
    languages: ['ar'],
  },
  {
    code: 'AF',
    name: 'Afghanistan',
    native: 'افغانستان',
    phone: '93',
    languages: ['ps', 'uz', 'tk'],
  },
  {
    code: 'AG',
    name: 'Antigua and Barbuda',
    native: 'Antigua and Barbuda',
    phone: '1268',
    languages: ['en'],
  },
  {
    code: 'AI',
    name: 'Anguilla',
    native: 'Anguilla',
    phone: '1264',
    languages: ['en'],
  },
  {
    code: 'AL',
    name: 'Albania',
    native: 'Shqipëria',
    phone: '355',
    languages: ['sq'],
  },
  {
    code: 'AM',
    name: 'Armenia',
    native: 'Հայաստան',
    phone: '374',
    languages: ['hy', 'ru'],
  },
  {
    code: 'AO',
    name: 'Angola',
    native: 'Angola',
    phone: '244',
    languages: ['pt'],
  },
  {
    code: 'AQ',
    name: 'Antarctica',
    native: 'Antarctica',
    phone: '672',
    languages: [],
  },
  {
    code: 'AR',
    name: 'Argentina',
    native: 'Argentina',
    phone: '54',
    languages: ['es', 'gn'],
  },
  {
    code: 'AS',
    name: 'American Samoa',
    native: 'American Samoa',
    phone: '1684',
    languages: ['en', 'sm'],
  },
  {
    code: 'AT',
    name: 'Austria',
    native: 'Österreich',
    phone: '43',
    languages: ['de'],
  },
  {
    code: 'AU',
    name: 'Australia',
    native: 'Australia',
    phone: '61',
    languages: ['en'],
  },
  {
    code: 'AW',
    name: 'Aruba',
    native: 'Aruba',
    phone: '297',
    languages: ['nl', 'pa'],
  },
  {
    code: 'AX',
    name: 'Åland',
    native: 'Åland',
    phone: '358',
    languages: ['sv'],
  },
  {
    code: 'AZ',
    name: 'Azerbaijan',
    native: 'Azərbaycan',
    phone: '994',
    languages: ['az'],
  },
  {
    code: 'BA',
    name: 'Bosnia and Herzegovina',
    native: 'Bosna i Hercegovina',
    phone: '387',
    languages: ['bs', 'hr', 'sr'],
  },
  {
    code: 'BB',
    name: 'Barbados',
    native: 'Barbados',
    phone: '1246',
    languages: ['en'],
  },
  {
    code: 'BD',
    name: 'Bangladesh',
    native: 'Bangladesh',
    phone: '880',
    languages: ['bn'],
  },
  {
    code: 'BE',
    name: 'Belgium',
    native: 'België',
    phone: '32',
    languages: ['nl', 'fr', 'de'],
  },
  {
    code: 'BF',
    name: 'Burkina Faso',
    native: 'Burkina Faso',
    phone: '226',
    languages: ['fr', 'ff'],
  },
  {
    code: 'BG',
    name: 'Bulgaria',
    native: 'България',
    phone: '359',
    languages: ['bg'],
  },
  {
    code: 'BH',
    name: 'Bahrain',
    native: 'البحرين',
    phone: '973',
    languages: ['ar'],
  },
  {
    code: 'BI',
    name: 'Burundi',
    native: 'Burundi',
    phone: '257',
    languages: ['fr', 'rn'],
  },
  {
    code: 'BJ',
    name: 'Benin',
    native: 'Bénin',
    phone: '229',
    languages: ['fr'],
  },
  {
    code: 'BL',
    name: 'Saint Barthélemy',
    native: 'Saint-Barthélemy',
    phone: '590',
    languages: ['fr'],
  },
  {
    code: 'BM',
    name: 'Bermuda',
    native: 'Bermuda',
    phone: '1441',
    languages: ['en'],
  },
  {
    code: 'BN',
    name: 'Brunei',
    native: 'Negara Brunei Darussalam',
    phone: '673',
    languages: ['ms'],
  },
  {
    code: 'BO',
    name: 'Bolivia',
    native: 'Bolivia',
    phone: '591',
    languages: ['es', 'ay', 'qu'],
  },
  {
    code: 'BQ',
    name: 'Bonaire',
    native: 'Bonaire',
    phone: '5997',
    languages: ['nl'],
  },
  {
    code: 'BR',
    name: 'Brazil',
    native: 'Brasil',
    phone: '55',
    languages: ['pt'],
  },
  {
    code: 'BS',
    name: 'Bahamas',
    native: 'Bahamas',
    phone: '1242',
    languages: ['en'],
  },
  {
    code: 'BT',
    name: 'Bhutan',
    native: 'ʼbrug-yul',
    phone: '975',
    languages: ['dz'],
  },
  {
    code: 'BV',
    name: 'Bouvet Island',
    native: 'Bouvetøya',
    phone: '47',
    languages: ['no', 'nb', 'nn'],
  },
  {
    code: 'BW',
    name: 'Botswana',
    native: 'Botswana',
    phone: '267',
    languages: ['en', 'tn'],
  },
  {
    code: 'BY',
    name: 'Belarus',
    native: 'Белару́сь',
    phone: '375',
    languages: ['be', 'ru'],
  },
  {
    code: 'BZ',
    name: 'Belize',
    native: 'Belize',
    phone: '501',
    languages: ['en', 'es'],
  },
  {
    code: 'CA',
    name: 'Canada',
    native: 'Canada',
    phone: '1',
    languages: ['en', 'fr'],
  },
  {
    code: 'CC',
    name: 'Cocos [Keeling] Islands',
    native: 'Cocos (Keeling) Islands',
    phone: '61',
    languages: ['en'],
  },
  {
    code: 'CD',
    name: 'Democratic Republic of the Congo',
    native: 'République démocratique du Congo',
    phone: '243',
    languages: ['fr', 'ln', 'kg', 'sw', 'lu'],
  },
  {
    code: 'CF',
    name: 'Central African Republic',
    native: 'Ködörösêse tî Bêafrîka',
    phone: '236',
    languages: ['fr', 'sg'],
  },
  {
    code: 'CG',
    name: 'Republic of the Congo',
    native: 'République du Congo',
    phone: '242',
    languages: ['fr', 'ln'],
  },
  {
    code: 'CH',
    name: 'Switzerland',
    native: 'Schweiz',
    phone: '41',
    languages: ['de', 'fr', 'it'],
  },
  {
    code: 'CI',
    name: 'Ivory Coast',
    native: "Côte d'Ivoire",
    phone: '225',
    languages: ['fr'],
  },
  {
    code: 'CK',
    name: 'Cook Islands',
    native: 'Cook Islands',
    phone: '682',
    languages: ['en'],
  },
  {
    code: 'CL',
    name: 'Chile',
    native: 'Chile',
    phone: '56',
    languages: ['es'],
  },
  {
    code: 'CM',
    name: 'Cameroon',
    native: 'Cameroon',
    phone: '237',
    languages: ['en', 'fr'],
  },
  {
    code: 'CN',
    name: 'China',
    native: '中国',
    phone: '86',
    languages: ['zh'],
  },
  {
    code: 'CO',
    name: 'Colombia',
    native: 'Colombia',
    phone: '57',
    languages: ['es'],
  },
  {
    code: 'CR',
    name: 'Costa Rica',
    native: 'Costa Rica',
    phone: '506',
    languages: ['es'],
  },
  {
    code: 'CU',
    name: 'Cuba',
    native: 'Cuba',
    phone: '53',
    languages: ['es'],
  },
  {
    code: 'CV',
    name: 'Cape Verde',
    native: 'Cabo Verde',
    phone: '238',
    languages: ['pt'],
  },
  {
    code: 'CW',
    name: 'Curacao',
    native: 'Curaçao',
    phone: '5999',
    languages: ['nl', 'pa', 'en'],
  },
  {
    code: 'CX',
    name: 'Christmas Island',
    native: 'Christmas Island',
    phone: '61',
    languages: ['en'],
  },
  {
    code: 'CY',
    name: 'Cyprus',
    native: 'Κύπρος',
    phone: '357',
    languages: ['el', 'tr', 'hy'],
  },
  {
    code: 'CZ',
    name: 'Czech Republic',
    native: 'Česká republika',
    phone: '420',
    languages: ['cs', 'sk'],
  },
  {
    code: 'DE',
    name: 'Germany',
    native: 'Deutschland',
    phone: '49',
    languages: ['de'],
  },
  {
    code: 'DJ',
    name: 'Djibouti',
    native: 'Djibouti',
    phone: '253',
    languages: ['fr', 'ar'],
  },
  {
    code: 'DK',
    name: 'Denmark',
    native: 'Danmark',
    phone: '45',
    languages: ['da'],
  },
  {
    code: 'DM',
    name: 'Dominica',
    native: 'Dominica',
    phone: '1767',
    languages: ['en'],
  },
  {
    code: 'DO',
    name: 'Dominican Republic',
    native: 'República Dominicana',
    phone: '1809,1829,1849',
    languages: ['es'],
  },
  {
    code: 'DZ',
    name: 'Algeria',
    native: 'الجزائر',
    phone: '213',
    languages: ['ar'],
  },
  {
    code: 'EC',
    name: 'Ecuador',
    native: 'Ecuador',
    phone: '593',
    languages: ['es'],
  },
  {
    code: 'EE',
    name: 'Estonia',
    native: 'Eesti',
    phone: '372',
    languages: ['et'],
  },
  {
    code: 'EG',
    name: 'Egypt',
    native: 'مصر',
    phone: '20',
    languages: ['ar'],
  },
  {
    code: 'EH',
    name: 'Western Sahara',
    native: 'الصحراء الغربية',
    phone: '212',
    languages: ['es'],
  },
  {
    code: 'ER',
    name: 'Eritrea',
    native: 'ኤርትራ',
    phone: '291',
    languages: ['ti', 'ar', 'en'],
  },
  {
    code: 'ES',
    name: 'Spain',
    native: 'España',
    phone: '34',
    languages: ['es', 'eu', 'ca', 'gl', 'oc'],
  },
  {
    code: 'ET',
    name: 'Ethiopia',
    native: 'ኢትዮጵያ',
    phone: '251',
    languages: ['am'],
  },
  {
    code: 'FI',
    name: 'Finland',
    native: 'Suomi',
    phone: '358',
    languages: ['fi', 'sv'],
  },
  {
    code: 'FJ',
    name: 'Fiji',
    native: 'Fiji',
    phone: '679',
    languages: ['en', 'fj', 'hi', 'ur'],
  },
  {
    code: 'FK',
    name: 'Falkland Islands',
    native: 'Falkland Islands',
    phone: '500',
    languages: ['en'],
  },
  {
    code: 'FM',
    name: 'Micronesia',
    native: 'Micronesia',
    phone: '691',
    languages: ['en'],
  },
  {
    code: 'FO',
    name: 'Faroe Islands',
    native: 'Føroyar',
    phone: '298',
    languages: ['fo'],
  },
  {
    code: 'FR',
    name: 'France',
    native: 'France',
    phone: '33',
    languages: ['fr'],
  },
  {
    code: 'GA',
    name: 'Gabon',
    native: 'Gabon',
    phone: '241',
    languages: ['fr'],
  },
  {
    code: 'GB',
    name: 'United Kingdom',
    native: 'United Kingdom',
    phone: '44',
    languages: ['en'],
  },
  {
    code: 'GD',
    name: 'Grenada',
    native: 'Grenada',
    phone: '1473',
    languages: ['en'],
  },
  {
    code: 'GE',
    name: 'Georgia',
    native: 'საქართველო',
    phone: '995',
    languages: ['ka'],
  },
  {
    code: 'GF',
    name: 'French Guiana',
    native: 'Guyane française',
    phone: '594',
    languages: ['fr'],
  },
  {
    code: 'GG',
    name: 'Guernsey',
    native: 'Guernsey',
    phone: '44',
    languages: ['en', 'fr'],
  },
  {
    code: 'GH',
    name: 'Ghana',
    native: 'Ghana',
    phone: '233',
    languages: ['en'],
  },
  {
    code: 'GI',
    name: 'Gibraltar',
    native: 'Gibraltar',
    phone: '350',
    languages: ['en'],
  },
  {
    code: 'GL',
    name: 'Greenland',
    native: 'Kalaallit Nunaat',
    phone: '299',
    languages: ['kl'],
  },
  {
    code: 'GM',
    name: 'Gambia',
    native: 'Gambia',
    phone: '220',
    languages: ['en'],
  },
  {
    code: 'GN',
    name: 'Guinea',
    native: 'Guinée',
    phone: '224',
    languages: ['fr', 'ff'],
  },
  {
    code: 'GP',
    name: 'Guadeloupe',
    native: 'Guadeloupe',
    phone: '590',
    languages: ['fr'],
  },
  {
    code: 'GQ',
    name: 'Equatorial Guinea',
    native: 'Guinea Ecuatorial',
    phone: '240',
    languages: ['es', 'fr'],
  },
  {
    code: 'GR',
    name: 'Greece',
    native: 'Ελλάδα',
    phone: '30',
    languages: ['el'],
  },
  {
    code: 'GS',
    name: 'South Georgia and the South Sandwich Islands',
    native: 'South Georgia',
    phone: '500',
    languages: ['en'],
  },
  {
    code: 'GT',
    name: 'Guatemala',
    native: 'Guatemala',
    phone: '502',
    languages: ['es'],
  },
  {
    code: 'GU',
    name: 'Guam',
    native: 'Guam',
    phone: '1671',
    languages: ['en', 'ch', 'es'],
  },
  {
    code: 'GW',
    name: 'Guinea-Bissau',
    native: 'Guiné-Bissau',
    phone: '245',
    languages: ['pt'],
  },
  {
    code: 'GY',
    name: 'Guyana',
    native: 'Guyana',
    phone: '592',
    languages: ['en'],
  },
  {
    code: 'HK',
    name: 'Hong Kong',
    native: '香港',
    phone: '852',
    languages: ['zh', 'en'],
  },
  {
    code: 'HM',
    name: 'Heard Island and McDonald Islands',
    native: 'Heard Island and McDonald Islands',
    phone: '61',
    languages: ['en'],
  },
  {
    code: 'HN',
    name: 'Honduras',
    native: 'Honduras',
    phone: '504',
    languages: ['es'],
  },
  {
    code: 'HR',
    name: 'Croatia',
    native: 'Hrvatska',
    phone: '385',
    languages: ['hr'],
  },
  {
    code: 'HT',
    name: 'Haiti',
    native: 'Haïti',
    phone: '509',
    languages: ['fr', 'ht'],
  },
  {
    code: 'HU',
    name: 'Hungary',
    native: 'Magyarország',
    phone: '36',
    languages: ['hu'],
  },
  {
    code: 'ID',
    name: 'Indonesia',
    native: 'Indonesia',
    phone: '62',
    languages: ['id'],
  },
  {
    code: 'IE',
    name: 'Ireland',
    native: 'Éire',
    phone: '353',
    languages: ['ga', 'en'],
  },
  {
    code: 'IL',
    name: 'Israel',
    native: 'יִשְׂרָאֵל',
    phone: '972',
    languages: ['he', 'ar'],
  },
  {
    code: 'IM',
    name: 'Isle of Man',
    native: 'Isle of Man',
    phone: '44',
    languages: ['en', 'gv'],
  },
  {
    code: 'ID',
    name: 'India',
    native: 'भारत',
    phone: '91',
    languages: ['hi', 'en'],
  },
  {
    code: 'IO',
    name: 'British Indian Ocean Territory',
    native: 'British Indian Ocean Territory',
    phone: '246',
    languages: ['en'],
  },
  {
    code: 'IQ',
    name: 'Iraq',
    native: 'العراق',
    phone: '964',
    languages: ['ar', 'ku'],
  },
  {
    code: 'IR',
    name: 'Iran',
    native: 'ایران',
    phone: '98',
    languages: ['fa'],
  },
  {
    code: 'IS',
    name: 'Iceland',
    native: 'Ísland',
    phone: '354',
    languages: ['is'],
  },
  {
    code: 'IT',
    name: 'Italy',
    native: 'Italia',
    phone: '39',
    languages: ['it'],
  },
  {
    code: 'JE',
    name: 'Jersey',
    native: 'Jersey',
    phone: '44',
    languages: ['en', 'fr'],
  },
  {
    code: 'JM',
    name: 'Jamaica',
    native: 'Jamaica',
    phone: '1876',
    languages: ['en'],
  },
  {
    code: 'JO',
    name: 'Jordan',
    native: 'الأردن',
    phone: '962',
    languages: ['ar'],
  },
  {
    code: 'JP',
    name: 'Japan',
    native: '日本',
    phone: '81',
    languages: ['ja'],
  },
  {
    code: 'KE',
    name: 'Kenya',
    native: 'Kenya',
    phone: '254',
    languages: ['en', 'sw'],
  },
  {
    code: 'KG',
    name: 'Kyrgyzstan',
    native: 'Кыргызстан',
    phone: '996',
    languages: ['ky', 'ru'],
  },
  {
    code: 'KH',
    name: 'Cambodia',
    native: 'Kâmpŭchéa',
    phone: '855',
    languages: ['km'],
  },
  {
    code: 'KI',
    name: 'Kiribati',
    native: 'Kiribati',
    phone: '686',
    languages: ['en'],
  },
  {
    code: 'KM',
    name: 'Comoros',
    native: 'Komori',
    phone: '269',
    languages: ['ar', 'fr'],
  },
  {
    code: 'KN',
    name: 'Saint Kitts and Nevis',
    native: 'Saint Kitts and Nevis',
    phone: '1869',
    languages: ['en'],
  },
  {
    code: 'KP',
    name: 'North Korea',
    native: '북한',
    phone: '850',
    languages: ['ko'],
  },
  {
    code: 'KR',
    name: 'South Korea',
    native: '대한민국',
    phone: '82',
    languages: ['ko'],
  },
  {
    code: 'KW',
    name: 'Kuwait',
    native: 'الكويت',
    phone: '965',
    languages: ['ar'],
  },
  {
    code: 'KY',
    name: 'Cayman Islands',
    native: 'Cayman Islands',
    phone: '1345',
    languages: ['en'],
  },
  {
    code: 'KZ',
    name: 'Kazakhstan',
    native: 'Қазақстан',
    phone: '76,77',
    languages: ['kk', 'ru'],
  },
  {
    code: 'LA',
    name: 'Laos',
    native: 'ສປປລາວ',
    phone: '856',
    languages: ['lo'],
  },
  {
    code: 'LB',
    name: 'Lebanon',
    native: 'لبنان',
    phone: '961',
    languages: ['ar', 'fr'],
  },
  {
    code: 'LC',
    name: 'Saint Lucia',
    native: 'Saint Lucia',
    phone: '1758',
    languages: ['en'],
  },
  {
    code: 'LI',
    name: 'Liechtenstein',
    native: 'Liechtenstein',
    phone: '423',
    languages: ['de'],
  },
  {
    code: 'LK',
    name: 'Sri Lanka',
    native: 'śrī laṃkāva',
    phone: '94',
    languages: ['si', 'ta'],
  },
  {
    code: 'LR',
    name: 'Liberia',
    native: 'Liberia',
    phone: '231',
    languages: ['en'],
  },
  {
    code: 'LS',
    name: 'Lesotho',
    native: 'Lesotho',
    phone: '266',
    languages: ['en', 'st'],
  },
  {
    code: 'LT',
    name: 'Lithuania',
    native: 'Lietuva',
    phone: '370',
    languages: ['lt'],
  },
  {
    code: 'LU',
    name: 'Luxembourg',
    native: 'Luxembourg',
    phone: '352',
    languages: ['fr', 'de', 'lb'],
  },
  {
    code: 'LV',
    name: 'Latvia',
    native: 'Latvija',
    phone: '371',
    languages: ['lv'],
  },
  {
    code: 'LY',
    name: 'Libya',
    native: 'ليبيا',
    phone: '218',
    languages: ['ar'],
  },
  {
    code: 'MA',
    name: 'Morocco',
    native: 'المغرب',
    phone: '212',
    languages: ['ar'],
  },
  {
    code: 'MC',
    name: 'Monaco',
    native: 'Monaco',
    phone: '377',
    languages: ['fr'],
  },
  {
    code: 'MD',
    name: 'Moldova',
    native: 'Moldova',
    phone: '373',
    languages: ['ro'],
  },
  {
    code: 'ME',
    name: 'Montenegro',
    native: 'Црна Гора',
    phone: '382',
    languages: ['sr', 'bs', 'sq', 'hr'],
  },
  {
    code: 'MF',
    name: 'Saint Martin',
    native: 'Saint-Martin',
    phone: '590',
    languages: ['en', 'fr', 'nl'],
  },
  {
    code: 'MG',
    name: 'Madagascar',
    native: 'Madagasikara',
    phone: '261',
    languages: ['fr', 'mg'],
  },
  {
    code: 'MH',
    name: 'Marshall Islands',
    native: 'M̧ajeļ',
    phone: '692',
    languages: ['en', 'mh'],
  },
  {
    code: 'MK',
    name: 'North Macedonia',
    native: 'Северна Македонија',
    phone: '389',
    languages: ['mk'],
  },
  {
    code: 'ML',
    name: 'Mali',
    native: 'Mali',
    phone: '223',
    languages: ['fr'],
  },
  {
    code: 'MM',
    name: 'Myanmar [Burma]',
    native: 'မြန်မာ',
    phone: '95',
    languages: ['my'],
  },
  {
    code: 'MN',
    name: 'Mongolia',
    native: 'Монгол улс',
    phone: '976',
    languages: ['mn'],
  },
  {
    code: 'MO',
    name: 'Macao',
    native: '澳門',
    phone: '853',
    languages: ['zh', 'pt'],
  },
  {
    code: 'MP',
    name: 'Northern Mariana Islands',
    native: 'Northern Mariana Islands',
    phone: '1670',
    languages: ['en', 'ch'],
  },
  {
    code: 'MQ',
    name: 'Martinique',
    native: 'Martinique',
    phone: '596',
    languages: ['fr'],
  },
  {
    code: 'MR',
    name: 'Mauritania',
    native: 'موريتانيا',
    phone: '222',
    languages: ['ar'],
  },
  {
    code: 'MS',
    name: 'Montserrat',
    native: 'Montserrat',
    phone: '1664',
    languages: ['en'],
  },
  {
    code: 'MT',
    name: 'Malta',
    native: 'Malta',
    phone: '356',
    languages: ['mt', 'en'],
  },
  {
    code: 'MU',
    name: 'Mauritius',
    native: 'Maurice',
    phone: '230',
    languages: ['en'],
  },
  {
    code: 'MV',
    name: 'Maldives',
    native: 'Maldives',
    phone: '960',
    languages: ['dv'],
  },
  {
    code: 'MW',
    name: 'Malawi',
    native: 'Malawi',
    phone: '265',
    languages: ['en', 'ny'],
  },
  {
    code: 'MX',
    name: 'Mexico',
    native: 'México',
    phone: '52',
    languages: ['es'],
  },
  {
    code: 'MY',
    name: 'Malaysia',
    native: 'Malaysia',
    phone: '60',
    languages: ['ms'],
  },
  {
    code: 'MZ',
    name: 'Mozambique',
    native: 'Moçambique',
    phone: '258',
    languages: ['pt'],
  },
  {
    code: 'NA',
    name: 'Namibia',
    native: 'Namibia',
    phone: '264',
    languages: ['en', 'af'],
  },
  {
    code: 'NC',
    name: 'New Caledonia',
    native: 'Nouvelle-Calédonie',
    phone: '687',
    languages: ['fr'],
  },
  {
    code: 'NE',
    name: 'Niger',
    native: 'Niger',
    phone: '227',
    languages: ['fr'],
  },
  {
    code: 'NF',
    name: 'Norfolk Island',
    native: 'Norfolk Island',
    phone: '672',
    languages: ['en'],
  },
  {
    code: 'NG',
    name: 'Nigeria',
    native: 'Nigeria',
    phone: '234',
    languages: ['en'],
  },
  {
    code: 'NI',
    name: 'Nicaragua',
    native: 'Nicaragua',
    phone: '505',
    languages: ['es'],
  },
  {
    code: 'NL',
    name: 'Netherlands',
    native: 'Nederland',
    phone: '31',
    languages: ['nl'],
  },
  {
    code: 'NO',
    name: 'Norway',
    native: 'Norge',
    phone: '47',
    languages: ['no', 'nb', 'nn'],
  },
  {
    code: 'NP',
    name: 'Nepal',
    native: 'नपल',
    phone: '977',
    languages: ['ne'],
  },
  {
    code: 'NR',
    name: 'Nauru',
    native: 'Nauru',
    phone: '674',
    languages: ['en', 'na'],
  },
  {
    code: 'NU',
    name: 'Niue',
    native: 'Niuē',
    phone: '683',
    languages: ['en'],
  },
  {
    code: 'NZ',
    name: 'New Zealand',
    native: 'New Zealand',
    phone: '64',
    languages: ['en', 'mi'],
  },
  {
    code: 'OM',
    name: 'Oman',
    native: 'عمان',
    phone: '968',
    languages: ['ar'],
  },
  {
    code: 'PA',
    name: 'Panama',
    native: 'Panamá',
    phone: '507',
    languages: ['es'],
  },
  {
    code: 'PE',
    name: 'Peru',
    native: 'Perú',
    phone: '51',
    languages: ['es'],
  },
  {
    code: 'PF',
    name: 'French Polynesia',
    native: 'Polynésie française',
    phone: '689',
    languages: ['fr'],
  },
  {
    code: 'PG',
    name: 'Papua New Guinea',
    native: 'Papua Niugini',
    phone: '675',
    languages: ['en'],
  },
  {
    code: 'PH',
    name: 'Philippines',
    native: 'Pilipinas',
    phone: '63',
    languages: ['en'],
  },
  {
    code: 'PK',
    name: 'Pakistan',
    native: 'Pakistan',
    phone: '92',
    languages: ['en', 'ur'],
  },
  {
    code: 'PL',
    name: 'Poland',
    native: 'Polska',
    phone: '48',
    languages: ['pl'],
  },
  {
    code: 'PM',
    name: 'Saint Pierre and Miquelon',
    native: 'Saint-Pierre-et-Miquelon',
    phone: '508',
    languages: ['fr'],
  },
  {
    code: 'PN',
    name: 'Pitcairn Islands',
    native: 'Pitcairn Islands',
    phone: '64',
    languages: ['en'],
  },
  {
    code: 'PR',
    name: 'Puerto Rico',
    native: 'Puerto Rico',
    phone: '1787,1939',
    languages: ['es', 'en'],
  },
  {
    code: 'PS',
    name: 'Palestine',
    native: 'فلسطين',
    phone: '970',
    languages: ['ar'],
  },
  {
    code: 'PT',
    name: 'Portugal',
    native: 'Portugal',
    phone: '351',
    languages: ['pt'],
  },
  {
    code: 'PW',
    name: 'Palau',
    native: 'Palau',
    phone: '680',
    languages: ['en'],
  },
  {
    code: 'PY',
    name: 'Paraguay',
    native: 'Paraguay',
    phone: '595',
    languages: ['es', 'gn'],
  },
  {
    code: 'QA',
    name: 'Qatar',
    native: 'قطر',
    phone: '974',
    languages: ['ar'],
  },
  {
    code: 'RE',
    name: 'Réunion',
    native: 'La Réunion',
    phone: '262',
    languages: ['fr'],
  },
  {
    code: 'RO',
    name: 'Romania',
    native: 'România',
    phone: '40',
    languages: ['ro'],
  },
  {
    code: 'RS',
    name: 'Serbia',
    native: 'Србија',
    phone: '381',
    languages: ['sr'],
  },
  {
    code: 'RU',
    name: 'Russia',
    native: 'Россия',
    phone: '7',
    languages: ['ru'],
  },
  {
    code: 'RW',
    name: 'Rwanda',
    native: 'Rwanda',
    phone: '250',
    languages: ['rw', 'en', 'fr'],
  },
  {
    code: 'SA',
    name: 'Saudi Arabia',
    native: 'العربية السعودية',
    phone: '966',
    languages: ['ar'],
  },
  {
    code: 'SB',
    name: 'Solomon Islands',
    native: 'Solomon Islands',
    phone: '677',
    languages: ['en'],
  },
  {
    code: 'SC',
    name: 'Seychelles',
    native: 'Seychelles',
    phone: '248',
    languages: ['fr', 'en'],
  },
  {
    code: 'SD',
    name: 'Sudan',
    native: 'السودان',
    phone: '249',
    languages: ['ar', 'en'],
  },
  {
    code: 'SE',
    name: 'Sweden',
    native: 'Sverige',
    phone: '46',
    languages: ['sv'],
  },
  {
    code: 'SG',
    name: 'Singapore',
    native: 'Singapore',
    phone: '65',
    languages: ['en', 'ms', 'ta', 'zh'],
  },
  {
    code: 'SH',
    name: 'Saint Helena',
    native: 'Saint Helena',
    phone: '290',
    languages: ['en'],
  },
  {
    code: 'SI',
    name: 'Slovenia',
    native: 'Slovenija',
    phone: '386',
    languages: ['sl'],
  },
  {
    code: 'SJ',
    name: 'Svalbard and Jan Mayen',
    native: 'Svalbard og Jan Mayen',
    phone: '4779',
    languages: ['no'],
  },
  {
    code: 'SK',
    name: 'Slovakia',
    native: 'Slovensko',
    phone: '421',
    languages: ['sk'],
  },
  {
    code: 'SL',
    name: 'Sierra Leone',
    native: 'Sierra Leone',
    phone: '232',
    languages: ['en'],
  },
  {
    code: 'SM',
    name: 'San Marino',
    native: 'San Marino',
    phone: '378',
    languages: ['it'],
  },
  {
    code: 'SN',
    name: 'Senegal',
    native: 'Sénégal',
    phone: '221',
    languages: ['fr'],
  },
  {
    code: 'SO',
    name: 'Somalia',
    native: 'Soomaaliya',
    phone: '252',
    languages: ['so', 'ar'],
  },
  {
    code: 'SR',
    name: 'Suriname',
    native: 'Suriname',
    phone: '597',
    languages: ['nl'],
  },
  {
    code: 'SS',
    name: 'South Sudan',
    native: 'South Sudan',
    phone: '211',
    languages: ['en'],
  },
  {
    code: 'ST',
    name: 'São Tomé and Príncipe',
    native: 'São Tomé e Príncipe',
    phone: '239',
    languages: ['pt'],
  },
  {
    code: 'SV',
    name: 'El Salvador',
    native: 'El Salvador',
    phone: '503',
    languages: ['es'],
  },
  {
    code: 'SX',
    name: 'Sint Maarten',
    native: 'Sint Maarten',
    phone: '1721',
    languages: ['nl', 'en'],
  },
  {
    code: 'SY',
    name: 'Syria',
    native: 'سوريا',
    phone: '963',
    languages: ['ar'],
  },
  {
    code: 'SZ',
    name: 'Eswatini',
    native: 'eSwatini',
    phone: '268',
    languages: ['en', 'ss'],
  },
  {
    code: 'TC',
    name: 'Turks and Caicos Islands',
    native: 'Turks and Caicos Islands',
    phone: '1649',
    languages: ['en'],
  },
  {
    code: 'TD',
    name: 'Chad',
    native: 'Tchad',
    phone: '235',
    languages: ['fr', 'ar'],
  },
  {
    code: 'TF',
    name: 'French Southern Territories',
    native: 'Territoire des Terres australes et antarctiques fr',
    phone: '262',
    languages: ['fr'],
  },
  {
    code: 'TG',
    name: 'Togo',
    native: 'Togo',
    phone: '228',
    languages: ['fr'],
  },
  {
    code: 'TH',
    name: 'Thailand',
    native: 'ประเทศไทย',
    phone: '66',
    languages: ['th'],
  },
  {
    code: 'TJ',
    name: 'Tajikistan',
    native: 'Тоҷикистон',
    phone: '992',
    languages: ['tg', 'ru'],
  },
  {
    code: 'TK',
    name: 'Tokelau',
    native: 'Tokelau',
    phone: '690',
    languages: ['en'],
  },
  {
    code: 'TL',
    name: 'East Timor',
    native: 'Timor-Leste',
    phone: '670',
    languages: ['pt'],
  },
  {
    code: 'TM',
    name: 'Turkmenistan',
    native: 'Türkmenistan',
    phone: '993',
    languages: ['tk', 'ru'],
  },
  {
    code: 'TN',
    name: 'Tunisia',
    native: 'تونس',
    phone: '216',
    languages: ['ar'],
  },
  {
    code: 'TO',
    name: 'Tonga',
    native: 'Tonga',
    phone: '676',
    languages: ['en', 'to'],
  },
  {
    code: 'TR',
    name: 'Turkey',
    native: 'Türkiye',
    phone: '90',
    languages: ['tr'],
  },
  {
    code: 'TT',
    name: 'Trinidad and Tobago',
    native: 'Trinidad and Tobago',
    phone: '1868',
    languages: ['en'],
  },
  {
    code: 'TV',
    name: 'Tuvalu',
    native: 'Tuvalu',
    phone: '688',
    languages: ['en'],
  },
  {
    code: 'TW',
    name: 'Taiwan',
    native: '臺灣',
    phone: '886',
    languages: ['zh'],
  },
  {
    code: 'TZ',
    name: 'Tanzania',
    native: 'Tanzania',
    phone: '255',
    languages: ['sw', 'en'],
  },
  {
    code: 'UA',
    name: 'Ukraine',
    native: 'Україна',
    phone: '380',
    languages: ['uk'],
  },
  {
    code: 'UG',
    name: 'Uganda',
    native: 'Uganda',
    phone: '256',
    languages: ['en', 'sw'],
  },
  {
    code: 'UM',
    name: 'U.S. Minor Outlying Islands',
    native: 'United States Minor Outlying Islands',
    phone: '1',
    languages: ['en'],
  },
  {
    code: 'US',
    name: 'United States',
    native: 'United States',
    phone: '1',
    languages: ['en'],
  },
  {
    code: 'UY',
    name: 'Uruguay',
    native: 'Uruguay',
    phone: '598',
    languages: ['es'],
  },
  {
    code: 'UZ',
    name: 'Uzbekistan',
    native: 'O‘zbekiston',
    phone: '998',
    languages: ['uz', 'ru'],
  },
  {
    code: 'VA',
    name: 'Vatican City',
    native: 'Vaticano',
    phone: '379',
    languages: ['it', 'la'],
  },
  {
    code: 'VC',
    name: 'Saint Vincent and the Grenadines',
    native: 'Saint Vincent and the Grenadines',
    phone: '1784',
    languages: ['en'],
  },
  {
    code: 'VE',
    name: 'Venezuela',
    native: 'Venezuela',
    phone: '58',
    languages: ['es'],
  },
  {
    code: 'VG',
    name: 'British Virgin Islands',
    native: 'British Virgin Islands',
    phone: '1284',
    languages: ['en'],
  },
  {
    code: 'VI',
    name: 'U.S. Virgin Islands',
    native: 'United States Virgin Islands',
    phone: '1340',
    languages: ['en'],
  },
  {
    code: 'VN',
    name: 'Vietnam',
    native: 'Việt Nam',
    phone: '84',
    languages: ['vi'],
  },
  {
    code: 'VU',
    name: 'Vanuatu',
    native: 'Vanuatu',
    phone: '678',
    languages: ['bi', 'en', 'fr'],
  },
  {
    code: 'WF',
    name: 'Wallis and Futuna',
    native: 'Wallis et Futuna',
    phone: '681',
    languages: ['fr'],
  },
  {
    code: 'WS',
    name: 'Samoa',
    native: 'Samoa',
    phone: '685',
    languages: ['sm', 'en'],
  },
  {
    code: 'XK',
    name: 'Kosovo',
    native: 'Republika e Kosovës',
    phone: '377,381,383,386',
    languages: ['sq', 'sr'],
  },
  {
    code: 'YE',
    name: 'Yemen',
    native: 'اليَمَن',
    phone: '967',
    languages: ['ar'],
  },
  {
    code: 'YT',
    name: 'Mayotte',
    native: 'Mayotte',
    phone: '262',
    languages: ['fr'],
  },
  {
    code: 'ZA',
    name: 'South Africa',
    native: 'South Africa',
    phone: '27',
    languages: ['af', 'en', 'nr', 'st', 'ss', 'tn', 'ts', 've', 'xh', 'zu'],
  },
  {
    code: 'ZM',
    name: 'Zambia',
    native: 'Zambia',
    phone: '260',
    languages: ['en'],
  },
  {
    code: 'ZW',
    name: 'Zimbabwe',
    native: 'Zimbabwe',
    phone: '263',
    languages: ['en', 'sn', 'nd'],
  },
];

// List of aliases for countries that are misspellings or other names that are not in the list above.
export const countryAliases = {
  'USA': 'US',
  'US Virgin Islands': 'VI',
  "Cote d'Ivoire": 'CI',
  'Cote d’Ivoire': 'CI',
  "Cote d'Ivoire, West Africa": 'CI',
  'Korea': 'KR',
  'England': 'GB',
  'Democratic Republic of Congo': 'CD',
  'Republic of China': 'TW',
  'Taiwan, Republic of China': 'TW',
  'Guatemala, Guatemala': 'GT',
  'Trinidad, Nicaragua': 'NI',
  'Russian Federation': 'RU',
  'The Bahamas': 'BS',
  'Antigua, Antigua and Barbuda': 'AG',
  'Timor-Leste': 'TL',
  'Panama, Panama': 'PA',
  'Myanmar': 'MM',
  'Republic of Singapore': 'SG',
  'PW': 'PW',
  'PR': 'PR',
  'Republic of South Africa': 'ZA',
  'Republic of Djibouti': 'DJ',
  'Ghana, West Africa': 'GH',
  'Comoros, Indian Ocean': 'KM',
  'St. Vincent and the Grenadines': 'VC',
  'Trinidad, Trinidad and Tobago': 'TT',
  'Bosnia-Herzegovina': 'BA',
  'Swaziland': 'SZ',
  'Micronesia, Federated States': 'FM',
  'Moldavia, Romania': 'RO',
  'Kyrgyz Republic': 'KG',
  'Macau': 'MO',
  'Scotland': 'GB', // ??? GB ? EntityID: 13029
  "Lao People's Democratic Republic": 'LA',
  'Western Samoa': 'WS',
  'I': 'ID', // Indonesia?  EntityID: 13399
  'Mauritius, Indian Ocean': 'MU', // EntityID: 13446
  'Cabo Verde': 'CV', // EntityID: 13548
  'The Gambia': 'GM', // EntityID: 13575
  'Kenya, East Africa': 'KE', // EntityID: 13603
  'C': 'UG', // Uganda?  EntityID: 13673
};
