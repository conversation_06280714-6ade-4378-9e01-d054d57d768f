export const languagesMap = {
  '- None -': {
    code: undefined,
    count: 1,
    entities: ['13108'], // DAVAO MISSION, Philippines
  },
  'English': {
    code: 'en',
    count: 57,
    entities: [
      '10001',
      '12852',
      '12856',
      '12947',
      '12954',
      '13084',
      '13093',
      '13153',
      '13203',
      '13219',
      '13220',
      '13221',
      '13222',
      '13257',
      '13306',
      '13348',
      '13353',
      '13387',
      '13408',
      '13499',
      '13502',
      '13528',
      '13605',
      '13622',
      '13631',
      '13645',
      '13657',
      '13677',
      '13701',
      '13940',
      '13941',
      '13956',
      '13957',
      '13974',
      '14159',
      '14164',
      '18234',
      '20199',
      '20954',
      '22092',
      '29832',
      '29836',
      '29842',
      '29852',
      '29861',
      '29883',
      '29892',
      '29935',
      '30061',
      '30114',
      '30129',
      '30137',
      '30149',
      '30980',
      '30981',
      '31658',
      '54229',
    ],
  },
  'English ': {
    code: 'en',
    count: 1,
    entities: ['13997'], // BELIZE UNION MISSION
  },
  'Russian': {
    code: 'ru',
    count: 2,
    entities: ['10067', '10074'],
  },
  'French': {
    code: 'fr',
    count: 15,
    entities: [
      '10145',
      '12869',
      '13288',
      '13465',
      '13471',
      '13513',
      '13550',
      '13552',
      '13559',
      '13778',
      '13787',
      '13881',
      '14073',
      '20446',
      '30230',
    ],
  },
  'French ': {
    code: 'fr',
    count: 1,
    entities: ['14086'], // ADVENTIST UNIVERSITY OF HAITI (C, AAA)
  },
  'french': {
    code: 'fr',
    count: 1,
    entities: ['14093'], // NORTH HAITI MISSION
  },
  'FRENCH': {
    code: 'fr',
    count: 1,
    entities: ['13551'], // NIGER REGION
  },
  'Spanish': {
    code: 'es',
    count: 11,
    entities: [
      '10273',
      '10503',
      '13441',
      '13866',
      '13879',
      '13883',
      '13891',
      '14009',
      '14032',
      '14133',
      '32107',
    ],
  },
  'spanish': {
    code: 'es',
    count: 3,
    entities: ['14058', '14106', '30372'], // DOMINICAN UNION CONFERENCE | NORTHEAST MEXICAN CONFERENCE | BAJIO MEXICAN CONFERENCE
  },
  'Español': {
    code: 'es',
    count: 2,
    entities: ['22033', '30947'], // NORTHEAST PERU MISSION, Peru | SOUTH QUINTANA ROO MISSION, Mexico
  },
  'German': {
    code: 'de',
    count: 2,
    entities: ['13763', '13766'],
  },
  'Deutsch': {
    code: 'de',
    count: 1,
    entities: ['13732'], // BOGENHOFEN SENIOR HIGH SCHOOL (CS, AAA), Austria
  },
  'Portuguese': {
    code: 'pt',
    count: 6,
    entities: ['10455', '10535', '10645', '10646', '13548', '30167'],
  },
  'Portugues': {
    code: 'pt',
    count: 1,
    entities: ['10536'], // CENTRAL BRAZIL UNION CONFERENCE, Brazil
  },
  'Portuguese (BR)': {
    code: 'pt-BR',
    count: 1,
    entities: ['31508'], // CENTRAL RIO GRANDE DO SUL CONFERENCE, Brazil
  },
  'Romanian': {
    code: 'ro',
    count: 3,
    entities: ['13825', '13827', '30104'],
  },
  'Arabic': {
    code: 'ar',
    count: 2,
    entities: ['12846', '12858'],
  },
  'Swedish': {
    code: 'sv',
    count: 1,
    entities: ['12865'],
  },
  'Serbian': {
    code: 'sr',
    count: 1,
    entities: ['12870'],
  },
  'Tamil': {
    code: 'ta',
    count: 1,
    entities: ['12890'],
  },
  'Malayalam': {
    code: 'ml',
    count: 1,
    entities: ['12911'],
  },
  'Nepali': {
    code: 'ne',
    count: 2,
    entities: ['12924', '32617'],
  },
  'Sesotho': {
    code: 'st',
    count: 1,
    entities: ['12960'],
  },
  'Albanian': {
    code: 'sq',
    count: 1,
    entities: ['13049'],
  },
  'hungarian': {
    code: 'hu',
    count: 1,
    entities: ['13071'],
  },
  'Thai': {
    code: 'th',
    count: 1,
    entities: ['13086'],
  },
  'Malay': {
    code: 'ms',
    count: 1,
    entities: ['13099'],
  },
  'Bahasa Indonesia': {
    code: 'id',
    count: 1,
    entities: ['13120'],
  },
  'Indonesia': {
    code: 'id',
    count: 1,
    entities: ['13126'],
  },
  'Indonesian': {
    code: 'id',
    count: 2,
    entities: ['13396', '13399'],
  },
  'Bisaya': {
    code: 'ceb',
    count: 1,
    entities: ['13123'],
  },
  'Khasi': {
    code: 'kha',
    count: 1,
    entities: ['13216'],
  },
  'Hindi': {
    code: 'hi',
    count: 1,
    entities: ['13240'],
  },
  'Mongolian': {
    code: 'mn',
    count: 1,
    entities: ['13261'],
  },
  'Bengali': {
    code: 'bn',
    count: 1,
    entities: ['13385'],
  },
  'Myanmar': {
    code: 'my',
    count: 1,
    entities: ['13416'],
  },
  'Akan': {
    code: 'ak',
    count: 1,
    entities: ['29923'],
  },
  'Akan (Twi)': {
    code: 'ak',
    count: 1,
    entities: ['13531'],
  },
  'Kiswahili': {
    code: 'sw',
    count: 1,
    entities: ['13668'],
  },
  'Papiamentu': {
    code: 'pap',
    count: 1,
    entities: ['14131'],
  },
  'Malagasy': {
    code: 'mg',
    count: 2,
    entities: ['29823', '29964'],
  },
  'tamil,english,hindi': {
    code: 'ta',
    count: 1,
    entities: ['29870'], // Periyakulam Seventh-day Adventist Higher Secondary School, India
  },
};
