// OrgMast Entiy types (extracted and adapted from https://orgmast.adventist.org/OrgMastAPI/api/OMEntityType)
export const entityTypesMap = {
  A: {
    code: 'A',
    name: 'organizationalUnit',
    label: 'Organizational Unit',
    labelPlural: 'Organizational Units',
    active: true,
  },
  AD: {
    code: 'AD',
    name: 'division',
    label: 'Division',
    labelPlural: 'Divisions',
    active: true,
  },
  ADB: {
    code: 'ADB',
    name: 'divisionBranchOffice',
    label: 'Division (Branch Office)',
    labelPlural: 'Divisions (Branch Offices)',
    active: true,
  },
  AF: {
    code: 'AF',
    name: 'attachedFiller',
    label: '(Attached Filler)',
    labelPlural: '(Attached Fillers)',
    active: true,
  },
  AG: {
    code: 'AG',
    name: 'generalConference',
    label: 'General Conference',
    labelPlural: 'General Conference',
    active: true,
  },
  AL: {
    code: 'AL',
    name: 'localOrganizationalUnit',
    label: 'Local Organizational Unit',
    labelPlural: 'Local Organizational Units',
    active: true,
  },
  ALC: {
    code: 'ALC',
    name: 'conference',
    label: 'Conference',
    labelPlural: 'Conferences',
    active: true,
  },
  ALF: {
    code: 'ALF',
    name: 'localFieldStation',
    label: 'Local Field Station',
    labelPlural: 'Local Field Stations',
    active: true,
  },
  ALM: {
    code: 'ALM',
    name: 'missionFieldSection',
    label: 'Mission/Field/Section',
    labelPlural: 'Missions/Fields/Sections',
    active: true,
  },
  ALR: {
    code: 'ALR',
    name: 'localRegion',
    label: 'Local Region',
    labelPlural: 'Local Regions',
    active: true,
  },
  AU: {
    code: 'AU',
    name: 'union',
    label: 'Union',
    labelPlural: 'Unions',
    active: true,
  },
  AUAF: {
    code: 'AUAF',
    name: 'unionAttachedField',
    label: 'Union-level Attached Field',
    labelPlural: 'Union-level Attached Fields',
    active: true,
  },
  AUC: {
    code: 'AUC',
    name: 'unionConference',
    label: 'Union Conference',
    labelPlural: 'Union Conferences',
    active: true,
  },
  AUM: {
    code: 'AUM',
    name: 'unionMission',
    label: 'Union Mission',
    labelPlural: 'Union Missions',
    active: true,
  },
  AUOC: {
    code: 'AUOC',
    name: 'unionOfChurches',
    label: 'Union of Churches',
    labelPlural: 'Union of Churches',
    active: true,
  },
  AUOCC: {
    code: 'AUOCC',
    name: 'unionOfChurchesConference',
    label: 'Union of Churches Conference',
    labelPlural: 'Union of Churches Conferences',
    active: true,
  },
  AUOCM: {
    code: 'AUOCM',
    name: 'unionOfChurchesMission',
    label: 'Union of Churches Mission',
    labelPlural: 'Union of Churches Missions',
    active: true,
  },
  AZ: {
    code: 'AZ',
    name: 'unorganizedCountryRegion',
    label: 'Unorganized Country/Region',
    labelPlural: 'Unorganized Countries/Regions',
    active: true,
  },
  C: {
    code: 'C',
    name: 'congregation',
    label: 'Congregation',
    labelPlural: 'Congregations',
    active: true,
  },
  CCH: {
    code: 'CCH',
    name: 'church',
    label: 'Church',
    labelPlural: 'Churches',
    active: true,
  },
  CCO: {
    code: 'CCO',
    name: 'company',
    label: 'Company',
    labelPlural: 'Companies',
    active: true,
  },
  CG: {
    code: 'CG',
    name: 'group',
    label: 'Group',
    labelPlural: 'Groups',
    active: true,
  },
  D: {
    code: 'D',
    name: 'adra',
    label: 'ADRA',
    labelPlural: 'ADRA',
    active: true,
  },
  DR: {
    code: 'DR',
    name: 'adraRegionalHeadquarters',
    label: 'ADRA Regional Headquarters',
    labelPlural: 'ADRA Regional Headquarters',
    active: true,
  },
  E: {
    code: 'E',
    name: 'education',
    label: 'Education',
    labelPlural: 'Education',
    active: true,
  },
  ED: {
    code: 'ED',
    name: 'dayCareCenter',
    label: 'Day Care Center',
    labelPlural: 'Day Care Centers',
    active: true,
  },
  EK: {
    code: 'EK',
    name: 'kindergarten',
    label: 'Kindergarten',
    labelPlural: 'Kindergartens',
    active: true,
  },
  EM: {
    code: 'EM',
    name: 'midLevelEducation',
    label: 'Mid-Level Education',
    labelPlural: 'Mid-Level Educations',
    active: true,
  },
  EP: {
    code: 'EP',
    name: 'primaryElementarySchool',
    label: 'Primary (Elementary) School',
    labelPlural: 'Primary (Elementary) Schools',
    active: true,
  },
  ER: {
    code: 'ER',
    name: 'religiousEducation',
    label: 'Preschool',
    labelPlural: 'Preschools',
    active: true,
  },
  ES: {
    code: 'ES',
    name: 'secondarySchool',
    label: 'Secondary School',
    labelPlural: 'Secondary Schools',
    active: true,
  },
  ESC: {
    code: 'ESC',
    name: 'completeSecondarySchool',
    label: 'Complete Secondary School',
    labelPlural: 'Complete Secondary Schools',
    active: true,
  },
  ESCB: {
    code: 'ESCB',
    name: 'completeSecondaryBoardingSchool',
    label: 'Complete Secondary Boarding School',
    labelPlural: 'Complete Secondary Boarding Schools',
    active: true,
  },
  ESCK: {
    code: 'ESCK',
    name: 'completeSecondaryK12School',
    label: 'Complete Secondary (K-12) School',
    labelPlural: 'Complete Secondary (K-12) Schools',
    active: true,
  },
  ESP: {
    code: 'ESP',
    name: 'partialSecondarySchool',
    label: 'Partial Secondary School',
    labelPlural: 'Partial Secondary Schools',
    active: true,
  },
  ESPK: {
    code: 'ESPK',
    name: 'partialSecondaryK10School',
    label: 'Partial Secondary (K-10) School',
    labelPlural: 'Partial Secondary (K-10) Schools',
    active: true,
  },
  ET: {
    code: 'ET',
    name: 'tertiarySchool',
    label: 'Tertiary School',
    labelPlural: 'Higher Education',
    active: true,
  },
  ETB: {
    code: 'ETB',
    name: 'collegeOrUniversity',
    label: 'College or University',
    labelPlural: 'Colleges & Universities',
    active: true,
  },
  ETBC: {
    code: 'ETBC',
    name: 'college',
    label: 'College (no post grad)',
    labelPlural: 'Colleges (no post grad)',
    active: true,
  },
  ETBG: {
    code: 'ETBG',
    name: 'university',
    label: 'University (post grad)',
    labelPlural: 'Universities (post grad)',
    active: true,
  },
  ETJ: {
    code: 'ETJ',
    name: 'juniorCollege',
    label: 'Junior College (no-degree)',
    labelPlural: 'Junior Colleges (no-degree)',
    active: true,
  },
  EW: {
    code: 'EW',
    name: 'workTrainingSchool',
    label: 'Worker Training School',
    labelPlural: 'Worker Training Schools',
    active: true,
  },
  F: {
    code: 'F',
    name: 'foodIndustry',
    label: 'Food Industry',
    labelPlural: 'Food Industries',
    active: true,
  },
  M: {
    code: 'M',
    name: 'medicalInstitution',
    label: 'Medical Institution',
    labelPlural: 'Medical',
    active: true,
  },
  MC: {
    code: 'MC',
    name: 'clinic',
    label: 'Clinic',
    labelPlural: 'Clinics',
    active: true,
  },
  MCA: {
    code: 'MCA',
    name: 'airplaneOrAirBase',
    label: 'Airplane or Air Base',
    labelPlural: 'Airplanes & Air Bases',
    active: true,
  },
  MCD: {
    code: 'MCD',
    name: 'dentalClinic',
    label: 'Dental Clinic',
    labelPlural: 'Dental Clinics',
    active: true,
  },
  MCM: {
    code: 'MCM',
    name: 'mobileClinic',
    label: 'Mobile Clinic',
    labelPlural: 'Mobile Clinics',
    active: true,
  },
  MH: {
    code: 'MH',
    name: 'hospital',
    label: 'Hospital',
    labelPlural: 'Hospitals',
    active: true,
  },
  ML: {
    code: 'ML',
    name: 'healthEducationAndLifestyleCenter',
    label: 'Health Education and Lifestyle Center',
    labelPlural: 'Health Education and Lifestyle Centers',
    active: true,
  },
  MN: {
    code: 'MN',
    name: 'healthCareCorporation',
    label: 'HealthCare Corporation',
    labelPlural: 'HealthCare Corporations',
    active: true,
  },
  MO: {
    code: 'MO',
    name: 'orphange',
    label: 'Orphanage',
    labelPlural: 'Orphanages',
    active: true,
  },
  MR: {
    code: 'MR',
    name: 'nursingHomeOrRetirementCenter',
    label: 'Nursing Home or Ret. Center',
    labelPlural: 'Nursing H. & Ret. Centers',
    active: true,
  },
  O: {
    code: 'O',
    name: 'mediaServices',
    label: 'Media Services',
    labelPlural: 'Media Services',
    active: true,
  },
  OB: {
    code: 'OB',
    name: 'broadcastMinistry',
    label: 'Broadcast Ministry',
    labelPlural: 'Broadcast Ministries',
    active: true,
  },
  OM: {
    code: 'OM',
    name: 'mediaCenter',
    label: 'Media Center',
    labelPlural: 'Media Centers',
    active: true,
  },
  OR: {
    code: 'OR',
    name: 'radioOrTVStation',
    label: 'Radio or TV Station',
    labelPlural: 'Radio and TV Stations',
    active: true,
  },
  ORR: {
    code: 'ORR',
    name: 'radioStation',
    label: 'Radio Station',
    labelPlural: 'Radio Stations',
    active: true,
  },
  ORT: {
    code: 'ORT',
    name: 'televisionStation',
    label: 'Television Station',
    labelPlural: 'Television Stations',
    active: true,
  },
  P: {
    code: 'P',
    name: 'publishingHouse',
    label: 'Publishing House',
    labelPlural: 'Publishing Houses',
    active: true,
  },
  S: {
    code: 'S',
    name: 'supportServices',
    label: 'Administrative Sub-Unit',
    labelPlural: 'Administrative Sub-Units',
    active: true,
  },
  SD: {
    code: 'SD',
    name: 'department',
    label: 'Department',
    labelPlural: 'Deparments',
    active: true,
  },
  SM: {
    code: 'SM',
    name: 'ministry',
    label: 'Ministry',
    labelPlural: 'Ministries',
    active: true,
  },
  SS: {
    code: 'SS',
    name: 'service',
    label: 'Service',
    labelPlural: 'Services',
    active: true,
  },
  X: {
    code: 'X',
    name: 'miscellaneous',
    label: 'Miscellaneous',
    labelPlural: 'Miscellaneous Entities',
    active: true,
  },
  XAC: {
    code: 'XAC',
    name: 'corporation',
    label: 'Corporation',
    labelPlural: 'Corporations',
    active: true,
  },
  XB: {
    code: 'XB',
    name: 'bookstore',
    label: 'Bookstore',
    labelPlural: 'Bookstores',
    active: true,
  },
  XC: {
    code: 'XC',
    name: 'campOrConferenceCenter',
    label: 'Camp or Conference Center',
    labelPlural: 'Camps & Conference Centers',
    active: true,
  },
  XE: {
    code: 'XE',
    name: 'englishLanguageSchool',
    label: 'English Language School',
    labelPlural: 'English Language Schools',
    active: true,
  },
  XG: {
    code: 'XG',
    name: 'guestHouse',
    label: 'Guest House',
    labelPlural: 'Guest Houses',
    active: true,
  },
  XH: {
    code: 'XH',
    name: 'healthFoodStore',
    label: 'Health Food Store',
    labelPlural: 'Health Food Stores',
    active: true,
  },
  XI: {
    code: 'XI',
    name: 'miscellaneousInstitution',
    label: 'Miscellaneous Institution',
    labelPlural: 'Miscellaneous Institutions',
    active: true,
  },
  XL: {
    code: 'XL',
    name: 'library',
    label: 'Library',
    labelPlural: 'Libraries',
    active: true,
  },
  XM: {
    code: 'XM',
    name: 'medicalClinic',
    label: 'Community Center',
    labelPlural: 'Community Centers',
    active: true,
  },
  XT: {
    code: 'XT',
    name: 'thriftStore',
    label: 'Thrift Store',
    labelPlural: 'Thrift Stores',
    active: true,
  },
};
