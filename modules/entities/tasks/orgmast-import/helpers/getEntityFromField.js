import { toUnixDate } from '#utils/dates.js';
import Entity from '../../../models/Entity.js';
import getAncestorsList from '../../../helpers/getAncestorsList.js';
import { languagesMap } from '../config/languages.js';
import { entityTypesMap } from '../config/types.js';
import getCountryCodeByName from './getCountryCodeByName.js';
import importEntity from './importEntity.js';

export default async function getEntityFromField({
  fieldsMap,
  fieldsCodeMap,
  id,
  stats = { importCount: 0, skippedCount: 0, updateCount: 0, deleteCount: 0 },
  countriesMap,
  lastSync,
} = {}) {
  const field = fieldsMap?.[id];

  if (!field) return;

  let localId;

  const {
    Active, // -> enabled
    Name, // -> name
    EntityID, // -> importID
    Code, // -> parent field code (only in yearbook entities)
    ParentCode, // -> parent code, like "GC", "NAD", "SAD", etc. (as a fallback for yearbook entities or admin fields without ParentEntityID)
    OfficialAbr, // -> shortName
    ParentEntityID, // -> parent entity ID (only in admin fields)
    AltName, // ->  shortName (if less than 20 characters)
    EntityType, // -> type
    Language, // -> language
    SAddr1, // -> address street
    SAddr2, // -> address additionalAddress
    SCity, // -> address city
    SStateProv, // -> address state
    SPostalCode, // -> address zip
    SCountry, // -> address country
    MAddr1, // -> mail address street
    MAddr2, // -> mail address additionalAddress
    MCity, // -> mail address city
    MStateProv, // -> mail address state
    MPostalCode, // -> mail address zip
    MCountry, // -> mail address country
    Website, // -> siteURL
    Email, // -> email (<EMAIL>)
    EmailName, // -> emailMame ("John Doe" or "Secretary to the President")
    Phone, // -> phone
    // CtryCode, // -> country code
    Fax, // -> fax
    Lat, // -> latitude
    Long, // -> longitude
    DateChanged, // -> updatedAt
    // DateActivated, // -> ??
    DateDeActivated, // -> ??
  } = field;

  // Get parent entity ID from ParentEntityID (admin fields should have it), or from fieldsCodeMap using the ParentCode (yearbook entities should have it)
  const parentEntityId =
    ParentEntityID || fieldsCodeMap?.[ParentCode] || undefined;

  // Get parent entity first (and its ancestors recursivelly) if it exists.
  const parentId = parentEntityId
    ? await getEntityFromField({
        fieldsMap,
        id: parentEntityId,
        stats,
        countriesMap,
        lastSync,
      })
    : undefined; // if no parent, return undefined

  // Check if entity is already synced
  if (!field.sync) {
    // Get parent field either by parent ID, or by parent code (AdmFieldID in Yearbook Entities)
    const parentField = parentId
      ? { id: parentId }
      : ParentCode
        ? await Entity.findOne({ code: ParentCode }) // NOTE: Keep in mind that his will not return valid data with DRY_RUN=true, as no data will be imported.
        : undefined;

    // Get parent field ID, if exists
    const parentFieldId = parentField?.id || undefined;

    // Map OrgMast Yearbook data with AWE entity data
    const baseEntity = {
      enabled: !!Active, // enable if Active
      deleted: !Active, // delete if not Active
      name: Name,
      code: Code || '',
      parent: parentFieldId,
      type: entityTypesMap[EntityType]?.name || undefined,
      language: languagesMap[Language]?.code || undefined,
      shortName: AltName?.length < 20 ? AltName : OfficialAbr || undefined,
      address: {
        street: SAddr1,
        additionalAddress: SAddr2,
        city: SCity,
        state: SStateProv,
        zip: SPostalCode,
        country: SCountry
          ? getCountryCodeByName(SCountry, countriesMap, id)
          : undefined,
      },
      mailAddress: {
        street: MAddr1,
        additionalAddress: MAddr2,
        city: MCity,
        state: MStateProv,
        zip: MPostalCode,
        country: MCountry
          ? getCountryCodeByName(MCountry, countriesMap, id)
          : undefined,
      },
      location: {
        type: 'Point',
        coordinates: [Long ?? 0, Lat ?? 0],
      },
      siteURL: Website,
      email: Email,
      emailName: EmailName,
      phone: Phone,
      fax: Fax,
    };

    // Get ancestors list if parent field exists
    if (parentFieldId) {
      const ancestors = await getAncestorsList(parentFieldId);
      baseEntity.ancestors = ancestors;
    }

    // Use DateChanged if Active, otherwise use DateDeActivated
    const updatedAt = Active ? DateChanged : DateDeActivated;

    localId = await importEntity({
      externalId: EntityID,
      data: baseEntity,
      stats,
      updatedAt: updatedAt ? toUnixDate(new Date(updatedAt)) : undefined, // convert to Unix date if DateChanged or DateDeActivated exists
      lastSync,
    });

    // Set sync to true to avoid importing the same entity again
    field.sync = true;
  }

  return localId;
}
