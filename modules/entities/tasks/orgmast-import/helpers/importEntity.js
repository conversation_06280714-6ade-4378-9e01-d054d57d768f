import isEmpty from 'lodash/isEmpty.js';
import uniqueId from 'lodash/uniqueId.js';

import Logger from '#utils/logger.js';
import { slugify } from '#utils/strings.js';
import { toUnixDate } from '#utils/dates.js';

import Entity from '../../../models/Entity.js';

import { DRY_RUN, VERBOSE, LOG_PREFIX } from '../config/constants.js';

/**
 * Import an entity from OrgMast data
 * @param {Object} options - Options object
 * @param {String} options.externalId - External ID of the entity (OrgMast's EntityID)
 * @param {Object} options.data - Entity data
 * @param {Object} options.stats - Import stats object to be updated
 * @param {Number} options.updatedAt - Updated at date (Unix timestamp)
 * @param {Number} options.lastSync - Last sync date (Unix timestamp)
 */
export default async function importEntity({
  externalId,
  data = {},
  stats = {
    importCount: 0,
    updateCount: 0,
    skippedCount: 0,
    deleteCount: 0,
  },
  updatedAt,
  lastSync,
}) {
  if (!externalId) {
    Logger.error(LOG_PREFIX, 'importEntity: externalId is required');
    return;
  }

  if (isEmpty(data)) {
    Logger.error(
      LOG_PREFIX,
      `importEntity: data is required! externalId: ${externalId}`
    );
    return;
  }

  // Define localId variable to be returned
  let localId;

  // Define import ID for matching existing entities
  const importID = {
    type: 'orgmast',
    recordID: externalId,
  };

  // Check if entity already exists
  const existingEntity = lastSync
    ? await Entity.findOne({ importIDs: { $elemMatch: importID } })
    : null;

  // Get existing entity's updatedAt date for orgmast import ID
  const existingUpdatedAt = existingEntity?.importIDs.find(
    (i) => i.type === 'orgmast'
  )?.updatedAt;

  // If imported record hasn't been updated since last sync, skip it
  if (
    existingUpdatedAt &&
    lastSync &&
    toUnixDate(existingUpdatedAt) <= lastSync
  ) {
    stats.skippedCount += 1;
    if (VERBOSE) {
      Logger.info(
        LOG_PREFIX,
        `Skipping entity with external ID ${externalId} and local ID ${existingEntity.id} (not updated)`
      );
    }

    localId = existingEntity.id;

    return localId;
  }

  // Update importId date to be the same as the updatedAt date
  importID.updatedAt = updatedAt
    ? new Date(updatedAt * 1000) // Convert from Unix timestamp back to JS date (in milliseconds)
    : new Date();

  // If entity already exists, just update it
  if (existingEntity) {
    // Filter out existing OrgMast import ID from the existing entity's import IDs to prevent duplicates (only one per type)
    const otherImportIDs =
      existingEntity.importIDs?.filter((i) => i.type !== 'orgmast') || [];

    const updatedEntityData = {
      ...data, // Update the entity's data
      name: existingEntity.name || data.name, // Keep the existing name if it exists (or fallback to the new name if is not there)
      officialName: data.name, // Update the official name to the entity's name as is imported from OrgMast
      importIDs: [...otherImportIDs, importID], // Add the import ID
    };

    // Update the existing entity
    if (DRY_RUN) {
      if (VERBOSE) {
        Logger.info(
          LOG_PREFIX,
          `Would update entity with external ID ${externalId} and local ID ${existingEntity.id}`
        );
      }
    } else {
      await existingEntity.updateOne(updatedEntityData);
    }

    // Update the new entity's ID in the localId variable to be returned
    localId = existingEntity.id;

    // Increment the stats's update count
    stats.updateCount += 1;

    // Ensure updatedIds array exists
    if (!stats.updatedIds) {
      stats.updatedIds = [];
    }

    // Add the existing entity's ID to the updatedIds array in stats
    stats.updatedIds.push(existingEntity.id);

    if (VERBOSE) {
      Logger.info(
        LOG_PREFIX,
        `Updated entity with external ID ${externalId} and local ID ${existingEntity.id} (name: ${data.name}, parent ID: ${data.parentId})`
      );
    }
  } else {
    // Otherwise, create a new entity
    try {
      // Generate slug based on entity name
      let slug = slugify(data.name);

      // Ensure entity has a valid slug
      slug = await Entity.getAvailableSlug(slug);

      const newEntityData = {
        ...data,
        name: data.name, // Set the local name to the entity's name (this can be changed by the users from the backend if needed)
        officialName: data.name, // Set the official name to the entity's name (this can only be changed if is changed in OrgMast)
        importIDs: [importID], // Add the import ID
        slug,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      if (DRY_RUN) {
        localId = uniqueId('fake-entity-');

        if (VERBOSE) {
          Logger.info(
            LOG_PREFIX,
            `Would import entity with external ID ${externalId} (${newEntityData.name}) and parent ID ${newEntityData.parentId} as new entity with local ID ${localId}`
          );
        }
      } else {
        // Create the new entity
        const newEntity = await Entity.create(newEntityData);

        // Update the new entity's ID in the localId variable to be returned
        localId = newEntity.id;
      }

      // Increment the stats's import count
      stats.importCount += 1;

      if (VERBOSE) {
        Logger.success(
          LOG_PREFIX,
          `Successfully imported entity. ID: ${externalId} (${data.name})`
        );
      }
    } catch (error) {
      Logger.error(
        LOG_PREFIX,
        `Error importing entity. external ID: ${externalId} (${data.name}). Error: ${error}`
      );
    }
  }

  return localId;
}
