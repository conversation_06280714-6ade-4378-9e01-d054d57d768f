import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const instanceSchema = SchemaFactory({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  url: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  isMain: {
    type: Boolean,
    default: false,
  },
  // The token is used to authenticate the instance (client token)
  token: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
});

instanceSchema.index({ name: 1, url: 1 });

// Only one main instance is allowed
instanceSchema.index(
  { isMain: 1 },
  { unique: true, partialFilterExpression: { isMain: true } }
);

export default mongoose.model('Instance', instanceSchema);
