import instanceServices from '../services/instanceServices.js';

export const getList = async (req, res) => {
  const { query } = req;
  const { statuses, limit, page, skip, sort, order, search } = query;

  const { instances, count, error } = await instanceServices.getInstances({
    statuses,
    limit,
    page,
    skip,
    sort,
    order,
    search,
  });

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json({ items: instances, count });
};

export const getItem = async (req, res) => {
  const { id } = req.params;
  const { instance, error } = await instanceServices.getInstance({ id });

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json(instance);
};

export const createItem = async (req, res) => {
  const { body } = req;
  const { name, url, token, isMain } = body || {};

  const { instance, error } = await instanceServices.createInstance({
    data: { name, url, token, isMain },
  });

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(201).json(instance);
};

export const updateItem = async (req, res) => {
  const { body, params } = req;
  const { id } = params || {};
  const { name, url, token, isMain } = body || {};

  if (!id) {
    return res
      .status(400)
      .json({ error: 'Instance not found. Missing id parameter.' });
  }

  const { instance, error } = await instanceServices.updateInstance({
    id,
    data: { name, url, token, isMain },
  });

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json(instance);
};

export const deleteItem = async (req, res) => {
  const { id } = req.params;

  const { instance, error } = await instanceServices.toggleDeleteInstance({
    id,
  });

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json({ instance });
};

export const restoreItem = async (req, res) => {
  const { id } = req.params;

  const { instance, error } = await instanceServices.toggleDeleteInstance({
    id,
  });

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json(instance);
};

export const disableItem = async (req, res) => {
  const { id } = req.params;

  const { instance, error } = await instanceServices.toggleEnabledInstance({
    id,
  });

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json(instance);
};

export const enableItem = async (req, res) => {
  const { id } = req.params;
  const { instance, error } = await instanceServices.toggleEnabledInstance({
    id,
  });

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json(instance);
};

export default {
  getList,
  getItem,
  createItem,
  updateItem,
  deleteItem,
  restoreItem,
  disableItem,
  enableItem,
};
