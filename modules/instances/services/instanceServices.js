import getCollationFilter from '#utils/api/collation/filter.js';
import getListFilters from '#utils/api/list/filters.js';
import Instance from '../models/Instance.js';

/*
 * Returns a list of instances
 *
 * @param {Object} params - The params object
 * @param {Number} params.limit - The number of instances to return
 * @param {Number} params.page - The page number
 * @param {Number} params.skip - The number of instances to skip
 * @param {String} params.sort - The field to sort by
 * @param {String} params.order - The order to sort by
 * @param {String} params.search - The search term
 * @param {String} params.language - The language to use for sorting
 * @returns {Object} - { instances, error } - The instances array, or an error
 */
export async function getInstances({
  statuses,
  limit,
  page,
  skip,
  sort,
  search,
  language = 'en',
}) {
  const filters = getListFilters({
    statuses,
    limit,
    page,
    skip,
    sort,
    sortFields: ['title'],
    search,
    searchFields: ['title', 'subtitle', 'description'],
  });

  const matchFilters = {
    source: null,
    $and: [filters.statuses, filters.search],
  };

  const collationFilters = getCollationFilter({ language });

  const instancesAggregate = Instance.aggregate()
    .match(matchFilters)
    .addFields({
      id: '$_id',
    })
    .project({
      id: 1,
      name: 1,
      url: 1,
      token: 1,
      isMain: 1,
      enabled: 1,
      deleted: 1,
    })
    .sort(filters.sort)
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit) // NOTE: limit must be after skip!
    .collation(collationFilters);

  try {
    const instances = await instancesAggregate;

    const countResults = await Instance.aggregate()
      .match(matchFilters)
      .count('count');

    const count = countResults[0]?.count ?? 0;

    return { instances, count };
  } catch (error) {
    return { error: error.message };
  }
}

/*
 * Returns a single instance by id
 *
 * @param {Object} params - The params object
 * @param {String} params.id - The id of the instance
 * @returns {Object} - { instance, error } - The instance, or an error
 */
export async function getInstance({ id }) {
  const instance = await Instance.findById(id);

  if (!instance) {
    return { error: 'Instance not found' };
  }

  return { instance };
}

/*
 * Creates a new instance
 *
 * @param {Object} params - The params object
 * @param {String} params.name - The name of the instance
 * @param {String} params.url - The url of the instance
 * @param {String} params.token - The token of the instance
 * @returns {Object} - { instance, error } - The created instance, or an error
 */
export async function createInstance({ data }) {
  const { name, url, token, isMain } = data || {};

  if (!name || !url || !token) {
    return { error: `createInstance: Missing required fields: ${data}` };
  }

  try {
    const instance = await Instance.create({
      name,
      url,
      token,
      isMain,
    });

    return { instance };
  } catch (error) {
    return { error: error.message };
  }
}

/*
 * Updates an instance
 *
 * @param {Object} params - The params object
 * @param {String} params.id - The id of the instance
 * @param {Object} params.data - The instance data to update
 * @returns {Object} - { instance, error } - The updated instance, or an error
 */
export async function updateInstance({ id, data }) {
  try {
    const instance = await Instance.findByIdAndUpdate(id, data, {
      new: true,
      runValidators: true,
    });

    return { instance };
  } catch (error) {
    return { error: error.message };
  }
}

/*
 * Toggles the enabled status of an instance
 *
 * @param {Object} params - The params object
 * @param {String} params.id - The id of the instance
 * @returns {Object} - { instance, error } - The enabled/disabled instance, or an error
 */
export async function toggleEnabledInstance({ id }) {
  const { instance } = await getInstance({ id });

  if (!instance) {
    return { error: `toggleEnabledInstance: Instance not found ${id}` };
  }

  try {
    const updatedInstance = await Instance.findByIdAndUpdate(
      id,
      { enabled: !instance.enabled },
      { new: true, runValidators: true }
    );

    return { instance: updatedInstance };
  } catch (error) {
    return { error: error.message };
  }
}

/*
 * Toggles the deleted status of an instance
 *
 * @param {Object} params - The params object
 * @param {String} params.id - The id of the instance
 * @returns {Object} - { instance, error } - The (soft) deleted/recovered instance, or an error
 */
export async function toggleDeleteInstance({ id }) {
  const { instance } = await getInstance({ id });

  if (!instance) {
    return { error: `toggleDeleteInstance: Instance not found ${id}` };
  }

  try {
    const updatedInstance = await Instance.findByIdAndUpdate(
      id,
      { deleted: !instance.deleted },
      {
        new: true,
        runValidators: true,
      }
    );

    return { instance: updatedInstance };
  } catch (error) {
    return { error: error.message };
  }
}

export default {
  getInstances,
  getInstance,
  createInstance,
  updateInstance,
  toggleEnabledInstance,
  toggleDeleteInstance,
};
