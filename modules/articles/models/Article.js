import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';
import { PersonBioRoleSchema } from '#modules/persons/models/Person.js';

// Schema for the sites attribute
const ArticleSiteSchema = new mongoose.Schema({
  enabled: { type: Boolean, required: true },
  flags: { type: Map, of: Boolean },
  slug: { type: String }, // @deprecated: Use slugOverrides instead
  startsAt: { type: Date, required: true },
  endsAt: { type: Date },
  slugOverrides: { type: Object },
});

const LinkedEpisodeSchema = new mongoose.Schema({
  episode: {
    type: mongoose.Types.ObjectId,
    ref: 'Episode',
  },
  defaultItem: {
    type: Boolean,
    default: false,
  },
});

const LinkedPublicationIssueSchema = new mongoose.Schema({
  publicationIssue: {
    type: mongoose.Types.ObjectId,
    ref: 'PublicationIssue',
  },
  publication: {
    type: mongoose.Types.ObjectId,
    ref: 'Publication',
  },
  defaultItem: {
    type: Boolean,
    default: false,
  },
});

/**
 * Reference to a bible verse:
 * Example: [ { book: 'Genesis', chaptersWithVerses: { 1: [1, 2], 2: [3] } } ]
 */
const BibleReferenceSchema = new mongoose.Schema({
  book: {
    type: String,
    enum: [
      'Genesis',
      'Exodus',
      'Leviticus',
      'Numbers',
      'Deuteronomy',
      'Joshua',
      'Judges',
      'Ruth',
      '1Samuel',
      '2Samuel',
      '1Kings',
      '2Kings',
      '1Chronicles',
      '2Chronicles',
      'Ezra',
      'Nehemiah',
      'Esther',
      'Job',
      'Psalm',
      'Proverbs',
      'Ecclesiastes',
      'SongOfSolomon',
      'Isaiah',
      'Jeremiah',
      'Lamentations',
      'Ezekiel',
      'Daniel',
      'Hosea',
      'Joel',
      'Amos',
      'Obadiah',
      'Jonah',
      'Micah',
      'Nahum',
      'Habakkuk',
      'Zephaniah',
      'Haggai',
      'Zechariah',
      'Malachi',
      'Matthew',
      'Mark',
      'Luke',
      'John',
      'Acts',
      'Romans',
      '1Corinthians',
      '2Corinthians',
      'Galatians',
      'Ephesians',
      'Philippians',
      'Colossians',
      '1Thessalonians',
      '2Thessalonians',
      '1Timothy',
      '2Timothy',
      'Titus',
      'Philemon',
      'Hebrews',
      'James',
      '1Peter',
      '2Peter',
      '1John',
      '2John',
      '3John',
      'Jude',
      'Revelation',
    ],
    required: true,
  },
  chaptersWithVerses: {
    type: Map,
    of: [Number],
    required: true,
  },
});

// valid statuses for an article
export const statuses = ['draft', 'done', 'approved'];

// Schema for the article model
const articleSchema = SchemaFactory(
  {
    // Main article title (free string)
    title: {
      type: String,
      trim: true,
    },

    // Type of article (string from a predefined list)
    type: {
      type: String, // see options at modules/articles/services/articleConfigServices.js
      trim: true,
    },

    // An unique string for indentifing the article in URLs. Based on the title but can be overridden by the user.
    slug: {
      type: String,
      trim: true,
    },

    // Secondary title for the article (free string)
    subtitle: {
      type: String,
      trim: true,
    },

    // Short text that summarizes the article
    abstract: {
      type: String,
      trim: true,
    },

    // Pull quote (short text that is highlighted in the article)
    pullQuote: {
      type: String,
      trim: true,
    },

    // Article main body text (Tiptap/ProseMirror JSON, see https://tiptap.dev/)
    body: {
      type: mongoose.SchemaTypes.Mixed,
    },

    // Article extra content (Tiptap/ProseMirror JSON, see https://tiptap.dev/)
    extraContent: {
      type: mongoose.SchemaTypes.Mixed,
    },

    // Cover image for the article
    image: {
      type: mongoose.SchemaTypes.Mixed,
    },

    // Article main video (if any)
    video: {
      type: mongoose.SchemaTypes.Mixed,
    },

    // Article location (name, placeName, coordinates)
    location: {
      name: String, // short name. e.g. "Viale"
      nameOverride: String, // short name override. e.g. "Viale City"
      context: {
        address: String, // e.g. "25 de Mayo 807"
        place: String, // e.g. "Viale"
        region: String, // e.g. "Entre Ríos"
        country: String, // e.g. "Argentina"
      },
      placeName: String, // long name. e.g. "Viale, Entre Ríos, Argentina"
      coordinates: [Number, Number], // geoppoint with [lat, long]. E.g. [-31.8680222,-60.007516]
      boundingBox: [Number, Number, Number, Number], // e.g. [-31.8680222,-60.007516,-31.8680222,-60.007516]
      bearing: Number, // value between 0 and 360 (default 0 is looking north, and 90 is looking east, etc.)
      pitch: Number, // value between 0 and 60 (default 0 is looking straight down at the map, and 60 is looking at the horizon)
      zoom: Number, // value between 0 and 20
    },

    // Old article legacy location (free string)
    // @deprecated: Use location instead // NOTE: Delete this field after migration
    oldLocation: {
      type: String,
      trim: true,
    },

    // Article organizations (entities) that is referenced in the article
    organizations: {
      type: [mongoose.Types.ObjectId],
      ref: 'Entity',
    },

    // Article author's name (free string), that may be different from the user who authored the article
    author: {
      type: String, // e.g. "John Doe"
      trim: true,
    },

    // Article author's email address (of the 'author' field above)
    authorEmail: {
      type: String,
      trim: true,
    },

    // Author(s) that authored the article (array of author-role objects)
    authors: {
      type: [PersonBioRoleSchema],
    },

    // Language of the article (ISO 639-1 code)
    language: {
      type: String, // e.g. 'en', 'es', 'es-ar', 'pt-br', etc.
      trim: true,
    },

    // Indicates if the article is a translation of another article (source)
    translationOf: {
      type: mongoose.Types.ObjectId,
      ref: 'Article',
      default: null,
    },

    // Related articles
    related: [
      {
        type: mongoose.Types.ObjectId,
        ref: 'Article',
      },
    ],

    // Categories the article belongs to, used for filtering and grouping in the frontend
    categories: {
      type: [mongoose.Types.ObjectId],
      ref: 'Category',
      default: [],
    },

    // Tags for the article for internal use in the backend
    tags: {
      type: [String],
      default: [],
    },

    // Entity the article belongs to
    entity: {
      type: mongoose.Types.ObjectId,
      ref: 'Entity',
      required: true,
    },

    // Sites where the article is published
    // TODO: Remove this field!!
    // @deprecated: Use ArticleSiteSchema instead
    sites: {
      type: Map,
      of: ArticleSiteSchema,
      default: {},
    },

    // Indicates if the article shouldn't be shared by default.
    // - When falsy, it will be shared.
    // - When true, it will NOT be shared.
    doNotShare: {
      type: Boolean,
    },

    // Indicates if the article shouldn't be translated by other entities.
    // - When falsy, it will be allowed to be translated.
    // - When true, it will NOT be allowed to be translated.
    disableTranslations: {
      type: Boolean,
    },

    // Keeps track of the entities where the article has been syndicated in.
    // This will also allow to show syndicated articles alongside local articles.
    syndicatedIn: {
      type: [mongoose.Types.ObjectId],
      ref: 'Entity',
      default: [],
    },

    // Indicates if the record is a revision of another article
    revisionOf: {
      type: mongoose.Types.ObjectId,
      ref: 'Article',
      default: null,
    },

    // Status of the article/revision ('draft', 'done' or 'approved')
    status: {
      type: String,
      enum: statuses,
      default: 'draft',
    },

    // The user who authored the article/revision
    authoredBy: {
      type: mongoose.Types.ObjectId,
      ref: 'User',
    },

    // The user who approved the article/revision
    approvedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'User',
    },

    // Indicate the article's last revision (if any)
    latestRevision: {
      type: mongoose.Types.ObjectId,
      ref: 'Article',
    },

    // Latest revision status (stored here to facilitate searching)
    latestRevisionStatus: {
      type: String,
      enum: statuses,
    },

    // Latest revision date (equal to revision's createdAt, and stored here to facilitate searching)
    latestRevisionAt: {
      type: Date,
    },

    // Stats about the article's translations
    translationsStats: {
      total: { type: Number }, // Total number of translations
      draft: { type: Number }, // Number of translations in draft status
      done: { type: Number }, // Number of translations in done status
      approved: { type: Number }, // Number of translations in approved status
      languages: [{ type: String }], // [ 'de', 'es', 'pt-br', 'fr', 'sw' ] etc.
      languagesDraft: [{ type: String }], // [ 'pt-br', 'es' ] etc.
      languagesDone: [{ type: String }], // [ 'de', 'es', 'fr' ] etc.
      languagesApproved: [{ type: String }], // [ 'de', 'es' ] etc.
    },

    // @deprecated: Use canonicalSitePage instead
    canonicalSite: {
      type: mongoose.Types.ObjectId,
      ref: 'Site',
    },

    // Canonical site page: reference to the AWE page that is the canonical version of this article
    canonicalSitePage: {
      type: mongoose.Types.ObjectId,
      ref: 'Page',
    },

    // Canonical URL: URL of the canonical version of this article
    canonicalUrl: {
      type: String,
      trim: true,
    },

    // Ids of the articles imported from other systems (e.g. Wordpress, Drupal, etc.)
    importIDs: {
      type: [mongoose.SchemaTypes.Mixed],
    },

    // Page ID of articles imported from TYPO3
    importPid: {
      type: String,
    },

    // Files attached to the article
    files: {
      type: mongoose.SchemaTypes.Mixed,
    },

    // Article bible references (to find articles that talk about a specific verse)
    bibleReferences: {
      type: [BibleReferenceSchema],
    },

    // Connected episodes
    linkedEpisodes: {
      type: [LinkedEpisodeSchema],
    },

    // Linked publication issues
    linkedPublicationIssues: {
      type: [LinkedPublicationIssueSchema],
    },

    // Series the article belongs to
    series: {
      type: [mongoose.Types.ObjectId],
      ref: 'ArticleSeries',
      default: [],
    },
  },

  // Schema options (see https://mongoosejs.com/docs/guide.html#options)
  {
    autoIndex: true, //
  }
);

articleSchema.index({
  title: 1,
  entity: 1,
  language: 1,
  createdAt: -1,
});
articleSchema.index(
  // Index for searching articles
  // https://docs.mongodb.com/manual/text-search/
  // Create an index manually from mongodb console: db.articles.createIndex({ title: 'text', subtitle: 'text', abstract: 'text', body: 'text', pullQuote: 'text', location: 'text', author: 'text' }, { weights: { title: 10, subtitle: 7, abstract: 6, body: 8, pullQuote: 3, location: 4, author: 5 }, "default_language": "en", "language_override": "en"  })
  {
    title: 'text',
    subtitle: 'text',
    abstract: 'text',
    body: 'text',
    pullQuote: 'text',
    location: 'text',
    author: 'text',
  },
  {
    weights: {
      title: 10,
      body: 8,
      subtitle: 7,
      abstract: 6,
      author: 5,
      location: 4,
      pullQuote: 3,
    },
    language_override: 'en',
    default_language: 'en',
  }
);
articleSchema.index({
  status: 1,
  latestRevisionStatus: 1,
  deleted: 1,
  enabled: 1,
});
articleSchema.index({
  entity: 1,
  deleted: 1,
  enabled: 1,
  revisionOf: 1,
  translationOf: 1,
  status: 1,
}); // More efficient index for matches in FE API
articleSchema.index({ statuses: 1, latestRevisionStatus: 1 });
articleSchema.index({ categories: 1 });
articleSchema.index({ revisionOf: 1 });
articleSchema.index({ title: 1, slug: 1, deleted: 1, enabled: 1, status: 1 });
articleSchema.index({ translationOf: 1, status: 1, enabled: 1, deleted: 1 });
articleSchema.index({ translationOf: 1 });
articleSchema.index({
  _id: 1,
  entity: 1,
  deleted: 1,
  enabled: 1,
  revisionOf: 1,
  translationOf: 1,
  status: 1,
});
articleSchema.index({
  deleted: 1,
  enabled: 1,
  revisionOf: 1,
  translationOf: 1,
  entity: 1,
  language: 1,
  status: 1,
});

// FE: Get articles by slug
articleSchema.index({
  slug: 1,
  enabled: 1,
  deleted: 1,
  status: 1,
  revisionOf: 1,
  createdAt: 1,
});

// BE: Articles list
articleSchema.index({
  revisionOf: 1,
  translationOf: 1,
  deleted: 1,
  enabled: 1,
  entity: 1,
  createdAt: -1,
});
// BE: Articles list (with syndication)
articleSchema.index({
  revisionOf: 1,
  translationOf: 1,
  deleted: 1,
  enabled: 1,
  syndicatedIn: 1,
  createdAt: -1,
});

articleSchema.index({ authoredBy: 1 });
articleSchema.index({ approvedBy: 1 });
articleSchema.index({ latestRevision: 1 });
articleSchema.index({ importPid: 1 });
articleSchema.index({ tags: 1 });
articleSchema.index({ 'translationsStats.languagesApproved.$**': 1 }); // Index all fields in the translationsStats.languagesApproved array

articleSchema.index({ slug: 1, status: 1 }); // Index for checking slugs by status
articleSchema.index({ canonicalSitePage: 1 });

export default mongoose.model('Article', articleSchema);
