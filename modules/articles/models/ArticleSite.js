import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

// Schema to allow publishing articles on a site
const articleSiteSchema = SchemaFactory({
  article: {
    type: mongoose.Types.ObjectId,
    ref: 'Article',
    required: true,
  },
  site: {
    type: mongoose.Types.ObjectId,
    ref: 'Site',
    required: true,
  },

  // Indicates if the article is enabled on the site
  enabled: {
    type: Boolean,
  },

  // Date range for the article to be published on the site
  // - From date
  startsAt: {
    type: Date,
    required: true,
  },
  // - To date
  endsAt: {
    type: Date,
    default: null,
  },

  // Site flags (e.g. { isFeatured: true, isBreaking: false })
  flags: {
    type: Map,
    of: Boolean,
  },

  // Custom slugs, locale based (e.g. { en: 'slug-en', fr: 'slug-fr' })
  slugOverrides: {
    type: Object,
  },
});

// Indexes
articleSiteSchema.index({ article: 1, site: 1 }, { unique: true });
articleSiteSchema.index({ site: 1, enabled: 1, startsAt: 1 });
articleSiteSchema.index({ site: 1, enabled: 1, startsAt: -1, endsAt: 1 });

// Export the model
export default mongoose.model('ArticleSite', articleSiteSchema);
