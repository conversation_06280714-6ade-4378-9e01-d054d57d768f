import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

// Schema to allow publishing articles on a site
const articleSeriesSchema = SchemaFactory({
  // The name of the article series
  name: {
    type: String,
    required: true,
  },

  // A short description of the article series
  description: {
    type: mongoose.SchemaTypes.Mixed,
  },

  // Language of the article series
  language: {
    type: String,
    required: true,
  },

  // List of articles in the series
  articles: {
    type: [mongoose.Types.ObjectId],
    ref: 'Article',
    default: [],
  },

  importIDs: {
    type: [String],
    default: [],
  },

  // The entity to which the article series belongs
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
});

// Export the model
export default mongoose.model('ArticleSeries', articleSeriesSchema);
