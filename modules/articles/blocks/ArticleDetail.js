import { convertRichTextToPlainText } from '#utils/richText.js';
import { stringArrayToString } from '#utils/strings.js';

/**
 * ArticleDetail block definition
 *
 * @param {Object} options
 * @param {Object} options.site - The site associated with the block
 * @returns {Object}
 */
export function ArticleDetail() {
  return {
    name: 'ArticleDetail',
    getSearchableContent,
  };
}

/**
 * Get searchable content for ArticleDetail
 * @param {Object} options
 * @param {String} options.language - The language associated with the block
 * @param {Object} options.node - The node associated with the block
 * @param {Object} options.page - The page associated with the block
 * @param {Object} options.site - The site associated with the block
 * @returns {String}
 */
function getSearchableContent({ node }) {
  const { Article } = node?.props ?? {};

  if (!Article) {
    return '';
  }

  const { abstract, body } = Article;

  return stringArrayToString(
    [abstract, convertRichTextToPlainText(body)],
    '\n'
  );
}
