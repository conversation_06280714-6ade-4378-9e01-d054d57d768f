import Joi from 'joi';

import { plainListSchema } from '#utils/api/list/schema.js';

import { statuses } from '../models/Article.js';

import { allArticleTypes } from '../services/articlesConfigServices.js';

// For listing articles
export const articlesListSchema = Joi.object().keys({
  ...plainListSchema,
  statuses: Joi.array().items(
    Joi.string().valid(
      'draft',
      'done',
      'approved',
      'active',
      'disabled',
      'deleted'
    )
  ),
  types: Joi.array()
    .items(Joi.string().valid(...allArticleTypes))
    .optional(),
  sort: Joi.string().default('createdAt').allow('', null).optional(),
  order: Joi.string().default('desc').allow('', null).optional(),
  authors: Joi.array().items(Joi.string()).optional(),
  categories: Joi.array().items(Joi.string()).optional(),
  organizations: Joi.array().items(Joi.string()).optional(),
  notPublished: Joi.boolean().optional(),
  siteId: Joi.string().optional(),
  flag: Joi.string().allow('', null).optional(),
  flagExceptions: Joi.array().items(Joi.string()).optional(),
  useSiteEntity: Joi.boolean().optional(),
});

export const articleTypesListSchema = Joi.object().keys({
  filterAllowed: Joi.boolean().optional(),
});

// For listing articles translations
export const articlesTranslationsListSchema = Joi.object().keys({
  ...plainListSchema,
  statuses: Joi.array().items(
    Joi.string().valid(
      'draft',
      'done',
      'approved',
      'active',
      'disabled',
      'deleted'
    )
  ),
  sort: Joi.string().default('createdAt').optional(),
  order: Joi.string().default('desc').optional(),
  siteId: Joi.string().optional(),
});

// For creating or updating an article
export const articleSchema = Joi.object().keys({
  title: Joi.string().allow(''),
  slug: Joi.string().allow(''),
  type: Joi.string().allow('', null),
  subtitle: Joi.string().allow('', null),
  abstract: Joi.string().allow('', null),
  pullQuote: Joi.string().allow('', null),
  body: Joi.object(),
  extraContent: Joi.object(),
  image: Joi.object(),
  video: Joi.object(),
  location: Joi.object().keys({
    name: Joi.string().allow(''),
    nameOverride: Joi.string().allow('', null),
    context: Joi.object().keys({
      address: Joi.string().allow('', null),
      place: Joi.string().allow('', null),
      region: Joi.string().allow('', null),
      country: Joi.string().allow('', null),
    }),
    placeName: Joi.string().allow(''),
    coordinates: Joi.array().items(Joi.number()),
    boundingBox: Joi.array().items(Joi.number()),
    bearing: Joi.number(),
    pitch: Joi.number(),
    zoom: Joi.number(),
  }),
  author: Joi.string().allow('', null),
  authorEmail: Joi.string().allow('', null),
  authors: Joi.array().items(Joi.object()).allow('', null),
  language: Joi.string().allow(''),
  related: Joi.array().items(Joi.string()),
  categories: Joi.array().items(Joi.string()),
  organizations: Joi.array().items(Joi.string()),
  tags: Joi.array().items(Joi.string()),
  bibleReferences: Joi.array().items(Joi.object()).optional().allow('', null),
  files: Joi.array().items(Joi.object()),
  status: Joi.string()
    .valid(...statuses)
    .required(),
  revisionOf: Joi.string().allow('', null),
});

// Shared articles schema
export const articlesSharedSchema = Joi.object().keys({
  ...plainListSchema,
  sort: Joi.string().default('createdAt').optional(),
  order: Joi.string().default('desc').optional(),
  categories: Joi.array().items(Joi.string()).optional(),
  types: Joi.array()
    .items(Joi.string().valid(...allArticleTypes))
    .optional(),
});

// Share article schema
export const articleShareSchema = Joi.object().keys({
  doNotShare: Joi.boolean().allow('', null),
  disableTranslations: Joi.boolean().allow(null),
});

// For publishing an article
export const articlePublishSchema = Joi.object().keys({
  sites: Joi.object().required(),
  canonicalUrl: Joi.string().allow('', null),
  canonicalSitePage: Joi.string().allow('', null),
});

export const articleYearsAndMonthsSchema = Joi.object().keys({
  siteId: Joi.string(),
});

export const articleByIdQuerySchema = Joi.object().keys({
  getOriginal: Joi.boolean().default(false),
});

export const articleLinkedContentSchema = Joi.object().keys({
  sort: Joi.string().default('title').optional(),
  order: Joi.string().default('desc').optional(),
  type: Joi.array()
    .items(Joi.string().valid('episode', 'publication-issue'))
    .optional(),
});

export const articleLinkedEpisodeContentSchema = {
  channelId: Joi.string().required(),
  episodeId: Joi.string().required(),
  defaultItem: Joi.boolean().optional(),
};

export const articleLinkedPublicationIssueContentSchema = {
  publicationId: Joi.string().required(),
  publicationIssueId: Joi.string().required(),
  defaultItem: Joi.boolean().optional(),
};
