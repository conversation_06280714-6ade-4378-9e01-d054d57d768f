import Joi from 'joi';

import { validate } from '#utils/validationMiddleware.js';

import {
  articleLinkedEpisodeContentSchema,
  articleLinkedPublicationIssueContentSchema,
} from './articlesValidation.js';

export const validateUpdateArticleLinkedItem = async (req, res, next) => {
  const { type } = req.params;

  const linkedItemTypeSchema = Joi.string()
    .valid('episode', 'publication-issue')
    .required();

  if (type === 'episode') {
    const linkedEpisodeSchema = Joi.object().keys({
      ...articleLinkedEpisodeContentSchema,
    });

    return validate(linkedEpisodeSchema, 'body')(req, res, next);
  }

  if (type === 'publication-issue') {
    const linkedPublicationIssueSchema = Joi.object().keys({
      ...articleLinkedPublicationIssueContentSchema,
    });

    return validate(linkedPublicationIssueSchema, 'body')(req, res, next);
  }

  // Fallback to checking the params
  const paramsSchema = Joi.object().keys({
    ...linkedItemTypeSchema,
    id: Joi.string().required(),
    linkedItemId: Joi.string().required(),
  });

  return validate(paramsSchema, 'params')(req, res, next);
};
