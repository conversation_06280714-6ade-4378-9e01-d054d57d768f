import Joi from 'joi';

import { validate } from '#utils/validationMiddleware.js';

import {
  articleLinkedEpisodeContentSchema,
  articleLinkedPublicationIssueContentSchema,
} from './articlesValidation.js';

export const validateAddArticleLinkedItem = async (req, res, next) => {
  const { type } = req.body;

  const linkedItemTypeSchema = {
    type: Joi.string().valid('episode', 'publication-issue').required(),
  };

  if (type === 'episode') {
    const linkedEpisodeSchema = Joi.object().keys({
      ...linkedItemTypeSchema,
      ...articleLinkedEpisodeContentSchema,
    });

    return validate(linkedEpisodeSchema, 'body')(req, res, next);
  }

  if (type === 'publication-issue') {
    const linkedPublicationIssueSchema = Joi.object().keys({
      ...linkedItemTypeSchema,
      ...articleLinkedPublicationIssueContentSchema,
    });

    return validate(linkedPublicationIssueSchema, 'body')(req, res, next);
  }

  const schema = Joi.object().keys({
    ...linkedItemTypeSchema,
  });

  return validate(schema, 'body')(req, res, next);
};
