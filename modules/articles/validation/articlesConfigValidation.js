import Joi from 'joi';

// For configuring the articles module for a network or entity
export const articlesConfigSchema = Joi.object().keys({
  entity: Joi.string().optional(),
  network: Joi.string(),
  hasApprovalWorkflow: Joi.boolean(),
  authorsCanEditAfterDone: Joi.boolean(),
  translateOnlyAfterApproval: Joi.boolean(),
  disableSharing: Joi.boolean(),
  disableSyndication: Joi.boolean(),
  disableTranslationOfShared: Joi.boolean(),
  dontUseArticleTypes: Joi.boolean(),
  articleTypes: Joi.object().allow(null),
  defaultArticleType: Joi.string().allow(null),
  fields: Joi.object().allow(null),
  hasEarlyAccess: Joi.boolean(),
  earlyAccessRequired: Joi.boolean(),
  earlyAccessLabel: Joi.string().allow(null),
  hasDisplayDate: Joi.boolean(),
  hasArticleSeries: Jo<PERSON>.boolean(),
});
