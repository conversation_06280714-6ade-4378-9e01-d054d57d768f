import {
  getArticlesConfig as getArticlesConfigService,
  setArticlesConfig as setArticlesConfigService,
} from '../services/articlesConfigServices.js';

export const getArticlesConfig = async (req, res) => {
  const { entity, user } = req;

  const { data, error } = await getArticlesConfigService({
    entity,
    user,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const setArticlesConfig = async (req, res) => {
  const { body, entity, user } = req;
  const { network, ...config } = body;

  // Update article config
  const { data, error } = await setArticlesConfigService({
    entity,
    network,
    user,
    config,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export default {
  getArticlesConfig,
  setArticlesConfig,
};
