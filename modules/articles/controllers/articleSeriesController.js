import {
  getArticleSeriesList,
  getArticleSeriesById,
  createArticleSeries,
  updateArticleSeries,
  deleteArticleSeries,
  addArticlesToSeries,
  removeArticleFromSeries,
  sortArticlesInSeries,
  toggleArticleSeries,
} from '../services/articleSeriesServices.js';

/**
 * Returns a list of article series for the given entity.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const getArticleSeriesAction = async (req, res) => {
  const { entity } = req;
  const { page, limit, statuses, search, sort, skip } = req.query;

  const { items, count, error } = await getArticleSeriesList({
    entity,
    page,
    limit,
    statuses,
    search,
    sort,
    skip,
  });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json({ items, count });
};

/**
 * Returns a single article series by ID.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const getArticleSeriesByIdAction = async (req, res) => {
  const { entity } = req;
  const { id } = req.params;

  const { articleSeries, error } = await getArticleSeriesById({ entity, id });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json(articleSeries);
};

/**
 * Creates a new article series.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const createArticleSeriesAction = async (req, res) => {
  const { entity } = req;
  const { name, description, language } = req.body;

  const { articleSeries, error } = await createArticleSeries({
    entity,
    name,
    description,
    language,
  });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(201).json(articleSeries);
};

/**
 * Updates an existing article series.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const updateArticleSeriesAction = async (req, res) => {
  const { entity } = req;
  const { id } = req.params;
  const { name, description, language } = req.body;

  const updateData = {
    name,
    description,
    language,
  };

  const { articleSeries, error } = await updateArticleSeries({
    id,
    updateData,
    entity,
  });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json(articleSeries);
};

/**
 * Toggles the status of an article series.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const toggleArticleSeriesAction = async (req, res) => {
  const { entity } = req;
  const { id } = req.params;

  const { articleSeries, error } = await toggleArticleSeries({
    id,
    entity,
  });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json(articleSeries);
};

/**
 * Deletes an article series.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const deleteArticleSeriesAction = async (req, res) => {
  const { entity } = req;
  const { id } = req.params;

  const { articleSeries, error } = await deleteArticleSeries({ entity, id });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json(articleSeries);
};

/**
 * Adds an article to an article series.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const addArticlesToSeriesAction = async (req, res) => {
  const { entity } = req;
  const { id } = req.params;
  const { articlesIds } = req.body;

  const { articleSeries, error } = await addArticlesToSeries({
    entity,
    id,
    articlesIds,
  });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json(articleSeries);
};

/**
 * Removes an article from an article series.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const removeArticleFromSeriesAction = async (req, res) => {
  const { entity } = req;
  const { id } = req.params;
  const { articleId } = req.body;

  const { articleSeries, error } = await removeArticleFromSeries({
    entity,
    id,
    articleId,
  });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json(articleSeries);
};

/**
 * Sort articles in an article series.
 * @param {Object} req The request object.
 * @param {Object} res The response object.
 * @returns {Promise<void>} A promise that resolves when the response is sent.
 */
export const sortArticlesInSeriesAction = async (req, res) => {
  const { entity } = req;
  const { id } = req.params;
  const { articlesIds } = req.body;

  const { articleSeries, error } = await sortArticlesInSeries({
    id,
    articlesIds,
    entity,
  });

  if (error) {
    res.status(500).json({ error });
    return;
  }

  res.status(200).json(articleSeries);
};
