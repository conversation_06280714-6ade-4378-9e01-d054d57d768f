import factory from '#utils/handlerFactory.js';

import Article from '../models/Article.js';
import {
  addArticle,
  approveArticle as approveService,
  articlesStats,
  deleteArticle as deleteService,
  editArticle,
  getArticleById,
  getArticleYearsAndMonthsForSite,
  getAvailableSlug,
  getArticles as listService,
  publishArticle as publishService,
  revertToRevision,
  getSharedArticles as sharedListService,
  shareArticle as shareService,
  syndicateArticle as syndicateService,
  toggleArticle as toggleService,
  translateArticle as translateService,
  getArticlesTranslations as translationsListService,
  addLinkedEpisode,
  getArticleLinkedItems,
  deleteLinkedEpisode,
  updateLinkedEpisode,
  addLinkedPublicationIssue,
  updateLinkedPublicationIssue,
  deleteLinkedPublicationIssue,
} from '../services/articlesServices.js';

import {
  getArticleTypesForEntity,
  getArticlesFieldsForEntity,
} from '../services/articlesConfigServices.js';

export const getArticles = async (req, res) => {
  const { query, entity, user } = req;
  const {
    statuses,
    types,
    limit,
    page,
    sort,
    order,
    search,
    authors,
    categories,
    flag,
    flagExceptions,
    organizations,
    notPublished,
    siteId,
    skip,
    useSiteEntity,
  } = query;

  const { data, error } = await listService({
    entity,
    language: user?.preferences?.language || 'en',
    statuses,
    types,
    authors,
    categories,
    flag,
    flagExceptions,
    notPublished,
    siteId,
    skip,
    limit,
    page,
    sort,
    order,
    organizations,
    search,
    useSiteEntity,
  });

  // const articles = await Article.find({}, { _id: 1, body: 1 }).lean();

  // // count the number of articles
  // const articlesCount = articles.length;
  // console.log('articlesCount', articlesCount);

  // // Look for any articles with a body that contains the word "footnote". For this the body needs to be a string
  // const articlesWithFootnotes = articles
  //   .filter((article) => {
  //     const searchForFootnoteType = (body) => {
  //       if (typeof body === 'object' && body !== null) {
  //         if (body.type === 'footnote') {
  //           return true;
  //         }
  //         return Object.values(body).some(searchForFootnoteType);
  //       }
  //       if (Array.isArray(body)) {
  //         return body.some(searchForFootnoteType);
  //       }
  //       return false;
  //     };
  //     return searchForFootnoteType(article.body);
  //   })
  //   .map((article) => ({ id: article._id, body: article.body }));

  // console.log(
  //   'articlesWithFootnotes',
  //   JSON.stringify(articlesWithFootnotes, null, 2)
  // );

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const getArticleTypes = async (req, res) => {
  const { query, entity } = req;

  const { articleTypes, defaultArticleType, error } =
    await getArticleTypesForEntity({
      filterAllowed: query.filterAllowed,
      entity,
    });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json({
    items: articleTypes || [],
    count: articleTypes?.length || 0,
    defaultArticleType,
  });
};

/**
 * Get available fields for articles
 */
export const getArticlesFields = async (req, res) => {
  const { entity } = req;

  const { availableFields, error } = await getArticlesFieldsForEntity({
    entity,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(availableFields);
};

export const getArticlesTranslations = async (req, res) => {
  const { query, entity, user } = req;
  const { limit, page, sort, order, search, skip, siteId, statuses } = query;

  const articlesTranslations = await translationsListService({
    entity,
    language: user?.preferences?.language || 'en',
    limit,
    order,
    page,
    search,
    siteId,
    skip,
    sort,
    statuses,
  });

  res.status(200).json(articlesTranslations);
};

export const getSharedArticles = async (req, res) => {
  const { query, entity, user } = req;
  const { limit, page, sort, order, search, categories, skip, types } = query;

  const { data, error } = await sharedListService({
    entity,
    language: user?.preferences?.language || 'en',
    categories,
    skip,
    limit,
    page,
    sort,
    order,
    search,
    types,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const getArticlesStats = async (req, res) => {
  const data = await articlesStats({ entityId: req.entity._id });
  res.status(200).json(data);
};

export const getUtf8Bug = async (req, res) => {
  const article = {
    slug: 'glueckskinder',
    title: 'Glückskinder',
    abstract:
      'Kinder sind Meister der Lebensfreude. Eine Doku darüber, wie Kinder rund um die Welt Freude und Glück erleben.',
    body: {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: 'Kinder sind Meister der Lebensfreude. Eine Dokumentation darüber, wie Kinder rund um die Welt Freude und Glück erleben.',
            },
          ],
        },
        {
          type: 'paragraph',
          content: [
            {
              type: 'text',
              text: 'Produziert von Adrian Duré.',
            },
          ],
        },
      ],
    },
    createdAt: '2023-05-19T07:59:37.000Z',
    updatedAt: '2023-05-26T09:45:04.803Z',
  };

  res.status(200).json(article);
};

export const createArticle = async (req, res) => {
  const { body, entity, user } = req;

  // Handle the article's slug
  if (body.slug) {
    body.slug = await getAvailableSlug({ slug: body.slug, entity: entity.id });
  }

  // Create article
  const { data, error } = await addArticle({ body, entity, user });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export const getArticle = async (req, res) => {
  const { entity } = req;
  const { getOriginal } = req.query;
  const { id } = req.params;

  const { data, error } = await getArticleById({ id, entity, getOriginal });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

// Deprecated - Use checkSlug from services
// TODO: Replace when the new server architecture is in place
// export const checkSlug = async (req, res) => {
//   const data = await factory.getAll(Article, req, {
//     filterByEntity: true,
//     filter: {
//       _id: { $ne: req.params.id },
//       slug: req.params.slug,
//     },
//   });

//   res.status(200).json(data);
// };

export const updateArticle = async (req, res) => {
  const { body, entity, params, user } = req;
  const { id } = params;

  // Update article
  const { data, error } = await editArticle({ id, body, entity, user });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export const revertArticle = async (req, res) => {
  const { entity, params, body, user } = req;
  const { id, revisionId } = params;
  const { status } = body || {};

  // Update article
  const { data, error } = await revertToRevision({
    id,
    revisionId,
    entity,
    user,
    status,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const getArticleRevisions = async (req, res) => {
  const data = await factory.getAll(Article, req, {
    filter: { revisionOf: req.params.id },
    filterByEntity: true,
    populate: ['authoredBy'],
  });

  // If no revisions found, return the original article instead
  if (data.count === 0) {
    const { data: original, error } = await getArticleById({
      id: req.params.id,
      entity: req.entity,
      getOriginal: true,
    });

    if (error) {
      return res.status(500).json({ status: 'error', message: error });
    }

    return res.status(200).json({ items: [original], count: 1 });
  }

  res.status(200).json(data);
};

export const getArticleRevision = async (req, res) => {
  const data = await factory.getOne(Article, req, {
    id: req.params.revisionId,
    filterByEntity: true,
    populate: ['authoredBy', 'revisionOf'],
  });

  res.status(200).json(data);
};

export const approveArticle = async (req, res) => {
  const { entity, params, user } = req;
  const { id } = params;

  const { data, error } = await approveService({
    id,
    entity,
    user,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export const publishArticle = async (req, res) => {
  const { entity, params, user } = req;
  const { id } = params;
  const { sites, canonicalSite, canonicalSitePage, canonicalUrl } = req.body;

  const { data, error } = await publishService({
    id,
    entity,
    user,
    sites,
    canonicalSite,
    canonicalSitePage,
    canonicalUrl,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export const shareArticle = async (req, res) => {
  const { entity, params, body } = req;
  const { id } = params;
  const { doNotShare, disableTranslations } = body;

  const { data, error } = await shareService({
    id,
    entity,
    doNotShare,
    disableTranslations,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export const syndicateArticle = async (req, res) => {
  const { entity, params } = req;
  const { id } = params;

  const { data, error } = await syndicateService({ id, entity });

  if (error) {
    return res.status(500).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export const translateArticle = async (req, res) => {
  const { user, params } = req;
  const { id, locale } = params;

  const { data, error } = await translateService({
    id,
    locale,
    entity: req.entity,
    data: req.body,
    user,
  });

  if (error) {
    // If service response has error, return it and stop execution
    res.status(400).json({
      status: 'error',
      message: error,
    });
    return;
  }

  // Otherwise, return data
  res.status(200).json(data);
};

export const disableArticle = async (req, res) => {
  const { entity, params } = req;
  const { id } = params;

  const { data, error } = await toggleService({
    id,
    entity,
    enabled: false,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const enableArticle = async (req, res) => {
  const { entity, params } = req;
  const { id } = params;

  const { data, error } = await toggleService({
    id,
    entity,
    enabled: true,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const deleteArticle = async (req, res) => {
  const { entity, params } = req;
  const { id } = params;

  const { data, error } = await deleteService({ id, entity });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  return res.status(204).json(data);
};

export const getArticleYearsAndMonths = async (req, res) => {
  const archive = await getArticleYearsAndMonthsForSite(req.query.siteId);
  res.status(200).json(archive);
};

export const getLinkedContent = async (req, res) => {
  const { entity, params, query } = req;
  const { id } = params;
  const { type, sort, order } = query;

  const { linkedItems, error } = await getArticleLinkedItems({
    articleId: id,
    entity,
    sort,
    order,
    type,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(linkedItems);
};

export const addLinkedContent = async (req, res) => {
  const { params, body } = req;
  const { id } = params;

  const { type, episodeId, publicationId, publicationIssueId, defaultItem } =
    body;

  let updatedArticle = null;

  if (type === 'episode') {
    const { error, article } = await addLinkedEpisode({
      articleId: id,
      episodeId,
      defaultItem,
    });

    if (error) {
      throw error;
    }

    updatedArticle = article;
  }

  if (type === 'publication-issue') {
    const { error, article } = await addLinkedPublicationIssue({
      articleId: id,
      defaultItem,
      publicationId,
      publicationIssueId,
    });

    if (error) {
      throw error;
    }

    updatedArticle = article;
  }

  res.status(200).json(updatedArticle);
};

export const updateLinkedContent = async (req, res) => {
  const { params, body } = req;
  const { id, type, linkedItemId } = params;

  let updatedArticle = null;

  if (type === 'episode') {
    const { error, article } = await updateLinkedEpisode({
      articleId: id,
      linkedItemId,
      ...body,
    });

    if (error) {
      throw error;
    }

    updatedArticle = article;
  }

  if (type === 'publication-issue') {
    const { error, article } = await updateLinkedPublicationIssue({
      articleId: id,
      linkedItemId,
      ...body,
    });

    if (error) {
      throw error;
    }

    updatedArticle = article;
  }

  res.status(200).json(updatedArticle);
};

export const deleteLinkedContent = async (req, res) => {
  const { params } = req;
  const { id, type, linkedItemId } = params;

  let updatedArticle = null;

  if (type === 'episode') {
    const { error, article } = await deleteLinkedEpisode({
      articleId: id,
      linkedItemId,
    });

    if (error) {
      throw error;
    }

    updatedArticle = article;
  }

  if (type === 'publication-issue') {
    const { error, article } = await deleteLinkedPublicationIssue({
      articleId: id,
      linkedItemId,
    });

    if (error) {
      throw error;
    }

    updatedArticle = article;
  }

  res.status(200).json(updatedArticle);
};

export default {
  createArticle,
  getArticles,
  getArticleTypes,
  getArticlesFields,
  getArticlesTranslations,
  getSharedArticles,
  getArticlesStats,
  getArticle,
  getArticleRevisions,
  getArticleRevision,
  getUtf8Bug,
  // checkSlug,
  updateArticle,
  revertArticle,
  approveArticle,
  publishArticle,
  shareArticle,
  syndicateArticle,
  translateArticle,
  disableArticle,
  enableArticle,
  deleteArticle,
  getArticleYearsAndMonths,
  getLinkedContent,
  addLinkedContent,
  updateLinkedContent,
  deleteLinkedContent,
};
