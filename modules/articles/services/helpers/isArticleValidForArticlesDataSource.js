/**
 * Check if an article is valid for a data source
 *
 * @param {Object} options The options
 * @param {Object} options.article The article
 * @param {Object} options.articleSite The article site
 * @param {Object} options.dataSource The data source
 *
 * @returns {Boolean} True if the article is valid for the data source
 */
export function isArticleValidForArticlesDataSource({
  article,
  articleSite,
  dataSource,
}) {
  if (!article || !articleSite || !dataSource || !dataSource.detailPage) {
    return false;
  }

  // If there are no settings, then the article is valid for the data source
  if (!dataSource.settings || Object.keys(dataSource.settings).length === 0) {
    return true;
  }

  return [
    validateFlag,
    validateFlagExceptions,
    validateCategories,
    validateOrganizations,
  ].every((fn) =>
    fn({ article, articleSite, dataSourceSettings: dataSource.settings })
  );
}

/**
 * Validate flag
 * @param {Object} options The options
 * @param {Object} options.articleSite The article site
 * @param {Object} options.dataSourceSettings The data source settings
 * @returns {Boolean} True if the flag is valid
 */
function validateFlag({ articleSite, dataSourceSettings }) {
  if (dataSourceSettings.flag) {
    const hasFlag = articleSite.flags[dataSourceSettings.flag];
    if (!hasFlag) {
      return false;
    }
  }
  return true;
}

/**
 * Validate flag exceptions
 * @param {Object} options The options
 * @param {Object} options.articleSite The article site
 * @param {Object} options.dataSourceSettings The data source settings
 * @returns {Boolean} True if the flag exceptions are valid
 */
function validateFlagExceptions({ articleSite, dataSourceSettings }) {
  if (dataSourceSettings.flagExceptions?.length && articleSite.flags?.length) {
    const hasExceptionFlags = Object.entries(articleSite.flags).some(
      ([flag, value]) =>
        dataSourceSettings.flagExceptions.includes(flag) && value
    );

    if (hasExceptionFlags) {
      return false;
    }
  }

  return true;
}

/**
 * Validate categories
 * @param {Object} options The options
 * @param {Object} options.article The article
 * @param {Object} options.dataSourceSettings The data source settings
 * @returns {Boolean} True if the categories are valid
 */
function validateCategories({ article, dataSourceSettings }) {
  if (dataSourceSettings.categories?.length) {
    if (!article.categories?.length) {
      return false;
    }

    const hasCategory = dataSourceSettings.categories.every((category) =>
      article.categories.map((c) => c.toString()).includes(category)
    );

    if (!hasCategory) {
      return false;
    }
  }

  return true;
}

/**
 * Validate organizations
 * @param {Object} options The options
 * @param {Object} options.article The article
 * @param {Object} options.dataSourceSettings The data source settings
 * @returns {Boolean} True if the organizations are valid
 */
function validateOrganizations({ article, dataSourceSettings }) {
  if (dataSourceSettings.organizations?.length) {
    if (!article.organizations?.length) {
      return false;
    }

    const hasOrganization = dataSourceSettings.organizations.every(
      (organization) =>
        article.organizations.map((o) => o.toString()).includes(organization)
    );

    if (!hasOrganization) {
      return false;
    }
  }

  return true;
}
