import { isValidObjectId, toObjectId } from '#utils/api/mongoose/id.js';

import { statuses as validStatuses } from '../../models/Article.js';
import ArticleSite from '../../models/ArticleSite.js';

/**
 * Get articles match filters.
 * @param {Object} options Filter options.
 * @param {String[]} options.authors List of author IDs.
 * @param {String[]} options.categories List of category IDs.
 * @param {String[]} options.types List of article types.
 * @param {String[]} options.organizations List of organization IDs.
 * @param {Object} options.entity Entity object.
 * @param {String} options.flag Flag ID.
 * @param {String[]} options.flagExceptions List of flag exceptions.
 * @param {String[]} options.systemStatuses List of system statuses.
 * @param {String} options.search Search query.
 * @param {String} options.siteId Site ID.
 * @param {String[]} options.statuses List of article statuses.
 * @returns {Object} Match filters for articles.
 */
export async function getArticlesMatchFilters({
  authors,
  categories,
  types = [],
  organizations = [],
  entity,
  flag,
  flagExceptions,
  systemStatuses,
  search,
  siteId,
  statuses,
}) {
  // Base filters:
  const baseFilters = getArticlesBaseFilters();

  // Filter by entity:
  const entityFilters = getArticlesEntityFilters(entity);

  // Filter by flag and site:
  const siteFilters = await getPublishedArticleSiteFilter({
    siteId,
    flag,
    flagExceptions,
  });

  // Filter by article statuses:
  const articleStatusesFilters = getArticleStatusesFilter(statuses);

  // Filter by categories:
  const authorsFilters = getAuthorsMatchFilter(authors);

  const categoriesFilters = getCategoriesMatchFilter(categories);

  // Filter by article types:
  const typesFilters = getArticleTypesFilter(types);

  // Filter by organizations:
  const organizationsFilters = getOrganizationsMatchFilter(organizations);

  // Filter by search query:
  const searchFilters = getAriclesSearchMatchFilter(search);

  const andFilters = [
    systemStatuses, // system statuses
    searchFilters, // search query
    articleStatusesFilters, // article statuses
    authorsFilters, // authors
    categoriesFilters, // categories
    typesFilters, // types
    organizationsFilters, // organizations
  ].filter((filter) => Object.keys(filter || {}).length > 0); // remove empty filters

  // Merge all filters
  const matchFilters = {
    ...baseFilters, // requried base filters (original articles, not revisions)
    ...entityFilters, // required entity filters (own articles and syndicated articles)
    ...siteFilters, // optional "published in site" filters (site, date and flag filters)
    ...(andFilters.length > 0 ? { $and: andFilters } : {}), // merge AND filters
  };

  return matchFilters;
}

/**
 * Get base filters for articles.
 * @returns {Object} Base filters for articles.
 */
export function getArticlesBaseFilters() {
  return {
    revisionOf: null, // only original articles (not revisions)
    translationOf: null, // only original articles (not translations)
  };
}

/**
 * Get entity filters for articles.
 * @param {Object} entity Entity object.
 * @returns {Object} Entity filters for articles.
 */
export function getArticlesEntityFilters(entity) {
  if (!entity) {
    return {};
  }

  return {
    $or: [
      // - Own articles:
      { entity: entity._id },
      // - Syndicated articles (from other entities):
      {
        doNotShare: { $ne: true }, // not marked as do not share
        syndicatedIn: { $in: [entity._id] }, // syndicated in entity
      },
    ],
  };
}

/**
 * Get article statuses filter.
 * @param {String[]} statuses List of article statuses.
 * @returns {Object} Article statuses filter.
 */
export function getArticleStatusesFilter(statuses = []) {
  // Get valid article statuses
  const articleStatuses = statuses?.filter((status) =>
    validStatuses.includes(status)
  );

  // If no valid article statuses, return empty filter
  if (!articleStatuses || articleStatuses.length === 0) {
    return {};
  }

  // Return filter for article statuses
  return {
    $or: [
      // original article statuses
      { status: { $in: articleStatuses } },
      // latest revision statuses
      {
        $and: [
          // latest revision status is in articleStatuses
          { latestRevisionStatus: { $in: articleStatuses } },
          // latest revision is newer than the original article
          { $expr: { $gt: ['$latestRevisionAt', '$updatedAt'] } },
        ],
      },
    ],
  };
}

/**
 * Get authors match filter for articles.
 *
 * @param {String[]} authors List of category IDs.
 * @returns {Object} Authors match filter.
 */
export function getAuthorsMatchFilter(authors = []) {
  // If no authors, return empty filter
  if (!authors || authors?.length === 0) {
    return {};
  }

  // Return filter for authors
  return {
    'authors.person': {
      $in: authors.reduce((acc, c) => {
        if (isValidObjectId(c)) {
          return [...acc, toObjectId(c)];
        }
        return acc;
      }, []),
    },
  };
}

/**
 * Get categories match filter for articles.
 *
 * @param {String[]} categories List of category IDs.
 * @returns {Object} Categories match filter.
 */
export function getCategoriesMatchFilter(categories = []) {
  // If no categories, return empty filter
  if (!categories || categories?.length === 0) {
    return {};
  }

  // Return filter for categories
  return {
    categories: {
      $in: categories.reduce((acc, c) => {
        if (isValidObjectId(c)) {
          return [...acc, toObjectId(c)];
        }
        return acc;
      }, []),
    },
  };
}

/**
 * Get article types filter.
 * @param {String[]} types List of article types.
 * @returns {Object} Article types filter.
 */
export function getArticleTypesFilter(types = []) {
  // If no types, return empty filter
  if (!types || types?.length === 0) {
    return {};
  }

  // Return filter for article types
  return {
    type: { $in: types },
  };
}

/**
 * Get organizations match filter for articles.
 *
 * @param {String[]} organizations List of organization IDs.
 * @returns {Object} Organizations match filter.
 */
export function getOrganizationsMatchFilter(organizations = []) {
  // If no organizations, return empty filter
  if (!organizations || organizations?.length === 0) {
    return {};
  }

  // Return filter for organizations
  return {
    organizations: {
      $in: organizations.reduce((acc, o) => {
        if (isValidObjectId(o)) {
          return [...acc, toObjectId(o)];
        }
        return acc;
      }, []),
    },
  };
}

/**
 * Get search match filter for articles.
 *
 * @param {String} search Search query.
 * @returns {Object} Search match filter.
 */
export function getAriclesSearchMatchFilter(search = '') {
  if (!search) {
    return {};
  }

  return {
    $or: [
      // Text search (requires text index!):
      { $text: { $search: search } },
      // Fallback to regex search:
      {
        title: { $regex: search, $options: 'i' },
      },
    ],
  };
}

/**
 * Get articles published in sites filter.
 * @param {Object} options Filter options.
 * @param {String} options.siteId Site ID.
 * @param {String} options.flag Flag ID.
 * @param {String[]} options.flagExceptions List of flag exceptions.
 * @param {Boolean} options.notPublished Not published flag.
 * @returns {Object} With article site filter and site filter objects.
 */
export async function getPublishedArticleSiteFilter({
  siteId,
  flag,
  flagExceptions,
}) {
  let flagFilters = {};
  const siteFilters = {};

  // If no siteId, return empty filters
  if (!siteId) {
    return siteFilters;
  }

  // Filter by flag:
  // - If flag is set, filter by flag
  const flagFilter = flag ? { [`flags.${flag}`]: true } : null;

  // - If flagExceptions is set, filter by flag exceptions
  const flagExceptionsFilter =
    flagExceptions?.length > 0
      ? flagExceptions.map((excludedFlag) => ({
          [`flags.${excludedFlag}`]: { $ne: true },
        }))
      : null;

  // - Combine flag filters
  if (flagFilter && flagExceptionsFilter) {
    flagFilters.$and = [flagFilter, ...flagExceptionsFilter];
  } else if (flagExceptionsFilter) {
    flagFilters.$and = [...flagExceptionsFilter];
  } else if (flagFilter) {
    flagFilters = flagFilter;
  }

  // Get article sites for the site
  const articleSites = await ArticleSite.find({
    enabled: true,
    site: siteId,
    ...flagFilters,
  }).select('article');

  // Get article IDs
  const articleIds = articleSites.map((site) => site.article);

  // Add site filter
  siteFilters._id = { $in: articleIds };

  return siteFilters;
}
