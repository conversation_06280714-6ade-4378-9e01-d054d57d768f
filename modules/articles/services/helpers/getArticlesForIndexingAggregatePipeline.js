import { toObjectId } from '#utils/api/mongoose/id.js';
import { pipe } from '#utils/function.js';

/**
 * Get the articles for a site aggregate pipeline
 * @param {Object} options The options
 * @param {String} options.articleId The article ID - Used to get a specific article
 * @param {String} options.dataSource The data source of the search index
 * @param {String} options.entityId The entity ID
 * @param {Boolean} options.getTranslations Whether to get translations
 * @param {String} options.language The language to filter by
 * @param {String} options.siteId The site ID
 * @returns {Array} The aggregate pipeline
 */
export function getArticlesForIndexingAggregatePipeline({
  articleId,
  dataSource,
  entityId,
  getTranslations = false,
  language = 'en',
  siteId,
}) {
  const articleSiteMatchFilters = {
    enabled: true,
    site: toObjectId(siteId),
    ...(articleId ? { article: toObjectId(articleId) } : {}),
  };

  if (Object.keys(dataSource?.settings || {}).length) {
    if (dataSource.settings.flag) {
      articleSiteMatchFilters[`flags.${dataSource.settings.flag}`] = true;
    }

    if (dataSource.settings.flagExceptions) {
      for (const flag of dataSource.settings.flagExceptions) {
        articleSiteMatchFilters[`flags.${flag}`] = { $ne: true };
      }
    }
  }

  return [
    {
      $match: articleSiteMatchFilters,
    },
    {
      $lookup: {
        from: 'articles',
        localField: 'article',
        foreignField: '_id',
        as: 'article',
      },
    },
    {
      $unwind: { path: '$article', preserveNullAndEmptyArrays: true },
    },
    {
      $match: getArticleFilters({
        categories: dataSource?.categories,
        organizations: dataSource?.organizations,
        getTranslations,
        language,
      }),
    },

    // Add translations to the pipeline (if required)
    ...getTranslationsAgreggatePipeline({
      entityId,
      language,
      getTranslations,
    }),
  ];
}

/**
 * Generate the filters for the article pipeline based on the filters (language, translations, categories, organizations, etc.)
 * @param {Object} options The options
 * @param {Array} options.categories The categories to filter by
 * @param {Array} options.organizations The organizations to filter by
 * @param {Boolean} options.getTranslations Whether to get translations
 * @param {String} options.language The language to filter by
 * @returns {Object} The Article filter object
 */
function getArticleFilters({
  categories,
  organizations,
  getTranslations = false,
  language = 'en',
} = {}) {
  // Match the articles records that:
  const baseFilter = {
    // - are enabled
    'article.enabled': true,
    // - aren't deleted
    'article.deleted': false,
    // - have been approved
    'article.status': 'approved',
    // - aren't the revisions
    'article.revisionOf': { $eq: null },
    // - aren't translation records (for now, done in a separate pipeline)
    'article.translationOf': { $eq: null },
  };

  // Add the filters based on the options
  return pipe(
    // - are in the requested categories
    filterByCategories(categories),
    // - are in the requested organizations
    filterByOrganizations(organizations),
    // - is in the requested language, or have translations in the requested language
    filterByLanguage({ language, getTranslations })
  )(baseFilter);
}

/**
 * Filter articles by categories
 * @param {Array} categories The categories to filter by
 * @returns {Function} The filter function that takes the current filter and returns the updated filter
 */
function filterByCategories(categories) {
  return function (currentFilter) {
    const filter = { ...currentFilter };

    if (!categories || !categories.length) {
      return filter;
    }

    filter['article.categories'] = { $in: categories.map(toObjectId) };

    return filter;
  };
}

/**
 * Filter articles by organizations
 * @param {Array} organizations The organizations to filter by
 * @returns {Function} The filter function that takes the current filter and returns the updated filter
 */
function filterByOrganizations(organizations) {
  return function (currentFilter) {
    const filter = { ...currentFilter };

    if (!organizations || !organizations.length) {
      return filter;
    }

    filter['article.organizations'] = { $in: organizations.map(toObjectId) };

    return filter;
  };
}

/**
 * Filter articles by language
 * @param {Object} options The options
 * @param {String} options.language The language to filter by
 * @param {Boolean} options.getTranslations Whether to get translations
 * @returns {Function} The filter function that takes the current filter and returns the updated filter
 */
export function filterByLanguage({ language, getTranslations }) {
  return function (currentFilter) {
    const filter = { ...currentFilter };

    // Check if we need to get translations, and if not just filter by the requested language
    if (!getTranslations) {
      filter['article.language'] = language;
      return filter;
    }

    if (!filter.$and) {
      filter.$and = [];
    }

    // Otherwise, get articles in either:
    filter.$and.push({
      $or: [
        // - the requested language
        { 'article.language': language },

        // - or those that have a translation in the requested language
        {
          $expr: {
            $in: [
              language,
              {
                $ifNull: ['$article.translationsStats.languagesApproved', []],
              },
            ],
          },
        },
      ],
    });

    return filter;
  };
}

/**
 * Get translations filters
 * @param {Object} options The options
 * @param {String} options.language The language to filter by
 * @param {Boolean} options.getTranslations Whether to get translations
 */
export function getTranslationsAgreggatePipeline({
  entityId,
  language,
  getTranslations,
}) {
  if (!getTranslations) {
    return [];
  }
  return [
    {
      $lookup: {
        from: 'articles',
        let: { articleId: '$article._id' },
        as: 'translatedArticles',
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$translationOf', '$$articleId'], // We get the translations of the original article
              },
              revisionOf: { $eq: null }, // We don't want to get the revisions
              deleted: false, // not deleted
              enabled: true, // and enabled
              $or: [
                {
                  entity: entityId, // If the entity is the same as the site entity, we use it
                },
                {
                  entity: '$entity', // Same entity as the original article
                },
              ],
              language, // Only get the ones in the requested language
              status: 'approved', // and that are approved
            },
          },
        ],
      },
    },
    {
      $addFields: {
        // NOTE: translatedArticles is an array that can contain 0, 1 or 2 translations.
        // - If it contains 0 translations, we don't need to do anything, we just use the original article.
        // - If it contains 1 translation, we use that translation.
        // - If it contains 2 translations, we use the one that matches the site's entity, or else we use the first one.
        translatedArticle: {
          // If the first object of the translatedArticles array has the entity matching site's Entity, use that as the translatedArticle, or else use the second object
          $cond: [
            { $gt: [{ $size: '$translatedArticles' }, 1] }, // if there are two translations...
            {
              $cond: [
                {
                  $eq: [
                    {
                      $arrayElemAt: ['$translatedArticles.entity', 0], // ...compare the first entity with the article entity
                    },
                    '$entity',
                  ],
                },
                { $arrayElemAt: ['$translatedArticles', 1] }, //
                { $arrayElemAt: ['$translatedArticles', 0] },
              ],
            },
            { $arrayElemAt: ['$translatedArticles', 0] },
          ],
        },
      },
    },
  ];
}
