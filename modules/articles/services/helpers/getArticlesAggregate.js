import getCollationFilter from '#utils/api/collation/filter.js';
import { isValidObjectId, toObjectId } from '#utils/api/mongoose/id.js';

import Article from '../../models/Article.js';

export function getArticlesAggregate({
  language = 'en',
  listFilters = {},
  matchFilters = {},
  notPublished = false,
  siteId = null,
  search = '',
  withApprovalWorkflow = false,
}) {
  const searchScore = search ? { score: { $meta: 'textScore' } } : {};

  // Calculate collation filters
  const collationFilters = getCollationFilter({ language });

  const articlesAggregate = Article.aggregate().match(matchFilters, {
    ...searchScore,
  });

  // Only match articles that are not published in any site
  if (notPublished) {
    articlesAggregate
      .lookup({
        from: 'articlesites',
        localField: '_id',
        foreignField: 'article',
        as: 'articleSite',
      })
      .match({ articleSite: { $eq: [] } });
  }

  // If listFilters.sort includes publishedAt, convert it to 'articleSite.startsAt'
  if (isValidObjectId(siteId)) {
    articlesAggregate
      .lookup({
        from: 'articlesites',
        localField: '_id',
        foreignField: 'article',
        as: 'articleSite',
      })
      .match({
        articleSite: {
          $elemMatch: {
            site: toObjectId(siteId),
          },
        },
      });

    listFilters.sort['articleSite.startsAt'] =
      listFilters.sort.publishedAt || -1;

    if (listFilters.sort.publishedAt) {
      delete listFilters.sort.publishedAt;
    }
  }

  // Sort and paginate
  articlesAggregate
    .sort({ ...searchScore, ...listFilters.sort })
    .skip(listFilters.pagination.skip)
    .limit(
      listFilters.pagination.limit > 0 ? listFilters.pagination.limit : 25
    ); // NOTE: limit must be after skip!

  // If no publishing filters are being applied yet...
  if (!notPublished && !isValidObjectId(siteId)) {
    // populate articleSite data to check if the article is published in any site
    articlesAggregate.lookup({
      from: 'articlesites',
      localField: '_id',
      foreignField: 'article',
      as: 'articleSite',
    });
  }

  // populate authoredBy
  articlesAggregate
    .lookup({
      from: 'users',
      localField: 'authoredBy',
      foreignField: '_id',
      as: 'authoredByData',
    })
    .unwind({
      path: '$authoredByData',
      preserveNullAndEmptyArrays: true,
    })
    .addFields({
      authoredBy: {
        _id: '$authoredByData._id',
        name: '$authoredByData.name',
        avatar: '$authoredByData.avatar',
        email: '$authoredByData.email',
      },
    })
    // populate entity data
    .lookup({
      from: 'entities',
      localField: 'entity',
      foreignField: '_id',
      as: 'entityData',
    })
    .unwind({
      path: '$entityData',
      preserveNullAndEmptyArrays: true,
    })
    .addFields({
      entity: {
        _id: '$entityData._id',
        id: '$entityData._id',
        name: '$entityData.name',
        slug: '$entityData.slug',
        language: '$entityData.language',
        logo: '$entityData.logo',
        type: '$entityData.type',
      },
    });

  // If approval workflow is enabled, populate approvedBy
  if (withApprovalWorkflow) {
    // populate approvedBy
    articlesAggregate
      .lookup({
        from: 'users',
        localField: 'approvedBy',
        foreignField: '_id',
        as: 'approvedBy',
      })
      .unwind({
        path: '$approvedBy',
        preserveNullAndEmptyArrays: true,
      })
      .addFields({
        approvedBy: {
          _id: '$approvedBy._id',
          name: '$approvedBy.name',
          avatar: '$approvedBy.avatar',
          email: '$approvedBy.email',
        },
      });
  }

  // populate latestRevision
  articlesAggregate
    .lookup({
      from: 'articles',
      let: { latestRevision: '$latestRevision' },
      as: 'latestRevision',
      pipeline: [
        {
          $match: {
            $expr: {
              $eq: ['$_id', '$$latestRevision'],
            },
          },
        },
        // populate latest revision's authoredBy
        {
          $lookup: {
            from: 'users',
            localField: 'authoredBy',
            foreignField: '_id',
            as: 'authoredBy',
          },
        },
        {
          $unwind: {
            path: '$authoredBy',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            authoredBy: {
              _id: '$authoredBy._id',
              name: '$authoredBy.name',
              avatar: '$authoredBy.avatar',
              email: '$authoredBy.email',
            },
          },
        },
        // populate latest revision's approvedBy (if applicable)
        ...(withApprovalWorkflow
          ? [
              {
                $lookup: {
                  from: 'users',
                  localField: 'approvedBy',
                  foreignField: '_id',
                  as: 'approvedBy',
                },
              },
              {
                $unwind: {
                  path: '$approvedBy',
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $addFields: {
                  approvedBy: {
                    _id: '$approvedBy._id',
                    name: '$approvedBy.name',
                    avatar: '$approvedBy.avatar',
                    email: '$approvedBy.email',
                  },
                },
              },
            ]
          : []),
      ],
    })
    .unwind({
      path: '$latestRevision',
      preserveNullAndEmptyArrays: true,
    })
    .addFields({
      latestRevision: {
        _id: '$latestRevision._id',
        title: '$latestRevision.title',
        subtitle: '$latestRevision.subtitle',
        abstract: '$latestRevision.abstract',
        body: '$latestRevision.body',
        image: '$latestRevision.image',
        video: '$latestRevision.video',
        location: '$latestRevision.location',
        authoredBy: {
          _id: '$latestRevision.authoredBy._id',
          name: '$latestRevision.authoredBy.name',
          avatar: '$latestRevision.authoredBy.avatar',
          email: '$latestRevision.authoredBy.email',
        },
        approvedBy: {
          _id: '$latestRevision.approvedBy._id',
          name: '$latestRevision.approvedBy.name',
          avatar: '$latestRevision.approvedBy.avatar',
          email: '$latestRevision.approvedBy.email',
        },
        status: '$latestRevision.status',
        createdAt: '$latestRevision.createdAt',
        updatedAt: '$latestRevision.updatedAt',
      },
    })
    .addFields({
      // Include "id" field (alias for "_id")
      id: '$_id',
      // Use last revision's title, subtitle, and image if they have values. Otherwise, use the original article's values
      title: {
        $cond: {
          if: '$latestRevision.title',
          then: '$latestRevision.title',
          else: '$title',
        },
      },
      subtitle: {
        $cond: {
          if: '$latestRevision.subtitle',
          then: '$latestRevision.subtitle',
          else: '$subtitle',
        },
      },
      image: {
        $cond: {
          if: '$latestRevision.image',
          then: '$latestRevision.image',
          else: '$image',
        },
      },
    })
    .addFields({
      locale: '$language',
    })
    .lookup({
      from: 'languages',
      //   localField: 'language',
      //   foreignField: 'locale',
      let: { languageLocale: '$language' },
      as: 'language',
      pipeline: [
        {
          $match: {
            $expr: {
              $eq: ['$locale', '$$languageLocale'],
            },
          },
        },
        {
          $project: {
            name: 1,
            nativeName: 1,
            locale: 1,
          },
        },
      ],
    })
    .unwind({
      path: '$language',
      preserveNullAndEmptyArrays: true,
    })
    .project({
      id: 1,
      articleSite: 1,
      title: 1,
      subtitle: 1,
      abstract: 1,
      image: 1,
      status: 1,
      // sites: 1,
      createdAt: 1,
      updatedAt: 1,
      revisionOf: 1,
      authoredBy: 1,
      approvedBy: 1,
      language: 1,
      latestRevision: 1,
      latestRevisionStatus: 1,
      latestRevisionAt: 1,
      entity: 1,
      doNotShare: 1,
      syndicatedIn: 1,
      translationsStats: 1,
      enabled: 1,
      deleted: 1,
    })
    .collation(collationFilters);

  return articlesAggregate;
}
