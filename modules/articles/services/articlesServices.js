import omit from 'lodash/omit.js';
import { DateTime } from 'luxon';
import mongoose from 'mongoose';

import Entity from '#modules/entities/models/Entity.js';
import siteServices from '#modules/web/services/siteServices.js';
import { getTranslationDoneField } from '#utils/aggregations.js';
import { errors } from '#utils/appError.js';
import getListFilters from '#utils/api/list/filters.js';
import { toObjectId } from '#utils/api/mongoose/id.js';
import { eventEmitter } from '#utils/eventEmitter.js';
import { logError } from '#utils/logger.js';
import { uniquifySlug } from '#utils/strings.js';

import { articlesEvents } from '../events.js';
import Article, { statuses as validStatuses } from '../models/Article.js';
import ArticleSite from '../models/ArticleSite.js';
import {
  getArticlesConfig,
  hasApprovalWorkflow,
  getValidArticleType,
  getArticlesFieldsForEntity,
} from './articlesConfigServices.js';
import { getArticlesAggregate } from './helpers/getArticlesAggregate.js';
import { getArticlesForSiteAggregatePipeline } from './helpers/getArticlesForSiteAggregatePipeline.js';
import {
  getArticlesMatchFilters,
  getArticleTypesFilter,
} from './helpers/getArticlesMatchFilters.js';

const systemStatuses = ['active', 'disabled', 'deleted'];

/**
 * Get a valid status based on user permissions.
 * @param {Object} params Function parameters
 * @param {String} params.status Status to validate
 * @param {Object} params.user User object
 * @param {Object} params.entity Entity object
 * @returns {Promise<String>} Promise. Resolves to a valid status string (draft, done, approved)
 */
async function getValidStatus({ status, user, entity, isTranslation }) {
  const entityHasApprovalWorkflow = await hasApprovalWorkflow({ entity });

  // Check if the user is allowed to approve articles
  const canApprove = entityHasApprovalWorkflow
    ? // If the entity uses the approval workflow, check if the user is allowed to approve articles
      user?.hasPermission({
        module: 'articles',
        permission: isTranslation ? 'approveTranslations' : 'approve',
      }) || false
    : // If the entity doesn't use the approval workflow, consider the user as allowed to approve articles
      true;

  // If status is not valid, or the user is not allowed to approve articles
  if (
    !validStatuses.includes(status) ||
    (status === 'approved' && !canApprove)
  ) {
    // ...set the status to draft
    return 'draft';
  }

  return status;
}

// Check if translations are done for an article:
// - Define expected translation fields
const expectedTranslationFields = [
  { name: 'title', type: 'string' },
  { name: 'subtitle', type: 'string' },
  { name: 'slug', type: 'string' },
  { name: 'body', type: 'object' },
  { name: 'abstract', type: 'string' },
  { name: 'pullQuote', type: 'string' },
];

/**
 * Get a list of articles.
 * @param {Object} params Function parameters
 * @param {Object} params.entity Entity object
 * @param {Array} params.statuses Article statuses to filter by (draft, done, approved, published, active, disabled, deleted)
 * @param {Array} params.categories Categories to filter by
 * @param {Array} params.types Article types to filter by
 * @param {String} params.flag Flag to filter by
 * @param {Array} params.flagExceptions Flag exceptions to filter by
 * @param {String} params.siteId Site ID to filter by
 * @param {Array} params.organizations Organizations to filter by
 * @param {Boolean} params.published Indicates that the article is published in at least one site
 * @param {Boolean} params.notPublished Indicates that the article is not published on any site
 * @param {Number} params.limit Limit number of results
 * @param {Number} params.page Page number (for pagination)
 * @param {Number} params.skip Number of items to skip (for pagination)
 * @param {String} params.sort Sort field
 * @param {String} params.order Sort order
 * @param {String} params.search Search query
 * @param {Boolean} params.useSiteEntity Indicates that the site entity should be used
 * @returns {Object} Object with articles data (in `data` attribute), or error (in `error` attribute)
 */
export async function getArticles({
  authors,
  categories,
  entity,
  types,
  flag,
  flagExceptions,
  language = 'en',
  limit,
  notPublished,
  order,
  organizations,
  page,
  search,
  siteId,
  skip,
  sort,
  statuses,
  useSiteEntity,
}) {
  siteId = notPublished ? undefined : siteId;

  // Get articles configuration for the entity
  const articlesConfig = await getArticlesConfig({ entity });

  try {
    let siteEntity;

    if (siteId && useSiteEntity) {
      const { site } = await siteServices.getSiteById(siteId);
      siteEntity = site?.entity;
    }

    const listFilters = getListFilters({
      statuses:
        statuses?.filter((status) => systemStatuses.includes(status)) || [], // Only allow system statuses here. Article statuses are handled below
      limit,
      page,
      skip,
      sort,
      order,
      sortFields: ['title', 'createdAt', 'updatedAt'],
    });

    const matchFilters = await getArticlesMatchFilters({
      authors,
      categories,
      types,
      organizations,
      entity: siteEntity ?? entity,
      flag,
      flagExceptions,
      systemStatuses: listFilters?.statuses,
      search,
      siteId,
      statuses,
      useSiteEntity,
    });

    // Check if the entity uses the approval workflow
    const withApprovalWorkflow = await hasApprovalWorkflow({ entity });

    // Get articles
    const articlesAggregate = getArticlesAggregate({
      language,
      listFilters,
      matchFilters,
      notPublished,
      search,
      siteId,
      withApprovalWorkflow,
    });

    const articles = await articlesAggregate.exec();

    const articlesWithComputedFields = articles.map((article) => ({
      ...article,
      ...(getArticleComputedFields(article, entity, articlesConfig) || {}),
    }));

    // get total count
    const count = await Article.countDocuments(matchFilters);
    const total = await Article.countDocuments({
      entity: entity._id,
      deleted: false,
    });

    return {
      data: {
        items: articlesWithComputedFields || [],
        count: count || 0,
        total: total || 0,
      },
    };
  } catch (error) {
    logError(error);
    return { error };
  }
}

export async function getArticlesTranslations({
  entity,
  language = 'en',
  limit,
  order,
  page,
  search,
  siteId,
  skip,
  sort,
  statuses: statusParam,
}) {
  const { data: articlesConfig } = (await getArticlesConfig({ entity })) || {};

  const statuses = articlesConfig?.translateOnlyAfterApproval
    ? ['approved']
    : statusParam;

  try {
    const listFilters = getListFilters({
      statuses:
        statuses?.filter((status) => systemStatuses.includes(status)) || [], // Only allow system statuses here. Article statuses are handled below
      limit,
      page,
      skip,
      sort,
      order,
      sortFields: ['title', 'createdAt', 'updatedAt'],
    });

    const matchFilters = await getArticlesMatchFilters({
      entity,
      listFilters,
      systemStatuses: listFilters?.statuses,
      search,
      siteId,
      statuses,
    });

    // Check if the entity uses the approval workflow
    const withApprovalWorkflow = await hasApprovalWorkflow({ entity });

    const articlesAggregate = getArticlesAggregate({
      language,
      listFilters,
      matchFilters,
      search,
      withApprovalWorkflow,
    });

    articlesAggregate.lookup({
      from: 'articles',
      //   localField: '_id',
      //   foreignField: 'translationOf',
      as: 'translations',
      let: {
        articleId: '$_id',
        source_entity_id: '$entity._id',
      },
      pipeline: [
        // Filter out translations that have been deleted or are not enabled
        {
          $match: {
            // Match to make this work for mongodb 4.4 🤦‍♂️
            $expr: {
              $eq: ['$translationOf', '$$articleId'],
            },
            deleted: false,
            enabled: true,
          },
        },
        {
          $addFields: {
            id: '$_id',
            // Check if translation belongs to the entity of the original article
            originalTranslation: {
              $eq: ['$entity', '$$source_entity_id'],
            },
            // Check if translation belongs to the current entity
            ownTranslation: {
              $eq: ['$entity', entity?._id],
            },
          },
        },
        {
          $lookup: {
            from: 'entities',
            // localField: 'entity',
            // foreignField: '_id',
            as: 'entity',
            let: { entityId: '$entity' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ['$_id', '$$entityId'],
                  },
                },
              },
              {
                $project: {
                  name: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$entity',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            entity: 1,
            id: 1,
            language: 1,
            originalTranslation: 1,
            ownTranslation: 1,
            slug: 1,
            status: 1,
          },
        },
      ],
    });

    const articlesWithTranslations = await articlesAggregate.exec();

    const articlesWithComputedFields = articlesWithTranslations.map(
      (article) => ({
        ...article,
        ...(getArticleComputedFields(article, entity) || {}),
      })
    );

    // get total count
    const count = await Article.countDocuments(matchFilters);
    const total = await Article.countDocuments({
      entity: entity._id,
      deleted: false,
    });

    return {
      items: articlesWithComputedFields || [],
      count: count || 0,
      total: total || 0,
    };
  } catch (error) {
    logError(error);
    return { error };
  }
}

/**
 * Get articles shared by other entities.
 * @param {Object} params Function parameters
 * @param {Object} params.entity Entity object
 * @param {Number} params.limit Limit number of results
 * @param {Number} params.page Page number (for pagination)
 * @param {Number} params.skip Number of items to skip (for pagination)
 * @param {String} params.sort Sort field
 * @param {String} params.order Sort order
 * @param {Array} params.categories Categories to filter by
 * @param {String} params.search Search query
 * @returns {Object} Object with articles data (in `data` attribute), or error (in `error` attribute)
 */
export async function getSharedArticles({
  entity,
  limit,
  page,
  skip,
  sort,
  order,
  categories,
  search,
  types,
}) {
  const filters = getListFilters({
    limit,
    page,
    skip,
    sort,
    order,
    sortFields: ['title', 'language', 'createdAt', 'updatedAt'],
    // search,
    // searchFields: ['title', 'subtitle', 'abstract'],
  });

  // Filter by categories:
  const categoriesFilters =
    categories?.length > 0 ? { categories: { $in: categories } } : {};

  // Filter by types:
  const typesFilters = getArticleTypesFilter(types);

  // Filter by search query:
  const searchFilters = search
    ? {
        $or: [
          // Text search (requires text index!):
          { $text: { $search: search } },
          // Fallback to regex search:
          {
            title: { $regex: search, $options: 'i' },
          },
        ],
      }
    : {};

  // If search query is set, add a text score to the search results
  const searchScore = search ? { score: { $meta: 'textScore' } } : {};

  const articleFilters = {
    doNotShare: { $ne: true }, // that are shared
    status: 'approved', // approved by the original entity
    entity: { $ne: entity._id }, // not from this entity
    enabled: true, // enabled
    deleted: false, // not deleted
    revisionOf: null, // not revisions
    translationOf: null, // not translations
    syndicatedIn: { $nin: [entity._id] }, // not syndicated in this entity
  };

  const userFilters = {
    $and: [
      filters.statuses, // system statuses

      {
        //...filters.search, // search query
        ...searchFilters,
        ...categoriesFilters,
        ...typesFilters,
      },
    ],
  };

  const matchFilters = {
    ...articleFilters,
    ...userFilters,
  };

  try {
    // Get articles
    const articles = await Article.aggregate()
      .match(matchFilters, { ...searchScore }) // filters and search score
      .sort({ ...searchScore, ...filters.sort }) // search score sort and sort order
      .skip(filters.pagination.skip) // pagination skip
      .limit(filters.pagination.limit > 0 ? filters.pagination.limit : 25) // NOTE: limit must be after skip!

      // Match only articles that are published on at least one site
      // - Populate  articleSites to check if the article is published on any site
      .lookup({
        from: 'articlesites',
        localField: '_id',
        foreignField: 'article',
        as: 'sites',
      })
      .unwind({
        path: '$sites',
        preserveNullAndEmptyArrays: true,
      })
      .match({
        'sites.enabled': true,
        '$and': [
          {
            $or: [
              { 'sites.startsAt': null },
              { 'sites.startsAt': { $lte: new Date() } },
            ],
          },
          {
            $or: [
              { 'sites.endsAt': null },
              { 'sites.endsAt': { $gte: new Date() } },
            ],
          },
        ],
      })

      // Populate entity data
      .lookup({
        from: 'entities',
        localField: 'entity',
        foreignField: '_id',
        as: 'entity',
      })
      .unwind({
        path: '$entity',
        preserveNullAndEmptyArrays: true,
      })
      .addFields({
        entity: {
          _id: '$entity._id',
          id: '$entity._id',
          name: '$entity.name',
          slug: '$entity.slug',
          language: '$entity.language',
          logo: '$entity.logo',
          type: '$entity.type',
        },
      })

      // Populate authoredBy
      .lookup({
        from: 'users',
        localField: 'authoredBy',
        foreignField: '_id',
        as: 'authoredBy',
      })
      .unwind({
        path: '$authoredBy',
        preserveNullAndEmptyArrays: true,
      })
      .addFields({
        authoredBy: {
          _id: '$authoredBy._id',
          name: '$authoredBy.name',
          avatar: '$authoredBy.avatar',
          email: '$authoredBy.email',
        },
      })

      .project({
        id: '$_id',
        title: 1,
        type: 1,
        subtitle: 1,
        abstract: 1,
        language: 1,
        image: 1,
        status: 1,
        sites: 1,
        createdAt: 1,
        updatedAt: 1,
        entity: {
          _id: 1,
          id: 1,
          name: 1,
          slug: 1,
          language: 1,
          logo: 1,
          type: 1,
        },
        authoredBy: 1,
        translationsStats: 1,
        enabled: 1,
        deleted: 1,
      });

    // get total count
    const count = await Article.countDocuments(matchFilters);
    const total = await Article.countDocuments(articleFilters);

    return {
      data: {
        items: articles || [],
        count: count || 0,
        total: total || 0,
      },
    };
  } catch (error) {
    logError(error);
    return { error };
  }
}

/**
 * Get computed fields for an article (`isDraft`, `isDone`, `isApproved`, `isPublished`)
 * @param {Object} article Article object
 * @param {Object} entity Current Entity object
 * @param {Object} articlesConfig Articles configuration object for the entity
 * @returns {Object} Object with computed fields
 */
function getArticleComputedFields(article, entity, articlesConfig) {
  if (!article) return null;

  const {
    status,
    latestRevisionStatus,
    latestRevisionAt,
    articleSite, // Article sites structure
    updatedAt,
  } = article;

  // Check if the latest revision is draft (by state and date)
  const isLatestRevisionDraft =
    latestRevisionStatus === 'draft' && //  latest revision is draft
    latestRevisionAt > updatedAt; // and latest revision is newer than the original article (when approved, the original article updatedAt is newer than the latest revision)

  // Check if the original article is as draft (or if its latest revision is draft)
  const isDraft = status === 'draft' || isLatestRevisionDraft;

  // Check if the article is set as done
  const isDone = status === 'done';

  // Check if the article is approved
  const isApproved = status === 'approved';

  // Check if article has been sindicated into the current entity
  const isSyndicated = isSyndicatedIn(article, entity);

  // Check if the article is shared (by the current entity if is an own article, or if is being syndicated in the current entity)
  const isShared = isSyndicated // If the article is syndicated,
    ? true // it means that is being shared by the original entity
    : article.doNotShare === undefined // If doNotShare is not set,
      ? !articlesConfig?.disableSharing // use the entity's configuration
      : !article.doNotShare; // Otherwise, use the article's doNotShare attribute (`true` if the article is not shared)

  // Check if the article is published on any site (enabled and within date range)
  const isPublished =
    articleSite?.some(
      (site) =>
        site.enabled &&
        (!site.startsAt ||
          DateTime.fromJSDate(site.startsAt) <= DateTime.now()) &&
        (!site.endsAt || DateTime.fromJSDate(site.endsAt) >= DateTime.now())
    ) ?? false;

  // Return the computed fields
  return {
    isDraft,
    isLatestRevisionDraft,
    isDone,
    isApproved,
    isSyndicated,
    isShared,
    isPublished,
  };
}

/**
 * Check if an article is syndicated in an entity.
 * @param {Object} article Article object
 * @param {Object} entity Entity object
 * @returns {Boolean} True if the article is syndicated in the entity, false otherwise
 */
function isSyndicatedIn(article, entity) {
  // If article or entity are not set, return false
  if (!article || !entity) return false;

  // if article doesn't have a syndicatedIn attribute, or it is empty, return false
  if (!article.syndicatedIn || article.syndicatedIn.length === 0) return false;

  return article.syndicatedIn.some((id) => id.equals(entity._id)) ?? false; // Compare against current entity's id (converted to string) or fallback to false
}

/**
 * Creates a new article.
 * @param {Object} params Function parameters
 * @param {Object} params.body Article data
 * @param {Object} params.entity Entity object
 * @param {Object} params.user User object
 * @returns {Object} Object with article data (in `data` attribute), or error (in `error` attribute)
 */
export async function addArticle({ body = {}, entity, user }) {
  // Remove sites attribute from body ('sites' is only set via the '/publish' endpoint)
  if (body?.sites) delete body.sites;

  const type = await getValidArticleType(body?.type, entity);

  try {
    // Get a valid status based on user permissions
    const status = await getValidStatus({ status: body?.status, user, entity });

    // Get language for the article
    const language =
      body?.language || // Article language (if set)
      entity?.language || // Entity language (if set).
      'en'; // Or fallback to "en"

    const fields = {
      ...body, // Body fields are already validated by the route's middleware (Joi)
      type, // Set article type
      language, // Set article language
      status, // Set article status
      authoredBy: user._id, // Set article author to the current user
      entity: entity._id, // Set article entity to the current entity
      translationsStats: {
        total: 0,
        draft: 0,
        done: 0,
        approved: 0,
        languages: [],
        languagesDraft: [],
        languagesDone: [],
        languagesApproved: [],
      }, // Initialize translations stats
    };

    // Create a new article
    const data = await Article.create({ ...fields });

    return { data };
  } catch (error) {
    return { error };
  }
}

/**
 * Get an article by ID.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object
 * @param {Boolean} params.getOriginal If true, returns the original article (not the latest revision)
 * @param {Array} params.populate Additional populate fields
 * @returns {Object} Object with article data (in `data` attribute), or error (in `error` attribute)
 */
export async function getArticleById({
  id,
  entity,
  getOriginal = false,
  populate = [],
}) {
  const withApprovalWorkflow = await hasApprovalWorkflow({ entity });

  // Get the article we are looking for
  const article = await Article.findById(id)
    .populate([...populateArticle(withApprovalWorkflow), ...populate])
    .lean();

  if (!article) return { error: `No article found with ID ${id}.` };

  const isOwnArticle = article.entity === entity._id;

  // We verify that the article is from the entity or is shared, or the translationOf exists
  if (article.translationOf && !isOwnArticle) {
    const translatedSyndicatedArticle = await Article.findOne({
      _id: article.translationOf, // The id of the article to get
      $or: [
        { entity: entity._id },
        { doNotShare: { $ne: true }, syndicatedIn: entity._id },
      ],
    }).lean();

    if (!translatedSyndicatedArticle) {
      return { error: `No article found with ID ${id}.` };
    }
  }

  if (!article) {
    return { error: `No article found with ID ${id}.` };
  }

  // Get translations (if any)
  const translations = await getArticleTranslations(id, entity);

  // But if has one, get latest revision
  const latestRevision = await getLatestRevision({ article: article });

  // Add computed fields to the original article
  const computedFields = getArticleComputedFields(article, entity);

  const articleSites = await ArticleSite.find({
    article: article._id,
  }).lean();

  const sites =
    // If there are ArticleSite records for this article, return them as the sites object
    articleSites?.length > 0
      ? articleSites.reduce((sitesObj, articleSite) => {
          // Ignore the article, site, and timestamp fields
          sitesObj[articleSite.site] = omit(articleSite, [
            'article',
            'site',
            'createdAt',
            'updatedAt',
          ]);
          return sitesObj;
        }, {})
      : article.sites; // Otherwise, return the original article's sites object

  // If getOriginal is true, or there is no latest revision, return the original article
  if (getOriginal || !latestRevision) {
    return {
      data: {
        ...article,
        sites,
        isTranslation: !!article.translationOf,
        ...computedFields,
        translations,
        id: article._id,
      },
    };
  }

  // Otherwise, return the latest revision data merged with the original article data
  const current = {
    ...article,
    sites,
    isTranslation: !!article.translationOf,
    ...latestRevision,
    ...computedFields,
    translations,
    authoredBy: article.authoredBy,
    status: article.status,
    // always return original article IDs:
    id: article._id,
    _id: article._id,
  };

  return { data: current };
}

/**
 * Get a list of translations for an article.
 * @param {String} id Article ID
 * @param {Object} entity Entity object
 * @param {Object} options Additional options
 * @returns array Array of article translations
 */
async function getArticleTranslations(id, entity, options = {}) {
  const defaultOptions = {
    ownTranslations: false,
  };

  const { ownTranslations } = { ...defaultOptions, ...options };

  // Get articles configuration for the entity
  const { availableFields } = await getArticlesFieldsForEntity({ entity });

  // Get required fields for the entity
  const requiredFields = Object.entries(availableFields).reduce(
    (fields, [fieldName, field]) => {
      if (field.mandatory && field.required) {
        fields.push(fieldName);
      }
      return fields;
    },
    []
  );

  // Agregate to get translations data, including latestRevision, authoredBy, approvedBy and computed fields
  const translations = await Article.aggregate()
    .match({
      translationOf: new mongoose.Types.ObjectId(id),
      deleted: false,
      ...(ownTranslations && entity ? { entity: entity._id } : {}),
    })
    .lookup({
      from: 'articles',
      let: { latestRevision: '$latestRevision' },
      // localField: 'latestRevision',
      // foreignField: '_id',
      as: 'latestRevision',
      pipeline: [
        // Match to make this work for mongodb 4.4 (https://stackoverflow.com/questions/66748716/lookup-with-pipeline-may-not-specify-localfield-or-foreignfield)
        {
          $match: {
            $expr: {
              $eq: ['$_id', '$$latestRevision'],
            },
          },
        },
        // populate latest revision's authoredBy
        {
          $lookup: {
            from: 'users',
            localField: 'authoredBy',
            foreignField: '_id',
            as: 'authoredBy',
          },
        },
        {
          $unwind: {
            path: '$authoredBy',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            authoredBy: {
              _id: '$authoredBy._id',
              name: '$authoredBy.name',
              avatar: '$authoredBy.avatar',
              email: '$authoredBy.email',
            },
          },
        },

        // populate latest revision's approvedBy (if applicable)
        {
          $lookup: {
            from: 'users',
            localField: 'approvedBy',
            foreignField: '_id',
            as: 'approvedBy',
          },
        },
        {
          $unwind: {
            path: '$approvedBy',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            approvedBy: {
              _id: '$approvedBy._id',
              name: '$approvedBy.name',
              avatar: '$approvedBy.avatar',
              email: '$approvedBy.email',
            },
          },
        },
      ],
    })
    .unwind({
      path: '$latestRevision',
      preserveNullAndEmptyArrays: true,
    })
    // Add revision field
    .addFields({
      latestRevision: {
        _id: '$latestRevision._id',
        title: '$latestRevision.title',
        subtitle: '$latestRevision.subtitle',
        abstract: '$latestRevision.abstract',
        body: '$latestRevision.body',
        image: '$latestRevision.image',
        video: '$latestRevision.video',
        location: '$latestRevision.location',
        authoredBy: {
          _id: '$latestRevision.authoredBy._id',
          name: '$latestRevision.authoredBy.name',
          avatar: '$latestRevision.authoredBy.avatar',
          email: '$latestRevision.authoredBy.email',
        },
        approvedBy: {
          _id: '$latestRevision.approvedBy._id',
          name: '$latestRevision.approvedBy.name',
          avatar: '$latestRevision.approvedBy.avatar',
          email: '$latestRevision.approvedBy.email',
        },
        status: '$latestRevision.status',
        createdAt: '$latestRevision.createdAt',
        updatedAt: '$latestRevision.updatedAt',
      },
    })

    // Populate entity data
    .lookup({
      from: 'entities',
      localField: 'entity',
      foreignField: '_id',
      as: 'entity',
    })
    .unwind({
      path: '$entity',
      preserveNullAndEmptyArrays: true,
    })
    .addFields({
      entity: {
        _id: '$entity._id',
        id: '$entity._id',
        name: '$entity.name',
        slug: '$entity.slug',
        language: '$entity.language',
        logo: '$entity.logo',
        type: '$entity.type',
      },
    })

    // Populate authoredBy
    .lookup({
      from: 'users',
      localField: 'authoredBy',
      foreignField: '_id',
      as: 'authoredBy',
    })
    .unwind({
      path: '$authoredBy',
      preserveNullAndEmptyArrays: true,
    })
    .addFields({
      authoredBy: {
        _id: '$authoredBy._id',
        name: '$authoredBy.name',
        avatar: '$authoredBy.avatar',
        email: '$authoredBy.email',
      },
    })

    // If approval workflow is enabled, populate approvedBy
    .lookup({
      from: 'users',
      localField: 'approvedBy',
      foreignField: '_id',
      as: 'approvedBy',
    })
    .unwind({
      path: '$approvedBy',
      preserveNullAndEmptyArrays: true,
    })
    .addFields({
      approvedBy: {
        _id: '$approvedBy._id',
        name: '$approvedBy.name',
        avatar: '$approvedBy.avatar',
        email: '$approvedBy.email',
      },
    })

    // Add other computed fields:
    .addFields({
      // Include "id" field (alias for "_id")
      id: '$_id',

      // Check if all expected translation fields are set
      translationDone: getTranslationDoneField(
        // Only include fields that are required by the current entity
        expectedTranslationFields.reduce((fields, { name, type }) => {
          if (requiredFields.includes(name)) {
            fields.push({ name, type });
          }
          return fields;
        }, [])
      ),

      // Check if translation belongs to the current entity
      ownTranslation: {
        $eq: ['$entity._id', entity?._id],
      },

      // title, based on the latest revision's title (if any) or the original article's title
      title: {
        $cond: {
          if: '$latestRevision.title',
          then: '$latestRevision.title',
          else: '$title',
        },
      },
    })
    .project({
      id: 1,
      title: 1,
      slug: 1,
      subtitle: 1,
      abstract: 1,
      language: 1,
      image: 1,
      status: 1,
      createdAt: 1,
      updatedAt: 1,
      revisionOf: 1,
      authoredBy: 1,
      approvedBy: 1,
      latestRevision: 1,
      translationDone: 1,
      ownTranslation: 1,
      entity: 1,
      enabled: 1,
      deleted: 1,
    });

  return translations;
}

function populateArticle(withApprovalWorkflow = false) {
  // Set approvedBy field (if approval workflow is enabled)
  const approvedBy = withApprovalWorkflow
    ? [
        {
          path: 'approvedBy',
          select: 'name avatar email',
        },
      ]
    : [];

  return [
    // add article's authoredBy
    {
      path: 'authoredBy',
      select: 'name avatar email',
    },
    // add article's entity
    {
      path: 'entity',
      select: 'name type code',
    },

    // add article's approvedBy (if applicable)
    ...approvedBy,

    // add article's latest revision
    {
      path: 'latestRevision',
      select: `status createdAt updatedAt authoredBy ${
        withApprovalWorkflow ? 'approvedBy' : '' // add approvedBy field (if applicable)
      }`,
      populate: [
        {
          path: 'authoredBy',
          select: 'name avatar email',
        },
        ...approvedBy, // populate latestRevision's approvedBy (if applicable)
      ],
    },
  ];
}

/**
 * Get the latest revision of an article.
 * @param {Object} params Function parameters
 * @param {String} params.article Article object
 * @returns {Object} Object with article data
 */
async function getLatestRevision({ article }) {
  if (!article) return null;

  // Get latest revision
  const latestRevision = await Article.findOne(
    {
      revisionOf: article._id,
    },
    {
      title: 1,
      slug: 1,
      subtitle: 1,
      abstract: 1,
      body: 1,
      image: 1,
      video: 1,
      location: 1,
      author: 1,
      authorEmail: 1,
      language: 1,
      related: 1,
      categories: 1,
      tags: 1,
      files: 1,
      status: 1,
    }
  )
    .sort({ createdAt: -1 })
    .lean();

  return latestRevision;
}

/**
 * Service to edit an article. Creates a new revision, and updates the original article.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.body Article data
 * @param {Object} params.entity Entity object
 * @param {Object} params.user User object
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function editArticle({ id, body = {}, entity, user }) {
  try {
    const article = await Article.findById(id)
      .select('status translationOf')
      .lean();

    if (!article) {
      return { error: `No article found with ID ${id}.` };
    }

    // Handle the article's slug
    if (body.slug && body.slug !== article.slug) {
      body.slug = await getAvailableSlug({
        slug: body.slug,
        entity: entity.id,
        excludeId: id,
      });

      // When the slug is changed, we need to remove the article from the index. The article will be re-indexed by the indexing task with the new slug.
      eventEmitter.emit(articlesEvents.DELETE_ARTICLE, {
        articleId: article.translationOf ? article.translationOf : id,
        entity,
        language: article.translationOf ? article.language : null,
      });
    }

    // Get a valid status based on user permissions
    const status = await getValidStatus({
      status: body?.status,
      user,
      entity,
      isTranslation: article?.translationOf,
    });

    // Check a valid type or get a default one
    const type = await getValidArticleType(body?.type, entity);

    // Remove sites attribute from body ('sites' is only set via the '/publish' endpoint)
    if (body?.sites) delete body.sites;

    // Sanitize Bible references by removing invalid _id attributes (new values contain these)
    if (body?.bibleReferences) {
      body.bibleReferences = body.bibleReferences.map((ref) => {
        const reference = { ...ref };
        const validId = toObjectId(ref._id, true);

        // If the reference is valid, set it to the validId
        if (!validId) {
          delete reference._id; // Remove the _id attribute from the reference object
        }

        return reference;
      });
    }

    // Create a new revision
    const revision = await Article.create({
      ...body, // Body fields are already validated by the route's middleware (Joi)
      type, // Set article type in the revision
      status,
      revisionOf: id, // Reference the original article
      authoredBy: user._id,
      entity: entity._id,
    });

    // Check if the entity uses the approval workflow
    const entityHasApprovalWorkflow = await hasApprovalWorkflow({ entity });

    // Update the article:
    // - If the status is "approved", or the entity doesn't use the approval workflow...
    if (status === 'approved' || !entityHasApprovalWorkflow) {
      // update the original article with the new revision data and set the status to "approved"
      await Article.updateOne(
        { _id: id, entity: entity._id },
        {
          ...body,
          type, // set the article type
          status, // set the status to "approved" (or "draft", if the entity doesn't use the approval workflow)
          authoredBy: user._id, // set the author to the current user (at this point, the editor/approver is the author of the revision)
          latestRevision: revision._id,
          latestRevisionStatus: status,
          latestRevisionAt: revision.createdAt,
        }
      );
    } else {
      // - Otherwise, just update the original's latestRevision to the new revision ID
      await Article.updateOne(
        { _id: id, entity: entity._id },
        {
          type, // set the article type
          status: article.status === 'approved' ? 'approved' : status, // set the status to "done" or "draft"
          latestRevision: revision._id, // Set the latest revision to the new revision ID
          latestRevisionStatus: status, // Add the latest revision status to improve filtering (e.g. to filter by "draft" articles)
          latestRevisionAt: revision.createdAt,
        },
        { timestamps: false } // don't update timestamps (createdAt, updatedAt) of the original article
      );
    }

    // If the original article is a translation,
    if (article.translationOf) {
      // Get original' source article
      const sourceArticle = await Article.findById(article.translationOf);

      // Update source article's translation stats (total and done):
      updateArticleTranslationsStats(sourceArticle._id, entity);
    }

    // Return the updated article
    return {
      data: { ...revision, id, _id: id }, // always return original article IDs
    };
  } catch (error) {
    // Return the error
    return { error };
  }
}

/**
 * Service to set an article as shared to other entities.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object
 * @param {Boolean} params.doNotShare Indicates if the article should not be shared with other entities
 * @param {Boolean} params.disableTranslations Indicates if the article should not be translated by other entities
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function shareArticle({
  id,
  entity,
  doNotShare,
  disableTranslations,
}) {
  // Get the original article
  const article = await Article.findOne({
    _id: id, // The id of the article to share
    entity: entity._id, // Ensure that the article belongs to the current entity
    translationOf: null, // Ensure that the article is not a translation
    revisionOf: null, // Ensure that the article is not a revision
    deleted: false, // Ensure that the article is not deleted
    // enabled: true, // Ensure that the article is not disabled // NOTE: sharing settings can be updated even if the article is disabled
  }).lean();

  // If there is not a article, or can't be shared:
  if (!article) {
    // return an error
    return {
      error: `This article doesn't exist or can't be shared (ID ${id})`,
      code: 404,
    };
  }

  // Parse doNotShare value (ensure boolean or set to null)
  const doNotShareValue = doNotShare === '' ? null : Boolean(doNotShare);

  try {
    // Update article attributes
    const updatedArticle = await Article.updateOne(
      { _id: id },
      {
        doNotShare: doNotShareValue,
        disableTranslations,
      }
    );

    return { data: { ...updatedArticle, id, _id: id } };
  } catch (error) {
    // Return the error
    return { error };
  }
}

/**
 * Service to syndicate an article as shared by other entities.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object (the receiving entity)
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function syndicateArticle({ id, entity }) {
  // Get the original article
  const article = await Article.findOne({
    _id: id, // The id of the article to share
    doNotShare: { $ne: true }, // Ensure that the article is shared
    translationOf: null, // Ensure that the article is not a translation
    revisionOf: null, // Ensure that the article is not a revision
    deleted: false, // Ensure that the article is not deleted
    enabled: true, // Ensure that the article is not disabled
  }).lean();

  // If there is not a article, or can't be shared:
  if (!article) {
    // return an error
    return {
      error: `This article doesn't exist or can't be shared (ID ${id})`,
      code: 404,
    };
  }

  try {
    // Check if the article is already syndicated in the entity
    const alreadySyndicated = isSyndicatedIn(article, entity);

    // Update article syndicatedIn attribute based if it's already syndicated or not
    const updatedArticle = await Article.updateOne(
      { _id: id },
      {
        syndicatedIn: alreadySyndicated // if is already syndicated,
          ? article.syndicatedIn.filter((eId) => !eId.equals(entity._id)) // remove the entity from syndicatedIn
          : [...(article.syndicatedIn || []), entity._id], // otherwise, add the entity
      },
      { timestamps: false } // don't update timestamps (createdAt, updatedAt) of the original article
    );

    return { data: { ...updatedArticle, id, _id: id } };
  } catch (error) {
    // Return the error
    return { error };
  }
}

/**
 * Toggle an article's status between "active" and "disabled".
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object
 * @param {Boolean} params.enabled Indicates if the article should be enabled or disabled
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function toggleArticle({ id, entity, enabled }) {
  // Get the original article
  const original = await Article.findOne({
    _id: id,
    entity: entity._id,
  }).lean();

  // If there is no original article,
  if (!original) {
    // return an error
    return { error: `No article found with ID ${id}.`, code: 404 };
  }

  try {
    // Update the original article
    const toggledArticle = await Article.findByIdAndUpdate(
      { _id: id },
      { enabled },
      { new: true }
    ).lean();

    // Notify event listeners that the article has been disabled
    if (!enabled) {
      eventEmitter.emit(articlesEvents.DISABLE_ARTICLE, {
        articleId: toggledArticle?.translationOf
          ? toggledArticle.translationOf
          : id,
        entity,
        language: toggledArticle?.translationOf
          ? toggledArticle.language
          : null,
      });
    }

    // Return the updated article
    return {
      data: { ...toggledArticle, id, _id: id }, // always return original article IDs
    };
  } catch (error) {
    // Return the error
    return { error };
  }
}

/**
 * Service to delete an article.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function deleteArticle({ id, entity }) {
  // Get the original article
  const article = await Article.findOne({ _id: id, entity: entity._id }).lean();

  // If there is no original article,
  if (!article) {
    // return an error
    return { error: `No article found with ID ${id}.`, code: 404 };
  }

  try {
    // Delete the article (soft delete)
    const deletedArticle = await Article.findOneAndUpdate(
      { _id: toObjectId(id) },
      { deleted: true },
      { new: true, runValidators: true }
    ).lean();

    // If the article is a translation, update the source article's translation stats (total and done):
    if (article.translationOf && deletedArticle.deleted) {
      updateArticleTranslationsStats(article.translationOf, entity);
    }

    // Notify event listeners that the article has been deleted
    eventEmitter.emit(articlesEvents.DELETE_ARTICLE, {
      articleId: article.translationOf ? deletedArticle.translationOf : id,
      entity,
      language: article.translationOf ? deletedArticle.language : null,
    });

    // Return the updated article (with deleted=true)
    return { data: { ...deletedArticle, id, _id: id } };
  } catch (error) {
    // Return the error
    return { error };
  }
}

/**
 * Approve an article.
 *
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object
 * @param {Object} params.user User object
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function approveArticle({ id, entity, user }) {
  // If current entity doesn't use the approval workflow...
  if (!hasApprovalWorkflow({ entity })) {
    // Stop here and return an error
    return { error: 'This entity does not use the approval workflow.' };
  }

  // Get the original article
  const original = await Article.findById(id);

  // If there is no original article,
  if (!original) {
    // return an error
    return { error: `No article found with ID ${id}.`, code: 404 };
  }

  // Get latest revision
  const revision = await getLatestRevision({ article: original });

  // If there is no latest revision, return the original article
  const body = revision ? omit(revision, ['id', '_id']) : {}; // Omit fields that should not be copied

  try {
    // Update the original article
    const data = await Article.findByIdAndUpdate(
      { _id: id, entity: entity._id },
      {
        ...body,
        status: 'approved',
        approvedBy: user._id,
      }
    );

    // Return the updated article
    return {
      data: { ...data, id, _id: id }, // always return original article IDs
    };
  } catch (error) {
    // Return the error
    return { error };
  }
}

/**
 * Publishes an article into one or more sites, and sets the canonical pages/URLs.
 *
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object
 * @param {Object} params.user User object
 * @param {Array} params.sites Sites to publish to
 * @param {String} params.canonicalSitePage Canonical site page id (for internal pages)
 * @param {String} params.canonicalUrl Canonical URL (if applicable, e.g. for external articles)
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function publishArticle({
  id,
  entity,
  user,
  sites,
  canonicalSitePage,
  canonicalUrl,
}) {
  // Check if the user is allowed to publish articles
  const canPublish = user?.hasPermission({
    module: 'articles',
    permission: 'publish',
  });

  // If the user is not allowed to publish articles, return an error
  if (!canPublish) {
    return {
      error: {
        message: 'You are not allowed to publish articles.',
        code: 'ARTICLES_PUBLISH_NOT_ALLOWED',
      },
    };
  }

  // Check if the user is allowed to approve articles
  const canApprove = user?.hasPermission({
    module: 'articles',
    permission: 'approve',
  });

  // Get current article, to check if its status is "approved"
  const article = await Article.findById(id);
  const isApproved = (await hasApprovalWorkflow({ entity }))
    ? article?.status === 'approved' // If the entity uses the approval workflow, check if the article is approved
    : true; // If the entity doesn't use the approval workflow, consider the article as approved

  // If the article is not approved, and the user is not allowed to approve articles, return an error
  if (!isApproved && !canApprove) {
    return {
      error: {
        message: 'You are not allowed to approve articles.',
        code: 'ARTICLES_PUBLISH_NOT_ALLOWED',
      },
    };
  }

  // If the article isn't approved yet, and the user is allowed to approve articles, approve the article
  const approveFields = !isApproved
    ? { approvedBy: user._id, status: 'approved' }
    : {};

  // First we find the article, and we check if it has shared: true and is syndicated in the current entity, or if the entity matches the current entity
  const isValidEntity =
    article.entity.toString() === entity._id.toString() ||
    (!article?.doNotShare &&
      article?.syndicatedIn?.includes(entity._id.toString()));

  if (!isValidEntity) {
    return {
      error: {
        message: 'This article is not valid for the current entity.',
        code: 'ARTICLES_PUBLISH_NOT_ALLOWED',
      },
    };
  }

  // Get the enabled sites ids
  const enabledSiteIds = Object.entries(sites)
    .filter(([, articleSite]) => articleSite.enabled)
    .map(([key]) => key);

  for (const siteId of enabledSiteIds) {
    // Get the site data to publish (omit _id field for olders records)
    const publishData = omit(sites[siteId], '_id') || {};

    // Check if the article is already published on the site
    const articleSite = await ArticleSite.findOneAndUpdate(
      {
        article: id,
        site: siteId,
      },
      { ...publishData }
    );

    // If the article hasn't been published on the site, create a new ArticleSite record for it
    if (!articleSite) {
      await ArticleSite.create({
        article: id,
        site: siteId,
        ...publishData,
      });
    }
  }

  // Disable all other sites that are not in the enabled list
  const disabledArticleSites = await ArticleSite.find({
    article: id,
    site: { $nin: enabledSiteIds },
  }).select('_id article site');

  if (disabledArticleSites?.length > 0) {
    await ArticleSite.updateMany(
      {
        _id: {
          $in: disabledArticleSites.map((articleSite) => articleSite._id),
        },
      },
      {
        enabled: false,
      }
    );

    // Notify event listeners that the article has been published
    eventEmitter.emit(articlesEvents.DISABLE_PUBLISHED_ARTICLES, {
      disabledArticleSites,
      entity,
    });
  }

  // Update the original article with the new data
  const updatedArticle = await Article.findOneAndUpdate(
    { _id: id },
    {
      ...approveFields, // Add approval fields (if applicable)
      canonicalSitePage: canonicalUrl ? undefined : canonicalSitePage, // Canonical site page id (e.g. for internal pages)
      canonicalUrl, // Canonical URL (e.g. for external articles)
    }
  );

  return { data: updatedArticle };
}

/**
 * Revets an article to a previous revision.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {String} params.revisionId the ID of the revision to revert to
 * @param {Object} params.entity Entity object
 * @param {Object} params.user User object
 * @param {String} params.status Status to set the reverted article to
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function revertToRevision({
  id,
  revisionId,
  entity,
  user,
  status = 'draft',
} = {}) {
  const article = await Article.findOne({
    _id: id,
    revisionOf: null,
    entity: entity._id,
  });

  const revision = await Article.findOne({
    _id: revisionId,
    revisionOf: id,
    entity: entity._id,
  });

  if (!revision) {
    return { error: `No revision found with ID ${revisionId}.` };
  }

  // Helper to get revision data without some fields
  function getRevisionData(rev) {
    return omit(rev.toObject(), [
      'id',
      '_id',
      'status',
      'sites',
      'createdAt',
      'updatedAt',
      'revisionOf',
      '__v',
    ]);
  }

  // Get a valid status based on user permissions
  const newStatus = await getValidStatus({ status, user, entity });

  // Check if the entity uses the approval workflow
  const entityHasApprovalWorkflow = await hasApprovalWorkflow({ entity });

  try {
    // Create a new revision
    const newRevision = await Article.create({
      ...getRevisionData(revision), // Omit fields that should not be copied
      status: newStatus, // Get a valid status based on user permissions
      revisionOf: id, // Reference the original article
      authoredBy: user._id, // Set the author to the current user
      entity: entity._id, // ensure current entity is set
    });

    // Update the original article with the new data:

    // Set status, authoredBy, latestRevision, latestRevisionStatus
    const statusAndRevisionFields = {
      authoredBy: user._id, // set the author to the current user (at this point, the editor/approver is the author of the revision)
      status: article.status === 'approved' ? 'approved' : newStatus, // if the original article is approved, keep it as approved, otherwise set it to the new status
      latestRevision: newRevision._id, // Set the latest revision to the new revision ID
      latestRevisionStatus: newStatus, // Add the latest revision status to improve filtering (e.g. to filter by "draft" articles)
      latestRevisionAt: newRevision.createdAt,
    };

    // - If the status is "approved", or the entity doesn't use the approval workflow...
    if (newStatus === 'approved' || !entityHasApprovalWorkflow) {
      // update the original article with the new revision data and set the status to "approved"
      await Article.updateOne(
        { _id: id, entity: entity._id },
        {
          ...getRevisionData(newRevision),
          ...statusAndRevisionFields,
        }
      );
    } else {
      // - Otherwise, just update the original's latestRevision to the new revision ID
      await Article.updateOne(
        { _id: id, entity: entity._id },
        { ...statusAndRevisionFields },
        { timestamps: false } // don't update timestamps (createdAt, updatedAt) of the original article
      );
    }

    // Return the new revision
    return { data: newRevision };
  } catch (error) {
    // Return the error
    return { error };
  }
}

const sortFields = {
  title: 'title',
  publishedAt: 'startsAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
};

/**
 * Get a list of articles for a given site, filtered by flags and date range. This is used to generate newsletters and mimics the frontend's behavior.
 * @param {Object} params Function parameters
 * @param {Array} params.flagExceptions Flags to exclude
 * @param {Array} params.flags Flags to filter by
 * @param {String} params.from From date
 * @param {String} params.getCount Wheterh to get the total number of articles
 * @param {String} params.siteId Site ID
 * @param {String} params.sortBy Sort by field
 * @param {String} params.sortDir Sort direction
 * @returns {Array} Array of articles
 */
export async function getArticlesForNewsletter({
  flagExceptions = [],
  flags = [],
  from,
  getCount,
  siteId: rawSiteId,
  sortBy = 'publishedAt',
  sortDir = 'desc',
}) {
  // Convert site ID to ObjectId
  const siteId = toObjectId(rawSiteId);

  // If the site is not found, return an error
  if (!siteId) {
    return { error: 'Site not found' };
  }

  // Calculate sort object based on sortBy and sortOrder
  const sort = {
    // Default to publishedAt (startsAt) field in descending order
    [sortFields[sortBy] ?? sortFields.publishedAt]: sortDir === 'asc' ? 1 : -1,
  };

  const aggregatePipeline = getArticlesForSiteAggregatePipeline({
    flagExceptions,
    flags,
    from,
    siteId,
  });

  // Aggregate the articles
  const articlesAggregate = ArticleSite.aggregate(aggregatePipeline, {
    allowDiskUse: true, // Allow disk use for large queries (if needed) // TODO: Check how can we avoid this and optimize the query without using disk
  });

  // Initialize the count to 0
  let count = 0;

  if (getCount) {
    // Get the total number of articles
    const totalArticlesAggregate =
      await ArticleSite.aggregate(aggregatePipeline).count('total');

    // Get the total number of articles
    count = totalArticlesAggregate[0]?.total;

    // If there are matched no articles, return an empty array
    if (!count) {
      return { items: [], count: 0 };
    }
  }

  // Sort, skip and limit the articles records and the fields to return
  articlesAggregate
    .sort(sort)
    // NOTE: We don't use skip and limit here, as we need to get all articles based on the last sync date for newsletter articles
    // .skip(pagination.skip)
    // .limit(pagination.limit || 25)

    // Populate categories
    .lookup({
      from: 'categories',
      as: 'categories',
      let: { categories: '$article.categories' },
      pipeline: [
        {
          // Match the categories that are enabled and not deleted
          $match: {
            deleted: false,
            enabled: true,
            $expr: {
              $in: [
                '$_id',
                {
                  $cond: [{ $isArray: '$$categories' }, '$$categories', []],
                },
              ],
            },
          },
        },
        // Sort the categories based on the order they appear in the article
        {
          $addFields: {
            sort: {
              $indexOfArray: ['$$categories', '$_id'],
            },
          },
        },
        {
          $sort: { sort: 1 },
        },
        // Project only the fields we need
        {
          $project: {
            _id: 1,
            title: 1,
            name: 1,
          },
        },
      ],
    })
    // Populate organizations
    .lookup({
      from: 'entities',
      localField: 'article.organizations',
      foreignField: '_id',
      as: 'organizations',
    })

    .lookup({
      from: 'sites',
      localField: 'site',
      foreignField: '_id',
      as: 'site',
    })
    .unwind({ path: '$site', preserveNullAndEmptyArrays: true })

    .addFields({
      flags: { $ifNull: ['$flags', {}] }, // Set flags to an empty object if it's null so we can use it in the next stage
    })

    // Get the matching site's flags (array with {id, name, disabled}) based on articleSite flags (object with flag IDs as keys and values as boolean to indicate if the flag is enabled). Only return the flags that aren't disabled.
    .addFields({
      flags: {
        $map: {
          input: { $objectToArray: '$flags' },
          as: 'flag',
          in: {
            $mergeObjects: [
              {
                id: '$$flag.k',
                enabled: '$$flag.v',
              },
              {
                $arrayElemAt: [
                  {
                    $filter: {
                      input: '$site.flags',
                      as: 'siteFlag',
                      cond: {
                        $eq: ['$$siteFlag.id', '$$flag.k'],
                      },
                    },
                  },
                  0,
                ],
              },
            ],
          },
        },
      },
    })

    // We expose the article ID as _id rather than the articleSite ID. This is required by e.g. the canonical URL generation
    .addFields({
      _id: '$article._id',
    })

    // Select only the fields we need
    .project({
      title: { $ifNull: ['$translatedArticle.title', '$article.title'] },
      subtitle: {
        $ifNull: ['$translatedArticle.subtitle', '$article.subtitle'],
      },
      slug: { $ifNull: ['$translatedArticle.slug', '$article.slug'] },
      abstract: {
        $ifNull: ['$translatedArticle.abstract', '$article.abstract'],
      },
      image: {
        file: {
          $ifNull: ['$translatedArticle.image.file', '$article.image.file'],
        },
        caption: {
          $ifNull: [
            '$translatedArticle.image.caption',
            '$article.image.caption',
          ],
        },
        altText: {
          $ifNull: [
            '$translatedArticle.image.altText',
            '$article.image.altText',
          ],
        },
        copyright: {
          $ifNull: [
            '$translatedArticle.image.copyright',
            '$article.image.copyright',
          ],
        },
      },
      location: {
        $ifNull: ['$translatedArticle.location', '$article.location'],
      },
      categories: 1,
      organizations: 1,
      canonicalUrl: '$article.canonicalUrl',
      canonicalSitePage: '$article.canonicalSitePage',
      publishedAt: '$startsAt',
      flags: {
        // Filter out the flags that are disabled
        $filter: {
          input: '$flags',
          as: 'flag',
          cond: {
            $and: [
              { $eq: ['$$flag.enabled', true] }, // ArticleSite flag is enabled
              { $ne: ['$$flag.disabled', true] }, // Site flag is not disabled
            ],
          },
        },
      },
    });

  const articles = await articlesAggregate;

  return { items: articles, count };
}

/**
 * Get a list of years and months for which there are articles for a given site.
 * @param {String} siteId Site ID
 * @returns {Array} Array of years and months
 */
export async function getArticleYearsAndMonthsForSite(siteId) {
  const articles = await ArticleSite.aggregate([
    {
      $match: {
        site: toObjectId(siteId),
        enabled: true,
      },
    },
    {
      $project: {
        year: {
          $dateToString: {
            format: '%Y',
            date: '$startsAt',
          },
        },
        month: {
          $dateToString: {
            format: '%m',
            date: '$startsAt',
          },
        },
      },
    },
    {
      $group: {
        _id: {
          year: '$year',
          month: '$month',
        },
      },
    },
    {
      $sort: {
        '_id.year': -1,
        '_id.month': -1,
      },
    },
  ]);

  return articles.map((article) => ({
    year: article._id.year,
    month: article._id.month,
  }));
}

/**
 * Calculates stats for articles for a given entity.
 * Stats are calculated for all sites of the entity unless siteId is provided.
 * Stats calculated are:
 * - total number of articles
 * - Articles published
 * - Articles in draft
 *
 * @param {Object} params Function parameters
 * @param {String} params.entityId Entity ID
 * @returns {Object} Object with stats
 */
export async function articlesStats({ entityId }) {
  const filter = {
    entity: entityId, // Only articles for this entity
    enabled: true, // enabled articles
    deleted: false, // not deleted articles
    revisionOf: null, // not revisions
    translationOf: null, // not translations
  };

  const stats = await Article.aggregate([
    {
      $match: filter,
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        draft: {
          $sum: {
            $cond: [
              {
                $or: [
                  { $eq: ['$status', 'draft'] },
                  { $eq: ['$latestRevisionStatus', 'draft'] },
                ],
              },
              1,
              0,
            ],
          },
        },
        done: {
          $sum: {
            $cond: [{ $eq: ['$status', 'done'] }, 1, 0],
          },
        },
        approved: {
          $sum: {
            $cond: [{ $eq: ['$status', 'approved'] }, 1, 0],
          },
        },
        // published: {
        //   $sum: {
        //     $cond: [{ $eq: [`$sites.${siteId}.enabled`, true] }, 1, 0],
        //   },
        // },
      },
    },
  ]);

  const results = stats.length > 0 ? stats[0] : null;

  return {
    total: results?.total || 0,
    draft: results?.draft || 0,
    done: results?.done || 0,
    approved: results?.approved || 0,
    published: results?.published || 0,
  };
}

/**
 * Creates an article's translation for a given locale.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object
 * @param {String} params.locale Locale to translate to
 * @param {Object} params.data Translation data
 * @param {Object} params.user User object
 * @returns {Object} Object with article data (in `data` attribute), or error (in `error` attribute)
 */
export async function translateArticle({ id, entity, locale, data, user }) {
  // Return error when either id or locale params are missing.
  if (!id || !locale) {
    return { error: 'translateArticle: Missing id or locale.' };
  }

  // If no user is provided, return error
  if (!user) {
    return { error: 'translateArticle: User not provided.' };
  }

  // Get source article
  const sourceArticle = await Article.findOne({
    _id: id,
    translationOf: null,
  });

  // Return error when source article is not found or already a translation.
  if (!sourceArticle) {
    return {
      error:
        'translateArticle: Source article not found or already a translation.',
    };
  }

  // Check if the article is syndicated in current entity
  const isSyndicated =
    entity?._id?.toString() !== sourceArticle.entity?.toString();

  // Get entity config to check if translations are allowed for the entity
  const { data: entityConfig } = await getArticlesConfig({
    entity: isSyndicated ? await Entity.findById(sourceArticle.entity) : entity,
    networkId: entity.network,
  });

  // if entity doesn't allow translations of its shared article, return error
  if (entityConfig && entityConfig?.disableTranslationOfShared) {
    return {
      error:
        'translateArticle: Article translations are disabled for the entity owner of this article.',
    };
  }

  // If it's a syndicated article and translations aren't allowed for it, return error
  if (isSyndicated && sourceArticle.disableTranslations) {
    return {
      error:
        'translateArticle: Article translations are disabled for this syndicated article.',
    };
  }

  // Check if translation already exists
  const existingTranslation = await Article.findOne({
    translationOf: id,
    locale,
    entity: entity._id,
  });

  // Return error when translation already exists.
  if (existingTranslation) {
    return { error: 'translateArticle: Translation already exists.' };
  }

  try {
    // Create translation
    const articleTranslation = await Article.create({
      ...data,
      translationOf: sourceArticle._id,
      language: locale,
      entity: entity._id,
      authoredBy: user._id,
    });

    // Update source article's translation stats (total and done):
    updateArticleTranslationsStats(sourceArticle._id, entity);

    return { data: articleTranslation };
  } catch (error) {
    console.error(error); // eslint-disable-line no-console
    return { error: 'translateArticle: Error creating article translation.' };
  }
}

/**
 * Updates the translation stats for an article.
 * NOTE: This function needs to called when a translation is created, updated or deleted.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @param {Object} params.entity Entity object
 * @returns {void} No return value (it will update the original article's translation stats)
 */
async function updateArticleTranslationsStats(originalArticleId, entity) {
  // - Get translations
  const translations = await getArticleTranslations(originalArticleId, entity, {
    ownTranslations: true,
  });

  const translationsStats = translations.reduce(
    (acc, translation) => {
      // Global translations stats
      acc.total += 1;

      if (!acc.languages.includes(translation.language)) {
        acc.languages.push(translation.language);
      }

      // Draft translations stats
      if (translation.status === 'draft') {
        acc.draft += 1;

        if (!acc.languagesDraft.includes(translation.language)) {
          acc.languagesDraft.push(translation.language);
        }
      }

      // Done translations stats
      if (translation.status === 'done') {
        acc.done += 1;

        if (!acc.languagesDone.includes(translation.language)) {
          acc.languagesDone.push(translation.language);
        }
      }

      // Approved translations stats
      if (translation.status === 'approved') {
        acc.approved += 1;

        if (!acc.languagesApproved.includes(translation.language)) {
          acc.languagesApproved.push(translation.language);
        }
      }

      return acc;
    },
    {
      total: 0,
      draft: 0,
      done: 0,
      approved: 0,
      languages: [],
      languagesDraft: [],
      languagesDone: [],
      languagesApproved: [],
    }
  );

  // - Update original source article with new translation stats
  await Article.updateOne(
    { _id: originalArticleId, translationOf: null, revisionOf: null }, // only an original article (not translations or revisions)
    {
      translationsStats,
    }
  );
}

/**
 * Helper to get middleware permissions array for a given article.
 * NOTE: It checks if the article is a translation or not, and not the user permissions.
 * @param {Object} params Function parameters
 * @param {String} params.id Article ID
 * @returns {Array} Array of permissions
 */
export async function getArticlePermissions({ id }) {
  // Get the article to check if it's a translation
  const article = await Article.findById(id);

  // If the article doesn't exist, return an empty array
  if (!article) {
    return [];
  }
  // If the article is a translation of another one, we check for the 'translate' permission, otherwise for 'update'
  return article.translationOf ? ['translate'] : ['update'];
}

/**
 * Helper to get a valid slug for an article.
 * @param {Object} params Function parameters
 * @param {String} params.slug The slug to check
 * @param {String} params.entity The entity object
 * @param {String} params.language The language code
 * @param {String} params.excludeId The article ID to exclude
 * @returns {Promise<String>} A valid slug
 */
export async function getAvailableSlug({
  slug,
  entity,
  language = null,
  excludeId,
}) {
  const query = { slug, deleted: false };

  if (entity) {
    query.entity = entity;
  }

  if (language) {
    query.language = language;
  }

  // Not a revisionOf
  query.revisionOf = null; // This will count not having a revisionOf field as valid

  if (excludeId) {
    query._id = { $ne: excludeId };
  }

  const existingArticle = await Article.findOne(query);

  return existingArticle ? uniquifySlug(slug) : slug;
}

export async function getArticleLinkedItems({
  articleId,
  entity,
  sort = 'title',
  order,
  type,
}) {
  const includeEpisodes = !type || Boolean(type?.includes('episode'));
  const includePublicationIssues =
    !type || Boolean(type?.includes('publication-issue'));

  const { data, error } = await getArticleById({
    id: toObjectId(articleId),
    entity,
    populate: [
      ...(includeEpisodes
        ? [
            {
              path: 'linkedEpisodes',
              populate: {
                path: 'episode',
                model: 'Episode',
                select: 'title slug image channel show season',
                populate: [
                  {
                    path: 'channel',
                    select: 'title translations',
                  },
                  {
                    path: 'show',
                    select: 'title channel',
                  },
                  {
                    path: 'season',
                    select: 'title groupName',
                  },
                ],
              },
            },
          ]
        : []),
      ...(includePublicationIssues
        ? [
            {
              path: 'linkedPublicationIssues',
              populate: {
                path: 'publicationIssue',
                model: 'PublicationIssue',
                select: 'title slug cover volume',
                populate: [
                  {
                    path: 'publication',
                    select: 'title volumes',
                  },
                ],
              },
            },
          ]
        : []),
    ],
  });

  if (!data || error) {
    return { error: errors.not_found('Article') };
  }

  return {
    linkedItems: {
      'episode': includeEpisodes
        ? (data.linkedEpisodes?.sort((a, b) =>
            order === 'asc'
              ? a.episode[sort] > b.episode[sort]
                ? 1
                : -1
              : a.episode[sort] < b.episode[sort]
                ? 1
                : -1
          ) ?? [])
        : [],
      'publication-issue': includePublicationIssues
        ? (data.linkedPublicationIssues?.sort((a, b) =>
            order === 'asc'
              ? a.publicationIssue[sort] > b.publicationIssue[sort]
                ? 1
                : -1
              : a.publicationIssue[sort] < b.publicationIssue[sort]
                ? 1
                : -1
          ) ?? [])
        : [],
    },
  };
}

export async function addLinkedEpisode({ articleId, episodeId, defaultItem }) {
  try {
    const article = await Article.findById(toObjectId(articleId));

    if (!article) {
      return { error: errors.not_found('Article', articleId) };
    }

    const existingEpisodeIndex = article.linkedEpisodes?.findIndex(
      (e) => e.episode.toString() === episodeId
    );

    if (existingEpisodeIndex > -1) {
      return { error: errors.already_exists('Episode', episodeId) };
    }

    // If the new episode is set as default, remove the default flag from the other linked episodes
    if (defaultItem) {
      article.linkedEpisodes?.forEach((e) => {
        e.defaultItem = false;
      });
    }

    article.linkedEpisodes.push({
      episode: toObjectId(episodeId),
      defaultItem,
    });

    await article.save();

    return { article };
  } catch (error) {
    return { error };
  }
}

export async function updateLinkedEpisode({
  articleId,
  linkedItemId,
  episodeId,
  defaultItem,
}) {
  try {
    const article = await Article.findById(toObjectId(articleId));

    if (!article) {
      return { error: errors.not_found('Article', articleId) };
    }

    const linkedEpisode = article.linkedEpisodes?.id(toObjectId(linkedItemId));

    if (!linkedEpisode) {
      return { error: errors.not_found('Linked episode', linkedItemId) };
    }

    linkedEpisode.episode = toObjectId(episodeId);
    linkedEpisode.defaultItem = defaultItem;

    if (defaultItem) {
      article.linkedEpisodes?.forEach((e) => {
        if (e._id.toString() !== linkedItemId) {
          e.defaultItem = false;
        }
      });
    }

    await article.save();

    return { article };
  } catch (error) {
    return { error };
  }
}

export async function deleteLinkedEpisode({ articleId, linkedItemId }) {
  try {
    const article = await Article.findById(toObjectId(articleId));

    if (!article) {
      return { error: errors.not_found('Article', articleId) };
    }

    await article.linkedEpisodes?.id(toObjectId(linkedItemId)).deleteOne();

    if (article.linkedEpisodes?.length === 1) {
      article.linkedEpisodes[0].defaultItem = true;
    }

    await article.save();

    return { article };
  } catch (error) {
    return { error };
  }
}

export async function addLinkedPublicationIssue({
  articleId,
  defaultItem,
  publicationId,
  publicationIssueId,
}) {
  try {
    const article = await Article.findById(toObjectId(articleId));

    if (!article) {
      return { error: errors.not_found('Article', articleId) };
    }

    const existingPublicationIssueIndex =
      article.linkedPublicationIssues?.findIndex(
        (e) => e.publicationIssue.toString() === publicationIssueId
      );

    if (existingPublicationIssueIndex > -1) {
      return {
        error: errors.already_exists('PublicationIssue', publicationIssueId),
      };
    }

    // If the new episode is set as default, remove the default flag from the other linked episodes
    if (defaultItem) {
      article.linkedPublicationIssues?.forEach((e) => {
        e.defaultItem = false;
      });
    }

    article.linkedPublicationIssues.push({
      publication: toObjectId(publicationId),
      publicationIssue: toObjectId(publicationIssueId),
      defaultItem,
    });

    await article.save();

    return { article };
  } catch (error) {
    return { error };
  }
}

export async function updateLinkedPublicationIssue({
  articleId,
  linkedItemId,
  publicationId,
  publicationIssueId,
  defaultItem,
}) {
  try {
    const article = await Article.findById(toObjectId(articleId));

    if (!article) {
      return { error: errors.not_found('Article', articleId) };
    }

    const linkedPublicationIssue = article.linkedPublicationIssues?.id(
      toObjectId(linkedItemId)
    );

    if (!linkedPublicationIssue) {
      return {
        error: errors.not_found('Linked publication issue', linkedItemId),
      };
    }

    linkedPublicationIssue.publication = toObjectId(publicationId);
    linkedPublicationIssue.publicationIssue = toObjectId(publicationIssueId);
    linkedPublicationIssue.defaultItem = defaultItem;

    if (defaultItem) {
      article.linkedPublicationIssues?.forEach((e) => {
        if (e._id.toString() !== linkedItemId) {
          e.defaultItem = false;
        }
      });
    }

    await article.save();

    return { article };
  } catch (error) {
    return { error };
  }
}

export async function deleteLinkedPublicationIssue({
  articleId,
  linkedItemId,
}) {
  try {
    const article = await Article.findById(toObjectId(articleId));

    if (!article) {
      return { error: errors.not_found('Article', articleId) };
    }

    await article.linkedPublicationIssues
      ?.id(toObjectId(linkedItemId))
      .deleteOne();

    if (article.linkedPublicationIssues?.length === 1) {
      article.linkedPublicationIssues[0].defaultItem = true;
    }

    await article.save();

    return { article };
  } catch (error) {
    return { error };
  }
}
