import getEntityAncestorAttribute from '#modules/entities/helpers/getEntityAncestorAttribute.js';
import { logError } from '#utils/logger.js';

import ArticlesConfig from '../models/ArticlesConfig.js';

/**
 * @typedef {import('../models/ArticlesConfig.js').ArticlesConfig} ArticlesConfig Articles module configuration for an entity/network
 *
 * Get articles module configuration for a given entity or network.
 * @param {Object} params Function parameters
 * @param {Object} params.entity Entity object (required if `entityId` is not provided)
 * @param {String} params.entityId Entity ID (required if `entity` is not provided)
 * @param {String} params.networkId Network ID (optional)
 * @returns {Promise<{data: ArticlesConfig, error: Error}>} Object with articles module configuration
 */
export async function getArticlesConfig({ entity, networkId }) {
  // If no entity is provided...
  if (!entity) {
    return { error: 'getArticlesConfig: No entity provided.' };
  }

  try {
    // Check if a networkId is provided...
    if (networkId) {
      // Check if the network has an articles configuration
      const networkIdArticlesConfig = await ArticlesConfig.findOne({
        network: networkId,
      });

      // If exists, return it
      if (networkIdArticlesConfig) {
        return { data: networkIdArticlesConfig };
      }
    }

    // Check if the entity has an articles config...
    const entityArticlesConfig = await ArticlesConfig.findOne({
      entity: entity._id,
    });

    // If the entity has an articles config, return it
    if (entityArticlesConfig) {
      return { data: entityArticlesConfig };
    }

    // If the entity doesn't have an articles config, check if the network does
    const closestNetwork =
      entity.network || (await getEntityAncestorAttribute(entity, 'network'));
    const networkArticlesConfig = await ArticlesConfig.findOne({
      network: closestNetwork?._id,
    });

    // If the network has an articles config, return it
    if (networkArticlesConfig) {
      return { data: networkArticlesConfig };
    }

    // If neither the entity nor the network have an articles config, return null

    return { data: null };
  } catch (error) {
    logError(error);
    return { error };
  }
}

/**
 * Set articles module configuration for a given entity.
 * @param {Object} params Function parameters
 * @param {Object} params.entity Entity object
 * @param {Object} params.user User object
 * @param {Object} params.config Articles module configuration
 * @returns {Object} Object with updated article data (in `data` attribute), or error (in `error` attribute)
 */
export async function setArticlesConfig({
  entity,
  networkId,
  user,
  config = {},
}) {
  // Check if the user has permission to configure the articles module
  const canConfigure =
    user?.hasPermission({ module: 'articles', permission: 'configure' }) ||
    false;

  // If a network is provided and the user is not an admin...
  if (networkId && !user?.isAdmin) {
    // Return an error
    return {
      error:
        'You do not have permission to configure articles module in a network',
    };
  }

  // If the user doesn't have permission...
  if (!canConfigure) {
    // Return an error
    return {
      error: 'You do not have permission to configure articles module.',
    };
  }

  try {
    const { data: articlesConfig, error } = await getArticlesConfig({
      entity,
      networkId,
    });

    // If there's an error, return it
    if (error) {
      return { error };
    }

    // If the articleConfig exists,
    if (articlesConfig) {
      // Update it with the new params
      const updatedArticlesConfig = await ArticlesConfig.findByIdAndUpdate(
        articlesConfig._id,
        { ...config },
        { new: true, runValidators: true }
      );

      // Return the updated article
      return {
        data: updatedArticlesConfig,
      };
    }

    // Otherwise, create a new articleConfig
    const newArticlesConfig = await ArticlesConfig.create({
      entity,
      ...config,
    });

    // Return the new articleConfig
    return {
      data: newArticlesConfig,
    };
  } catch (error) {
    // Return the error
    return { error };
  }
}

export async function hasApprovalWorkflow({ entity }) {
  // Check if the entity has an articles config...
  const { data: articlesConfig } = await getArticlesConfig({ entity });

  // And if it has an approval workflow enabled, return true (otherwise, return false)
  return articlesConfig?.hasApprovalWorkflow || false;
}

/**
 * @typedef {'article'|'news'|'interview'|'story'|'editorial'|'opinion'|'faq'|'tutorial'|'recipies'|'infographic'|'report'} ArticleType A valid Article type
 *
 * @typedef {Object} ArticleTypeConfig Configuration of article types for an entity/network
 * @property {Boolean} article Whether the article type is enabled
 * @property {Boolean} news Whether the article type is enabled
 * @property {Boolean} interview Whether the article type is enabled
 * @property {Boolean} story Whether the article type is enabled
 * @property {Boolean} editorial Whether the article type is enabled
 * @property {Boolean} opinion Whether the article type is enabled
 * @property {Boolean} faq Whether the article type is enabled
 * @property {Boolean} tutorial Whether the article type is enabled
 * @property {Boolean} recipies Whether the article type is enabled
 * @property {Boolean} infographic Whether the article type is enabled
 * @property {Boolean} report Whether the article type is enabled
 *
 * List of available article types
 * // NOTE: IMPORTANT: Updating this list will require migrating ArticleConfig records in the database that may have articleTypes no longer available.
 * @type {Array<ArticleType>}
 */
export const allArticleTypes = [
  'article', // Evergreen content // NOTE: this is the fallback type
  'news', // Time-sensitive content
  'interview', // Q&A with someone
  'story', // Narrative or anecdotal
  'editorial', // Opinion from the editor
  'opinion', // Third-party opinion or commentary
  'faq', // Frequently asked questions
  'tutorial', // Step-by-step instructions
  'recipies', // Vegan cooking or baking instructions
  'infographic', // Visual representation of data
  'report', // Research or analysis of data
  'bookReview', // Review of a book
];

/**
 * Returns an array of article types
 * @param {Object} params Function parameters
 * @param {Boolean} params.filterAllowed Whether to filter out disallowed article types
 * @param {Object} params.entity Entity object
 * @returns {Promise<Object>} Object with article types array and default article type
 */
export async function getArticleTypesForEntity({
  filterAllowed = false,
  entity = null,
}) {
  // If we don't need to filter out disallowed article types, return all of them
  if (!filterAllowed) {
    return { articleTypes: allArticleTypes };
  }

  // Get the articles config for the entity

  const { data: articlesConfig, error } = await getArticlesConfig({
    entity,
  });

  // If there's an error, return it
  if (error) {
    return {
      error,
    };
  }

  const {
    articleTypes,
    dontUseArticleTypes,
    defaultArticleType = 'article',
  } = articlesConfig || {};

  // If the entity doesn't use article types, return articleTypes as an empty array
  if (dontUseArticleTypes) {
    return { articleTypes: [] };
  }

  // Otherwise, return only enabled article types and the default article type
  return {
    articleTypes: articleTypes
      ? allArticleTypes.filter((type) => articleTypes[type] === true)
      : allArticleTypes,
    defaultArticleType,
  };
}

/**
 * Check if the article type is valid
 * @param {String} type The article type to check
 * @param {Object} entity The entity object
 * @returns {Promise<Boolean>} True if the type is valid, false otherwise
 */
export async function getValidArticleType(type, entity = null) {
  const { articleTypes, defaultArticleType, error } =
    await getArticleTypesForEntity({
      entity,
      filterAllowed: true,
    });

  if (error) {
    logError('getValidArticleType', error);
    return false;
  }

  // If the type is valid, return it directly
  if (articleTypes.includes(type)) {
    return type;
  }

  // Otherwise, if the default article type is valid, return it
  if (defaultArticleType && articleTypes.includes(defaultArticleType)) {
    return defaultArticleType;
  }

  // Finally, return the fallback valid article type
  return 'article';
}

/**
 * @typedef {Object} ArticlesFieldConfig Article field configuration
 * @property {Boolean} mandatory Whether the field is mandatory
 * @property {Boolean} preset Whether the field is preset
 * @property {Boolean} required Whether the field is required (it can be overridden by the entity/network)
 * @property {String} label Field label (for the UI)
 * @property {Boolean} defaultRequired Whether the field is required by default (only available when getting the fields)
 * @property {Boolean} mandatoryRequired Whether the field is mandatory required and cant be changed (only available when getting the fields)
 * @property {Boolean} visible Whether the field will be displayed (only available when getting the fields)
 */

/**
 * @typedef {Object} ArticlesFieldsMap Map of articles fields
 * @property {ArticlesFieldConfig} title Title field configuration
 * @property {ArticlesFieldConfig} slug Slug field configuration
 * @property {ArticlesFieldConfig} subtitle Subtitle field configuration
 * @property {ArticlesFieldConfig} body Body field configuration
 * @property {ArticlesFieldConfig} abstract Abstract field configuration
 * @property {ArticlesFieldConfig} pullQuote Pull quote field configuration
 * @property {ArticlesFieldConfig} imageCaption Image caption field configuration
 * @property {ArticlesFieldConfig} imageAlt Image alt field configuration
 * @property {ArticlesFieldConfig} imageCopyright Image copyright field configuration
 * @property {ArticlesFieldConfig} location Location field configuration
 * @property {ArticlesFieldConfig} organizations Organizations field configuration
 * @property {ArticlesFieldConfig} author Author field configuration
 * @property {ArticlesFieldConfig} authorEmail Author email field configuration
 * @property {ArticlesFieldConfig} authors Authors field configuration
 * @property {ArticlesFieldConfig} files Files field configuration
 * @property {ArticlesFieldConfig} categories Categories field configuration
 * @property {ArticlesFieldConfig} tags Tags field configuration
 */
const articlesFieldsMap = {
  title: { mandatory: true, preset: true, required: true },
  slug: { mandatory: true, preset: true, required: true },
  subtitle: { mandatory: false, preset: true, required: false },
  body: { mandatory: true, preset: true, required: true },
  abstract: { mandatory: false, preset: true, required: false },
  pullQuote: { mandatory: false, preset: true, required: false },
  imageFile: {
    mandatory: true,
    mandatoryRequired: false,
    preset: true,
    required: false,
    defaultRequired: true,
  },
  imageCaption: { mandatory: false, preset: true, required: false },
  imageAlt: { mandatory: false, preset: true, required: false },
  imageCopyright: { mandatory: false, preset: true, required: false },
  language: { mandatory: true, preset: true, required: true },
  location: { mandatory: false, preset: true, required: false },
  type: {
    mandatory: true,
    preset: true,
    required: false,
    defaultRequired: false,
  },
  organizations: { mandatory: false, preset: true, required: false },
  author: { mandatory: false, preset: true, required: false },
  authorEmail: { mandatory: false, preset: true, required: false },
  authors: { mandatory: false, preset: false, required: false },
  extraContent: { mandatory: false, preset: false, required: false },
  files: { mandatory: false, preset: true, required: false },
  bibleReferences: { mandatory: false, preset: false, required: false },
  categories: { mandatory: false, preset: true, required: false },
  tags: { mandatory: false, preset: true, required: false },
};

/**
 * Get the allowed article fields for an entity/network
 * @param {Object} params Function parameters
 * @param {Object} params.entity Entity object (or its ancestors, or its network)

 * @returns {Promise<{availableFields: ArticlesFieldsMap, error: Error}>} Object with allowed article fields
 */
export async function getArticlesFieldsForEntity({ entity }) {
  // Get the articles config for the entity
  const { data: articlesConfig, error } = await getArticlesConfig({
    entity,
  });

  if (error) {
    logError('getArticlesFields', error);
    return { error };
  }

  const { fields = {} } = articlesConfig || {};

  // Go through all fields
  const availableFields = Object.keys(articlesFieldsMap).reduce((acc, key) => {
    // if key is 'type',
    if (key === 'type') {
      // Check if the entity/network doesn't use article types
      if (articlesConfig?.dontUseArticleTypes) {
        // Skip the type field
        return acc;
      }
    }

    // Get the field configuration
    /** @type {ArticlesFieldConfig} */
    const { mandatory, preset, required, defaultRequired, mandatoryRequired } =
      articlesFieldsMap[key];

    // Set the available fields, merging the default fields with the entity/network fields configuration
    /** @type {ArticlesFieldConfig} */
    const fieldConfig = {
      mandatory,
      preset,
      required, // It may be overridden by the entity/network
      visible: preset, // Set the field as visible if it's preset is true
      defaultRequired,
      mandatoryRequired: mandatoryRequired ?? mandatory,
      ...fields[key],
    };

    acc[key] = fieldConfig;

    // Return the accumulator
    return acc;
  }, {});

  return { availableFields };
}
