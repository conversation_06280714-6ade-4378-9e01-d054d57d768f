import { getJSONOpenAIRequest } from '#modules/ai/helpers/openai.js';

/**
 * Categorize the content using OpenAI API
 * @param {Object} params The parameters object
 * @param {String} params.title The article title
 * @param {String} params.subtitle The article subtitle
 * @param {String} params.abstract The article abstract
 * @param {String} params.body The article body (as plain text)
 * @param {String[]} params.tags The article tags
 * @param {String[]} params.availableCategories The available categories to use
 * @returns {Promise<String[]>} The categories array
 */
export async function categorizeArticleWithAI({
  title,
  subtitle,
  abstract,
  body,
  tags,
  availableCategories,
}) {
  // Extract the categories from the title, abstract and tags using OpenAI
  const content = await getJSONOpenAIRequest({
    prompt: `From the list of available categories, categorize the following article based on its title, subtitle, abstract, body text, location and tags. Return a JSON array only with the available categories.

     - Available categories, comma-separated:
        "${availableCategories.join(', ')}"

     - Article values:
        Title: "${title || ''}"
        Subtitle: "${subtitle || ''}"
        Abstract: "${abstract || ''}"
        Body: "${body || ''}"
        Tags: "${tags ? tags?.join(', ') : ''}"`,
  });

  // Get the categories from the parsed content or return an empty array
  return content?.categories || [];
}
