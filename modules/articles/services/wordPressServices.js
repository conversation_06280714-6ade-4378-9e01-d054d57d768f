import ky from 'ky';

import Logger from '#utils/logger.js';

export async function getPosts(apiUrl, params = {}) {
  try {
    if (!apiUrl) {
      return [];
    }
    const query = new URLSearchParams(params);
    const response = await ky.get(`${apiUrl}/posts?${query.toString()}`);
    const data = await response.json();
    const headers = Object.fromEntries(response.headers.entries());
    return { posts: data, headers };
  } catch (error) {
    Logger.error(
      `Error querying WordPress posts with params ${JSON.stringify(params)}`,
      error
    );
    return [];
  }
}

export async function getCategories(apiUrl, params = { per_page: 100 }) {
  try {
    if (!apiUrl) {
      return [];
    }
    const query = new URLSearchParams(params);
    const data = await ky
      .get(`${apiUrl}/categories?${query.toString()}`)
      .json();
    return data;
  } catch (error) {
    Logger.error(
      `Error querying WordPress categories with params ${JSON.stringify(
        params
      )}`,
      error
    );
    return [];
  }
}

export async function getTags(apiUrl, params = { per_page: 100 }) {
  try {
    if (!apiUrl) {
      return [];
    }
    const query = new URLSearchParams(params);
    const data = await ky.get(`${apiUrl}/tags?${query.toString()}`).json();
    return data;
  } catch (error) {
    Logger.error(
      `Error querying WordPress tags with params ${JSON.stringify(params)}`,
      error
    );
    return [];
  }
}

export async function getImage(apiUrl, mediaId) {
  try {
    const data = await ky.get(`${apiUrl}/media/${mediaId}`).json();
    return data;
  } catch (error) {
    Logger.error(`Error querying WordPress media ${mediaId}`, error);
    return null;
  }
}
