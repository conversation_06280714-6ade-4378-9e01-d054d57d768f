import { getImageUrl } from '#utils/images.js';
import formatArticlePagePath from './formatArticlePagePath.js';
import getCanonicalUrl from './getCanonicalUrl.js';

/**
 * Get the article item for a specific site
 * @param {Object} options
 * @param {Object} options.article - The article
 * @param {Object} options.detailPage - The detail page
 * @param {Boolean} options.includeSiteDomain - Whether to include the site domain in the URL
 * @param {Object} options.site - The site
 * @param {Object} options.siteOverride - The site override. This is used to get the article for a specific site which is different to the request site
 * @returns {Object} The article item for the site
 */
export default async function getArticleItemForSite({
  article,
  detailPage,
  includeSiteDomain,
  site,
  siteOverride,
}) {
  // If the article or site is not found, return null
  if (!article) return null;

  const { flags, publishedAt, slug } = article;
  let url;

  // If the site override is provided,

  if (siteOverride) {
    // get the canonical URL for the article based on the site override
    const { canonicalUrl } = await getCanonicalUrl({
      article,
      site: siteOverride, // ASK: @glensomerville - Can we only have one of the site or siteOverride?
    });
    // and set the URL to the canonical URL
    url = canonicalUrl;
  } else {
    // Otherwise, get the page path for the article based on the detail page
    const pagePath = formatArticlePagePath({
      page: detailPage,
      publishedAt,
      slug,
    });

    // and set the URL to the page path
    url = includeSiteDomain
      ? `${site.domain.startsWith('localhost') ? 'http' : 'https'}://${site.domain}${pagePath}`
      : pagePath;
  }

  // Remove the canonical site page from the article source to prevent outputting it in the response
  delete article.canonicalSitePage;
  delete article.canonicalUrl;
  delete article.sites; // TODO: Remove this once Article's sites object is no longer needed and is removed from the model and the the records

  return {
    ...article, // NOTE: Article should be filtered at this point with only data that is needed for the response exposed to the public.
    image: getImageUrl(article.image.file),
    url,
    flags:
      // Filter the flags to only include the ID, name, and label
      flags?.map(({ id, name, label }) => ({
        id,
        name,
        label,
      })) ?? [],
  };
}
