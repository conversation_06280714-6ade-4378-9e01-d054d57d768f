import pageServices from '#modules/web/services/pageServices.js';
import Logger from '#utils/logger.js';
import ArticleSite from '../models/ArticleSite.js';
import formatArticlePagePath from './formatArticlePagePath.js';

export default async function getCanonicalUrl({ article, site }) {
  const {
    canonicalSitePage: canonicalSitePageId,
    canonicalUrl: staticCanonicalUrl,
  } = article;
  // Initialize canonical site domain to the current site
  let canonicalSiteDomain = site.domain;

  // If there is a static canonical url, return it immediately
  if (staticCanonicalUrl) {
    return {
      canonicalUrl: staticCanonicalUrl,
      canonicalSiteDomain,
    };
  }

  // If there is no canonical site page, log a warning and return only the canonical site domain
  if (!canonicalSitePageId) {
    Logger.warning(
      `Articles - getCanonicalUrl: No canonical site page or static canonical URL found for article ${article._id}`
    );
    return {
      canonicalUrl: '',
      canonicalSiteDomain,
    };
  }

  // Get the canonical site page by ID
  const canonicalSitePage = await pageServices.getPage(canonicalSitePageId, {
    populate: ['site'],
    lean: true,
  });

  // Get the canonical articleSite record for the article and canonical site
  const canonicalArticleSite = await ArticleSite.findOne({
    article: article._id,
    site: canonicalSitePage?.site?._id,
  });

  // If the canonical site page and canonical article site are found, generate the canonical URL:
  if (canonicalSitePage && canonicalArticleSite) {
    // Get the canonical domain
    const canonicalDomain = canonicalSitePage.site.domain;

    // Generate the canonical URL
    const pagePath = formatArticlePagePath({
      page: canonicalSitePage,
      publishedAt: canonicalArticleSite.startsAt,
      slug: article.slug,
    });

    // If the canonical domain is different to the current site domain, overwrite the canonical site domain
    if (canonicalDomain !== site.domain) {
      canonicalSiteDomain = canonicalDomain;
    }

    // Determine the protocol based on the canonical domain (use http for localhost)
    const protocol = canonicalDomain.startsWith('localhost') ? 'http' : 'https';

    return {
      canonicalUrl: `${protocol}://${canonicalDomain}${pagePath}`,
      canonicalSiteDomain,
    };
  }

  Logger.warning(
    `Articles - getCanonicalUrl: Canonical site page or canonical article site not found for article ${article._id}`
  );

  // If the canonical site page or canonical article site are not found, log a warning and return only the canonical site domain
  return {
    canonicalUrl: '',
    canonicalSiteDomain,
  };
}
