import fs from 'fs/promises';
import Handlebars from 'handlebars';
import { DateTime } from 'luxon';
import mjml2html from 'mjml';
import path from 'path';
import url from 'url';

import Page from '#modules/web/models/Page.js';
import Site from '#modules/web/models/Site.js';
import Logger from '#utils/logger.js';

import getArticleItemForSite from '#modules/articles/helpers/getArticleItemForSite.js';
import { getArticlesForNewsletter } from '../../services/articlesServices.js';
import {
  createCampaignDraft,
  createCampaignDraftContent,
  sendCampaignDraft,
} from '../../services/mailjetServices.js';

const __dirname = url.fileURLToPath(new URL('.', import.meta.url));

async function getArticleDetailPage(task) {
  const { articleDetailPageId } = task.settings;

  try {
    return await Page.findById(articleDetailPageId);
  } catch (_err) {
    Logger.error(
      'Mailjet article error',
      `Error generating article detail page url for pageId: ${articleDetailPageId}`
    );
  }
}

async function getCampaignArticles(site, task) {
  try {
    const { items: articles, error } = await getArticlesForNewsletter({
      flagExceptions: task.settings.flagExceptions,
      flags: task.settings.flags,
      from: task.lastSync,
      siteId: site.id,
    });

    if (error) {
      Logger.error(
        'Mailjet articles error',
        `Error fetching articles for campaign`
      );
      return [];
    }

    if (!articles) {
      Logger.info('No articles found to send.');
      return [];
    }

    Logger.info(`Found ${articles.length} articles to send.`);

    const articleDetailPage = await getArticleDetailPage(task);

    const articlesForSite = await Promise.all(
      articles.map(
        async (item) =>
          await getArticleItemForSite({
            article: item,
            detailPage: articleDetailPage,
            includeSiteDomain: true,
            site,
          })
      )
    );

    return articlesForSite.map(
      ({ location, organizations, publishedAt, ...article }) => ({
        ...article,
        publishedAt: DateTime.fromJSDate(publishedAt)
          .setLocale(site.language || 'en-US')
          .toLocaleString(DateTime.DATE_FULL),
        location: location.nameOverride || location.placeName,
        organizations: organizations
          ?.map((organization) => organization.name)
          .join(', '),
      })
    );
  } catch (_err) {
    Logger.error(
      'Mailjet articles error',
      `Error fetching articles for campaign`
    );

    return [];
  }
}

async function generateCampaignContent(site, task, articles) {
  const { template } = task.settings;

  try {
    Logger.info(`Generating campaign content`);

    const mjml = await fs.readFile(
      path.join(__dirname, 'templates', `${template}.mjml`),
      { encoding: 'utf8' }
    );

    const mjmlTemplate = Handlebars.compile(mjml);

    const mjmlResult = mjmlTemplate({
      articles,
      dateSent: DateTime.now()
        .setLocale(site.language ?? 'en_US')
        .toLocaleString(DateTime.DATE_FULL),
      domain: site.domain,
      subject: task.settings.subject,
    });
    const { html } = mjml2html(mjmlResult);

    return html;
  } catch (_err) {
    Logger.error(
      'Mailjet content error',
      `Error generating content for campaign with template ${template}`
    );
  }
}

async function contentToCampaignDraft(site, draftId, content) {
  Logger.info(`Setting campaign draft content`);
  await createCampaignDraftContent(site, draftId, {
    'Headers': {},
    'Html-part': content,
  });
}

export default async function runMailjetNewsletter(task) {
  /**
   * EXECUTION PLAN
   *
   * 1. Retrive articles for newsletter
   * 2. Generate campaign email HTML
   *   - Generated from a predefined template saved in `./templates`
   *   - Use task settings to determine template
   * 3. Create Mailjet campaign draft
   *   - Use site settings to determine contact list
   *   - Use task settings to params
   * 4. Create the content for the campaign draft using the HTML from step 1
   * 5. Send the campaign
   */

  const { site: siteId } = task?.settings ?? {};

  if (!siteId) {
    Logger.error(
      `Error running Mailjet Campaign task (${task.id})`,
      'Task not properly configured'
    );
  }

  const site = await Site.findById(siteId);

  Logger.info('');
  Logger.info(`Running Mailjet newsletter campaign for ${site.title}`);
  Logger.info('-----------------------------------------------------');

  const articles = await getCampaignArticles(site, task);

  if (articles.length > 0) {
    const content = await generateCampaignContent(site, task, articles);

    Logger.info(`Creating campaign draft`);
    const campaignDraft = await createCampaignDraft(site, task.settings);
    const draftId = campaignDraft.ID;

    await contentToCampaignDraft(site, draftId, content);

    Logger.info(`Sending campaign`);
    await sendCampaignDraft(site, draftId);
    // await sendCampaignDraft(
    //   site,
    //   draftId,
    //   {
    //     Recipients: [
    //       {
    //         Name: 'Glen Somerville',
    //         Email: '<EMAIL>',
    //       },
    //     ],
    //   },
    //   'test'
    // );
  }

  Logger.info('');
  Logger.info(`Mailjet newsletter campaign task finished`);
  Logger.info('-----------------------------------------');
}
