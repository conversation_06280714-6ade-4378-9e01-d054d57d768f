// import { getRelativeISODate } from '#utils/dates.js';

// General constants:

// String with type to be used in the importIDs arrays
export const IMPORT_TYPE = 'acl-v1';

// Base locale in ACL v1 is English (US) by default
export const BASE_LOCALE = 'en-US';

// Number of articles to import per batch
export const IMPORT_BATCH_SIZE = 100;

// Client Origin:
export const CLIENT_ORIGIN = 'https://www.zafir.io'; // TODO: Update this value when we have the new AWE domain

// Testing Overrides:
// TODO: Make sure they are set to false/undefined before committing!!!

// Indicate if import will run dry (no changes will be made, only console outputs) or not (DB records will be created/updated). Default: false.
export const DRY_RUN = false;

// Indicate if import will run with verbose logging. Default: false.
export const VERBOSE = false;

// Override task's lastSync date. Default: undefined.
export const LAST_SYNC = undefined; // getRelativeISODate({ months: 2 }); // or a specific ISO date like '2024-02-01T00:00:00Z'
