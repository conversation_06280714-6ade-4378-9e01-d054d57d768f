// NOTE: This file is used to map ACL organizations to AWE's entities (and some categories).
// NOTE: IMPORTANT: IDs will need to be updated when the entities are (re)imported to AWE.

// Structure for organization to entity:
// <acl-v1-entity-key> : {
//   name: <entity-name>,
//   stagingId: <staging-entity-id>,
//   productionId: <production-entity-id>,
// }

// Structure for organization to category:
// <acl-v1-entity-key> : {
//   name: <category-name>,
//   asCategory: true, // <== Indicates that the organization will be mapped to a category.
//   stagingId: <staging-category-id>,
//   productionId: <production-category-id>,
// }

const organizationToEntitiesMap = {
  '5e3ab8465cb0680ccdd95f7c': {
    name: 'General Conference',
    stagingId: '65cb9509ee2f27f2ed1d21a1',
    productionId: '648710de30889f2e8a57dae8',
  },
  '5e3ab8475cb0680ccdd95f7e': {
    name: 'Euro-Asia Division',
    stagingId: '65cb950aee2f27f2ed1d21e3',
    productionId: '65e8ec8ed9988b1907692c05',
  },
  '5e3ab84c5cb0680ccdd95fad': {
    name: 'Northern-Asia-Pacific Division',
    stagingId: '65cb950eee2f27f2ed1d233c',
    productionId: '65e8ec91d9988b1907692eb8',
  },
  '5e3ab84c5cb0680ccdd95fb4': {
    name: 'South-American Division',
    stagingId: '65cb950fee2f27f2ed1d2388',
    productionId: '65e8ec92d9988b1907692f5f',
  },
  '5e3ab84b5cb0680ccdd95fab': {
    name: 'North-American Division',
    stagingId: '65cb9522ee2f27f2ed1d2966',
    productionId: '65e8ec8ed9988b1907692b9e',
  },
  '5e3ab84c5cb0680ccdd95fb2': {
    name: 'Trans-European Division',
    stagingId: '65cb9526ee2f27f2ed1d2ab9',
    productionId: '65e8ec9dd9988b1907693ad8',
  },
  '5e3ab8465cb0680ccdd95f7d': {
    name: 'East-Central Africa Division',
    stagingId: '65cb9529ee2f27f2ed1d2bb3',
    productionId: '65e8ec90d9988b1907692e19',
  },
  '5e3ab84c5cb0680ccdd95fb1': {
    name: 'Southern-Asia Division',
    stagingId: '65cb952bee2f27f2ed1d2c41',
    productionId: '65e8ec9fd9988b1907693d0e',
  },
  // '5e3ab84c5cb0680ccdd95fae': {
  //   name: 'Africa-Indian-Ocean Division', // ASK: This has been mapped to Southern Africa-Indian Ocean Division. Is this correct?
  //   stagingId: '65cb950dee2f27f2ed1d22de',
  // 'productionId': '',
  // },
  '5e3ab84c5cb0680ccdd95fae': {
    name: 'Southern Africa-Indian Ocean Division',
    stagingId: '65cb952fee2f27f2ed1d2d81',
    productionId: '65e8eca0d9988b1907693de6',
  },
  '5e3ab84c5cb0680ccdd95fb0': {
    name: 'Southern Asia-Pacific Division',
    stagingId: '65cb9533ee2f27f2ed1d2ec9',
    productionId: '65e8ec91d9988b1907692f18',
  },
  '5e3ab84c5cb0680ccdd95faf': {
    name: 'South Pacific Division',
    stagingId: '65cb953fee2f27f2ed1d324d',
    productionId: '65e8eca8d9988b190769458a',
  },

  '5e3ab84b5cb0680ccdd95fac': {
    name: 'Inter-European Division',
    stagingId: '65cb9562ee2f27f2ed1d3b81',
    productionId: '65e8ecb6d9988b19076952be',
  },
  '5e3ab84c5cb0680ccdd95fb3': {
    name: 'Inter-American Division',
    stagingId: '65cb956fee2f27f2ed1d3e5c',
    productionId: '65e8ecbbd9988b19076956d0',
  },
  '5e3ab8515cb0680ccdd95fe5': {
    name: 'West-Central Africa Division',
    stagingId: '65cb950eee2f27f2ed1d231a',
    productionId: '65e8ec90d9988b1907692df3',
  },
  '5e3ab84f5cb0680ccdd95fcc': {
    name: 'Middle East and North Africa Mission',
    stagingId: '65cb9529ee2f27f2ed1d2b96',
    productionId: '65e8ec9ed9988b1907693bc1',
  },
  '5e3ab84f5cb0680ccdd95fcd': {
    name: 'Israel Field',
    stagingId: '65cb952bee2f27f2ed1d2c2f',
    productionId: '65e8ec9fd9988b1907693d06',
  },
  '6375f7c02e6b0b07a8550074': {
    name: 'Ukrainian Union Conference',
    stagingId: '65cb950bee2f27f2ed1d224f',
    productionId: '65e8ec8fd9988b1907692ce7',
  },
  '5e3ab8535cb0680ccdd95ff6': {
    name: 'Chinese Union Mission',
    stagingId: '65cb9513ee2f27f2ed1d24f5',
    productionId: '65e8ec94d9988b19076931c9',
  },
  '5e3ab8525cb0680ccdd95fee': {
    name: 'Loma Linda University',
    stagingId: '65cb9509ee2f27f2ed1d21c5',
    productionId: '65e8ec8ed9988b1907692bed',
  },
  '5f726dd1c393de06ae93ec1b': {
    name: 'Andrews University',
    stagingId: '65cb9509ee2f27f2ed1d21b3',
    productionId: '65e8ec8ed9988b1907692bd5',
  },
  '5f770d769c87d306c23d80a9': {
    name: 'Adventist Health',
    stagingId: '65cb9644ee2f27f2ed1d70af',
    productionId: '65e8ed23d9988b190769a721',
  },
  '5f770d829c87d306c23d80ab': {
    name: 'AdventHealth', // NAD entity
    stagingId: '65cb9594ee2f27f2ed1d4825',
    productionId: '65e8eccfd9988b190769679e',
  },
  '5f770d97a1defc06f2c8721c': {
    name: 'Adventist Healthcare', // NAD entity
    stagingId: '65cb951eee2f27f2ed1d284c',
    productionId: '65e8ec99d9988b1907693747',
  },
  '5e3ab84d5cb0680ccdd95fb8': {
    name: 'Adventist World Radio',
    stagingId: '65cb9509ee2f27f2ed1d21ad',
    productionId: '65e8ec8ed9988b1907692bcd',
  },
  '5e3ab8515cb0680ccdd95fe7': {
    name: 'ADRA',
    stagingId: '65cb9509ee2f27f2ed1d21a7',
    productionId: '65e8ec8ed9988b1907692bc5',
  },

  '5e3ab84e5cb0680ccdd95fc0': {
    name: 'Hope Channel International',
    stagingId: '',
    productionId: '620e23d4c49d64006595c192',
  },

  // NOTE: The following organizations are not in the orgmast database, and are mapped to categories:
  '5e3ab84c5cb0680ccdd95fb5': {
    name: 'Adventist History',
    asCategory: true,
    stagingId: '',
    productionId: '65e8f193d9988b19076a7f89',
  },
  '5e3ab84c5cb0680ccdd95fb6': {
    name: 'Adventist Mission',
    asCategory: true,
    stagingId: '',
    productionId: '65e8f17fd9988b19076a7ed3',
  },
  '5e3ab84d5cb0680ccdd95fb7': {
    name: 'Adventist Review',
    asCategory: true,
    stagingId: '',
    productionId: '65e8f3aad9988b19076a9c7d',
  },
};

export default organizationToEntitiesMap;
