import Logger from '#utils/logger.js';

import categoryTypesMap from './categoryTypesMap.js';

/**
 * Get categories from old ACL API
 * @param {Object} params
 * @param {Object} params.client - Axios client
 * @param {Number} params.limit - Limit of categories to get (default 50)
 * @param {Number} params.page - Page number (default 1)
 * @param {String} params.lastSync - Last sync date
 * @param {String[]} params.ids - Array of category IDs to get
 * @param {String[]} params.types - Array of category types to get
 * @returns
 */
export default async function getCategories({
  client,
  limit = 50,
  page = 1,
  lastSync = null,
  ids,
  types = [],
}) {
  try {
    const { data } = await client.get('/categories', {
      params: {
        // Params to get categories from old ACL API (a Loopback 3 API)
        filter: {
          where: getCategoriesWhereCondition({ lastSync, ids, types }), // Where condition to filter Articles
          order: 'createdAt ASC', // Order by createdAt in ascending order
          limit, // Limit of items per page
          offset: limit * (page - 1), // Offset is calculated based on the page number and the limit of items per page
        },
      },
    });

    return { data };
  } catch (error) {
    Logger.error('Error when trying to get categories:', error.respose?.data);
    return { error: error.respose?.data };
  }
}

export async function getTotalCategories({ client, lastSync, ids, types }) {
  try {
    const { data: totalArticles } = await client.get('/categories/count', {
      params: {
        where: getCategoriesWhereCondition({ lastSync, ids, types }),
      },
    });

    return { total: totalArticles.count };
  } catch (error) {
    Logger.error(
      'Error when trying to get total categories:',
      error.respose?.data || error
    );
    return { error: error.respose?.data || error };
  }
}

function getCategoriesWhereCondition({ lastSync, ids, types }) {
  const where = {};

  const categoryTypeIds = types.map((type) => categoryTypesMap[type]);

  if (categoryTypeIds?.length > 0) {
    where.categoryTypeId = { inq: categoryTypeIds };
  }

  if (ids?.length > 0) {
    where.id = { inq: [...ids] };
  }

  if (lastSync) {
    // where.updatedAt = { gt: lastSync }; // TODO: only get new/updated categories
  }

  return where;
}
