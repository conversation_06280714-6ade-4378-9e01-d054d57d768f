import uniqueId from 'lodash/uniqueId.js';

import Category from '#modules/categories/models/Category.js';
import { toUnixDate } from '#utils/dates.js';
import Logger from '#utils/logger.js';
import { slugify } from '#utils/strings.js';

import { getLocaleKeys, localeToLanguage } from '../helpers/locales.js';
import { DRY_RUN, IMPORT_TYPE, VERBOSE } from '../constants.js';

/**
 * Import an category from the old ACL to the new ACL
 * @param {Object} options
 * @param {Object} options.category - Category data
 * @param {Object} options.categoriesMap - Map of categories
 * @param {Boolean} options.dryRun - Indicates if the import is a dry run or not
 * @returns {Promise<void>}
 */
export default async function importCategory({
  category, // Category data
  entity, // AWE's target Entity
  stats = {
    importCount: 0,
    updateCount: 0,
    updatedIds: [],
    skippedCount: 0,
    deleteCount: 0,
  },
  lastSync, // Last sync date (Unix timestamp)
}) {
  // Extract the category data
  const { id, label, createdAt, updatedAt } = category;

  const locales = [...getLocaleKeys(label)];

  // Define import ID for matching existing entities
  const importID = {
    type: IMPORT_TYPE,
    recordID: id,
  };

  // Check if the category already exists
  const existingCategory = await Category.findOne({
    importIDs: {
      $elemMatch: importID,
    },
  });

  // Update importId date to be the same as the updatedAt date
  importID.updatedAt = updatedAt
    ? new Date(updatedAt * 1000) // Convert from Unix timestamp back to JS date (in milliseconds)
    : new Date();

  // Create the english version of the category first
  const categoryData = {
    name: slugify(label['en-US']), // Use the slugified english label as the name
    title: {}, // Filled bellow with the label for each locale (including "en-US")
    type: 'global', // Flag as global category
    entity: entity?._id, // Set current current entity as the category's owner entity.
    createdAt,
    updatedAt,
    importIDs: [{ recordID: id, type: IMPORT_TYPE, updatedAt }],
  };

  // Add the title for each locale ("en-US" "should" be there, but we don't check for it)
  for (const locale of locales) {
    categoryData.title[localeToLanguage(locale)] = label[locale];
  }

  let categoryRecord = DRY_RUN
    ? { ...categoryData, id: uniqueId('fake-') }
    : undefined;

  if (existingCategory) {
    // Set the category record to the existing category
    categoryRecord = existingCategory;

    // Get existing category's updatedAt date for orgmast import ID
    const existingUpdatedAt = existingCategory.importIDs.find(
      (i) => i.type === IMPORT_TYPE
    )?.updatedAt;

    // If imported record hasn't been updated since last sync, skip it
    if (
      existingUpdatedAt &&
      lastSync &&
      toUnixDate(existingUpdatedAt) <= lastSync
    ) {
      stats.skippedCount += 1;
      if (VERBOSE)
        Logger.info(
          `Skipping category with external ID ${id} and local ID ${existingCategory.id} (not updated since last sync)`
        );
      return;
    }

    // Filter out existing OrgMast import ID from the existing category's import IDs to prevent duplicates (only one per type)
    const otherImportIDs =
      existingCategory.importIDs?.filter((i) => i.type !== IMPORT_TYPE) || [];

    if (DRY_RUN) {
      if (VERBOSE)
        console.log(`- Updating category with ID ${existingCategory.id}`); // eslint-disable-line no-console
    } else {
      // Update the existing category
      await existingCategory.updateOne({
        ...categoryData, // Update the category's data
        importIDs: [...otherImportIDs, importID], // Add the import ID
      });
    }

    // Increment the stats's update count
    stats.updateCount += 1;

    // Ensure updatedIds array exists
    if (!stats.updatedIds) {
      stats.updatedIds = [];
    }

    // Add the existing category's ID to the updatedIds array in stats
    stats.updatedIds.push(existingCategory.id);
  } else {
    // If it's not a dry run, create the category
    if (DRY_RUN) {
      if (VERBOSE) Logger.info(`- Creating category ${categoryData.name}`);
    } else {
      // Create the category record
      categoryRecord = await Category.create(categoryData);
    }

    // Increment the stats's import count
    stats.importCount += 1;
  }

  if (VERBOSE) {
    Logger.info(
      `Imported category with external ID ${id} and local ID ${categoryRecord.id}`
    );
  }
}
