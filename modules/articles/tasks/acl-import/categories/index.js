import Logger from '#utils/logger.js';

import getCategories from './getCategories.js';
import importCategory from './importCategory.js';

/**
 * Import categories from old ACL API
 * @param {Object} params
 * @param {Object} params.client - Axios client
 * @param {Object} params.entity - AWE's target Entity
 * @param {Object} params.task - Task data
 */
export default async function categoriesImport({ client, entity, task }) {
  const {
    // settings,
    lastSync,
  } = task;

  Logger.info(`- Getting categories...`);

  const { data: categoriesData, error } = await getCategories({
    client,
    lastSync,
    types: ['topic'], // only topics, as organizations and tags are handled differently (as entities and article tags respectively)
  });

  const categoriesMap = {};

  for (const category of categoriesData) {
    await importCategory({ category, categoriesMap, entity, lastSync });
  }

  return { categoriesMap, error };
}
