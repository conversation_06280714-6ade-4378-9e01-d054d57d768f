import getACLContentByType, {
  getTotalContent,
} from '../helpers/getACLContentByType.js';

/**
 * Get articles from old ACL API
 * @param {Object} params
 * @param {Object} params.client - Axios client
 * @param {String} params.contentTypeId - Articles's ContentType ID
 * @param {Number} params.limit - Limit of articles to get (default 50)
 * @param {Number} params.page - Page number (default 1)
 * @param {String} params.lastSync - Last sync date
 * @returns
 */
export default async function getArticles({
  client,
  contentTypeId,
  limit = 50,
  page = 1,
  lastSync = null,
  fromDate = null,
  toDate = null,
}) {
  return getACLContentByType({
    client,
    contentName: 'Articles',
    contentTypeId,
    limit,
    page,
    lastSync,
    fromDate,
    toDate,
  });
}

export async function getTotalArticles({
  client,
  contentTypeId,
  lastSync,
  fromDate,
  toDate,
}) {
  return getTotalContent({
    client,
    contentName: 'Articles',
    contentTypeId,
    lastSync,
    fromDate,
    toDate,
  });
}
