import jsSHA from 'jssha';
import isEmpty from 'lodash/isEmpty.js';

import { saveRemoteFile } from '#modules/documents/controllers/documentController.js';
import Logger from '#utils/logger.js';

import { CDN_URL, SIGNNAURE_KEY } from './cdnConfig.js';

/**
 * Get document data for the ACL v1 CDN
 * @param {Object} params
 * @param {Object} params.client - Axios client
 * @param {Object} params.doc - Document object (with either an 'id', or 'name', 'resourceName' and 'containerId')
 * @returns {Promise<Object>} - Document data with url and caption
 */
export async function getACLDocumentData({
  client, // Axios client
  doc, // Document object
  debugId = null,
}) {
  if (isEmpty(doc)) return {};

  const { id, name, resourceName, containerId } = doc;

  const data = {
    file: doc,
    title: '',
  };

  // If we have only an ID within the doc object, we must get the doc record from the API using that ID.
  if (id && (!name || !resourceName || !containerId)) {
    const { data: docRecord, error } = await getDocumentRecord({
      client,
      id,
      debugId,
    });

    if (docRecord) {
      const { file, title } = docRecord || {};
      data.file = file;
      data.title = title;
    } else if (error) {
      Logger.error(
        `Error when trying to get document record with ID "${id}" (ACL ID: ${debugId}):`,
        error
      );
    }
  }

  return {
    title: data?.title,
    url: getACLDocumentUrl({
      docFile: data.file,
      debugId,
    }),
  };
}

/**
 * Returns a Document's absolute url
 *
 * @param {Object} doc
 */
export function getACLDocumentUrl({ docFile, debugId }) {
  if (!docFile) return null;

  const {
    resourceName,
    containerId,
    name,
    originalFilename,
    customName,
    extension,
  } = docFile;

  if (!resourceName || !containerId || !name) {
    Logger.error(
      `Error when trying to get document URL: Missing required data ${{ name, resourceName, containerId }} (Debug ID: ${debugId})`
    );
    return null;
  }

  // Set baseUrl
  const baseUrl = `${CDN_URL}/${resourceName}/${containerId}`;

  const fileName = `${(customName || originalFilename)
    .trim()
    .replace(/[^\w-]/g, '_')}${extension}`;

  // Set security hash based on containerId and file name
  const sha = new jsSHA('SHA-1', 'TEXT');
  sha.setHMACKey(SIGNNAURE_KEY, 'TEXT');
  sha.update(containerId);
  sha.update(name);
  const securityHash = sha.getHMAC('HEX');

  return `${baseUrl}/${securityHash}/${name}/${fileName}`;
}

/**
 * Get doc record from the ACL v1 API
 * @param {Object} params
 * @param {Object} params.client - Axios client
 * @param {Number} params.id - Document ID
 * @returns {Promise<Object|null>} - Document record
 */
export async function getDocumentRecord({ client, id, debugId }) {
  if (!client) {
    return { error: 'Axios client is required' };
  }

  if (!id) {
    return { error: 'ID is required' };
  }

  try {
    const { data } = await client.get(`/documents/${id}`);
    return { data };
  } catch (error) {
    Logger.error(
      `Error when trying to get document record with ID "${id}" (Debug ID: ${debugId}):`,
      error
    );
    return { error };
  }
}

/**
 * Import an document from a remote URL into AWE's CDN
 * @param {String} documentUrl - URL of the document
 * @param {Object} entity - AWE's target Entity
 * @returns {Promise<Object|null>} - Document file object
 */
export async function importACLDocument({ documentUrl, entity }) {
  if (!documentUrl || !entity) return null;

  return await saveRemoteFile(documentUrl, entity);
}
