import isEmpty from 'lodash/isEmpty.js';

import Logger from '#utils/logger.js';

import { importACLImage, getACLImageData } from '../images.js';

import { DRY_RUN } from '../../constants.js';
import { getTextNodeMarks } from './utils.js';
import { serializeChildren } from './serialize.js';
import { getACLDocumentData, importACLDocument } from '../documents.js';

export const markMappings = {
  bold: { type: 'bold' },
  italic: { type: 'italic' },
  subscript: { type: 'subscript' },
  superscript: { type: 'superscript' },
};

export const nodeMappings = {
  'text': async ({ text, ...rest }) => {
    if (!text) return null;

    const node = {
      type: 'text',
      marks: [],
      text,
    };

    // Add text format (bold, italic, subscript, superscript)
    node.marks = getTextNodeMarks(rest).map((mark) => markMappings[mark]);

    // Remove property if empty
    if (isEmpty(node.marks)) delete node.marks;

    return node;
  },
  'heading-one': async ({ children }, entity, options) => ({
    type: 'heading',
    attrs: { level: 1 },
    content: await serializeChildren(children, entity, options),
  }),
  'heading-two': async ({ children }, entity, options) => ({
    type: 'heading',
    attrs: { level: 2 },
    content: await serializeChildren(children, entity, options),
  }),
  'heading-three': async ({ children }, entity, options) => ({
    type: 'heading',
    attrs: { level: 3 },
    content: await serializeChildren(children, entity, options),
  }),
  'heading-four': async ({ children }, entity, options) => ({
    type: 'heading',
    attrs: { level: 4 },
    content: await serializeChildren(children, entity, options),
  }),
  'heading-five': async ({ children }, entity, options) => ({
    type: 'heading',
    attrs: { level: 5 },
    content: await serializeChildren(children, entity, options),
  }),
  'paragraph': async ({ children }, entity, options) => {
    const content = await serializeChildren(children, entity, options);
    return {
      type: 'paragraph',
      ...(content && { content }),
    };
  },
  'unordered-list': async ({ children }, entity, options) => ({
    type: 'bulletList',
    content: await serializeChildren(children, entity, options),
  }),
  'ordered-list': async ({ children }, entity, options) => ({
    type: 'orderedList',
    attrs: { start: 1 },
    content: await serializeChildren(children, entity, options),
  }),
  'list-item': async ({ children }, entity, options) => ({
    type: 'listItem',
    content: [
      {
        type: 'paragraph',
        content: await serializeChildren(children, entity, options),
      },
    ],
  }),
  'quote': async ({ children }, entity, options) => ({
    type: 'blockquote',
    content: [
      {
        type: 'paragraph',
        content: await serializeChildren(children, entity, options),
      },
    ],
  }),
  'image': async (
    { caption, copyright, alt, clickEnlarge, alignment, image, id },
    entity,
    { client, locale, debugId } = {} // extract client from options
  ) => {
    // alignment: left, right, center-full, center-medium, enlarged
    // alt: not on new platform

    // Prepare empty image
    let imageNode = {
      type: 'image',
      attrs: {
        caption,
        copyright,
        alt,
        file: {
          containerId: '', // '602bd46f36432128d40d5e81',
          extension: '', // '.jpg',
          name: '', // '8e41643297997886.jpg',
          originalFilename: '', // 'photo-1475938476802-32a7e851dad1',
          size: 0, // 216635,
          mime: '', // 'image/jpeg',
          width: 0, // 1950,
          height: 0, // 1302,
          blurhash: '', // 'LAEfWv}Q0001LgMd#5vy9FXn%M%2',
        },
        url: '',
        priority: '',
        clickToEnlarge: clickEnlarge,
      },
    };

    // Get image URL and metadata (caption, alt, copyright) from ACL API
    const {
      url: imageUrl,
      caption: imageCaption,
      alt: imageAlt,
      copyright: imageCopyright,
    } = await getACLImageData({
      client,
      image: image ?? { id },
      debugId,
    });

    const translatedCaption =
      caption ||
      (typeof imageCaption === 'string'
        ? imageCaption
        : imageCaption?.[locale]);
    const translatedAlt =
      alt || (typeof imageAlt === 'string' ? imageAlt : imageAlt?.[locale]);

    // Download image if URL exists
    if (imageUrl) {
      const imageFile = DRY_RUN
        ? { url: imageUrl } // Fake image file
        : await importACLImage({ imageUrl, entity, debugId }); // Import image to AWE

      // Set image node if file has been downloaded
      if (imageFile) {
        imageNode = {
          type: 'image',
          attrs: {
            file: imageFile,
            caption: translatedCaption,
            alt: translatedAlt,
            copyright: copyright || imageCopyright,
            url: '',
            priority: '',
            clickToEnlarge: clickEnlarge,
            alignment,
          },
        };
      }
    }

    return imageNode;
  },

  'image-gallery': async (
    { images, caption, prefix },
    entity,
    { client, locale, debugId } = {}
  ) => {
    const imagesData = await images?.reduce(async (accPromise, image) => {
      const acc = await accPromise;

      const {
        url: imageUrl,
        caption: imageCaption,
        alt: imageAlt,
        copyright: imageCopyright,
      } = await getACLImageData({
        client,
        image,
        debugId,
      });

      if (imageUrl) {
        const imageFile = DRY_RUN
          ? { url: imageUrl } // Fake image file
          : await importACLImage({ imageUrl, entity, debugId });

        if (imageFile) {
          acc.push({
            image: imageFile,
            caption: imageCaption?.[locale] || '',
            alt: imageAlt?.[locale] || '',
            copyright: imageCopyright?.[locale] || '',
          });
        }
      }
      return await acc;
    }, []);

    const nodeData = {
      type: 'imageGallery',
      attrs: {
        images: imagesData,
        caption,
        prefix,
      },
    };

    return nodeData;
  },
  'video': async (
    { id, provider, caption, copyright },
    _entity,
    { debugId } = {} // extract client from options
  ) => {
    const providers = {
      youtube: 'youtube',
      vimeo: 'vimeo',
      jetstream: null,
    };
    if (!providers[provider]) {
      Logger.error(
        `slateToTiptap: No mapping found for node type 'video' and provider '${provider}' (Debug ID: ${debugId})`
      );
      return null;
    }
    return {
      type: 'media',
      attrs: {
        caption,
        copyright,
        id,
        provider: providers[provider],
        startAt: null,
        endAt: null,
        priority: '',
        type: 'video',
      },
    };
  },

  'link': async ({ href, children }) =>
    await convertToLink({ href, type: 'external', target: '_blank', children }),
  'email-link': async ({ email, children }) =>
    await convertToLink({ href: email, type: 'email', children }),
  'phone-link': async ({ phone, children }) =>
    await convertToLink({ href: phone, type: 'phone', children }),
  'document-link': async (
    { id, doc, children },
    entity,
    { client, debugId } = {}
  ) => {
    const { url: documentUrl } = await getACLDocumentData({
      client,
      doc: doc || { id },
      debugId,
    });

    let file = null;

    if (documentUrl) {
      const docFile = DRY_RUN
        ? { url: documentUrl } // Fake document file
        : await importACLDocument({ documentUrl, entity });

      if (docFile) {
        file = docFile;
      }
    }

    return await convertToLink({ href: '', file, type: 'file', children });
  },

  'bible-verse': async ({ passage, verses, translation }) => ({
    type: 'bibleVerse',
    attrs: {
      passage,
      text: verses,
      bible: translation || '',
    },
  }),
  'footnote': async ({ children }, entity, options) => ({
    type: 'footnote',
    content: await serializeChildren(children, entity, options),
  }),
};

async function convertToLink({ href, file, type, target, children }) {
  // Different behavior between slate and tiptap:
  // --> slate: one node with many children
  // --> tiptap: one node per child
  const nodes = await children?.reduce(async (accPromise, node) => {
    const acc = await accPromise;
    const textMapping = nodeMappings.text;
    const mapping = await textMapping(node);

    if (!mapping) return await acc; // Ignore empty nodes

    if (isEmpty(mapping.marks)) mapping.marks = [];
    mapping.marks.push({
      type: 'link',
      attrs: {
        href,
        file,
        type,
        target,
      },
    });
    acc.push(mapping);
    return await acc;
  }, []);

  return nodes || [];
}
