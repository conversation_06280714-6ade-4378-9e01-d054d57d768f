import { Text } from 'slate';

import Logger from '#utils/logger.js';
import { isArray, isFunction } from '#utils/types.js';
import { nodeMappings } from './mappings.js';

/**
 * Serializes a node to tiptap format
 * @param {Object} node - Slate node
 * @param {Object} entity - AWE's target Entity
 * @param {Object} options - Options to pass to the mappings functions (e.g. debugId, client, locale, etc.)
 * @returns {Promise<Object|null>} - Tiptap node
 */
export async function serialize(node, entity, options = {}) {
  const type = !Text.isText(node) ? node.type : 'text';
  const mapping = nodeMappings[type];
  if (!mapping) {
    Logger.error(
      `slateToTiptap: No mapping found for node type '${type}' (Debug ID: ${options?.debugId})`
    );
    return null;
  }
  return isFunction(mapping) ? await mapping(node, entity, options) : mapping;
}

/**
 * Serializes children nodes to tiptap format
 * @param {Array} children - Slate children nodes
 * @param {Object} entity - AWE's target Entity
 * @param {Object} options - Options to pass to the mappings functions (e.g. debugId, client, locale, etc.)
 * @returns {Promise<Array|null>} - Tiptap nodes
 */
export async function serializeChildren(children, entity, options = {}) {
  return await children?.reduce(async (accPromise, node) => {
    let acc = await accPromise;
    const _node = await serialize(node, entity, options);
    acc = _node ? (isArray(_node) ? [...acc, ..._node] : [...acc, _node]) : acc;
    return await acc;
  }, []);
}
