import isEmpty from 'lodash/isEmpty.js';
import jsSHA from 'jssha';

import Logger from '#utils/logger.js';

import { saveRemoteImage } from '#modules/images/controllers/imageController.js';
import { CDN_URL, SIGNNAURE_KEY } from './cdnConfig.js';
import { VERBOSE } from '../constants.js';

/**
 * Get image data for the ACL v1 CDN
 * @param {Object} params
 * @param {Object} params.client - Axios client
 * @param {Object} params.image - Image object (with either an 'id', or 'name', 'resourceName' and 'containerId')
 * @param {String} params.sizeOptions - Size options for the image ('0x0' === original image size)
 * @returns {Promise<Object>} - Image data with url and caption
 */
export async function getACLImageData({
  client, // Axios client
  image, // Image file object
  sizeOptions = '0x0', // Size options for the image ('0x0' === original image size)
  debugId = null,
}) {
  if (isEmpty(image)) return {};

  const { id, name, resourceName, containerId } = image;

  const data = {
    file: image,
    caption: '',
    copyright: '',
  };

  // If we have only an ID within the image object, we must get the image record from the API using that ID.
  if (id && (!name || !resourceName || !containerId)) {
    const { data: imageRecord, error } = await getImageRecord({
      client,
      id,
      debugId,
    });

    if (imageRecord) {
      const {
        file,
        caption,
        originalCaption,
        description,
        originalDescription,
        copyright,
      } = imageRecord || {};
      data.file = file;
      data.caption = caption || originalCaption;
      data.alt = description || originalDescription;
      data.copyright = copyright;
    } else if (error) {
      Logger.error(
        `Error when trying to get image record with ID "${id}" (ACL v1 Debug ID: ${debugId}):`,
        error.message
      );
    }
  }

  return {
    caption: data?.caption,
    alt: data?.alt,
    copyright: data?.copyright,
    url: getACLImageUrl({
      imageFile: data.file,
      sizeOptions,
      debugId,
    }),
  };
}

/**
 * Get image URL with signature for the ACL v1 CDN
 * @param {Object} params Parameters
 * @param {Object} params.imageFile Image file object (with name, resourceName, containerId)
 * @param {String} params.sizeOptions Size options for the image ('0x0' === original image size)
 * @param {String} params.debugId ID for logging/debugging purposes
 * @returns {String|Null} Image URL with signature
 */
export function getACLImageUrl({
  imageFile, // Image file object (with name, resourceName, containerId)
  sizeOptions = '0x0',
  debugId = null,
}) {
  if (isEmpty(imageFile)) return null;

  const { name, resourceName, containerId } = imageFile;

  if (!name || !resourceName || !containerId) {
    if (VERBOSE) {
      Logger.error(`getACLImageUrl: wrong image data (ACL ID: ${debugId})`);
    }
    return null;
  }

  // Set baseUrl
  const baseUrl = `${CDN_URL}/${resourceName}/${containerId}`;

  // And url for original image
  const sha = new jsSHA('SHA-1', 'TEXT');
  sha.setHMACKey(SIGNNAURE_KEY, 'TEXT');

  sha.update(containerId);
  sha.update(sizeOptions);
  sha.update(name);

  const signature = sha.getHMAC('HEX');

  return `${baseUrl}/${signature}/${sizeOptions}/${name}`;
}

/**
 * Get image record from the ACL v1 API
 * @param {Object} params
 * @param {Object} params.client - Axios client
 * @param {String} params.id - Image ID
 * @returns {Promise<Object|null>} - Image record
 */
export async function getImageRecord({ client, id }) {
  if (!client) {
    return { error: 'Axios client is required' };
  }

  if (!id) {
    return { error: 'ID is required' };
  }

  try {
    const { data } = await client.get(`/images/${id}`);
    return { data };
  } catch (error) {
    return { error };
  }
}

/**
 * Import an image from a remote URL into AWE's CDN
 * @param {String} imageUrl - URL of the image
 * @param {Object} entity - AWE's target Entity
 * @param {Function} getOriginalFileNameFn - Function to get the original file name (optional)
 * @returns {Promise<Object|null>} - Image file object
 */
export async function importACLImage({
  imageUrl,
  entity,
  getOriginalFileNameFn = undefined,
  debugId = null,
}) {
  if (!imageUrl || !entity) return null;
  try {
    // Upload the file to the CDN
    const imageFile = await saveRemoteImage(
      imageUrl,
      entity,
      getOriginalFileNameFn
    );
    return imageFile;
  } catch (error) {
    Logger.error(
      `Error when trying to download image from ${imageUrl}. (Debug ID: ${debugId})`,
      error
    );
    return undefined;
  }
}
