import Logger from '#utils/logger.js';
import ky from 'ky';
import { CLIENT_ORIGIN } from '../constants.js';

export default async function getClient({
  apiUrl,
  apiToken,
  username,
  password,
  origin,
  entityId = null,
}) {
  // Initialize ky
  let client = ky.create({
    prefixUrl: apiUrl,
    headers: {
      Accept: 'application/json',
      ClientToken: apiToken,
      Origin: origin || CLIENT_ORIGIN,
    },
  });

  try {
    Logger.info(`- Logging in as ${username}`);
    // Login
    // NOTE: the user used for the sync should not enable 2FA for this to work
    const data = await client
      .post('accounts/login', {
        json: {
          email: username,
          password: password,
        },
      })
      .json();

    Logger.info(`- Setting Header Authorization token to ${data?.id}`);
    client = client.extend({
      headers: {
        Authorization: data?.id,
      },
    });
  } catch (error) {
    Logger.error('Error when trying to log in:', error);
  }

  if (entityId) {
    Logger.info(`- Setting Header entityId to ${entityId}`);
    client = client.extend({
      headers: {
        entityid: entityId,
      },
    });
  }

  return client;
}
