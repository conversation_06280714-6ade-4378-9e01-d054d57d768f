/**
 * Get the keys of the locales in a field
 * @param {Object} field Field with locales
 * @returns {String[]} Array of locale keys
 * @example
 * const field = {
 *   en: 'Hello',
 *   es: 'Hola',
 *   de-DE: 'Hallo',
 *   fr: '',
 *   de: null
 * };
 *
 * getLocaleKeys(field); // => ['en', 'es', 'de-DE']
 */
export function getLocaleKeys(field = {}) {
  return Object.keys(field).reduce((acc, lang) => {
    // Only add the language if the field has a value
    if (field[lang]) acc.push(lang);
    return acc;
  }, []);
}

/**
 * Get the language code from a locale
 * @param {String} locale Locale code
 * @returns {String} Language code
 * @example localeToLanguage('en') // => 'en-US'
 */
export function localeToLanguage(locale = '') {
  return locale.toLowerCase().split('-')[0];
}
