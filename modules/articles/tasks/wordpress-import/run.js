import he from 'he';
import _ from 'lodash';
import { stripHtml } from 'string-strip-html';
import { htmlToTiptap } from '#scripts/import/tiptap/parse.js';
import { logInfo, logError } from '#utils/logger.js';

import { saveRemoteImage } from '#modules/images/controllers/imageController.js';

import Article from '#modules/articles/models/Article.js';
import ArticleSite from '#modules/articles/models/ArticleSite.js';
import Entity from '#modules/entities/models/Entity.js';
import Site from '#modules/web/models/Site.js';
import { toObjectId } from '#utils/api/mongoose/id.js';

import {
  getCategories,
  getImage,
  getPosts,
  getTags,
} from '../../services/wordPressServices.js';

const IMPORT_BATCH_SIZE = 10;

async function importImage(apiUrl, entity, imageId) {
  if (!imageId) {
    return;
  }

  try {
    const image = await getImage(apiUrl, imageId);
    const { media_type, source_url } = image ?? {};
    if (media_type !== 'image') {
      return;
    }
    // Upload the file to the CDN
    return await saveRemoteImage(source_url, entity);
  } catch (error) {
    logError(`Error when trying to obtain image ${imageId}`, error);
  }
}

async function importFlags({ apiUrl, organizationMappings, postId, siteId }) {
  try {
    const postCategories = await getCategories(apiUrl, {
      post: postId,
    });

    const site = await Site.findById(siteId);

    const organizationCategoryIds = organizationMappings.map(({ source }) =>
      parseInt(source, 10)
    );

    // Merge with existing flags
    const flags = postCategories.reduce(
      (acc, { id, slug, name, description }) => {
        // Skip organization categories
        if (organizationCategoryIds.includes(id)) {
          return acc;
        }

        const foundFlag = acc.find((f) => f.id === slug);
        if (foundFlag) {
          return acc;
        }
        return [...acc, { id: slug, name: he.decode(name), description }];
      },
      site.flags ?? []
    );

    await Site.findOneAndUpdate(
      { _id: siteId },
      {
        flags,
      },
      {
        runValidators: true,
      }
    );

    // Return only post categories
    return postCategories.reduce((acc, { id, slug }) => {
      if (organizationCategoryIds.includes(id)) {
        return acc;
      }
      return { ...acc, [slug]: true };
    }, {});
  } catch (error) {
    logError(
      `Error when trying to import categories for post ${postId} into site ${siteId}`,
      error
    );
  }
}

async function importTags(apiUrl, postId) {
  try {
    const postTags = await getTags(apiUrl, { post: postId });
    return postTags.map(({ slug }) => slug);
  } catch (error) {
    logError(`Error when trying to import tags for post ${postId}`, error);
  }
}

async function getPostOrganizations({ apiUrl, postId, organizationMappings }) {
  try {
    const postCategories = await getCategories(apiUrl, {
      post: postId,
    });

    return postCategories.reduce((acc, { id }) => {
      const foundOrganization = organizationMappings.find(
        ({ source }) => parseInt(source, 10) === id
      );
      if (!foundOrganization) {
        return acc;
      }
      return [...acc, foundOrganization.target];
    }, []);
  } catch (error) {
    logError(
      `Error when trying to import organizations for post ${postId}.`,
      error
    );
  }
}

async function getPublishedSites({
  apiUrl,
  getMappedField,
  organizationMappings,
  postId,
  sites,
}) {
  const publishedSites = new Map();
  for (const siteConfig of sites) {
    const { site, flag } = siteConfig;
    let flags = { [flag]: true };
    if (flag === 'import') {
      flags = await importFlags({
        apiUrl,
        organizationMappings,
        postId,
        siteId: site,
      });
    }
    publishedSites.set(site, {
      enabled: true,
      // slug: getMappedField('slug', 'slug'),
      startsAt: new Date(getMappedField('startsAt', 'date_gmt')),
      flags,
    });
  }
  return publishedSites;
}

/**
 * Replaces and fixes some values in the body nodes. Returns the modified body.
 * @param {Object} body The body object to fix
 * @returns {Object} The fixed body object
 */
async function fixBodyNodes(body, entity) {
  const nodes = body.content.map(async (node) => {
    // - Image block: download the `url` and set the `file` attribute
    if (node.type === 'image') {
      const { url, ...rest } = node.attrs;

      if (!url) {
        return node;
      }

      const file = await saveRemoteImage(url, entity);

      return {
        ...node,
        attrs: {
          ...rest,
          file,
        },
      };
    }

    return node;
  });

  return {
    ...body,
    content: await Promise.all(nodes), // Wait for all promises to resolve
  };
}

async function importPost({
  apiUrl,
  canonicalSitePageId,
  entity,
  getMappedField,
  language,
  organizationMappings,
  postId,
  sites,
  updateSitesOnly,
}) {
  try {
    let image;
    let tags;

    if (!updateSitesOnly) {
      image = await importImage(
        apiUrl,
        entity,
        getMappedField('imageId', 'featured_media')
      );
      tags = await importTags(apiUrl, postId);
    }

    const publishedSites = await getPublishedSites({
      apiUrl,
      getMappedField,
      organizationMappings,
      postId,
      sites,
    });

    const organizations = await getPostOrganizations({
      apiUrl,
      postId,
      organizationMappings,
    });

    const articleIdentifier = {
      entity: entity.id,
      canonicalSitePage: canonicalSitePageId,
      importIDs: [{ postId, source: apiUrl, type: 'wp' }],
    };

    const articleRecord = updateSitesOnly
      ? { ...articleIdentifier }
      : {
          ...articleIdentifier,
          title: he.decode(getMappedField('title', 'title.rendered')),
          slug: getMappedField('slug', 'slug'),
          image: {
            file: image,
            caption: getMappedField('imageCaption'),
            copyright: getMappedField('imageCopyright'),
          },
          location: getMappedField('location'),
          author: getMappedField('author', 'modified_by'),
          abstract: he.decode(
            stripHtml(getMappedField('abstract', 'excerpt.rendered')).result
          ),
          body: await fixBodyNodes(
            htmlToTiptap(
              he.decode(getMappedField('body', 'content.rendered')) ?? ''
            ),
            entity
          ),
          tags,
          canonicalUrl: canonicalSitePageId
            ? null
            : getMappedField('link', 'link'),
          language: language || 'en',
          status: 'approved',
          organizations,
          createdAt: new Date(getMappedField('createdAt', 'date_gmt')),
        };

    const article = await Article.findOneAndUpdate(
      articleIdentifier,
      articleRecord,
      {
        upsert: true,
        new: true,
        timestamps: false,
        runValidators: true,
      }
    );

    for (const [siteId, siteData] of publishedSites.entries()) {
      const { enabled, startsAt, flags } = siteData;
      if (!enabled) {
        continue;
      }

      await ArticleSite.findOneAndUpdate(
        { site: toObjectId(siteId), article: article._id },
        {
          startsAt,
          flags,
          enabled: true,
        },
        {
          upsert: true,
          runValidators: true,
        }
      );
    }

    logInfo(`Imported post:`, postId);
  } catch (error) {
    logError(`Error when trying to import post: ${postId}`, error);
  }
}

function getApiUrl(task) {
  const { restApiUrl } = task?.settings ?? {};

  if (!restApiUrl) {
    logError(
      `Error running WordPress import (${task.id})`,
      'Rest API URL not configured'
    );
    return;
  }

  return restApiUrl.endsWith('/')
    ? restApiUrl.substring(0, restApiUrl.length - 1)
    : restApiUrl;
}

function getFieldMappings(task) {
  const { fieldMappings = [] } = task?.settings ?? {};
  return fieldMappings.reduce(
    (acc, { target, source }) => ({ ...acc, [target]: source }),
    {}
  );
}

function getPostMappedField(post, fieldMappings) {
  return function (target, defaultPath) {
    if (fieldMappings[target]) {
      return _.get(post, fieldMappings[target]);
    }
    return defaultPath ? _.get(post, defaultPath) : undefined;
  };
}

export default async function runWordpressImport(task) {
  const {
    restApiUrl,
    canonicalSitePage: canonicalSitePageId,
    sites = [],
    importAll,
    updateSitesOnly,
    categories = [],
    categoriesExclude = [],
    // TODO: Add language to task settings form
    language = 'en',
  } = task?.settings ?? {};

  if (!restApiUrl || sites.length === 0) {
    logError(
      `Error running WordPress import (${task.id})`,
      'Task not properly configured'
    );
  }

  const entity = await Entity.findById(task.entity, 'id name');

  logInfo('');
  logInfo('Importing articles');
  logInfo('-------------------------------');

  const apiUrl = getApiUrl(task);
  const lastSyncParam =
    task.lastSync && !importAll
      ? {
          after: task.lastSync.toISOString(),
        }
      : {};

  const categoryFilters = {
    ...(categories.length > 0 && { categories }),
    ...(categoriesExclude.length > 0 && {
      categories_exclude: categoriesExclude,
    }),
  };

  // Initial query just to get total posts
  const { headers } = await getPosts(apiUrl, {
    per_page: 1,
    ...categoryFilters,
    ...lastSyncParam,
  });

  const totalPosts = parseInt(headers['x-wp-total'], 10);
  const totalPages = Math.ceil(totalPosts / IMPORT_BATCH_SIZE);

  logInfo(`Posts to import: ${totalPosts}`);
  logInfo(`Import batches:  ${totalPages}`);

  let currentPage = 1;

  while (currentPage <= totalPages) {
    logInfo('');
    logInfo(`Importing batch ${currentPage} of ${totalPages}`);
    logInfo('-------------------------------');

    const { posts } = await getPosts(apiUrl, {
      per_page: IMPORT_BATCH_SIZE,
      page: currentPage,
      ...categoryFilters,
      ...lastSyncParam,
    });

    const promises = posts.map(
      async (post) =>
        await importPost({
          apiUrl,
          canonicalSitePageId,
          entity,
          getMappedField: getPostMappedField(post, getFieldMappings(task)),
          language,
          organizationMappings: task.settings.organizationMappings ?? [],
          postId: post.id,
          sites,
          updateSitesOnly,
        })
    );

    await Promise.all(promises);

    currentPage += 1;
  }
}
