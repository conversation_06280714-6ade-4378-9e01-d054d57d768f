import dotenv from 'dotenv';
import { exit } from 'process';

import { newMongoDBUUid, toJsonObjectId } from '#utils/api/mongoose/id.js';
import { saveToJsonFile } from '#utils/json.js';
import Logger from '#utils/logger.js';
import { getS3Client } from '#utils/storage.js';

import { getAllModxResources } from './helpers/modx.js';
import { parseArticle, saveArticles } from './helpers/articles.js';
import { setAuthor, saveAuthors } from './helpers/authors.js';
import { loadImagesMap, saveImages } from './helpers/images.js';
import {
  getCategoriesMap,
  loadArticleCategoriesMap,
  saveCategories,
} from './helpers/categories.js';
import { getEntitiesMap } from './helpers/entities.js';
import { enabledInstances } from './helpers/instances.js';
import {
  VERBOSE,
  USE_CACHE,
  IMPORT_IMAGES,
  CACHE_FOLDER,
  OUTPUT_FOLDER,
} from './constants.js';
import { getSiteEnvironment } from './helpers/sites.js';

// Load environment variables
const envPath = new URL('.env', import.meta.url);

dotenv.config({ path: envPath });

const {
  MODE, // ENVIROMENT returns undefined now, so we use MODE instead
  DEBUG,
  MODX_API_KEY,
  LOCAL_ENTITY_ID,
  BUCKET_ENDPOINT,
  BUCKET_SECRET,
  BUCKET_KEY_ID,
  BUCKET_IMAGES,
} = process.env;

// Check if the script should produce also debug information (default: undefined)
const DEBUG_MODE = DEBUG === 'true';

// Initalize a custom S3 client to upload the images to the CDN
const customS3Client = getS3Client({
  endpoint: BUCKET_ENDPOINT,
  secretAccessKey: BUCKET_SECRET,
  accessKeyId: BUCKET_KEY_ID,
});

// Get the site environment map for the current environment (staging or production)
// - siteId: The target site id
// - siteCategoriesMap: A map of site categories (aka flags)
const { siteId, flagsMap } = getSiteEnvironment({ environment: MODE });

/**
 * @typedef {{[name: String]: import('./helpers/authors.js').ModxAuthor}} AuthorsMap The authors map object
 * @typedef {{[name: String]: {person: String, bios: String[]}}} AuthorsIDMap The authors id map object
 * @typedef {{[name: String]: {name: String, id: String}}} OrganizationsMap The organizations map object
 */

// Initialize maps and arrays to store the data during the migration process
// - the images map from the cache folder (if exists)
const imagesMap = loadImagesMap({ tempFolder: CACHE_FOLDER });

// - the authors map (persons)
/** @type {AuthorsMap} */
const authorsMap = {};

// - the authors id map
/** @type {AuthorsIDMap} */
const authorsIdMap = {};
// - categories maps (presetted with the global categories)
const categoriesMap = getCategoriesMap();

// - article categories map (used for ai categorization)
const articleCategoriesMap = loadArticleCategoriesMap({
  tempFolder: CACHE_FOLDER,
});

// - article sites
const articleSites = [];

// - articles map
const articlesMap = {};

// - debug collector map (for debugging purposes)
const debugCollectorMap = {};

// Get the organizations map for the curreny environment
/** @type {OrganizationsMap} */
const organizationsMap = getEntitiesMap({ environment: MODE });

// Loop through the instances to import from
for (const instance of enabledInstances) {
  const { name, baseDomain, contexts } = instance;

  for (const [context, siteDomain] of Object.entries(contexts)) {
    try {
      await migrate({ instanceName: name, baseDomain, context, siteDomain });
    } catch (error) {
      Logger.error(`Migration failed for domain ${siteDomain}`, error);
    }
  }
}

/**
 * Migrate the articles of a specific domain from MODX to MongoDB
 * @param {Object} options The options object
 * @param {String} options.instanceName The name of the instance
 * @param {String} options.baseDomain The base domain of the MODX instance
 * @param {String} options.context The context to import from
 * @param {String} options.siteDomain The site domain (more human readable than the context)
 * @returns {Promise<void>}
 */
async function migrate({ instanceName, baseDomain, context, siteDomain }) {
  if (!instanceName || !baseDomain || !context || !siteDomain) {
    Logger.error('Missing required parameters. Skipping migration...');
    return;
  }

  // Get all the MODX resources
  const rawEntries = await getAllModxResources({
    apiKey: MODX_API_KEY,
    baseDomain,
    context,
    siteDomain,
    useCache: USE_CACHE,
    cacheFolder: CACHE_FOLDER,
  });

  // Loop through the rawEntries
  for (const rawArticle of rawEntries) {
    const { id } = rawArticle || {};

    const articleId = newMongoDBUUid();

    if (
      articlesMap[id] &&
      articlesMap[id]?.pagetitle === rawArticle.pagetitle
    ) {
      Logger.info(
        `Article with an id ${id} and title (${rawArticle.pagetitle} )already exists. Skipping...`
      );
      continue;
    }

    // Parse the raw article entry to get the article and its related data.
    const article = await parseArticle({
      articleId,
      rawArticle,
      imagesMap, // NOTE: The images map will be updated with the images found within the article
      categoriesMap,
      articleCategoriesMap,
      flagsMap,
      debugCollectorMap,
    });

    // Skip the article if it is not valid
    if (!article) {
      continue;
    }

    // Add the author (if exitst )to the authors map
    const { authorId, bioId } = setAuthor({
      author: article.author,
      entityId: LOCAL_ENTITY_ID,
      authorsMap,
      authorsIdMap,
      imagesMap,
    });

    // If the author exists,
    if (authorId) {
      // Add the author to the article, including the bio for the current article
      article.authors = [
        {
          person: toJsonObjectId(authorId),
          bio: toJsonObjectId(bioId),
        },
      ];
    }

    // Add the article to the map
    articlesMap[articleId] = article;
  }
}

// Save the images in the cache folder, upload them to the CDN (within the entity folder), and update the images map with file objects
await saveImages({
  imagesMap,
  entityId: LOCAL_ENTITY_ID,
  tempFolder: CACHE_FOLDER,
  verbose: VERBOSE,
  customS3Client,
  bucket: BUCKET_IMAGES,
  dryRun: !IMPORT_IMAGES,
});

// Save the categories to the output folder
saveCategories({
  categoriesMap,
  outputFolder: OUTPUT_FOLDER,
  entityId: LOCAL_ENTITY_ID,
});

// Save the article categories to the cache folder
saveToJsonFile({
  data: articleCategoriesMap,
  fileName: 'article-categories',
  folder: CACHE_FOLDER,
});

//  Save the authors to the output folder
saveAuthors({
  authorsMap,
  imagesMap,
  outputFolder: OUTPUT_FOLDER,
  entityId: LOCAL_ENTITY_ID,
});

// Save the articles to the output folder
saveArticles({
  articlesMap,
  imagesMap,
  categoriesMap,
  organizationsMap,
  articleSites,
  flagsMap,
  siteId,
  entityId: LOCAL_ENTITY_ID,
  outputFolder: OUTPUT_FOLDER,
});

// Save the article sites to the output folder
saveToJsonFile({
  data: articleSites,
  fileName: 'articlesites',
  folder: OUTPUT_FOLDER,
});

if (DEBUG_MODE) {
  // Save the author names to the output folder (for debugging purposes)
  saveToJsonFile({
    data: Object.keys(authorsIdMap),
    fileName: 'author-names',
    folder: OUTPUT_FOLDER,
  });

  // Save the debug collector to the output folder (for debugging purposes)
  saveToJsonFile({
    data: debugCollectorMap,
    fileName: 'debug-collector',
    folder: OUTPUT_FOLDER,
  });
}

Logger.info('Migration completed');

// Exit the process (required for the script to end in Node v20)
exit(0);
