import omit from 'lodash/omit.js';

import { newMongoDBUUid, toJsonObjectId } from '#utils/api/mongoose/id.js';
import { toMongoDateObject } from '#utils/dates.js';
import { saveToJsonFile } from '#utils/json.js';
import Logger from '#utils/logger.js';
import { convertRichTextToPlainText } from '#utils/richText.js';
import { slugify } from '#utils/strings.js';

import { categorizeArticleWithAI } from '../../../services/articlesAiServices.js';
import { AI_CATEGORIZATION } from '../constants.js';
import { handleNonObjectAuthors } from './authors.js';
import { getCategoriesIds } from './categories.js';
import { formatModXDate } from './dates.js';
import { fixImageUrl, getImagesFormTiptapNodes } from './images.js';
import { sanitize } from './strings.js';
import convertToTiptap from './tiptap/convertToTiptap.js';

/**
 * @typedef {import('#modules/persons/models/Person.js').PersonBioRole} PersonBioRole A PersonBioRole object
 */

const customSettingsSchema = {
  'sidebar': { type: 'html' },
  'division': { type: 'text' },
  'country': { type: 'text' },
  'state': { type: 'text' },
  'city': { type: 'text' },
  'address': { type: 'text' },
  'resource-image': { type: 'image' },
  'header-caption': { type: 'text' }, // this is resource-image's caption
  'tags': { type: 'comma-separated' },
  'author': {
    type: 'object',
    schema: {
      name: { type: 'text' },
      bio: { type: 'text' },
      image: { type: 'image' },
    },
    fallbackFn: (value) => handleNonObjectAuthors(value),
  },
  'email': { type: 'text' },
  'website': { type: 'text' },
  'youtube-id': { type: 'text' },
  'gallery': {
    type: 'array',
    itemSchema: {
      type: 'object',
      schema: {
        image: { type: 'image' },
        caption: { type: 'text' },
      },
    },
  },
};

const ignoreCustomSettingsNamesMap = {
  'title': {},
  'maps': {},
  'img-display': {},
  'category': { type: 'text' }, // NOTE: now we use the export_settings.category instead
  'contents': {},
  'header': {},
  'landing-limit': {},
  'rotator-section': {},
  'ms-credits': {},
  'link-section': {},
  'links-after-curated': {},
  'links-after': {},
  'links-heading': {},
  'links-local': {},
  'link-curated': {}, // ASK: What is this?
  'theme': {},
  'landinglist': {},
  'injections': {},
  'cap': {}, // ASK - What is this?
  'grid-tpl': {},
  'grid-size': {},
  'custom-footer': {},
};

/**
 * Parse an attribute based on its type
 * @param {Object} params The parameters object
 * @param {String} params.key The attribute key
 * @param {String} params.type The attribute type
 * @param {String} params.value The attribute value
 * @param {Object} params.schema The schema to use to parse the attribute
 * @param {Object} params.imagesMap The images map to save the assets
 * @param {String} params.debugId The debug id to use for logging
 * @param {Object} params.debugCollectorMap The debug collector map to track snippets
 * @returns {any} The parsed attribute
 * @example
 * parseAttribute({
 *   key: 'author',
 *   type: 'object',
 *   value: { name: 'John Doe\r\n', bio: 'A <i>great</i> author', image: 'https://example.com/image.jpg' },
 *   schema: {
 *     name: { type: 'text' }, // Fixes some frormating issues
 *     bio: { type: 'html' }, // Parses the HTML to Tiptap
 *     image: { type: 'image' }, // Leaves the image url as is (see below)
 *   },
 * });
 * // Returns { name: 'John Doe', bio: '<tiptap-document-here>', image: 'https://example.com/image.jpg' }
 */
function parseAttribute({
  key,
  type,
  value,
  schema,
  imagesMap,
  debugId,
  debugCollectorMap,
}) {
  switch (type) {
    case 'html': {
      return convertToTiptap({ html: value, debugId, debugCollectorMap });
    }

    case 'text': {
      return sanitize(value);
    }

    case 'image': {
      if (!value) {
        return undefined;
      }
      const fixedImageUrl = fixImageUrl(sanitize(value)); // Fix the image URL

      imagesMap[fixedImageUrl] = {
        debugId: `${debugId}-image`,
        originalUrl: value,
      };

      return fixedImageUrl;
    }

    case 'comma-separated': {
      return sanitize(value)
        .split(',')
        .map((v) => v.trim());
    }

    case 'object': {
      if (!value || typeof value !== 'object') {
        // If there's a fallback function, use it to get the value
        if (typeof schema[key]?.fallbackFn === 'function') {
          return schema[key].fallbackFn(value);
        }

        Logger.warning(
          `parseAttribute: Can't parse object with value: ${value}`
        );
        return undefined;
      }

      return Object.entries(value).reduce((acc, [subKey, val]) => {
        // Get the sub-schema for this object
        const subSchema = schema[key]?.schema || schema || {};

        if (!subSchema[subKey]) {
          return acc;
        }

        // Recursively parse the object's attributes
        acc[subKey] = parseAttribute({
          type: subSchema[subKey]?.type,
          value: val,
          schema: subSchema,
          imagesMap,
          debugId: `${debugId}-${subKey}`,
          debugCollectorMap,
        });
        return acc;
      }, {});
    }

    case 'array': {
      if (!Array.isArray(value)) {
        Logger.warning(
          `parseAttribute: Can't parse array with value: ${value} (${typeof value})`
        );
        return undefined;
      }

      // Get the item schema for this array
      const { itemSchema } = schema[key] || {};

      if (!itemSchema) {
        Logger.warning(`parseAttribute: Missing itemSchema for array`, {
          key,
          schema,
        });
        return undefined;
      }

      // Recursively parse the array's items
      // const items = [];
      const items = value.map((val) =>
        parseAttribute({
          key,
          type: itemSchema.type,
          schema: itemSchema.schema,
          value: val,
          imagesMap,
          debugId: `${debugId}-item`,
          debugCollectorMap,
        })
      );

      return items;
    }

    default: {
      Logger.warning(`parseAttribute: Can't handle value type: ${type}`, {
        value,
      });
      return undefined;
    }
  }
}

/**
 * Parse an article object from MODX to AWE's Article schema
 * @param {Object} params The parameters object
 * @param {String} params.articleId The article id to use (generated)
 * @param {Object} params.rawArticle The raw article object from MODX
 * @param {Object} params.imagesMap The images map to save the assets
 * @param {import('../migrate.js').CategoriesMap} params.categoriesMap The categories map to reference the categories
 * @param {Array} params.flagsMap A flags map to reference the article categories
 * @returns {Promise<Object>} The parsed article object
 */
export async function parseArticle({
  articleId,
  rawArticle,
  imagesMap,
  categoriesMap,
  flagsMap,
  articleCategoriesMap,
  debugCollectorMap,
}) {
  const {
    id, // The article id in MODX
    uri,
    pagetitle,
    description,
    introtext,
    content,
    createdon,
    editedon,
    publishedon,
    export_settings = {},
    custom_settings = [],
  } = rawArticle;

  const { type, category: mainCategory } = export_settings;

  // Count the article types
  if (type) {
    if (!debugCollectorMap.articleTypes) {
      debugCollectorMap.articleTypes = {};
    }

    debugCollectorMap.articleTypes[type] =
      (debugCollectorMap.articleTypes?.[type] || 0) + 1;
  }

  if (type !== 'article') {
    return;
  }

  if (!content) {
    Logger.warning(`Article with id ${id} has no content. Skipping...`);
    return;
  }

  if (!pagetitle) {
    Logger.warning(`Article with id ${id} has no title. Skipping...`);
    return;
  }

  // TODO: Remove this when contents are filtered by the customer in Modx API Sitemap
  if (uri.endsWith('.xml') || uri.endsWith('.js')) {
    return;
  }

  // Get some custom values from the custom_settings
  const customSettings = custom_settings?.reduce((acc, { name, value }) => {
    const { type: settingType } = customSettingsSchema[name] || {};

    if (!settingType) {
      if (!ignoreCustomSettingsNamesMap[name]) {
        Logger.warning(`Custom setting with name "${name}" not found`, {
          value,
        });
      }
      return acc;
    }

    // Parse the cutom attribute based on the settingType
    acc[name] = parseAttribute({
      key: name,
      type: settingType,
      value,
      schema: customSettingsSchema,
      debugId: `article-${id}-${name}`,
      debugCollectorMap,
      imagesMap,
    });

    return acc;
  }, {});

  const flags = {};

  // Parse the content to Tiptap format (JSON)
  const body = content
    ? convertToTiptap({
        html: content,
        debugId: `article-body-${id}`,
        debugCollectorMap,
      })
    : undefined;

  if (!body || body?.content?.length === 0) {
    Logger.warning(
      `Article "${pagetitle}" (ID: ${id}) has no body. Skipping...`
    );
    return;
  }

  // If there is a image gallery in the custom settings:
  const gallery = customSettings.gallery || undefined;

  if (gallery) {
    // 1. Add the images to the images map to save them later
    gallery.forEach((item, index) => {
      if (item.image && !imagesMap[item.image]) {
        imagesMap[item.image] = {
          debugId: `article-${id}-gallery-${index}`,
          originalUrl: item.image,
        };
      }
    });

    // 2. Add a node at the end of the body
    // - Create a gallery node
    const galleryNode = {
      type: 'imageGallery',
      attrs: {
        images: gallery,
      },
    };

    // - Add the gallery node to the end of the body content
    body.content.push(galleryNode);
  }

  if (body?.content && imagesMap) {
    getImagesFormTiptapNodes({
      nodes: body.content,
      imagesMap,
      debugId: `article-${id}-body`,
    });
  }

  // If there is a youtube id in the custom settings, add it at the beginning of the body
  const youtubeId = customSettings['youtube-id'];

  if (youtubeId) {
    // Create a youtube node
    const youtubeNode = {
      type: 'media',
      attrs: {
        id: youtubeId,
        provider: 'youtube',
        type: 'video',
      },
    };

    // Add the youtube node to the beginning of the body content
    body.content = [youtubeNode, ...body.content];
  }

  // Sidebar content: Add it to the article's extra content field
  const sidebar = customSettings.sidebar || undefined;

  // If the article has a category
  if (mainCategory?.name) {
    // If the category is not yet in the categories map,
    if (categoriesMap && !categoriesMap[mainCategory.name]) {
      // Create a new category object with a new id
      const newCategoryId = newMongoDBUUid();

      /** @type {import('./categories.js').NewCategory} */
      const newCategory = {
        id: newCategoryId,
        name: mainCategory.name, // Name in English
        slug: mainCategory.id || slugify(mainCategory.name), // Use the category id or the slugified name or fallback to the name (it should not happen, but just in case)
        debugId: `article-${id}-category-${newCategoryId}`, // Add a debug id for reference
      };

      // Add it to the categories map to be saved later
      categoriesMap[mainCategory.name] = newCategory;
    }

    // If A flags map is provided, and the category is a flag key there...
    if (flagsMap && flagsMap[mainCategory.id]) {
      // Add the site category as an article flags
      flags[mainCategory.id] = true;
    }
  }

  const title = sanitize(pagetitle);
  const slug = sanitize(uri || slugify(title)); // Use the uri or the slugified title
  const subtitle = sanitize(description) || undefined;
  const abstract = sanitize(introtext) || undefined;

  const bodyText = convertRichTextToPlainText(body);
  const tags = customSettings.tags || undefined;

  // Calculate the location based on the custom settings
  const { address, city, state, country } = customSettings;
  const location = {};

  if (address || city || state || country) {
    // Based on the address, city, state, and country:
    // - Conatenate the placeName string
    location.placeName =
      [address, city, state, country].filter(Boolean).join(', ') || undefined;

    // - Create the context object
    const context = {
      address, // e.g. "25 de Mayo 101"
      place: city, // e.g. "Viale"
      region: state, // e.g. "Entre Rios"
      country, // e.g. "Argentina"
    };

    // - Remove empty values from the context
    location.context = Object.entries(context).reduce((acc, [key, value]) => {
      if (value) {
        acc[key] = value;
      }
      return acc;
    }, {});
  }

  // Get the organization from the custom settings (as name of the division).
  // It will be used later to find the organization id.
  const organization = customSettings.division || undefined;

  const aiCategories = [];

  // If the article is already categorized, use the categories from the articleCategoriesMap
  if (articleCategoriesMap?.[id]) {
    aiCategories.push(
      ...(getCategoriesIds({
        categories: articleCategoriesMap[id].categories,
        categoriesMap,
      }) || [])
    );
  } else if (AI_CATEGORIZATION && articleCategoriesMap) {
    // Categorize the article based on the title, description, and tags using OpenAI
    const suggestedCategories = await categorizeArticleWithAI({
      title,
      subtitle,
      abstract,
      body: bodyText,
      tags,
      availableCategories: Object.keys(categoriesMap),
    });

    // And add the categories to the article categories array
    aiCategories.push(
      ...(getCategoriesIds({
        categories: suggestedCategories,
        categoriesMap,
      }) || [])
    );

    // Save the suggested categories to the article categories map for future reference
    articleCategoriesMap[id] = {
      categories: suggestedCategories,
    };
  }

  // Create the article object, mapping the fields to match AWE's Article schema
  const article = {
    id: articleId,
    title,
    slug,
    subtitle: description || undefined,
    abstract: introtext || undefined,
    body,
    author: customSettings.author || undefined,
    createdAt: formatModXDate(createdon),
    updatedAt: formatModXDate(editedon),
    publishedAt: formatModXDate(publishedon), // It will be used later as the articleSite's startAt date
    image: customSettings['resource-image'] || undefined,
    location,
    organization,
    mainCategory, // Provide the main category object as is, it will converted later in saveArticles
    aiCategories, // Add the ai-generated categories
    tags,
    status: 'approved',
    language: 'en',
    enabled: true,
    deleted: false,
    extraContent: sidebar,
    type,
    flags, // Add the flags to the article. Later it will be used to set the articleSite flags.
    importId: id, // Add the modx entry id for reference
  };

  // If the article has an image, and it is not yet in the images map,
  if (article.image && !imagesMap[article.image]) {
    // Add it to the images map to be saved later
    imagesMap[article.image] = {
      debugId: `article-${id}-image`,
    };
  }

  return article;
}

/**
 * Save the articles to a file
 * @param {Object} params The parameters object
 * @param {Object} params.articlesMap The articles map to save
 * @param {Object} params.categoriesMap The categories map to reference the categories
 * @param {Object} params.articleSites The article sites array to save the articles to
 * @param {Object} params.imagesMap The images map to reference the images
 * @param {Object} params.organizationsMap The organizations map to reference the organizations
 * @param {String} params.siteId The site id where to publish the articles
 * @param {Object} params.flagsMap The flags map to reference the article categories
 * @param {String} params.entityId The entity id to save the articles to
 * @param {String} params.outputFolder The folder path to save the articles to
 * @returns {void}
 */
export function saveArticles({
  articlesMap,
  imagesMap,
  organizationsMap,
  categoriesMap,
  articleSites,
  siteId,
  flagsMap,
  entityId,
  outputFolder,
}) {
  if (!Array.isArray(articleSites)) {
    Logger.warning('Missing articleSites array');
  }

  // Convert the articles map to an array
  const articles = Object.values(articlesMap).map((article) => {
    const {
      id,
      mainCategory,
      aiCategories,
      image,
      publishedAt,
      flags,
      body,
      createdAt,
      updatedAt,
      importId,
      ...rest
    } = article;

    // Update the body content to include the images file objects
    const bodyContentWithImages = body?.content?.map((node) => {
      if (node.type === 'image') {
        const imageFile = imagesMap[node.attrs.src]?.file;

        if (imageFile) {
          return {
            ...node,
            attrs: {
              ...node.attrs,
              src: undefined, // Remove the src attribute
              file: imageFile, // Add the file object
            },
          };
        }
      }

      if (node.type === 'imageGallery') {
        const images = node.attrs.images.map((item) => {
          const imageFile = imagesMap[item.image]?.file;

          if (imageFile) {
            return {
              ...item,
              image: imageFile,
            };
          }

          return item;
        });

        return {
          ...node,
          attrs: {
            ...node.attrs,
            images,
          },
        };
      }

      return node;
    });

    // Add an entry of this article to the articleSites array for publishing to the target site
    articleSites.push({
      article: toJsonObjectId(id),
      site: toJsonObjectId(siteId),
      startsAt: toMongoDateObject(publishedAt || createdAt), // Use the publishedAt date or the current date
      flags: Object.keys(flags || {}).length ? flags : undefined, // Do not add empty flags
      enabled: true, // Enabled by default
      deleted: false, // Not deleted by default
    });

    // Get the organization id from the organizations map based on the organization name
    const organizationId =
      organizationsMap[article.organization]?.id || undefined;

    // Set the categories array
    const categories = [];

    // Add the main category id to the category ids
    const mainCategoryId = categoriesMap[mainCategory?.name]?.id;
    if (mainCategoryId) {
      categories.push(mainCategoryId);
    }

    // Add the ai-generated categories ids to the category ids
    if (aiCategories?.length > 0) {
      categories.push(...aiCategories);
    }

    const canonicalSitePageId =
      flagsMap?.[mainCategory?.id]?.canonicalPage || '';

    // Create the article object, mapping the fields to match AWE's Article schema
    return {
      // Spread the rest fields (title, body, ...) first to avoid overriding the following fields
      ...omit(rest, 'author', 'organization'), // ignore the author and organization fields (they are handled separately)

      // Generate a new article id (as ObjectId)
      _id: toJsonObjectId(id),

      // Set the body, including the images at the bottom
      body: {
        ...body,
        content: bodyContentWithImages,
      },

      // Get the image file object from the images map based on the image url (if exists)
      image: {
        file: imagesMap[image]?.file || undefined,
      },

      // Set the article ownweship to the local entity id
      entity: entityId ? toJsonObjectId(entityId) : undefined,

      // Get the organization id from the organizations map based on the organization name
      organizations: organizationId
        ? [toJsonObjectId(organizationId)]
        : undefined,

      // Get the category ids from the categories map based on the category names
      categories,

      // Filter tags, by removing organizations and categories from tags
      tags:
        article.tags?.length > 0
          ? article.tags
              ?.filter((tag) => !organizationsMap[tag]) // Remove organizations from tags
              .filter((tag) => !categoriesMap[tag]) // Remove categories from tags
          : undefined,

      // Set dates of creation and update
      createdAt: toMongoDateObject(createdAt),
      updatedAt: toMongoDateObject(updatedAt),

      // Set the canonical site page id, based on the main category's name (brand)
      canonicalSitePage: toJsonObjectId(canonicalSitePageId),

      // Set revision and translation references to null to indicate this is an original article
      revisionOf: null, // No revisions yet
      translationOf: null, // No translations yet

      // Add the modx entry to the importIDs array for reference
      importIDs: [{ type: 'modx-import', recordID: importId }],
    };
  });

  saveToJsonFile({
    data: articles,
    fileName: 'articles',
    folder: outputFolder,
  });
}
