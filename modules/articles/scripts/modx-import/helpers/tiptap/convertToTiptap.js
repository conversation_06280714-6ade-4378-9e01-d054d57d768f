import { htmlToTiptap } from '#scripts/import/tiptap/parse.js';
import { sanitize } from '../strings.js';

// Define some regex patterns to find:
// - all MODX snippets [[something]]
const modxSnippetsRegexMap = {
  all: /\[\[\S+\]\]/g, // All MODX snippets [[something]]
  links: /\[\[~\d+\]\]/g, // MODX links to internal references that generate a URI [[~id]] (examples: [[~1]], [[~2]])
  uncached: /\[\[!\S+\]\]/g, // MODX uncached calls [[!something]] (examples: [[!author]], [[!msDownloads]])
  staticHtml: /\[\[\$\S+\]\]/g, // MODX static HTML chunks [[$something]] (examples: [[$gallery]], [[$slideshow]], [[$mq-list]])
  tags: /\[\[\*\S+\]\]/g, // MODX tags [[*something]] (examples: [[*id]], [[*isfolder]])
  placeholders: /\[\[\+?\S+\]\]/g, // MODX placeholders for injection existing session/script data [[+something]] (examples: [[+page.nav]], [[+site_name]], [[+site_start]])
  settings: /\[\[\+\+\S+\]\]/g, // MODX settings [[++something]] (examples: [[++site_start]])
};

// - special patters to modify
const specialPatterns = {
  // Drop caps pattern <p class=\"p1 _drop-cap\">T</p>\n<p class=\"p1\">, where T is the drop cap letter
  dropCapsP: {
    pattern: /<p class="p1 _drop-cap">(\S)<\/p>\n<p class="p1">/g,
    replacement: '<p>$1',
  },
  // Drop caps pattern <p><span class=\"cap\">I</span><span class=\"cap\"> </span>\r\n</p><p>, where T is the drop cap letter
  dropCapsSpan: {
    pattern:
      /<p><span class="cap">(\S)<\/span><span class="cap"> <\/span>\r\n<\/p><p>/g,
    replacement: '<p>$1',
  },
};

/**
 * Convert HTML to Tiptap JSON, sanitizing the HTML first removing MODX snippets.
 * @param {Object} options - The options object
 * @param {String} options.html - The HTML string to convert
 * @param {string|null} options.debugId - The debug ID to use for tracking
 * @param {Object|null} options.debugCollectorMap - The debug collector map to track snippets
 * @returns {Object} Tiptap JSON.
 */
export default function convertToTiptap({
  html = '',
  debugId = null,
  debugCollectorMap = null,
}) {
  if (!html) {
    return null;
  }

  // Detect strings that match the patter [[something]]
  const matchedSnippets = html.match(modxSnippetsRegexMap.all);

  // If there are matched snippets, update the debugCollectorMap
  if (matchedSnippets?.length > 0 && debugCollectorMap) {
    for (const snippet of matchedSnippets) {
      let snippetType = null;
      // Check the snippet type and handle accordingly
      if (snippet.match(modxSnippetsRegexMap.links)) {
        snippetType = 'link';
      } else if (snippet.match(modxSnippetsRegexMap.uncached)) {
        snippetType = 'uncached';
      } else if (snippet.match(modxSnippetsRegexMap.staticHtml)) {
        snippetType = 'staticHtml';
      } else if (snippet.match(modxSnippetsRegexMap.tags)) {
        snippetType = 'tag';
      } else if (snippet.match(modxSnippetsRegexMap.placeholders)) {
        snippetType = 'placeholder';
      } else if (snippet.match(modxSnippetsRegexMap.settings)) {
        snippetType = 'settings';
      }

      // Add the snippet type to the stringsToReplaceMap if it doesn't exist yet
      if (!debugCollectorMap[snippetType]) {
        debugCollectorMap[snippetType] = { count: 0 };
      }

      // Initialize the snippet entry if it doesn't exist yet
      if (!debugCollectorMap[snippetType][snippet]) {
        debugCollectorMap[snippetType][snippet] = { count: 0, debugIds: [] };
      }

      // Add the debugId to the debugCollectorMap
      debugCollectorMap[snippetType][snippet].debugIds.push({
        snippet,
        debugId,
      });
      // Increment the count for the specific snippet
      debugCollectorMap[snippetType][snippet].count += 1;
      // Increment the total count for the snippet type
      debugCollectorMap[snippetType].count += 1;
    }
  }

  // Remove all MODX snippets from the HTML
  let replacedString = html.replaceAll(modxSnippetsRegexMap.all, '');

  // Replace special patterns
  for (const modifier of Object.values(specialPatterns)) {
    replacedString = replacedString.replaceAll(
      modifier.pattern,
      modifier.replacement
    );
  }

  // Convert the sanitized HTML to Tiptap JSON
  const tiptapJson = htmlToTiptap(sanitize(replacedString));

  // Remove paragraphs without content
  tiptapJson.content = tiptapJson.content.filter((node) => {
    if (node.type === 'paragraph' && !node.content) {
      return false;
    }

    return true;
  });

  // Return the Tiptap JSON
  return tiptapJson;
}
