import fs from 'fs';

import {
  categoriesMap as allCategoriesMap,
  getCategoriesIds,
} from '#modules/media-library/scripts/modx-videos-import/helpers/categories.js';
import { toJsonObjectId } from '#utils/api/mongoose/id.js';
import { saveToJsonFile } from '#utils/json.js';
import Logger from '#utils/logger.js';
import { slugify } from '#utils/strings.js';

// Use the categories map to get the categories ids from video categories
export { getCategoriesIds };

/**
 * @typedef {Object} NewCategory A new category object
 * @property {String} id The category id
 * @property {String} name The category name
 * @property {String} type The category type
 * @property {String} slug The category slug
 * @property {String} debugId The category debug id
 */

/**
 * @typedef {{[name: String]: String | NewCategory}} CategoriesMap The categories map object
 * @typedef {{[key: String]: NewCategory}} NewCategoriesMap The new categories map object
 */

/**
 * Simple helper to get the categories map as a new object and avoid name conflicts
 * @returns {CategoriesMap} The categories map object
 */
export function getCategoriesMap() {
  return { ...allCategoriesMap };
}

/**
 * Load the categories map from the cache folder
 * @param {Object} params The parameters object
 * @param {String} params.tempFolder The folder that contains the categories cache file
 * @returns {Object} The article categories map
 */
export function loadArticleCategoriesMap({ tempFolder }) {
  const categoriesCacheFile = `${tempFolder}/article-categories.json`;

  // Read the categories cache file if it exists
  if (fs.existsSync(categoriesCacheFile)) {
    try {
      const categoriesCache = fs.readFileSync(categoriesCacheFile, 'utf8');
      return JSON.parse(categoriesCache);
    } catch (error) {
      Logger.error('Failed to read article categories cache file', error);
    }
  }

  return {};
}

/**
 * Save the categories to a JSON file to be imported into MongoDB
 * @param {Object} params The parameters object
 * @param {CategoriesMap} params.categoriesMap The categories map object
 * @param {String} params.outputFolder The folder path to save the categories to
 * @param {String} params.entityId The entity id to save the categories to
 * @returns {void}
 */
export function saveCategories({ categoriesMap, outputFolder, entityId }) {
  // Convert the categories map to an array of objects with the required fields
  const categories = [];

  for (const [categoryName, category] of Object.entries(categoriesMap)) {
    // Skip global categories (harcoded as strings), as they are already saved in production
    if (typeof category === 'string') {
      continue;
    }

    // Create a new category object
    const newCategory = {
      _id: toJsonObjectId(category.id),
      title: { en: category.name },
      name: category.slug ?? slugify(category.name),
      type: 'entity', // Set the category type to 'entity'
      entity: toJsonObjectId(entityId), // For the target entity
      enabled: true,
      deleted: false,
      importIDs: [
        {
          type: 'modx-import',
          recordID: category.name,
          updatedAt: new Date(),
        },
      ],
    };

    // Add the new category to the categories array
    categories.push(newCategory);

    // Update the categories map with the new category id
    categoriesMap[categoryName].id = newCategory._id;
  }

  // Save categories to the output folder
  saveToJsonFile({
    data: categories,
    fileName: 'categories',
    folder: outputFolder,
  });
}
