/**
 * @typedef {Object} Flag A site map flags object, including the canonical page for each flag
 * @property {String} canonicalPage The canonical page for the current flag
 *
 * @typedef {{[name: String]: Flag}} FlagsMap The site map flags object
 *
 * @typedef {Object} SiteMap A site map object
 * @property {String} siteId The target site id for the current environment
 * @property {FlagsMap} flagsMap Flags (Site categories) for the current site
 *
 * @typedef {Object} SitesMap A sites map object
 * @property {SiteMap} staging The staging site map, with dev values
 * @property {SiteMap} production The production site map, with real values
 *
 */

/** @type {SitesMap} */
export const sitesEnvironmentMap = {
  staging: {
    siteId: '66e93d044a339de8f5cefe55',
    flagsMap: {
      NAF: { canonicalPage: '672233d57e2a830b427da793' },
      MQA: { canonicalPage: '672233eb7e2a830b427da87e' },
      MQC: { canonicalPage: '67223d517e2a830b427dc6f9' },
      M360MAG: { canonicalPage: '67223dad7e2a830b427dcb05' },
      PS: { canonicalPage: '67223e187e2a830b427dd01b' },
      FTL: { canonicalPage: '67223e877e2a830b427dd51b' },
    },
  },
  production: {
    siteId: '6616ee3db994cdbd6bb31e1d',
    flagsMap: {
      NAF: { canonicalPage: '6728dceedebf047b7919f1cb' },
      MQA: { canonicalPage: '6728da9bdebf047b7919ae09' },
      MQC: { canonicalPage: '6728db44debf047b7919b7f6' },
      M360MAG: { canonicalPage: '6728dbcfdebf047b7919bf60' },
      PS: { canonicalPage: '6728dc9fdebf047b7919cdfc' },
      FTL: { canonicalPage: '6728dc57debf047b7919c8cf' },
    },
  },
};

/**
 * Get the site environment map for a specific environment
 * @param {Object} options The options object
 * @param {'staging'|'production'} options.environment The environment to use (default: 'production', options: 'production' or 'staging')
 * @returns {SiteMap} The site environment map
 */
export function getSiteEnvironment({ environment = 'production' }) {
  return sitesEnvironmentMap[environment];
}
