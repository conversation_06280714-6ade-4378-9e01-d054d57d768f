import { newMongoDBUUid, toJsonObjectId } from '#utils/api/mongoose/id.js';
import { saveToJsonFile } from '#utils/json.js';
import Logger from '#utils/logger.js';

import convertToTiptap from './tiptap/convertToTiptap.js';

/**
 * @typedef {import('#modules/persons/models/Person.js').PersonBio} PersonBio A PersonBio object
 */

/**
 * @typedef {Object} ModxAuthor A ModX author object
 * @property {String} name The author name (required)
 * @property {String} bio The author bio, that can be in HTML format (optional)
 * @property {String} image The author image, as a file path (optional)
 * @property {String} _id The author id (generated and added by this script)
 * @property {PersonBio[]} bios The author bios (generated and added by this script)
 */

/**
 * Sets an author to the authors map. If the author already exists, it will update the author, otherwise, it will add the author to the map.
 * This will mutate the authorsMap, authorsIdMap, and imagesMap
 * @param {Object} params
 * @param {ModxAuthor} params.author The author to add
 * @param {import('../migrate.js').AuthorsMap} params.authorsMap The authors map to add the author to
 * @param {import('../migrate.js').AuthorsIDMap} params.authorsIdMap The authors id map to add the author id to
 * @param {ModxImportImageMap} params.imagesMap The images map to add the author image to
 * @returns {{authorId?: String, bio?: String }} The author id and bio id that was added or updated
 */
export function setAuthor({
  author,
  authorsMap,
  authorsIdMap,
  imagesMap,
  entityId,
}) {
  if (!author) {
    return {};
  }

  if (!author.name) {
    Logger.warning(`Author has no name. Skipping...`);
    return {};
  }

  let bioId = null;

  // Destructure the author object to get the bio and the rest of the author data
  const { name, bio: authorBio, ...authorRest } = author;

  // Ensure the author name is unique
  const authorName = checkDuplicateAuthorName(name);

  //  Create a new author object with the author data (except the name and bio)
  const updatedAuthor = { ...authorRest };

  // Check if the author already exists in the authors map
  /** @type {ModxAuthor} */
  const authorPrevData = authorsMap[authorName] || {};

  // If the author has been added before (i.e. exists in the authors map),
  if (authorPrevData?._id) {
    if (authorBio) {
      const { bio, bioLong } = parseAuthorBio(authorBio);

      // Check if the author's bio already exists in the author's bios array
      const bioExists = authorPrevData.bios?.some((b) => b.bio === bio);

      // If current's author bio does not exist yet in the author's bios array,
      if (!bioExists) {
        // Generate a new bio id
        bioId = newMongoDBUUid();

        /** @type {PersonBio} */
        const newBio = {
          _id: bioId,
          bio: bio,
          bioLong: bioLong, // This can be undefined if the bio is not in HTML format
          isPrimary: false, // We set the first bio found as primary
        };

        // Add  bio to the author's bios array (with isPrimary set to false)
        updatedAuthor.bios = [...(authorPrevData.bios || []), newBio];
      } else {
        // If the author's bio exists, set the bio id to the existing bio id
        bioId = authorPrevData.bios.find((b) => b.bio === bio)?._id;
      }
    }
  }
  // If it doesn't exist yet
  else {
    // Generate a new author id
    updatedAuthor._id = newMongoDBUUid();

    if (shouldUseFullName(authorName)) {
      // Set the author's full name
      updatedAuthor.fullName = authorName;
    } else {
      // Split name into first and last name
      const [firstName, ...otherNames] = authorName.split(' ');

      // Set the author's first name
      updatedAuthor.firstName = firstName;

      // Set the author's last name
      updatedAuthor.lastName = otherNames.join(' ');
    }

    // Set entity id if available
    if (entityId) {
      updatedAuthor.entity = toJsonObjectId(entityId);
    }

    // If the author has a bio,
    if (authorBio) {
      // Generate the bio and bioLong fields
      const { bio, bioLong } = parseAuthorBio(authorBio);

      // Generate a new bio id
      bioId = newMongoDBUUid();

      // Add the author bio to the author's bios array (with isPrimary set to true)
      /** @type {PersonBio} */
      const newBio = {
        _id: bioId,
        bio: bio,
        bioLong: bioLong, // This can be undefined if the bio is not in HTML format
        isPrimary: true, // We set the first bio found as primary
      };

      /** @type {PersonBio[]} */
      updatedAuthor.bios = [newBio];
    }

    // Add the author to the authors id map
    authorsIdMap[authorName] = {
      person: updatedAuthor._id,
      bios: updatedAuthor.bios?.map((bio) => bio._id) || [],
    };

    // Check if the author has an image and it is not yet in the images map,
    if (author.image && !imagesMap[author.image]) {
      // Add the author image to the images map to be saved later to the CDN

      imagesMap[author.image] = {
        debugId: `author-${authorName}-image`,
      };
    }
  }

  // Add the author to the authors map
  authorsMap[authorName] = { ...authorPrevData, ...updatedAuthor };

  // Return the author id and bio that was added or updated
  return { authorId: updatedAuthor._id, bioId };
}

/**
 * Saves the authors map to a JSON file that can be imported into MongoDB
 * @param {Object} params
 * @param {{[x:string]: ModxAuthor}} params.authorsMap The authors map to save
 * @param {Object} params.imagesMap The images map to reference the author images
 * @param {String} params.outputFolder The path to save the authors file to
 * @returns {void} Nothing, as this will save the authors to the output folder
 */
export function saveAuthors({ authorsMap, imagesMap, outputFolder, entityId }) {
  // Generate the authors array
  const authors = Object.values(authorsMap).map((author) => {
    const { _id, bios, image, ...rest } = author;

    return {
      _id: toJsonObjectId(_id), // Convert the author id (String) to an ObjectId object ({ $oid: String })
      ...rest,
      roles: ['author'], // Set the default role to 'author'
      description: bios?.[0]?.bio || undefined, // Set the description to the first bio (if available)
      bios: bios || undefined, // Set the bios array (if available)
      image: imagesMap[image]?.file || undefined, // Get the image file object from the images map (if exists)
      enabled: true, // Set the author to enabled by default
      deleted: false, // Set the author to not deleted by default
      entity: entityId ? toJsonObjectId(entityId) : undefined, // Set the entity id (if available)
      importIDs: [
        { type: 'modx-import', recordID: author.name }, // Add the modx entry to the importIDs array for reference
      ],
    };
  });

  saveToJsonFile({
    data: authors,
    fileName: 'people',
    folder: outputFolder,
  });
}

const ignoredAuthors = ['-none-'];

/**
 * @param {String} authorName The author name to handle
 * @returns {{[name: String]: String}|Null} The author object
 */
export function handleNonObjectAuthors(authorName) {
  // If the author is not an object or is an ignored author, return null
  if (!authorName || ignoredAuthors.includes(authorName)) {
    return null;
  }

  return {
    name: authorName,
  };
}

// Duplicate authors: key is the duplicate author name, value is the correct author name
const duplicateAuthors = {
  'Karilyn Suvankham': 'Karen Suvankham',
  'Joshua Segala': 'Joshua Sagala',
  'Rick Mcedward': 'Rick McEdward',
};

/**
 * Checks if the author is a duplicate and returns the correct author name
 * @param {String} authorName The author name to handle
 * @returns {String} The correct author name
 */
export function checkDuplicateAuthorName(authorName = '') {
  if (duplicateAuthors[authorName]) {
    return duplicateAuthors[authorName];
  }

  return authorName;
}

// Authors that are couples and should use the full name field
const coupleAuthors = [
  'Greg and Audrey Shank',
  'Olen and Danae Netteburg',
  'Pastor Bogdan and Kseniia Osadchuk',
  'Elias Orlando and Melina Schimpf',
  'G.T. and Ivy Ng',
  'Tyler and Melissa Pender',
];

// Authors that are non-persons and should use the full name field
const nonPersonAuthors = [
  'Adventist Mission staff',
  'News TED',
  'News ANN',
  'News ECD',
];

/**
 * Checks if the person should use the full name field, instead of the atomic name fields (firstName, lastName, prefix, suffix, etc.)
 * @param {String} personName The person object
 * @returns {Boolean} Whether the person should use the full name field
 */
export function shouldUseFullName(personName) {
  // If the person is a couple, return true
  if (
    coupleAuthors.includes(personName) ||
    nonPersonAuthors.includes(personName)
  ) {
    return true;
  }

  return false;
}

/**
 * @typedef {Object} AuthorBio The author bio object
 * @property {String} bio The short bio (truncated to 120 characters is in HTML format)
 * @property {import('#utils/richText.js').RichText} bioLong The long bio in RichText format (tiptap)
 */

/**
 * Gets the author bio and bioLong fields
 * @param {String} author The author's bio text to parse
 * @returns {AuthorBio} The author bio object
 */
function parseAuthorBio(authorBio) {
  // If author has no bio, return empty bio fields
  if (!authorBio) return { bio: '' };

  // Check if bio has HTML content
  const bioHasHTML = /<[^>]*>/.test(authorBio);

  // If bio is in html format,
  if (bioHasHTML) {
    // Convert the bio from rich text to plain text
    const trunctatedBio = authorBio
      // strip the tags
      .replace(/<[^>]*>/g, '')
      // truncate to 120 characters
      .substring(0, 120)
      // replace new lines (\n) with spaces
      .replace(/\n/g, ' ');
    // convert the bio to rich text format (tiptap)
    const bioLong = convertToTiptap({ html: authorBio });

    return { bio: trunctatedBio, bioLong };
  }

  // Otherwise, return the bio as is, and no long bio
  return { bio: authorBio };
}
