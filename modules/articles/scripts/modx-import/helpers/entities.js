// These are divisions and unions of the Adventist Church around the world that are used in the MODX site, where they are referenced by name.

import Logger from '#utils/logger.js';

// They are mapped to their respective production and staging counterparts in AWE.
const entitiesMap = {
  // AFRICA
  'Middle East and North Africa Union': {
    slug: 'middle-east-and-north-africa-union',
    productionId: '65e8ec9ed9988b1907693bc1',
    stagingId: '65e8a8ce9cdf48f839241b40',
  },
  'East-Central Africa Division': {
    slug: 'east-central-africa-division',
    productionId: '65e8ec90d9988b1907692e19',
    stagingId: '65e8a8cf9cdf48f839241b63',
  },
  'West-Central Africa Division': {
    slug: 'west-central-africa-division',
    productionId: '65e8ec90d9988b1907692df3',
    stagingId: '65e8a89d9cdf48f839240d2d',
  },
  'Southern Africa-Indian Ocean Division': {
    slug: 'southern-africa-indian-ocean-division',
    productionId: '65e8eca0d9988b1907693de6',
    stagingId: '65e8a8d99cdf48f839241d3a',
  },

  // EUROPE
  'Inter-European Division': {
    slug: 'inter-european-division',
    productionId: '65e8ecb6d9988b19076952be',
    stagingId: '62e3988a723ddbaefb3e52a0',
  },
  'Trans-European Division': {
    slug: 'trans-european-division',
    productionId: '65e8ec9dd9988b1907693ad8',
    stagingId: '65e8a8ca9cdf48f839241a60',
  },
  'Euro-Asia Division': {
    slug: 'euro-asia-division',
    productionId: '65e8ec8ed9988b1907692c05',
    stagingId: '65e8a8979cdf48f839240bdb',
  },

  // ASIA / PACIFIC
  'Southern Asia Division': {
    slug: 'southern-asia-division',
    productionId: '65e8ec9fd9988b1907693d0e',
    stagingId: '65e8a8d29cdf48f839241bf7',
  },
  'Northern Asia-Pacific Division': {
    slug: 'northern-asia-pacific-division',
    productionId: '65e8ec91d9988b1907692eb8',
    stagingId: '65e8a89e9cdf48f839240d54',
  },
  'Southern Asia-Pacific Division': {
    slug: 'southern-asia-pacific-division',
    productionId: '65e8ec91d9988b1907692f18',
    stagingId: '65e8a8e09cdf48f839241eaa',
  },
  'South Pacific Division': {
    slug: 'south-pacific-division',
    productionId: '65e8eca8d9988b190769458a',
    stagingId: '65e8a8f59cdf48f839242289',
  },

  // AMERICAS
  'North American Division': {
    slug: 'north-american-division',
    productionId: '65e8ec8ed9988b1907692b9e',
    stagingId: '65e8a8c09cdf48f839241892',
  },
  'Inter-American Division': {
    slug: 'inter-american-division',
    productionId: '65e8ecbbd9988b19076956d0',
    stagingId: '65e8a9399cdf48f839242f6e',
  },
  'South American Division': {
    slug: 'south-american-division',
    productionId: '65e8ec92d9988b1907692f5f',
    stagingId: '65e8a8a09cdf48f839240dac',
  },

  // ASK: Should we use the GC as an entity here?
  'World Church': {
    slug: 'world-church',
    productionId: '648710de30889f2e8a57dae8', // GC production ID
    stagingId: '65e8a8969cdf48f839240b9c', // GC staging ID
  },
};

/**
 * Get the entities map for a specific environment
 * @param {Object} options The options object
 * @param {String} options.environment The environment to use (default: 'production', options: 'production' or 'staging')
 * @returns {Object} The entities map
 */
export function getEntitiesMap({ environment = 'production' }) {
  Logger.info(`Getting entities map for environment: ${environment}`);

  return Object.keys(entitiesMap).reduce((acc, name) => {
    const { slug, productionId, stagingId } = entitiesMap[name];

    acc[name] = {
      name,
      slug,
      id: environment === 'production' ? productionId : stagingId,
    };

    return acc;
  }, {});
}
