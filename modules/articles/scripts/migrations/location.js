// NOTE: Fake db object to prevent syntax errors in the editor, do not run this code in mongodb shell
const db = {};

// Actual migration script (run in mongodb shell):

// 1) Copy location field to oldLocation field
db.articles.updateMany({}, [{ $set: { oldLocation: '$location' } }]);

// 2) Remove location field (we can't set a subfield to it if it has a string, so we need to unset it first)
db.articles.updateMany({}, [{ $unset: ['location'] }]);

// 3) Add location field with placeName subfield
db.articles.updateMany({}, [
  { $set: { 'location.placeName': '$oldLocation' } },
]);

// 4) Remove oldLocation field (eventually, but not until we're sure the migration is successful)
db.articles.updateMany({}, [{ $unset: ['oldLocation'] }]);

// For mongodb v4.0.3
// 1) Copy location field to oldLocation field
db.articles.find({}).forEach(function (doc) {
  db.articles.updateOne(
    { _id: doc._id },
    { $set: { oldLocation: doc.location } }
  );
});

// 2) Remove location field (we can't set a subfield to it if it has a string, so we need to unset it first)
db.articles.updateMany({}, { $unset: { location: '' } });

// 3) Add location field with placeName subfield
db.articles.find({}).forEach(function (doc) {
  db.articles.updateOne(
    { _id: doc._id },
    { $set: { 'location.placeName': doc.oldLocation } }
  );
});

// 4) Remove oldLocation field (eventually, but not until we're sure the migration is successful)
db.articles.updateMany({}, { $unset: { oldLocation: '' } });
