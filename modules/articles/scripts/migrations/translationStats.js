const db = {};

// Adds translationsStats to articles
db.articles
  .find({
    translationOf: { $eq: null },
    revisionOf: { $eq: null },
  })
  .forEach(function (doc) {
    let total = 0;
    let draft = 0;
    let done = 0;
    let approved = 0;
    const languages = [];
    const languagesDraft = [];
    const languagesDone = [];
    const languagesApproved = [];

    db.articles
      .find({
        translationOf: doc._id,
        deleted: false,
        enabled: true,
        revisionOf: { $eq: null },
      })
      .forEach(function (translation) {
        total += 1;
        if (translation.status === 'draft') {
          draft += 1;
          languagesDraft.push(translation.language);
        }
        if (translation.status === 'done') {
          done += 1;
          languagesDone.push(translation.language);
        }
        if (translation.status === 'approved') {
          approved += 1;
          languagesApproved.push(translation.language);
        }
        if (!languages.includes(translation.language)) {
          languages.push(translation.language);
        }
      });

    db.articles.updateOne(
      { _id: doc._id },
      {
        $set: {
          'translationsStats.total': total,
          'translationsStats.draft': draft,
          'translationsStats.done': done,
          'translationsStats.approved': approved,
          'translationsStats.languages': languages,
          'translationsStats.languagesDraft': languagesDraft,
          'translationsStats.languagesDone': languagesDone,
          'translationsStats.languagesApproved': languagesApproved,
        },
      }
    );
  });
