const db = {};
const print = console.log; // eslint-disable-line no-console

class ObjectId {
  constructor(id) {
    this.id = id;
  }
}

/**
 * This script is used to migrate article categories from string format to ObjectId format.
 * It is intended to be run in a MongoDB shell environment.
 *
 * During migration, categories may be stored as strings instead of ObjectId.
 * This script converts them to ObjectId where applicable.
 * It also logs any invalid category IDs for review.
 */

// Simulate the MongoDB ObjectId function
function isValidObjectId(str) {
  try {
    new ObjectId(str);
    return true;
  } catch (_e) {
    return false;
  }
}

// Iterate through all articles
let updatedCount = 0;
const cursor = db.articles.find({
  entity: ObjectId('65de02451beb6f51dee0d1e4'),
});

while (cursor.hasNext()) {
  const article = cursor.next();
  let needsUpdate = false;
  const newCategories = [];

  // Process each category in the categories array
  (article.categories || []).forEach((category) => {
    if (typeof category === 'string' && isValidObjectId(category)) {
      // Convert valid string to ObjectId
      newCategories.push(new ObjectId(category));
      needsUpdate = true;
    } else if (category instanceof ObjectId) {
      // Already an ObjectId, keep as is
      newCategories.push(category);
    } else {
      // Log invalid category IDs for review
      print(`Invalid category ID in article ${article._id}: ${category}`);
    }
  });

  // Update the article if any categories were converted
  if (needsUpdate) {
    db.articles.updateOne(
      { _id: article._id },
      { $set: { categories: newCategories } }
    );
    updatedCount++;
    print(`Updated article ${article._id} with converted categories`);
  }
}

print(`Migration complete. Updated ${updatedCount} documents.`);
