const db = {};
const ObjectId = () => '';

// const originSiteId = '65dc66df1beb6f51dedb9bb0';
// const targetSiteId = '66729f5fb5706ea2346436bd';

// Adds translationsStats to articles
db.articlesites
  .find({
    site: ObjectId('65dc66df1beb6f51dedb9bb0'),
    enabled: true,
    startsAt: { $gte: new Date('2022-01-01') },
  })
  .forEach(function (doc) {
    db.articlesites.updateOne(
      {
        article: doc.article,
        site: ObjectId('66729f5fb5706ea2346436bd'),
      },
      {
        $set: {
          article: doc.article,
          createdAt: doc.createdAt,
          enabled: true,
          endsAt: doc.endsAt,
          flags: doc.flags,
          site: ObjectId('66729f5fb5706ea2346436bd'),
          startsAt: doc.startsAt,
          updatedAt: doc.updatedAt,
        },
      },
      {
        upsert: true,
      }
    );
  });
