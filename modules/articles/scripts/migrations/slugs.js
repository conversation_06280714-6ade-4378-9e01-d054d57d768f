/* eslint-disable prefer-destructuring */

const db = {};

// Adds language to articles
db.articles.find().forEach(function (article) {
  // If language already exists, return early
  if (article.language) {
    return;
  }

  var entity = db.entities.findOne({ _id: article.entity });
  if (entity) {
    db.articles.updateOne(
      { _id: article._id },
      { $set: { language: entity.language } }
    );
  }
});

// For adding slug to main article
db.articles.find().forEach(function (article) {
  // If article is null or undefined, return early
  if (!article) {
    return;
  }

  var sites = article.sites;

  // If sites is null, undefined, or an empty object, return early
  if (!sites || Object.keys(sites).length === 0) {
    return;
  }

  var canonicalSitePage = article.canonicalSitePage
    ? article.canonicalSitePage.toString()
    : null;
  var canonicalSite = article.canonicalSite
    ? article.canonicalSite.toString()
    : null;
  var siteIdToUse = null;

  // First, try to find the site ID from the canonicalSitePage
  if (canonicalSitePage) {
    var page = db.pages.findOne({ _id: ObjectId(canonicalSitePage) });
    if (page && page.site) {
      siteIdToUse = page.site.toString();
    }
  }

  // If no site ID was found from the canonicalSitePage, use the canonicalSite
  if (!siteIdToUse) {
    siteIdToUse = canonicalSite;
  }

  // Now, find the matching site and update the slug
  for (var siteId in sites) {
    if (siteId == siteIdToUse) {
      var site = sites[siteId];
      if (site && site.slug) {
        db.articles.updateOne(
          { _id: article._id },
          { $set: { slug: site.slug } }
        );
        return; // Exit the function early
      }
    }
  }

  // If no matching site was found, use the slug from the first site in the sites object
  for (var siteId in sites) {
    var site = sites[siteId];
    if (site && site.slug) {
      db.articles.updateOne(
        { _id: article._id },
        { $set: { slug: site.slug } }
      );
      break;
    }
  }
});

// Adds slugOverrides to articles
db.articles.find().forEach(function (article) {
  // If article is null or undefined, return early
  if (!article) {
    return;
  }

  var sites = article.sites;

  // If sites is null, undefined, or an empty object, return early
  if (!sites || Object.keys(sites).length === 0) {
    return;
  }

  // Get the article's language
  var language = article.language;

  // Iterate over the sites and compare the site's slug with the root slug
  for (var siteId in sites) {
    var site = sites[siteId];
    if (site && site.slug) {
      if (site.slug !== article.slug) {
        // If the site's slug is different from the root slug, add it to slugOverrides
        var update = { $set: {} };
        update.$set['sites.' + siteId + '.slugOverrides.' + language] =
          site.slug;
        db.articles.updateOne({ _id: article._id }, update);
      }
    }
  }
});
