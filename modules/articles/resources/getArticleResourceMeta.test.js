import { expect, test, describe } from 'vitest';

import {
  getArticleResourceMeta,
  transformBibleReferences,
} from './getArticleResourceMeta.js';

describe('getArticleResourceMeta', () => {
  test('should return null when resource is null or undefined', () => {
    expect(getArticleResourceMeta({ resource: null })).toBeNull();
    expect(getArticleResourceMeta({ resource: undefined })).toBeNull();
  });

  test('should extract basic metadata from a resource', () => {
    const resource = {
      author: '<PERSON>',
      publishedAt: '2023-01-01',
      tags: ['tag1', 'tag2'],
    };

    const result = getArticleResourceMeta({ resource });

    expect(result).toEqual({
      author: '<PERSON>',
      authors: [],
      categories: [],
      flags: [],
      location: {},
      organizations: [],
      publishedAt: '2023-01-01',
      tags: ['tag1', 'tag2'],
    });
  });

  test('should handle authors array correctly', () => {
    const resource = {
      authors: [{ person: '<PERSON>' }, { person: '<PERSON>' }],
    };

    const result = getArticleResourceMeta({ resource });

    expect(result.authors).toEqual(['<PERSON> Doe', 'Jane Smith']);
  });

  test('should handle categories array correctly', () => {
    const resource = {
      categories: [{ id: 'cat1' }, { id: 'cat2' }],
    };

    const result = getArticleResourceMeta({ resource });

    expect(result.categories).toEqual(['cat1', 'cat2']);
  });

  test('should handle flags array correctly', () => {
    const resource = {
      flags: [{ id: 'flag1' }, { id: 'flag2' }],
    };

    const result = getArticleResourceMeta({ resource });

    expect(result.flags).toEqual(['flag1', 'flag2']);
  });

  test('should handle organizations array correctly', () => {
    const resource = {
      organizations: [{ id: 'org1' }, { id: 'org2' }],
    };

    const result = getArticleResourceMeta({ resource });

    expect(result.organizations).toEqual(['org1', 'org2']);
  });

  test('should handle location with all properties', () => {
    const resource = {
      location: {
        coordinates: [10, 20],
        context: {
          country: 'USA',
          place: 'New York',
          region: 'NY',
        },
        nameOverride: 'Manhattan',
      },
    };

    const result = getArticleResourceMeta({ resource });

    expect(result.location).toEqual({
      coordinates: [10, 20],
      country: 'USA',
      place: 'New York',
      placeName: 'Manhattan',
      region: 'NY',
    });
  });

  test('should handle location with placeName fallback', () => {
    const resource = {
      location: {
        placeName: 'Los Angeles',
        context: {
          country: 'USA',
        },
      },
    };

    const result = getArticleResourceMeta({ resource });

    expect(result.location).toEqual({
      country: 'USA',
      placeName: 'Los Angeles',
    });
  });

  test('should handle location with missing properties', () => {
    const resource = {
      location: {
        coordinates: [10, 20],
      },
    };

    const result = getArticleResourceMeta({ resource });

    expect(result.location).toEqual({
      coordinates: [10, 20],
    });
  });

  test('should handle Bible references correctly', () => {
    const resource = {
      bibleReferences: [
        {
          book: 'John',
          chaptersWithVerses: {
            3: ['16', '17'],
            1: ['1', '2', '3'],
          },
        },
      ],
    };

    const result = getArticleResourceMeta({ resource });

    // Check books array
    expect(result.bible.books).toEqual(['John']);

    // Check chapters array (order doesn't matter)
    expect(result.bible.chapters).toEqual(
      expect.arrayContaining(['John 3', 'John 1'])
    );
    expect(result.bible.chapters.length).toBe(2);

    // Check verses array (order doesn't matter)
    expect(result.bible.verses).toEqual(
      expect.arrayContaining([
        'John 3:16',
        'John 3:17',
        'John 1:1',
        'John 1:2',
        'John 1:3',
      ])
    );
    expect(result.bible.verses.length).toBe(5);
  });

  test('should handle a complete resource with all properties', () => {
    const resource = {
      author: 'John Doe',
      authors: [{ person: 'John Doe' }, { person: 'Jane Smith' }],
      bibleReferences: [
        {
          book: 'Matthew',
          chaptersWithVerses: { 5: ['1', '2'] },
        },
      ],
      categories: [{ id: 'cat1' }],
      flags: [{ id: 'flag1' }],
      location: {
        coordinates: [10, 20],
        context: { country: 'USA', place: 'New York', region: 'NY' },
        nameOverride: 'Manhattan',
      },
      organizations: [{ id: 'org1' }],
      publishedAt: '2023-01-01',
      tags: ['tag1', 'tag2'],
    };

    const result = getArticleResourceMeta({ resource });

    // Create a copy of the result to check specific parts
    const { bible, ...restResult } = result;

    // Check everything except bible
    expect(restResult).toEqual({
      author: 'John Doe',
      authors: ['John Doe', 'Jane Smith'],
      categories: ['cat1'],
      flags: ['flag1'],
      location: {
        coordinates: [10, 20],
        country: 'USA',
        place: 'New York',
        placeName: 'Manhattan',
        region: 'NY',
      },
      organizations: ['org1'],
      publishedAt: '2023-01-01',
      tags: ['tag1', 'tag2'],
    });

    // Check bible separately with order-insensitive comparison
    expect(bible.books).toEqual(['Matthew']);
    expect(bible.chapters).toEqual(['Matthew 5']);
    expect(bible.verses).toEqual(
      expect.arrayContaining(['Matthew 5:1', 'Matthew 5:2'])
    );
    expect(bible.verses.length).toBe(2);
  });
});

describe('transformBibleReferences', () => {
  test('should return empty arrays when bibleReferences is null or undefined', () => {
    expect(transformBibleReferences(null)).toEqual({
      books: [],
      verses: [],
      chapters: [],
    });

    expect(transformBibleReferences(undefined)).toEqual({
      books: [],
      verses: [],
      chapters: [],
    });
  });

  test('should return empty arrays when bibleReferences is not an array', () => {
    expect(transformBibleReferences({})).toEqual({
      books: [],
      verses: [],
      chapters: [],
    });
  });

  test('should transform Bible references correctly', () => {
    const bibleReferences = [
      {
        book: 'John',
        chaptersWithVerses: {
          3: ['16', '17'],
          1: ['1', '2', '3'],
        },
      },
      {
        book: 'Matthew',
        chaptersWithVerses: {
          5: ['1', '2'],
        },
      },
    ];

    const result = transformBibleReferences(bibleReferences);

    // Check books array (order matters for books)
    expect(result.books).toEqual(expect.arrayContaining(['John', 'Matthew']));
    expect(result.books.length).toBe(2);

    // Check chapters array (order doesn't matter)
    expect(result.chapters).toEqual(
      expect.arrayContaining(['John 3', 'John 1', 'Matthew 5'])
    );
    expect(result.chapters.length).toBe(3);

    // Check verses array (order doesn't matter)
    expect(result.verses).toEqual(
      expect.arrayContaining([
        'John 3:16',
        'John 3:17',
        'John 1:1',
        'John 1:2',
        'John 1:3',
        'Matthew 5:1',
        'Matthew 5:2',
      ])
    );
    expect(result.verses.length).toBe(7);
  });

  test('should not duplicate book entries', () => {
    const bibleReferences = [
      {
        book: 'John',
        chaptersWithVerses: {
          3: ['16'],
        },
      },
      {
        book: 'John',
        chaptersWithVerses: {
          1: ['1'],
        },
      },
    ];

    const result = transformBibleReferences(bibleReferences);

    expect(result.books).toEqual(['John']);

    // Check chapters array (order doesn't matter)
    expect(result.chapters).toEqual(
      expect.arrayContaining(['John 3', 'John 1'])
    );
    expect(result.chapters.length).toBe(2);

    // Check verses array (order doesn't matter)
    expect(result.verses).toEqual(
      expect.arrayContaining(['John 3:16', 'John 1:1'])
    );
    expect(result.verses.length).toBe(2);
  });

  test('should not duplicate chapter entries', () => {
    const bibleReferences = [
      {
        book: 'John',
        chaptersWithVerses: {
          3: ['16'],
        },
      },
      {
        book: 'John',
        chaptersWithVerses: {
          3: ['17'],
        },
      },
    ];

    const result = transformBibleReferences(bibleReferences);

    expect(result.books).toEqual(['John']);
    expect(result.chapters).toEqual(['John 3']);

    // Check verses array (order doesn't matter)
    expect(result.verses).toEqual(
      expect.arrayContaining(['John 3:16', 'John 3:17'])
    );
    expect(result.verses.length).toBe(2);
  });
});
