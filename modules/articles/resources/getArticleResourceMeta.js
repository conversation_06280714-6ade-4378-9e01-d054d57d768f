import { getPersonName } from '#modules/persons/helpers/getPersonName.js';

/**
 * Get Article resource meta
 * @param {Object} options
 * @param {Object} options.resource - The Article resource
 * @returns {Object}
 */
export function getArticleResourceMeta({ resource }) {
  if (!resource) {
    return null;
  }

  const {
    author,
    authors,
    authorsWithBios,
    bibleReferences,
    categories,
    flags,
    location,
    organizations,
    publishedAt,
    tags,
  } = resource;

  const { books, chapters, verses } = transformBibleReferences(bibleReferences);

  return {
    author: authorsWithBios
      ? authorsWithBios.map((a) => getPersonName(a)).join(', ')
      : author,
    authors: authors ? authors.map(({ person }) => person) : [],
    categories: categories ? categories.map(({ id }) => id) : [],
    flags: flags ? flags.map(({ id }) => id) : [],
    location: location
      ? {
          ...(location.coordinates
            ? { coordinates: location.coordinates }
            : {}),
          ...(location.context?.country
            ? { country: location.context?.country }
            : {}),
          ...(location.context?.place
            ? { place: location.context?.place }
            : {}),
          ...(location.nameOverride || location.placeName
            ? { placeName: location.nameOverride || location.placeName }
            : {}),
          ...(location.context?.region
            ? { region: location.context?.region }
            : {}),
        }
      : {},
    ...(bibleReferences ? { bible: { books, chapters, verses } } : {}),
    organizations: organizations ? organizations.map(({ id }) => id) : [],
    publishedAt,
    tags,
  };
}

export function transformBibleReferences(bibleReferences) {
  if (!bibleReferences || !Array.isArray(bibleReferences)) {
    return {
      books: [],
      verses: [],
      chapters: [],
    };
  }

  const books = [];
  const chapters = [];
  const verses = [];

  bibleReferences.forEach(({ book, chaptersWithVerses }) => {
    // Add book to books if not already present
    if (!books.includes(book)) {
      books.push(book);
    }

    // Iterate over chapters and verses
    for (const [chapter, chapterVerses] of Object.entries(chaptersWithVerses)) {
      // Add book-chapter to chapters
      const chapterRef = `${book} ${chapter}`;
      if (!chapters.includes(chapterRef)) {
        chapters.push(chapterRef);
      }

      // Add book-chapter-verse to verses
      chapterVerses.forEach((verse) => {
        verses.push(`${book} ${chapter}:${verse}`);
      });
    }
  });

  return {
    books,
    verses,
    chapters,
  };
}
