import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { validate } from '#utils/validationMiddleware.js';

import {
  getArticleSeriesAction,
  getArticleSeriesByIdAction,
  createArticleSeriesAction,
  updateArticleSeriesAction,
  deleteArticleSeriesAction,
  addArticlesToSeriesAction,
  removeArticleFromSeriesAction,
  sortArticlesInSeriesAction,
  toggleArticleSeriesAction,
} from './controllers/articleSeriesController.js';

import {
  listArticleSeriesSchema,
  getArticleSeriesSchema,
  createArticleSeriesSchema,
  updateArticleSeriesSchema,
  addArticlesToSeriesSchema,
  removeArticleFromSeriesSchema,
  sortArticleSeriesSchema,
} from './validation/articleSeriesValidation.js';

const articleSeriesRouter = express.Router();

// Require ClientToken and Entity
articleSeriesRouter.use(authorizeRequest());

// Require user for all routes after this middleware
articleSeriesRouter.use(protect);

articleSeriesRouter
  .route('/')
  .get(validate(listArticleSeriesSchema, 'query'), getArticleSeriesAction)
  .post(
    restrictTo({ module: 'articles-series', permissions: ['create'] }),
    validate(createArticleSeriesSchema, 'body'),
    logRequest({ module: 'articles-series', action: 'CREATE_ARTICLE_SERIES' }),
    createArticleSeriesAction
  );

articleSeriesRouter
  .route('/:id')
  .get(validate(getArticleSeriesSchema, 'params'), getArticleSeriesByIdAction)
  .patch(
    restrictTo({ module: 'articles-series', permissions: ['update'] }),
    validate(updateArticleSeriesSchema, 'body'),
    logRequest({ module: 'articles-series', action: 'UPDATE_ARTICLE_SERIES' }),
    updateArticleSeriesAction
  )
  .delete(
    restrictTo({ module: 'articles-series', permissions: ['delete'] }),
    logRequest({ module: 'articles-series', action: 'DELETE_ARTICLE_SERIES' }),
    deleteArticleSeriesAction
  );

articleSeriesRouter.route('/:id/toggle').patch(
  restrictTo({ module: 'articles-series', permissions: ['update'] }),
  logRequest({
    module: 'articles-series',
    action: 'ACTIVATE_ARTICLE_SERIES',
  }),
  toggleArticleSeriesAction
);

articleSeriesRouter
  .route('/:id/add-articles')
  .post(
    restrictTo({ module: 'articles-series', permissions: ['update'] }),
    validate(addArticlesToSeriesSchema, 'body'),
    logRequest({ module: 'articles-series', action: 'ADD_ARTICLES_TO_SERIES' }),
    addArticlesToSeriesAction
  );

articleSeriesRouter.route('/:id/remove-article').post(
  restrictTo({ module: 'articles-series', permissions: ['update'] }),
  validate(removeArticleFromSeriesSchema, 'body'),
  logRequest({
    module: 'articles-series',
    action: 'REMOVE_ARTICLE_FROM_SERIES',
  }),
  removeArticleFromSeriesAction
);

articleSeriesRouter
  .route('/:id/sort')
  .patch(
    restrictTo({ module: 'articles-series', permissions: ['update'] }),
    validate(sortArticleSeriesSchema, 'body'),
    logRequest({ module: 'articles-series', action: 'SORT_ARTICLE_SERIES' }),
    sortArticlesInSeriesAction
  );

export default articleSeriesRouter;
