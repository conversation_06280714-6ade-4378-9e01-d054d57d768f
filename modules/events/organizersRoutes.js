import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';

import organizerController from './controllers/organizerController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(organizerController.getAllOrganizers)
  .post(
    restrictTo({
      module: 'events',
      permissions: ['create'],
    }),
    logRequest({ module: 'events', action: 'CREATE_EVENT' }),
    organizerController.createOrganizer
  );

router
  .route('/:id')
  .get(organizerController.getOrganizer)
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT' }),
    organizerController.updateOrganizer
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT' }),
    organizerController.deleteOrganizer
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'DISABLE_EVENT' }),
  organizerController.disableOrganizer
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'ENABLE_EVENT' }),
  organizerController.enableOrganizer
);

export default router;
