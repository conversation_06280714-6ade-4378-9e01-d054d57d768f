import mongoose from 'mongoose';

import getListFilters from '#utils/api/list/filters.js';
import PaymentMethod from '#modules/payments/models/PaymentMethod.js';
import { logError } from '#utils/logger.js';

import EventParticipant from '../models/EventParticipant.js';
import { calculateParticipantsCount } from '../helpers/participants.js';
import {
  handleStatusEmail,
  sendParticipantCancellation,
  sendParticipantConfirmation,
  sendParticipantRefund,
  sendParticipantWaitlist,
} from '../helpers/emails.js';

const eventParticipantsSearchFields = [
  'firstName',
  'lastName',
  'middleName',
  'prefix',
  'suffix',
  'fullName',
  'email',
  'phone',
  'status',
  'countryOfResidence',
  'countryOfBirth',
];

/**
 * Retrieves all participants based on the provided query and entity.
 * @function getParticipants
 * @param {Object} query The query object to filter participants.
 * @param {Object} entity The entity object to filter participants.
 * @returns {Object} An object containing the list of participants and the count.
 */
export async function getParticipants({
  limit,
  page,
  search = '',
  sortBy = 'fullName',
  sortDir = 'desc',
  statuses = [],
  skip = 0,
  eventId,
  entity,
  participantTypes = [],
  registrationStatuses = [],
  downloadFormat,
}) {
  const filters = getListFilters({
    statuses,
    limit: downloadFormat ? 100000 : limit,
    page,
    search,
    searchFields: eventParticipantsSearchFields,
    sort: sortBy,
    order: sortDir,
    skip,
  });

  const matchFilters = {
    entity: entity._id,
    event: new mongoose.Types.ObjectId(eventId),
    $and: [filters.search, filters.statuses],
    ...(participantTypes.length > 0
      ? { participantType: { $in: participantTypes } }
      : {}),
    ...(registrationStatuses.length > 0
      ? { registrationStatus: { $in: registrationStatuses } }
      : {}),
  };

  const pipeline = [{ $match: matchFilters }, { $sort: filters.sort }];
  const itemsPipeline = [
    ...pipeline,
    {
      // Overwrite the 'author.fullName' field with a new 'fullName' built from the author's name parts
      $addFields: {
        fullName: {
          $cond: {
            if: {
              $eq: [{ $ifNull: ['$fullName', ''] }, ''],
            },
            then: {
              $trim: {
                input: {
                  $concat: [
                    { $ifNull: ['$prefix', ''] },
                    ' ',
                    { $ifNull: ['$firstName', ''] },
                    ' ',
                    { $ifNull: ['$middleName', ''] },
                    ' ',
                    { $ifNull: ['$lastName', ''] },
                    ' ',
                    { $ifNull: ['$suffix', ''] },
                  ],
                },
              },
            },
            else: '$fullName',
          },
        },
      },
    },
    { $skip: filters.pagination.skip },
    { $limit: filters.pagination.limit },
  ];
  const countPipeline = [...pipeline, { $count: 'count' }];

  const items = await EventParticipant.aggregate(itemsPipeline);
  const [count] = await EventParticipant.aggregate(countPipeline);

  return { items, count: count ? count.count : 0 };
}

/**
 * Retrieves a participant by ID.
 * @function getParticipant
 * @param {string} id The ID of the participant to retrieve.
 * @param {string} entityId The ID of the entity to filter by.
 * @returns {Object} The participant object or null if not found.
 */
export async function getParticipant({ id, entityId }) {
  return await EventParticipant.findOne({
    _id: id,
    entity: entityId,
  });
}

/**
 * Creates a new participant.
 * @function addParticipant
 * @param {Object} entity The entity object to associate with the participant.
 * @param {Object} participant The participant object to create.
 * @returns {Object} The created participant object or an error object.
 */
export async function addParticipant({ participant, entity, eventId }) {
  if (!entity) {
    return {
      error: {
        method: 'addCategory',
        code: 'entity_required',
        message: 'Entity is required',
      },
    };
  }

  const participantToSave = {
    ...participant,
    entity: entity._id,
    event: eventId,
    language: entity.language, // For now the participant language is the same as the entity language, if needed a different language can be set. This is useful for the confirmation email.
    _id: undefined,
  };

  const result = await EventParticipant.create(participantToSave);

  // If the confirmation email needs to be sent, send it here.
  if (result.registrationStatus === 'confirmed') {
    await result.populate([
      'event',
      'price',
      'food',
      'accommodation',
      'workshops',
      {
        path: 'event',
        populate: {
          path: 'organizer',
        },
      },
    ]);

    const { config } = await PaymentMethod.findById(result.event.paymentMethod);
    const { emailSender } = config || {};

    const { success, error } = sendParticipantConfirmation({
      participant: result.toObject(),
      emailSender,
    });
    if (error) {
      logError('Error sending confirmation email:', error);
      return { error: 'Error sending confirmation email.' };
    }

    if (success) {
      result.emailSent = true;
      await result.save();
    }
  }

  await calculateParticipantsCount(eventId);
  return { data: result, error: null };
}

/**
 * Edits an existing participant.
 * @function editParticipant
 * @param {Object} participant The participant object to edit.
 * @returns {Object} The edited participant object or an error object.
 */
export async function editParticipant({ body = {} }) {
  const participant = await EventParticipant.findById(body._id).lean();

  if (!participant) return { error: 'Participant not found.' };

  const data = await EventParticipant.findByIdAndUpdate(body._id, body, {
    runValidators: true,
    new: true,
  });
  // Map registration statuses to their corresponding email handlers and error messages
  const statusEmailHandlers = {
    confirmed: {
      sendFn: sendParticipantConfirmation,
      errorMsg: 'Error sending confirmation email.',
    },
    cancelled: {
      sendFn: sendParticipantCancellation,
      errorMsg: 'Error sending cancellation email.',
    },
    waitlisted: {
      sendFn: sendParticipantWaitlist,
      errorMsg: 'Error sending waitlist email.',
    },
    refunded: {
      sendFn: sendParticipantRefund,
      errorMsg: 'Error sending refund email.',
    },
  };

  // Check if registrationStatus changed and handle email sending accordingly
  if (
    body.registrationStatus &&
    participant.registrationStatus !== body.registrationStatus &&
    statusEmailHandlers[body.registrationStatus] // Since pending is not handled here, it will not trigger an email
  ) {
    const { sendFn, errorMsg } = statusEmailHandlers[body.registrationStatus];
    const result = await handleStatusEmail({
      data,
      type: body.registrationStatus,
      sendFn,
      errorMsg,
    });
    if (result.error) return result;
  }

  await calculateParticipantsCount(participant.event);

  return data;
}

/**
 * Deletes a participant by ID.
 * @function deleteParticipant
 * @param {string} id The ID of the participant to delete.
 * @returns {Object} The deleted participant object or an error object.
 */
export async function deleteParticipant({ id }) {
  const participant = await EventParticipant.findById(id);
  if (!participant) return { error: 'Entry not found' };

  const deletedParticipant = await EventParticipant.findByIdAndUpdate(
    { _id: id, deleted: false },
    { $set: { deleted: true } },
    { new: true, runValidators: true }
  );

  await calculateParticipantsCount(participant.event);

  return { data: deletedParticipant };
}

/**
 * Toggles the enabled status of a participant.
 * @function toggleParticipantService
 * @param {Object} id The ID of the participant to toggle.
 * @param {Object} entity The entity object to filter by.
 * @returns {Object} The updated participant object or an error object.
 */
export async function toggleParticipantService({ id }) {
  const participant = await EventParticipant.findById(id);
  if (!participant) {
    return { error: 'Participant not found.' };
  }

  const enabled = !participant.enabled;

  const data = await EventParticipant.findByIdAndUpdate(
    id,
    { enabled },
    {
      runValidators: true,
    }
  ).lean();

  return { data };
}
