import getListFilters from '#utils/api/list/filters.js';
import { slugify, uniquifySlug } from '#utils/strings.js';
import mongoose from 'mongoose';
import Event from '../models/Event.js';
import EventConfig from '../models/EventConfig.js';

/**
 * Retrieves a list of events with filtering, sorting, and pagination options.
 *
 * @async
 * @function getEvents
 * @param {Object} params The parameters for retrieving events.
 * @param {number} params.limit The maximum number of events to return.
 * @param {number} params.page The page number for pagination.
 * @param {string} [params.search=''] Search query to filter events.
 * @param {string} [params.sortBy='createdAt'] Field to sort by.
 * @param {string} [params.sortDir='desc'] Sort direction ('asc' or 'desc').
 * @param {number} [params.skip=0] Number of records to skip.
 * @param {Object} params.entity The entity object containing at least an _id property.
 * @param {Object} [params.filter={}] Additional filters for events (organizer, regions, category).
 * @param {Object} [params.user={}] The user object with permission methods.
 * @returns {Promise<{count: number, items: Array<Object>}>} An object containing the total count and the list of event items.
 */
export async function getEvents({
  limit,
  page,
  search = '',
  sortBy = 'createdAt',
  sortDir = 'desc',
  skip = 0,
  entity,
  filter = {},
  user = {},
  past = false,
}) {
  const filters = getListFilters({
    limit,
    page,
    search,
    searchFields: ['title', 'description', 'slug'],
    sort: sortBy,
    order: sortDir,
    skip,
  });

  const restrictedRecords = user.getRestrictedRecords({
    module: 'events-organizers',
    permission: 'read',
  });

  const today = new Date();
  const isTemplate = filters?.filter?.isTemplate;
  const matchFilters = {
    entity: entity._id,
    $and: [filters.search, filters.statuses],
    ...(isTemplate ? { isTemplate } : {}),
    endsAt: { [past ? '$lt' : '$gte']: today },
    ...(restrictedRecords && {
      organizer: { $in: restrictedRecords },
    }),
  };

  if (filter.organizer?.$in?.length > 0) {
    const organizersWithMongoIds = filter.organizer.$in.map(
      (id) => new mongoose.Types.ObjectId(id)
    );
    matchFilters.organizer = {
      $in: organizersWithMongoIds,
    };
  }

  if (filter.regions?.$in?.length > 0) {
    const regionsWithMongoIds = filter.regions.$in.map(
      (id) => new mongoose.Types.ObjectId(id)
    );
    matchFilters.regions = {
      $in: regionsWithMongoIds,
    };
  }

  if (filter.category?.$in?.length > 0) {
    const categoriesWithMongoIds = filter.category.$in.map(
      (id) => new mongoose.Types.ObjectId(id)
    );
    matchFilters.category = {
      $in: categoriesWithMongoIds,
    };
  }

  //Get participant count for each event based on participant registration status
  const basePipeline = [
    {
      $lookup: {
        from: 'eventparticipants',
        let: { eventId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$event', '$$eventId'] },
                  { $eq: ['$enabled', true] },
                  { $eq: ['$deleted', false] },
                ],
              },
            },
          },
        ],
        as: 'participants',
      },
    },
    {
      $addFields: {
        confirmedParticipants: {
          $size: {
            $filter: {
              input: '$participants',
              as: 'participant',
              cond: {
                $eq: ['$$participant.registrationStatus', 'confirmed'],
              },
            },
          },
        },
        pendingParticipants: {
          $size: {
            $filter: {
              input: '$participants',
              as: 'participant',
              cond: {
                $eq: ['$$participant.registrationStatus', 'pending'],
              },
            },
          },
        },
        waitlistedParticipants: {
          $size: {
            $filter: {
              input: '$participants',
              as: 'participant',
              cond: {
                $eq: ['$$participant.registrationStatus', 'waitlisted'],
              },
            },
          },
        },
      },
    },
    {
      $match: matchFilters,
    },
    {
      $sort: filters.sort,
    },
    // Add id (since this is an aggregation, and id is not provided)
    {
      $addFields: {
        id: { $toString: '$_id' },
      },
    },
  ];

  const itemsPipeline = [
    ...basePipeline,
    { $skip: filters.pagination.skip },
    { $limit: filters.pagination.limit },
  ];
  const countPipeline = [...basePipeline, { $count: 'count' }];

  const items = await Event.aggregate(itemsPipeline);
  const [count] = await Event.aggregate(countPipeline);

  return { count: count?.count || 0, items };
}

export async function getEventService({ id, entity }) {
  // Bring the event with the given ID and entity, populating the necessary fields
  // Build the aggregation pipeline
  const pipeline = [
    {
      $match: {
        _id: new mongoose.Types.ObjectId(id),
        entity: entity._id,
      },
    },
    {
      $lookup: {
        from: 'eventparticipants',
        let: { eventId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$event', '$$eventId'] },
                  { $eq: ['$enabled', true] },
                  { $eq: ['$deleted', false] },
                ],
              },
            },
          },
        ],
        as: 'participants',
      },
    },
    {
      $addFields: {
        confirmedParticipants: {
          $size: {
            $filter: {
              input: '$participants',
              as: 'participant',
              cond: {
                $eq: ['$$participant.registrationStatus', 'confirmed'],
              },
            },
          },
        },
        pendingParticipants: {
          $size: {
            $filter: {
              input: '$participants',
              as: 'participant',
              cond: {
                $eq: ['$$participant.registrationStatus', 'pending'],
              },
            },
          },
        },
        waitlistedParticipants: {
          $size: {
            $filter: {
              input: '$participants',
              as: 'participant',
              cond: {
                $eq: ['$$participant.registrationStatus', 'waitlisted'],
              },
            },
          },
        },
      },
    },
    {
      $project: {
        participants: 0,
      },
    },
    {
      $lookup: {
        from: 'eventfoods',
        localField: 'foods',
        foreignField: '_id',
        as: 'foods',
      },
    },
    {
      $lookup: {
        from: 'eventaccommodations',
        localField: 'accommodations',
        foreignField: '_id',
        as: 'accommodations',
      },
    },
    {
      $lookup: {
        from: 'eventprices',
        localField: 'prices',
        foreignField: '_id',
        as: 'prices',
      },
    },
    {
      $lookup: {
        from: 'eventworkshops',
        localField: 'workshops',
        foreignField: '_id',
        as: 'workshops',
      },
    },
    // Convert _id fields in lookup arrays to string
    {
      $addFields: {
        foods: {
          $map: {
            input: '$foods',
            as: 'item',
            in: {
              $mergeObjects: ['$$item', { id: { $toString: '$$item._id' } }],
            },
          },
        },
        accommodations: {
          $map: {
            input: '$accommodations',
            as: 'item',
            in: {
              $mergeObjects: ['$$item', { id: { $toString: '$$item._id' } }],
            },
          },
        },
        prices: {
          $map: {
            input: '$prices',
            as: 'item',
            in: {
              $mergeObjects: ['$$item', { id: { $toString: '$$item._id' } }],
            },
          },
        },
        workshops: {
          $map: {
            input: '$workshops',
            as: 'item',
            in: {
              $mergeObjects: ['$$item', { id: { $toString: '$$item._id' } }],
            },
          },
        },
      },
    },
  ];

  const eventAggregation = Event.aggregate(pipeline);

  const [event] = await eventAggregation;

  if (!event) {
    return {
      error: {
        message: 'Event not found',
      },
    };
  }

  // If the event is found, convert the _id to a string and assign it to id
  if (event && event._id) {
    event.id = event._id.toString();
  }

  return { data: event };
}

export async function createEventFromTemplate({ eventId, entity, body }) {
  const template = await Event.findOne({
    _id: eventId,
    entity: entity._id,
  }).lean();

  if (!template) {
    return {
      error: {
        method: 'createEventFromTemplate',
        code: 'template_event_not_found',
        message: 'Template Event not found',
      },
    };
  }

  const originEventSlug = slugify(body.title);
  const existingEvent = await Event.findOne({
    slug: originEventSlug,
    entity,
  });

  const eventToSave = {
    ...template,
    _id: undefined,
    slug: existingEvent ? uniquifySlug(originEventSlug) : originEventSlug,
    isTemplate: false,
    title: body.title,
    enabled: true,
    startsAt: body.startsAt,
    endsAt: body.endsAt,
    setTime: body.setTime,
    ...(body.organizer ? { organizer: body.organizer } : {}),
  };

  const createdEventId = (await Event.create({ ...eventToSave }))._id;
  const createdEvent = await Event.findById(createdEventId).lean();

  return { data: createdEvent, message: 'Event created successfully' };
}

export async function setEventsConfigService({ entityId, config }) {
  try {
    const eventConfig = await EventConfig.findOne({
      entity: entityId,
    });

    // If the eventConfig exists,
    if (eventConfig) {
      // Update it with the new params
      const updatedEventConfig = await EventConfig.findByIdAndUpdate(
        eventConfig._id,
        { ...config },
        { new: true, runValidators: true }
      );

      // Return the updated config
      return {
        data: updatedEventConfig,
      };
    }

    // Otherwise, create a new eventConfig
    const newEventConfig = await EventConfig.create({
      entity: entityId,
      ...config,
    });

    // Return the new eventConfig
    return {
      data: newEventConfig,
    };
  } catch (error) {
    // Return the error
    return { error };
  }
}
