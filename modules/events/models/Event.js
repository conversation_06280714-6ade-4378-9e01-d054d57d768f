import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';
import { FieldSchema } from '#utils/schemas.js';

const RegistrationTermSchema = new mongoose.Schema({
  name: { type: String, required: true },
  title: { type: String, required: true },
  description: {
    type: mongoose.SchemaTypes.Mixed, // RICH TEXT
  },
  required: { type: Boolean },
});

const EventParticipantSchema = new mongoose.Schema({
  // Name reated field
  prefix: { type: String, trim: true, default: '' },
  firstName: { type: String, trim: true, default: '', required: true },
  middleName: { type: String, trim: true, default: '' },
  lastName: { type: String, trim: true, default: '', required: true },
  suffix: { type: String, trim: true, default: '' },
  fullName: { type: String, trim: true, default: '' },
  // Miscellaneous data
  phone: { type: String, trim: true },
  email: { type: String, trim: true, default: '' },
  birthday: { type: Date },
  countryOfBirth: { type: mongoose.Types.ObjectId, ref: 'Countries' },
  countryOfResidence: {
    type: mongoose.Types.ObjectId,
    trim: true,
    ref: 'Countries',
  },
  // Gender data management
  gender: {
    type: String,
    required: true,
    enum: {
      values: ['male', 'female', 'other'],
      message: 'INVALID_GENDER_ENUM',
    },
  },
  // Event-related data
  participantType: {
    type: String,
    enum: {
      values: ['attendee', 'staff', 'volunteer', 'speaker', 'sponsor', 'other'],
      message: 'INVALID_PARTICIPANT_TYPE_ENUM',
    },
    default: 'attendee',
  },
});

const eventSchema = SchemaFactory({
  title: {
    type: String,
    required: true,
  },
  slug: {
    type: String,
    trim: true,
  },
  onlineRegistration: {
    type: Boolean,
  },
  ageOfConsent: {
    type: Number,
  },
  startsAt: {
    type: Date,
    // required: true, // Disabled to allow template creation
  },
  endsAt: {
    type: Date,
    // required: true, // Disabled to allow template creation
  },
  setTime: {
    type: Boolean,
  },
  description: {
    type: String,
  },
  body: {
    type: mongoose.SchemaTypes.Mixed, // RICH TEXT
  },
  keywords: {
    type: [String],
  },
  image: {
    type: mongoose.SchemaTypes.Mixed, // Image File object
  },
  location: {
    type: String,
  },
  mapLocation: {
    type: {
      type: String,
      enum: ['Point'],
    },
    placeName: {
      type: String,
    },
    boundingBox: {
      type: [Number],
    },
    coordinates: {
      type: [Number],
    },
  },
  audience: {
    type: String,
  },
  theme: {
    type: String,
  },
  currency: {
    type: String,
  },
  status: {
    type: Date,
  },
  participants: {
    type: [EventParticipantSchema],
  },
  minParticipants: {
    type: Number,
  },
  maxParticipants: {
    type: Number,
  },
  countParticipants: {
    type: Number,
    default: 0,
  },
  fewPlacesThreshold: {
    type: Number, // the minumum amount of free places (maxParticipants - countParticipants) to indicate that there are still few places open (A.K.A. "freePlaces"). When countParticipants is larger fewPlacesThreshold an indicator can be displayed that few places are available.
  },
  ageMin: {
    type: Number,
  },
  ageMax: {
    type: Number,
  },
  trainingSessions: {
    type: String,
  },
  deadline: {
    type: Date,
  },
  earlyBookingDeadline: {
    type: Date,
  },
  infoName: {
    type: String,
  },
  infoEmail: {
    type: String,
  },
  isTemplate: {
    type: Boolean,
    default: false,
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
  organizer: {
    type: mongoose.Types.ObjectId,
    ref: 'EventOrganizer',
  },
  organizerEmail: {
    type: String,
  },
  organizerWebsite: {
    type: String,
  },
  organizerPhone: {
    type: String,
  },
  organizerFax: {
    type: String,
  },
  organizerBank: {
    name: {
      type: String,
    },
    iban: {
      type: String,
    },
    bic: {
      type: String,
    },
  },
  category: {
    type: mongoose.Types.ObjectId,
    ref: 'EventCategory',
    // required: true, // Disabled to allow template creation
  },
  regions: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'EventRegion',
    },
  ],
  accommodations: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'EventAccommodation',
    },
  ],
  prices: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'EventPrice',
    },
  ],
  foods: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'EventFood',
    },
  ],
  workshops: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'EventWorkshop',
    },
  ],
  additionalFields: {
    type: [FieldSchema],
  },
  registrationTerms: {
    type: [RegistrationTermSchema],
  },
  inclusiveRegistration: {
    type: Boolean,
  },
  importID: {
    type: mongoose.SchemaTypes.Mixed,
  },
  paymentMethod: {
    type: mongoose.Types.ObjectId,
    ref: 'PaymentMethod',
  },
  manualConfirmation: {
    type: Boolean,
    default: false,
  },
  waitlistEnabled: {
    type: Boolean,
    default: false,
  },
});

eventSchema.index({ title: 1 });
eventSchema.index({ slug: 1 });
eventSchema.index({ keywords: 1 });
eventSchema.index({ audience: 1 });
eventSchema.index({ startsAt: 1 });
eventSchema.index({ endsAt: 1 });
eventSchema.index({ category: 1 });
eventSchema.index({ entity: 1 });
eventSchema.index({ importID: 1 });

eventSchema.statics.getAvailableSlug = async function (
  slug,
  entity,
  eventId = null
) {
  const query = { slug, entity, deleted: false };

  if (eventId) {
    query._id = { $ne: eventId };
  }

  const existingEvent = await this.findOne(query);

  return existingEvent ? uniquifySlug(slug) : slug;
};

export default mongoose.model('Event', eventSchema);
