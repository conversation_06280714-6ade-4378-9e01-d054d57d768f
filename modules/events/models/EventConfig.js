import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const eventConfigSchema = SchemaFactory({
  defaultPaymentMethod: {
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'PaymentMethod',
  },
  entity: {
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'Entity',
    required: true,
  },
});

eventConfigSchema.index({ entity: 1 });

export default mongoose.model('EventConfig', eventConfigSchema);
