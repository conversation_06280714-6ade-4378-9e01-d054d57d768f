import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const EventParticipantSchema = SchemaFactory({
  // Name reated field
  prefix: { type: String, trim: true, default: '' },
  firstName: { type: String, trim: true, default: '' },
  middleName: { type: String, trim: true, default: '' },
  lastName: { type: String, trim: true, default: '', required: true },
  suffix: { type: String, trim: true, default: '' },
  fullName: { type: String, trim: true, default: '' },
  // Parent's data
  parent: {
    prefix: { type: String, trim: true, default: '' },
    firstName: { type: String, trim: true, default: '' },
    middleName: { type: String, trim: true, default: '' },
    lastName: { type: String, trim: true, default: '' },
    suffix: { type: String, trim: true, default: '' },
    gender: {
      type: String,
      enum: {
        values: ['male', 'female', 'other'],
        message: 'INVALID_GENDER_ENUM',
      },
    },
    email: { type: String, trim: true, default: '' },
    phone: { type: String, trim: true, default: '' },
    church: { type: String, trim: true, default: '' },
  },
  // Miscellaneous data
  phone: { type: String, trim: true, default: '' },
  email: { type: String, trim: true, default: '' },
  birthday: { type: Date },
  countryOfBirth: { type: String },
  address: {
    careOf: String,
    street: String,
    additionalAddress: String,
    postalCode: String,
    city: String,
    state: String,
    country: String,
  },
  slug: { type: String, trim: true },
  entity: { type: mongoose.Types.ObjectId, ref: 'Entity', required: true },
  // Gender data management
  gender: {
    type: String,
    required: true,
    enum: {
      values: ['male', 'female', 'other'],
      message: 'INVALID_GENDER_ENUM',
    },
  },
  genderDescription: {
    type: String,
  },
  // Event-related data
  participantType: {
    type: String,
    enum: {
      values: ['attendee', 'staff', 'volunteer', 'speaker', 'sponsor', 'other'],
      message: 'INVALID_PARTICIPANT_TYPE_ENUM',
    },
    default: 'attendee',
  },
  registrationStatus: {
    type: String,
    enum: {
      values: [
        'pending',
        'waitlisted',
        'refunded',
        'confirmed',
        'cancelled',
        'unfinished',
      ],
      message: 'INVALID_REGISTRATION_STATUS_ENUM',
    },
    default: 'confirmed', // Default status is confirmed
  },
  comments: {
    type: String,
    trim: true,
  },
  // Additional data
  accommodation: {
    type: mongoose.Types.ObjectId,
    ref: 'EventAccommodation',
  },
  price: {
    type: mongoose.Types.ObjectId,
    ref: 'EventPrice',
  },
  food: {
    type: mongoose.Types.ObjectId,
    ref: 'EventFood',
  },
  workshops: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'EventWorkshop',
    },
  ],
  additional: {
    type: mongoose.SchemaTypes.Mixed,
  },
  // Event data relations
  event: { type: mongoose.Types.ObjectId, ref: 'Event', required: true },
  user: { type: mongoose.Types.ObjectId, ref: 'User' },
  terms: {
    type: mongoose.SchemaTypes.Mixed,
  },
  referenceCode: {
    type: String,
  },
  amount: {
    type: Number,
  },
  currency: {
    type: String,
    trim: true,
    default: 'EUR',
  },
  paymentDate: { type: Date },
  emailSent: {
    type: Boolean,
    default: false,
  },
  // Language is used for emails
  language: {
    type: String,
    trim: true,
    default: 'en',
  },
  // For safe keeping, historical options will not be updated. This tracks price as well as the title of the options chosen at the time of registration
  historicalOptions: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
});

export default mongoose.model('EventParticipant', EventParticipantSchema);
