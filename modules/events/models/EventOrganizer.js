import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const eventOrganizerSchema = SchemaFactory({
  title: {
    type: String,
    required: true,
  },
  acronym: {
    type: String,
  },
  street: {
    type: String,
  },
  postalCode: {
    type: String,
  },
  city: {
    type: String,
  },
  region: {
    type: String,
  },
  country: {
    type: String,
  },
  email: {
    type: String,
  },
  phone: {
    type: String,
  },
  fax: {
    type: String,
  },
  bank: {
    name: {
      type: String,
    },
    iban: {
      type: String,
    },
    bic: {
      type: String,
    },
  },
  website: {
    type: String,
  },
  participationConditions: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      type: 'doc',
      content: [],
    },
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
  importID: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

eventOrganizerSchema.index({ title: 1 });
eventOrganizerSchema.index({ entity: 1 });
eventOrganizerSchema.index({ event: 1 });
eventOrganizerSchema.index({ acronym: 1 });
eventOrganizerSchema.index({ street: 1 });
eventOrganizerSchema.index({ postalCode: 1 });
eventOrganizerSchema.index({ city: 1 });
eventOrganizerSchema.index({ region: 1 });
eventOrganizerSchema.index({ country: 1 });
eventOrganizerSchema.index({ importID: 1 });

export default mongoose.model('EventOrganizer', eventOrganizerSchema);
