import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const eventPriceSchema = SchemaFactory({
  title: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    default: 0,
  },
  commission: {
    type: Number,
  },
  earlyBooking: {
    type: Boolean,
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
  event: {
    type: mongoose.Types.ObjectId,
    ref: 'Event',
    required: true,
  },
  importID: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

eventPriceSchema.index({ title: 1 });
eventPriceSchema.index({ entity: 1 });
eventPriceSchema.index({ event: 1 });
eventPriceSchema.index({ importID: 1 });

export default mongoose.model('EventPrice', eventPriceSchema);
