import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const eventCategorySchema = SchemaFactory({
  title: {
    type: String,
    required: true,
  },
  slug: {
    type: String,
    required: true,
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
  importID: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

eventCategorySchema.index({ title: 1 });
eventCategorySchema.index({ importID: 1 });

eventCategorySchema.statics.getAvailableSlug = async function (
  slug,
  entity,
  categoryId = null
) {
  const query = { slug, entity, deleted: false };

  if (categoryId) {
    query._id = { $ne: categoryId };
  }

  const existingCategory = await this.findOne(query);

  return existingCategory ? uniquifySlug(slug) : slug;
};

export default mongoose.model('EventCategory', eventCategorySchema);
