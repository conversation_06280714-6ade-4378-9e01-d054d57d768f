import mongoose from 'mongoose';

import { uniquifySlug } from '#utils/strings.js';
import SchemaFactory from '#utils/schemaFactory.js';

const eventRegionSchema = SchemaFactory({
  title: {
    type: String,
    required: true,
  },
  slug: {
    type: String,
    required: true,
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
  importID: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

eventRegionSchema.index({ title: 1 });
eventRegionSchema.index({ entity: 1 });
eventRegionSchema.index({ importID: 1 });

eventRegionSchema.statics.getAvailableSlug = async function (
  slug,
  entity,
  regionId = null
) {
  const query = { slug, entity, deleted: false };

  if (regionId) {
    query._id = { $ne: regionId };
  }

  const existingRegion = await this.findOne(query);

  return existingRegion ? uniquifySlug(slug) : slug;
};

export default mongoose.model('EventRegion', eventRegionSchema);
