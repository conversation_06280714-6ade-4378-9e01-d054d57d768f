import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const eventRegistrationSchema = SchemaFactory({
  firstName: {
    type: String,
    required: true,
  },
  lastName: {
    type: String,
    required: true,
  },
  street: {
    type: String,
    required: true,
  },
  postalCode: {
    type: String,
    required: true,
  },
  city: {
    type: String,
    required: true,
  },
  region: {
    type: String,
  },
  country: {
    type: String,
  },
  email: {
    type: String,
    required: true,
  },
  phone: {
    type: String,
    required: true,
  },
  gender: {
    type: String,
  },
  genderDescription: {
    type: String,
  },
  birthday: {
    type: Date,
  },
  parentFirstName: {
    type: String,
  },
  parentLastName: {
    type: String,
  },
  parentEmail: {
    type: String,
  },
  parentPhone: {
    type: String,
  },
  church: {
    type: String,
  },
  additional: {
    type: mongoose.SchemaTypes.Mixed,
  },
  accommodation: {
    type: mongoose.Types.ObjectId,
    ref: 'EventAccommodation',
  },
  price: {
    type: mongoose.Types.ObjectId,
    ref: 'EventPrice',
  },
  food: {
    type: mongoose.Types.ObjectId,
    ref: 'EventFood',
  },
  workshops: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'EventWorkshop',
    },
  ],
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
  event: {
    type: mongoose.Types.ObjectId,
    ref: 'Event',
    required: true,
  },
  terms: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

eventRegistrationSchema.index({ entity: 1 });
eventRegistrationSchema.index({ event: 1 });
eventRegistrationSchema.index({ firstName: 1 });
eventRegistrationSchema.index({ lastName: 1 });

export default mongoose.model('EventRegistration', eventRegistrationSchema);
