import factory from '#utils/handlerFactory.js';
import Category from '../models/EventCategory.js';

export const createCategory = async (req, res) => {
  const data = await Category.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getAllCategories = async (req, res) => {
  const data = await factory.getAll(Category, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const getCategory = async (req, res) => {
  const data = await factory.getOne(Category, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const updateCategory = async (req, res) => {
  const data = await factory.updateOne(Category, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disableCategory = async (req, res) => {
  const data = await factory.disableOne(Category, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enableCategory = async (req, res) => {
  const data = await factory.enableOne(Category, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteCategory = async (req, res) => {
  const event = await factory.getOne(Category, req, { filterByEntity: true });

  await Category.deleteOne({ _id: event._id });

  res.status(204).json({});
};

export default {
  createCategory,
  getAllCategories,
  getCategory,
  updateCategory,
  disableCategory,
  enableCategory,
  deleteCategory,
};
