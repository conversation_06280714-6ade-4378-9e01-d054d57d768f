import factory from '#utils/handlerFactory.js';
import Region from '../models/EventRegion.js';

export const createRegion = async (req, res) => {
  const data = await Region.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getAllRegions = async (req, res) => {
  const data = await factory.getAll(Region, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const getRegion = async (req, res) => {
  const data = await factory.getOne(Region, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const updateRegion = async (req, res) => {
  const data = await factory.updateOne(Region, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disableRegion = async (req, res) => {
  const data = await factory.disableOne(Region, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enableRegion = async (req, res) => {
  const data = await factory.enableOne(Region, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteRegion = async (req, res) => {
  const event = await factory.getOne(Region, req, { filterByEntity: true });

  await Region.deleteOne({ _id: event._id });

  res.status(204).json({});
};

export default {
  createRegion,
  getAllRegions,
  getRegion,
  updateRegion,
  disableRegion,
  enableRegion,
  deleteRegion,
};
