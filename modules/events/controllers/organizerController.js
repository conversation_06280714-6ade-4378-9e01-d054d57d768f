import factory from '#utils/handlerFactory.js';
import Organizer from '../models/EventOrganizer.js';

export const createOrganizer = async (req, res) => {
  const data = await Organizer.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getAllOrganizers = async (req, res) => {
  const data = await factory.getAll(Organizer, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const getOrganizer = async (req, res) => {
  const data = await factory.getOne(Organizer, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const updateOrganizer = async (req, res) => {
  const data = await factory.updateOne(Organizer, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disableOrganizer = async (req, res) => {
  const data = await factory.disableOne(Organizer, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enableOrganizer = async (req, res) => {
  const data = await factory.enableOne(Organizer, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteOrganizer = async (req, res) => {
  const event = await factory.getOne(Organizer, req, { filterByEntity: true });

  await Organizer.deleteOne({ _id: event._id });

  res.status(204).json({});
};

export default {
  createOrganizer,
  getAllOrganizers,
  getOrganizer,
  updateOrganizer,
  disableOrganizer,
  enableOrganizer,
  deleteOrganizer,
};
