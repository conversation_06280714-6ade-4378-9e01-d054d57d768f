import factory from '#utils/handlerFactory.js';
import { slugify } from '#utils/strings.js';
import { isValidObjectId } from '#utils/api/mongoose/id.js';

import Event from '../models/Event.js';
import EventAccommodation from '../models/EventAccommodation.js';
import EventFood from '../models/EventFood.js';
import EventPrice from '../models/EventPrice.js';
import EventWorkshop from '../models/EventWorkshop.js';
import EventConfig from '../models/EventConfig.js';

import {
  createEventFromTemplate,
  getEvents,
  getEventService,
  setEventsConfigService,
} from '../services/eventService.js';

async function findEvent(id) {
  const event = await Event.findById(id);

  if (!event) throw Error(`Event not found! id: ${id}`);

  return event;
}

// EVENT:

export const createEvent = async (req, res) => {
  // Ensure event has a valid slug within its siblings (or create one from its title)
  const slug = await Event.getAvailableSlug(
    slugify(req.body.slug || req.body.title),
    req.entity
  );

  const { defaultPaymentMethod } = await EventConfig.findOne({
    entity: req.entity._id,
  });

  const data = await Event.create({
    ...req.body,
    slug,
    entity: req.entity._id,
    paymentMethod: defaultPaymentMethod || null,
  });

  res.status(200).json(data);
};

export const getAllEvents = async (req, res) => {
  let data = { count: 0, items: [] };

  if (
    !req.user.isAdmin &&
    !req.user.hasPermission({ module: 'events', permission: 'read' })
  ) {
    return res.status(200).json(data);
  }

  const parseBoolean = (val) =>
    typeof val === 'string' ? val.toLowerCase() === 'true' : !!val;

  const { limit, page, past, sortBy, sortDir, search, skip } = req.query;
  const pastEvents = parseBoolean(past);

  try {
    data = await getEvents({
      limit,
      page,
      search,
      sortBy,
      sortDir,
      skip,
      entity: req.entity,
      filter: JSON.parse(req.query.filter),
      user: req.user,
      past: pastEvents,
    });
  } catch (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const getEvent = async (req, res) => {
  // If the  id is not valid return 400 Bad Request
  if (!isValidObjectId(req.params.id)) {
    return res.status(400).json({ status: 'error', message: 'Invalid ID' });
  }

  const { data, error } = await getEventService({
    id: req.params.id,
    entity: req.entity,
  });

  if (error) {
    return res.status(500).json({ status: 'error', message: error.message });
  }
  res.status(200).json(data);
};

export const updateEvent = async (req, res) => {
  const event = await factory.getOne(Event, req, {
    paramId: 'id',
    fields: ['_id '],
  });

  // Ensures new slug doesn't exists
  if (req.body.slug) {
    req.body.slug = await Event.getAvailableSlug(
      slugify(req.body.slug),
      req.entity,
      event._id
    );
  }

  const data = await factory.updateOne(Event, req, {
    filterByEntity: true,
    populate: ['foods', 'accommodations', 'prices', 'workshops'],
  });

  res.status(200).json(data);
};

export const disableEvent = async (req, res) => {
  const data = await factory.disableOne(Event, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const enableEvent = async (req, res) => {
  const data = await factory.enableOne(Event, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const deleteEvent = async (req, res) => {
  const event = await factory.getOne(Event, req, { filterByEntity: true });

  await Event.deleteOne({ _id: event._id });

  res.status(204).json({});
};

// EVENT - FOOD:

export const createFood = async (req, res) => {
  const event = await findEvent(req.params.eventId);

  const data = await EventFood.create({
    ...req.body,
    event: event.id,
    entity: req.entity._id,
  });

  // Add new foods to event.foods array
  event.foods = [...event.foods, data.id];
  event.save();

  res.status(200).json(data);
};

export const updateFood = async (req, res) => {
  const data = await factory.updateOne(EventFood, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteFood = async (req, res) => {
  const eventFood = await factory.getOne(EventFood, req, {
    filterByEntity: true,
  });

  await EventFood.deleteOne({ _id: eventFood._id });

  res.status(204).json({});
};

// EVENT - Accommodation:

export const createAccommodation = async (req, res) => {
  const event = await findEvent(req.params.eventId);

  const data = await EventAccommodation.create({
    ...req.body,
    event: event.id,
    entity: req.entity._id,
  });

  // Add new accommodations to event.accommodations array
  event.accommodations = [...event.accommodations, data.id];
  event.save();

  res.status(200).json(data);
};

export const updateAccommodation = async (req, res) => {
  const data = await factory.updateOne(EventAccommodation, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteAccommodation = async (req, res) => {
  const eventAccommodation = await factory.getOne(EventAccommodation, req, {
    filterByEntity: true,
  });

  await EventAccommodation.deleteOne({ _id: eventAccommodation._id });

  res.status(204).json({});
};

// EVENT - PRICE:

export const createPrice = async (req, res) => {
  const event = await findEvent(req.params.eventId);

  const data = await EventPrice.create({
    ...req.body,
    event: event.id,
    entity: req.entity._id,
  });

  // Add new price to event.prices array
  event.prices = [...event.prices, data.id];
  event.save();

  res.status(200).json(data);
};

export const updatePrice = async (req, res) => {
  const data = await factory.updateOne(EventPrice, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deletePrice = async (req, res) => {
  const eventPrice = await factory.getOne(EventPrice, req, {
    filterByEntity: true,
  });

  await EventPrice.deleteOne({ _id: eventPrice._id });

  res.status(204).json({});
};

// EVENT - WORKSHOP:

export const createWorkshop = async (req, res) => {
  const event = await findEvent(req.params.eventId);

  const data = await EventWorkshop.create({
    ...req.body,
    event: event.id,
    entity: req.entity._id,
  });

  // Add new workshop to event.workshops array
  event.workshops = [...event.workshops, data.id];
  event.save();

  res.status(200).json(data);
};

export const updateWorkshop = async (req, res) => {
  const data = await factory.updateOne(EventWorkshop, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteWorkshop = async (req, res) => {
  const eventWorkshop = await factory.getOne(EventWorkshop, req, {
    filterByEntity: true,
  });

  await EventWorkshop.deleteOne({ _id: eventWorkshop._id });

  res.status(204).json({});
};

export const createFromTemplate = async (req, res) => {
  const { params, entity, body } = req;

  if (!params.eventId) {
    return res
      .status(400)
      .json({ status: 'error', message: 'Event ID is required' });
  }

  const { data, error } = await createEventFromTemplate({
    eventId: params.eventId,
    entity,
    body,
  });

  if (error) {
    return res.status(400).json({ status: 'error', message: error });
  }

  res.status(200).json(data);
};

export const getEventsConfig = async (req, res) => {
  const data =
    (await EventConfig.findOne({
      entity: req.entity._id,
    })) || {};

  res.status(200).json(data);
};

export const setEventsConfig = async (req, res) => {
  const { body, entity } = req;

  if (!body) {
    return res.status(400).json({ status: 'error', message: 'Invalid data' });
  }

  const { data, error } = await setEventsConfigService({
    entityId: entity._id,
    config: body,
  });

  if (error) {
    return res.status(400).json({ status: 'error', message: error.message });
  }

  res.status(200).json(data);
};

export const getEventPaymentMethod = async (req, res) => {
  const { eventId } = req.params;

  if (!eventId) {
    return res.status(400).json({
      status: 'error',
      message: 'Event ID is required',
    });
  }

  const event = await Event.findById(eventId)
    .populate({
      path: 'paymentMethod',
      select: 'config.currencyMinLimit config.currency name',
    })
    .select('paymentMethod')
    .lean();

  if (!event) {
    return res.status(404).json({
      status: 'error',
      message: 'Event not found',
    });
  }

  res.status(200).json(event.paymentMethod);
};

export default {
  createEvent,
  getAllEvents,
  getEvent,
  updateEvent,
  disableEvent,
  enableEvent,
  deleteEvent,
  createFood,
  updateFood,
  deleteFood,
  createAccommodation,
  updateAccommodation,
  deleteAccommodation,
  createPrice,
  updatePrice,
  deletePrice,
  createWorkshop,
  updateWorkshop,
  deleteWorkshop,
  createFromTemplate,
  getEventsConfig,
  setEventsConfig,
  getEventPaymentMethod,
};
