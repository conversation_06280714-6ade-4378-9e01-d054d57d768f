import factory from '#utils/handlerFactory.js';
import Registration from '../models/EventRegistration.js';

export const createRegistration = async (req, res) => {
  const data = await Registration.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getAllRegistrations = async (req, res) => {
  const data = await factory.getAll(Registration, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const getRegistration = async (req, res) => {
  const data = await factory.getOne(Registration, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const updateRegistration = async (req, res) => {
  const data = await factory.updateOne(Registration, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const disableRegistration = async (req, res) => {
  const data = await factory.disableOne(Registration, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enableRegistration = async (req, res) => {
  const data = await factory.enableOne(Registration, req, {
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const deleteRegistration = async (req, res) => {
  const event = await factory.getOne(Registration, req, {
    filterByEntity: true,
  });

  await Registration.deleteOne({ _id: event._id });

  res.status(204).json({});
};

export default {
  createRegistration,
  getAllRegistrations,
  getRegistration,
  updateRegistration,
  disableRegistration,
  enableRegistration,
  deleteRegistration,
};
