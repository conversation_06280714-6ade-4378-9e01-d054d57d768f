import { buildCsvContent } from '#utils/csv.js';
import { formatCurrency } from '#utils/currency.js';
import Event from '../models/Event.js';
import {
  getParticipants,
  addParticipant,
  editParticipant,
  deleteParticipant as deleteParticipantService,
  getParticipant,
  toggleParticipantService,
} from '../services/participantService.js';

/**
 * Retrieves all participants based on the provided query and entity.
 * @function getAllParticipants
 * @param {Object} req The request object containing the query and entity.
 * @param {Object} res The response object used to send the response.
 * @returns {Object} A JSON response containing the list of participants and the count.
 */
export const getAllParticipants = async (req, res) => {
  const { query, entity } = req;
  const {
    limit,
    page,
    search,
    sort,
    sortDir,
    filter,
    downloadFormat,
    eventId,
    participantTypes,
    registrationStatuses,
  } = query;

  if (!eventId) {
    return res.status(400).json({
      status: 'error',
      message: 'Event ID is required.',
    });
  }

  const { statuses } = filter ? JSON.parse(filter) : {};

  const { items, count } = await getParticipants({
    limit,
    page,
    search,
    sortBy: sort,
    sortDir,
    statuses,
    entity,
    eventId,
    participantTypes,
    registrationStatuses,
    downloadFormat,
  });

  if (downloadFormat === 'csv') {
    const { workshops, prices, foods, accommodations, additionalFields } =
      await Event.findById(eventId)
        .populate(['accommodations', 'prices', 'foods', 'workshops'])
        .lean();

    const atLeastOneHasParentInfo =
      items.some(
        (part) => Object.values(part.parent || {}).join('').length > 0
      ) && items.some((part) => part.isMinor);
    const atLeastOneHasAddressInfo = items.some(
      (part) => Object.values(part.address || {}).join('').length > 0
    );
    const eventHasAdditionalFields = additionalFields.length > 0;

    // Collect all keys from participants' additional info
    const getEventDetails = (participant) => {
      const eventDetailsObj = {};

      if (workshops.length > 0) {
        eventDetailsObj.workshops =
          participant.workshops
            .map((work) =>
              workshops.filter((w) => w._id.toString() === work._id.toString())
            )
            .map((work) => work[0]?.title || '')
            .join(`, `) || '';
      }

      if (prices.length > 0) {
        eventDetailsObj.price =
          prices.find(
            (price) => price._id.toString() === participant.price?.toString()
          )?.title || '';
      }

      if (foods.length > 0) {
        eventDetailsObj.food =
          foods.find(
            (food) => food._id.toString() === participant.food?.toString()
          )?.title || '';
      }

      if (accommodations.length > 0) {
        eventDetailsObj.accommodation =
          accommodations.find(
            (acc) =>
              acc._id.toString() === participant.accommodation?.toString()
          )?.title || '';
      }

      if (eventHasAdditionalFields) {
        additionalFields.forEach(({ label, name, type }) => {
          let participantValue = participant?.additional?.[name];
          if (type === 'Checkbox') {
            participantValue = participantValue ? 'X' : '';
          }
          eventDetailsObj[label] = participantValue || '';
        });
      }

      return eventDetailsObj;
    };

    const participantsToExport =
      items.map((item) => {
        const parent = item.parent || {};
        const address = item.address || {};

        const localParticipant = {
          prefix: item.prefix || '',
          firstName: item.firstName || '',
          middleName: item.middleName || '',
          lastName: item.lastName || '',
          suffix: item.suffix || '',
          fullName: item.fullName || '',
          phone: item.phone || '',
          email: item.email || '',
          birthday: item.birthday
            ? item.birthday.toISOString().split('T')[0]
            : '',
          isMinor: item.isMinor ? 'X' : '',
          countryOfBirth: item.countryOfBirth || '',
          gender: item.gender || '',
          participantType: item.participantType || '',
          registrationStatus: item.registrationStatus || '',
          comments: item.comments || '',
          ...(items.some((localItem) => accommodations.includes(localItem._id))
            ? { accommodations }
            : {}),
          ...(atLeastOneHasParentInfo
            ? {
                parentPrefix: parent?.prefix || '',
                parentFirstName: parent?.firstName || '',
                parentMiddleName: parent?.middleName || '',
                parentLastName: parent?.lastName || '',
                parentSuffix: parent?.suffix || '',
                parentGender: parent?.gender || '',
                parentEmail: parent?.email || '',
              }
            : {}),
          ...(atLeastOneHasAddressInfo
            ? {
                addressCareOf: address.careOf || '',
                addressStreet: address.street || '',
                addressAdditionalAddress: address.additionalAddress || '',
                addressPostalCode: address.postalCode || '',
                addressCity: address.city || '',
                addressState: address.state || '',
                addressCountry: address.country || '',
              }
            : {}),
          ...getEventDetails(item),
          amount: formatCurrency(
            item.amount ? item.amount / 100 : 0,
            item.currency || 'EUR'
          ),
        };
        return localParticipant;
      }) || [];

    if (participantsToExport.length === 0) return;
    const csvContent = buildCsvContent(participantsToExport);

    res.setHeader('Content-Type', 'text/csv');
    res.status(200).json({ data: csvContent });
    return;
  }

  res.status(200).json({ items, count });
};

/**
 * Retrieves a participant by their ID within the context of the current entity.
 *
 * @function getParticipantById
 * @async
 * @param {Object} req Express request object, expects `id` in params and `entity` in request.
 * @param {Object} res Express response object.
 * @returns {Object} Responds with participant data if found, otherwise a 404 error.
 */
export const getParticipantById = async (req, res) => {
  const { id } = req.params;
  const { entity } = req;

  const participant = await getParticipant({ id, entityId: entity._id });

  if (!participant) {
    return res.status(404).json({
      status: 'error',
      message: 'Participant not found.',
    });
  }

  return res.status(200).json(participant);
};

/**
 * Creates a new participant.
 * @function createParticipant
 * @param {Object} req The request object containing the participant data.
 * @param {Object} res The response object used to send the response.
 * @returns {Object} A JSON response containing the created participant object or an error object.
 */
export const createParticipant = async (req, res) => {
  const { body, entity } = req;

  const participantInfoSent = {};
  for (const key of Object.keys(body)) {
    const currentKeyValue = body[key];
    if (currentKeyValue != null && key !== 'eventId') {
      participantInfoSent[key] = currentKeyValue;
    }
  }

  const { data, error } = await addParticipant({
    participant: participantInfoSent,
    eventId: body.eventId,
    entity,
  });

  if (error) {
    return res
      .status(400)
      .json({ status: 'error', message: error.message || error });
  }

  return res.status(201).json(data);
};

/**
 * Edits an existing participant.
 * @function updateParticipant
 * @param {Object} req The request object containing the participant data.
 * @param {Object} res The response object used to send the response.
 * @returns {Object} A JSON response containing the edited participant object or an error object.
 */
export const updateParticipant = async (req, res) => {
  const { data, error } = await editParticipant({
    body: req.body,
  });

  if (error) {
    return res.status(400).json({ status: 'error', message: error });
  }

  return res.status(200).json({ data });
};

/**
 * Deletes a participant by ID.
 * @function deleteParticipant
 * @param {Object} req The request object containing the participant ID.
 * @param {Object} res The response object used to send the response.
 * @returns {Object} A JSON response indicating the deletion status.
 */
export const deleteParticipant = async ({ params, entity }, res) => {
  const { data, error } = await deleteParticipantService({
    id: params.id,
    entity,
  });
  if (error) {
    return res.status(400).json({ status: 'error', message: error });
  }
  return res.status(200).json(data);
};

/**
 * Toggles the status of a participant by ID.
 * @function toggleParticipant
 * @param {Object} req The request object containing the participant ID.
 * @param {Object} res The response object used to send the response.
 * @returns {Object} A JSON response indicating the toggle status.
 */
export const toggleParticipant = async (req, res) => {
  const { data, error } = await toggleParticipantService({
    id: req.params.id,
    entity: req.entity,
  });

  if (error) {
    return res.status(400).json({ status: 'error', message: error.message });
  }

  return res.status(200).json(data);
};

export default {
  getAllParticipants,
  getParticipantById,
  createParticipant,
  updateParticipant,
  deleteParticipant,
  toggleParticipant,
};
