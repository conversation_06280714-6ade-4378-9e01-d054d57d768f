import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';

import categoryController from './controllers/categoryController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(categoryController.getAllCategories)
  .post(
    restrictTo({
      module: 'events',
      permissions: ['create'],
    }),
    logRequest({ module: 'events', action: 'CREATE_EVENT' }),
    categoryController.createCategory
  );

router
  .route('/:id')
  .get(categoryController.getCategory)
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT' }),
    categoryController.updateCategory
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT' }),
    categoryController.deleteCategory
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'DISABLE_EVENT' }),
  categoryController.disableCategory
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'ENABLE_EVENT' }),
  categoryController.enableCategory
);

export default router;
