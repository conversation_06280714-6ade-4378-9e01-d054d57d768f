import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';

import participantController from './controllers/participantController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(
    restrictTo({
      module: 'events-participants',
      permissions: ['read'],
    }),
    logRequest({
      module: 'events-participants',
      action: 'READ_EVENT_PARTICIPANTS',
    }),
    participantController.getAllParticipants
  )
  .post(
    restrictTo({
      module: 'events-participants',
      permissions: ['create'],
    }),
    logRequest({
      module: 'events-participants',
      action: 'CREATE_EVENT_PARTICIPANT',
    }),
    participantController.createParticipant
  );

router
  .route('/:id')
  .get(
    restrictTo({
      module: 'events-participants',
      permissions: ['read'],
    }),
    logRequest({
      module: 'events-participants',
      action: 'READ_EVENT_PARTICIPANTS',
    }),
    participantController.getParticipantById
  )
  .patch(
    restrictTo({
      module: 'events-participants',
      permissions: ['update'],
    }),
    logRequest({
      module: 'events-participants',
      action: 'UPDATE_EVENT_PARTICIPANT',
    }),
    participantController.updateParticipant
  )
  .delete(
    restrictTo({
      module: 'events-participants',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({
      module: 'events-participants',
      action: 'DELETE_EVENT_PARTICIPANT',
    }),
    participantController.deleteParticipant
  );

router.route('/:id/toggle').patch(
  restrictTo({
    module: 'events-participants',
    action: 'TOGGLE_EVENT_PARTICIPANT',
  }),
  logRequest({
    module: 'events-participants',
    action: 'TOGGLE_EVENT_PARTICIPANT',
  }),
  participantController.toggleParticipant
);

export default router;
