/**
 * Formats an event's date or date range.
 * @param {Object} event - The event object.
 * @param {string|Date} event.startsAt - The start date/time.
 * @param {string|Date} [event.endsAt] - The optional end date/time.
 * @returns {string} - Formatted date or date range.
 */
export function formatEventDate(event, language) {
  if (!event || !event.startsAt) return '';

  const start = new Date(event.startsAt);
  const end = event.endsAt ? new Date(event.endsAt) : null;

  const options = { year: 'numeric', month: 'short', day: 'numeric' };
  const locale = language || undefined;

  if (end && !Number.isNaN(end.getTime())) {
    // If start and end are on the same day, show only one date
    if (
      start.getFullYear() === end.getFullYear() &&
      start.getMonth() === end.getMonth() &&
      start.getDate() === end.getDate()
    ) {
      return start.toLocaleDateString(locale, options);
    }
    return `${start.toLocaleDateString(locale, options)} - ${end.toLocaleDateString(locale, options)}`;
  }

  return start.toLocaleDateString(locale, options);
}
