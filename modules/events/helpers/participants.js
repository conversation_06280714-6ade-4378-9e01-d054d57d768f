import Event from '../models/Event.js';
import EventParticipant from '../models/EventParticipant.js';

export async function calculateParticipantsCount(eventId) {
  // Update the event's participant count, based on how many participants are confirmed and pending.
  // Count how many participants are confirmed or pending
  const confirmedCount = await EventParticipant.countDocuments({
    event: eventId,
    $or: [
      { registrationStatus: 'confirmed' },
      { registrationStatus: 'pending' },
    ],
    deleted: false,
  });

  await Event.findByIdAndUpdate(eventId, {
    countParticipants: confirmedCount,
  });
}
