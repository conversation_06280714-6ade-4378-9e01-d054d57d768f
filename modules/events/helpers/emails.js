import PaymentMethod from '#modules/payments/models/PaymentMethod.js';
import { logError } from '#utils/logger.js';
import { sendEmail } from '#utils/notifier.js';
import { t } from '#utils/translator.js';

import { formatEventDate } from './dates.js';

export async function handleStatusEmail({ data, type, sendFn, errorMsg }) {
  const populateOptions =
    type === 'confirmed'
      ? [
          'event',
          'price',
          'food',
          'accommodation',
          'workshops',
          { path: 'event', populate: { path: 'organizer' } },
        ]
      : ['event', { path: 'event', populate: { path: 'organizer' } }];

  await data.populate(populateOptions);

  const { config } = await PaymentMethod.findById(data.event.paymentMethod);
  const { emailSender } = config || {};

  const { success, error } = sendFn({
    participant: data.toObject(),
    emailSender,
  });

  if (error) {
    logError(errorMsg, error);
    return { error: errorMsg };
  }

  if (success) {
    data.emailSent = true;
    await data.save();
  }
  return {};
}

// Helper to send participant emails
function sendParticipantEmail({
  participant,
  emailSender,
  templateName,
  subjectKey,
  templateValues = {},
  extraValues = {},
}) {
  if (!participant || !participant.email) {
    return { error: 'Participant data is missing or email is not provided' };
  }
  const { language = 'en' } = participant;
  sendEmail({
    to: participant.email,
    from: emailSender,
    replyTo: participant.event.email || participant.event.organizer.email,
    subject: t(language, subjectKey, { eventTitle: participant.event.title }),
    templateName,
    templateValues: { ...templateValues, ...extraValues },
  });
  return { success: true };
}

export function sendParticipantConfirmation({ participant, emailSender }) {
  const {
    language = 'en',
    price,
    accommodation,
    food,
    workshops = [],
  } = participant;
  const workshopItems = workshops.map((w, i) => ({
    title: w.title || '',
    key: `workshop-${i}`,
  }));

  return sendParticipantEmail({
    participant,
    emailSender,
    templateName: 'events/registration',
    subjectKey: 'confirmationSubject',
    templateValues: {
      confirmationTitle: t(language, 'confirmationTitle', {
        eventTitle: participant.event.title,
      }),
      greeting: t(language, 'greeting', {
        firstName: participant.firstName || '',
      }),
      message: t(language, 'confirmationMessage', {
        eventTitle: participant.event.title,
      }),
      eventTitle: participant.event.title || '',
      eventDate: formatEventDate(participant.event, language) || '',
      eventDescription: participant.event.abstract || '',
      footer: t(language, 'confirmationFooter'),
      organizer: participant.event.organizer.title || '',
      totalLabel: t(language, 'totalLabel'),
      priceTitle: t(language, 'priceTitle'),
      accommodationTitle: t(language, 'accommodationTitle'),
      foodTitle: t(language, 'foodTitle'),
      workshopsTitle: t(language, 'workshopsTitle'),
      optionsTitle: t(language, 'optionsTitle'),
    },
    extraValues: {
      ...(price && { price: price.title }),
      ...(accommodation && { accommodation: accommodation.title }),
      ...(food && { food: food.title }),
      ...(workshopItems.length > 0 && { workshops: workshopItems }),
    },
  });
}

function getCommonTemplateValues(participant, language) {
  return {
    greeting: t(language, 'greeting', {
      firstName: participant.firstName || '',
    }),
    followUp: t(language, 'followUpMessage'),
    thanks: t(language, 'thanksMessage'),
    signature: t(language, 'signatureMessage', {
      organizationName: participant.event.organizer.title || '',
    }),
    appreciation: t(language, 'appreciationMessage', {
      eventTitle: participant.event.title,
    }),
  };
}

export function sendParticipantCancellation({ participant, emailSender }) {
  const { language = 'en' } = participant;
  return sendParticipantEmail({
    participant,
    emailSender,
    templateName: 'events/statusChange',
    subjectKey: 'cancellationSubject', // Is translated in the function
    templateValues: {
      ...getCommonTemplateValues(participant, language),

      message: t(language, 'cancellationMessage', {
        eventTitle: participant.event.title,
      }),
    },
  });
}

export function sendParticipantRefund({ participant, emailSender }) {
  const { language = 'en' } = participant;
  return sendParticipantEmail({
    participant,
    emailSender,
    templateName: 'events/statusChange',
    subjectKey: 'refundSubject',
    templateValues: {
      ...getCommonTemplateValues(participant, language),
      message: t(language, 'refundMessage', {
        eventTitle: participant.event.title,
      }),
    },
  });
}

export function sendParticipantWaitlist({ participant, emailSender }) {
  const { language = 'en' } = participant;
  return sendParticipantEmail({
    participant,
    emailSender,
    templateName: 'events/statusChange',
    subjectKey: 'waitlistSubject',
    templateValues: {
      ...getCommonTemplateValues(participant, language),
      message: t(language, 'waitlistMessage', {
        eventTitle: participant.event.title,
      }),
    },
  });
}
