import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';

import regionController from './controllers/regionController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(regionController.getAllRegions)
  .post(
    restrictTo({
      module: 'events',
      permissions: ['create'],
    }),
    logRequest({ module: 'events', action: 'CREATE_EVENT' }),
    regionController.createRegion
  );

router
  .route('/:id')
  .get(regionController.getRegion)
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT' }),
    regionController.updateRegion
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT' }),
    regionController.deleteRegion
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'DISABLE_EVENT' }),
  regionController.disableRegion
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'ENABLE_EVENT' }),
  regionController.enableRegion
);

export default router;
