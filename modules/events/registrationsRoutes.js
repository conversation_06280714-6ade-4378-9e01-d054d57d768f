import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';

import registrationController from './controllers/registrationController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(registrationController.getAllRegistrations)
  .post(
    restrictTo({
      module: 'events',
      permissions: ['create'],
    }),
    logRequest({ module: 'events', action: 'CREATE_EVENT' }),
    registrationController.createRegistration
  );

router
  .route('/:id')
  .get(registrationController.getRegistration)
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT' }),
    registrationController.updateRegistration
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT' }),
    registrationController.deleteRegistration
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'DISABLE_EVENT' }),
  registrationController.disableRegistration
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'ENABLE_EVENT' }),
  registrationController.enableRegistration
);

export default router;
