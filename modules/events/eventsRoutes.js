import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';

import eventController from './controllers/eventController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

// Configure
router
  .route('/configure')
  .get(eventController.getEventsConfig)
  .patch(
    restrictTo({ module: 'events-settings', permissions: ['configure'] }),
    logRequest({ module: 'events-settings', action: 'UPDATE_EVENTS_CONFIG' }),
    eventController.setEventsConfig
  );

router
  .route('/')
  .get(eventController.getAllEvents)
  .post(
    restrictTo({
      module: 'events',
      permissions: ['create'],
    }),
    logRequest({ module: 'events', action: 'CREATE_EVENT' }),
    eventController.createEvent
  );

router
  .route('/:id')
  .get(eventController.getEvent)
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT' }),
    eventController.updateEvent
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT' }),
    eventController.deleteEvent
  );

router.route('/:id/disable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'DISABLE_EVENT' }),
  eventController.disableEvent
);

router.route('/:id/enable').patch(
  restrictTo({
    module: 'events',
    permissions: ['update'],
    paramId: 'id',
  }),
  logRequest({ module: 'events', action: 'ENABLE_EVENT' }),
  eventController.enableEvent
);

// EVENT FOOD
router.route('/:eventId/foods').post(
  restrictTo({
    module: 'events',
    permissions: ['create'],
  }),
  logRequest({ module: 'events', action: 'CREATE_EVENT_FOOD' }),
  eventController.createFood
);

router
  .route('/:eventId/foods/:id')
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT_FOOD' }),
    eventController.updateFood
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT_FOOD' }),
    eventController.deleteFood
  );

// EVENT ACCOMMODATION
router.route('/:eventId/accommodations').post(
  restrictTo({
    module: 'events',
    permissions: ['create'],
  }),
  logRequest({ module: 'events', action: 'CREATE_EVENT_ACCOMMODATION' }),
  eventController.createAccommodation
);

router
  .route('/:eventId/accommodations/:id')
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT_ACCOMMODATION' }),
    eventController.updateAccommodation
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT_ACCOMMODATION' }),
    eventController.deleteAccommodation
  );

// EVENT PRICE
router.route('/:eventId/prices').post(
  restrictTo({
    module: 'events',
    permissions: ['create'],
  }),
  logRequest({ module: 'events', action: 'CREATE_EVENT_PRICE' }),
  eventController.createPrice
);

router
  .route('/:eventId/prices/:id')
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT_PRICE' }),
    eventController.updatePrice
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT_PRICE' }),
    eventController.deletePrice
  );

// EVENT WORKSHOP
router.route('/:eventId/workshops').post(
  restrictTo({
    module: 'events',
    permissions: ['create'],
  }),
  logRequest({ module: 'events', action: 'CREATE_EVENT_WORKSHOP' }),
  eventController.createWorkshop
);

router
  .route('/:eventId/workshops/:id')
  .patch(
    restrictTo({
      module: 'events',
      permissions: ['update'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'UPDATE_EVENT_WORKSHOP' }),
    eventController.updateWorkshop
  )
  .delete(
    restrictTo({
      module: 'events',
      permissions: ['delete'],
      paramId: 'id',
    }),
    logRequest({ module: 'events', action: 'DELETE_EVENT_WORKSHOP' }),
    eventController.deleteWorkshop
  );

// EVENT TEMPLATE
router.route('/:eventId/createFromTemplate').post(
  restrictTo({
    module: 'events',
    permissions: ['create'],
  }),
  logRequest({ module: 'events', action: 'CREATE_FROM_TEMPLATE' }),
  eventController.createFromTemplate
);

// Event payment method
router.route('/:eventId/paymentMethod').get(
  restrictTo({
    module: 'events',
    permissions: ['read'],
  }),
  eventController.getEventPaymentMethod
);

export default router;
