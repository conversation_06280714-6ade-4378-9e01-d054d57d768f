import eventCategoriesRouter from './categoriesRoutes.js';
import eventsRouter from './eventsRoutes.js';
import eventOrganizersRouter from './organizersRoutes.js';
import eventRegionsRouter from './regionsRoutes.js';
import eventParticipantRouter from './participantRoutes.js';
import eventsTasks from './tasks/index.js';

export default function events() {
  return {
    routes: {
      '/events': eventsRouter,
      '/event-categories': eventCategoriesRouter,
      '/event-organizers': eventOrganizersRouter,
      '/event-regions': eventRegionsRouter,
      '/event-participants': eventParticipantRouter,
    },

    tasks: eventsTasks,
  };
}
