import { getClientIp } from '@supercharge/request-ip/dist/index.js';
import crypto from 'crypto';
import _ from 'lodash';
import { authenticator } from 'otplib';

import Client from '#modules/clients/models/Client.js';
import Entity from '#modules/entities/models/Entity.js';

import getEntityAncestorAttribute, {
  ancestorsPopulate,
} from '#modules/entities/helpers/getEntityAncestorAttribute.js';
import { getEntityLocales } from '#modules/entities/helpers/getEntityLocales.js';
import { hasAccessToEntity } from '#modules/entities/helpers/restrictAccessToEntity.js';
import { errors } from '#utils/appError.js';
import { signToken, verifyToken } from '#utils/auth.js';
import { compare } from '#utils/dates.js';
import { getLanguage } from '#utils/helpers.js';
import Logger from '#utils/logger.js';
import { sendEmail } from '#utils/notifier.js';
import { randomString } from '#utils/strings.js';
import { t } from '#utils/translator.js';

import User from '../models/User.js';
import { getBackendURL } from '../utils/ancestors.js';

const resetPath = process.env.PASSWORD_RESET_PATH;

const createSendToken = async (user, statusCode, req, res) => {
  const token = await signToken(user._id);

  return res.status(statusCode).json({
    token,
  });
};

const sendPasswordChangeAlert = (user, entity) => {
  // Define language for the email
  const language = getLanguage(user.preferences, entity);

  sendEmail({
    language,
    entity,
    to: user.email,
    subject: t(language, 'passwordChangeTitle'),
    templateName: 'passwordChange',
    templateValues: {
      title: t(language, 'passwordChangeTitle'),
      text: t(language, 'passwordChangeText'),
      warning: t(language, 'passwordChangeWarning'),
    },
  });
};

const checkPasswordAndToken = async (userId, password, token) => {
  // We need to get the user to obtain the current password
  const user = await User.findById(userId).select('+password +twoFactorSecret');

  // Verify the password and the token provided are correct
  const passwordValid = await user.correctPassword(password);

  if (!passwordValid) return false;

  if (user.twoFactorEnabled) {
    const tokenValid = authenticator.verify({
      secret: user.twoFactorSecret,
      token: `${token}`,
    });

    if (!tokenValid) return false;
  }

  return true;
};

const extractTokensFromReq = (req) => {
  let token;
  let email;
  let pass;
  const header = req.get('Authorization');

  if (header) {
    if (header.startsWith('Basic')) {
      let basicAuth = header.split(' ')[1];
      try {
        // This should have email and password joined by a colon
        basicAuth = Buffer.from(basicAuth, 'base64').toString('utf8');

        [email, pass] = basicAuth.split(':');
      } catch (error) {
        Logger.error('Error trying to parse basic auth!', error);
      }
    } else if (header.startsWith('Bearer')) {
      token = header.split(' ')[1];
    } else {
      token = header;
    }
  } else {
    token = req.cookies['hope-jwt']; // TODO: Check if cookies are readable.
  }

  return { token, email, pass };
};

export const loadCurrentEntity = async (req, res, next) => {
  // Set current entity's find filters
  const filters = {};

  // If EntityId header is provided, use it
  if (req.get('EntityID')) {
    filters._id = req.get('EntityID');
  }
  // If not, use Origin header to get the entity through it's backend domain
  else if (req.get('Origin')) {
    // Get the backend domain from the Origin header
    const domain = `${req.get('Origin')}`.split(/https?:\/\//).pop();
    filters['config.backendDomains'] = {
      $elemMatch: {
        domain,
      },
    };
  }
  try {
    // Get current entity from DB
    const data = await Entity.findOne(filters)
      .select('_id name config language network')
      .populate([
        ancestorsPopulate,
        {
          path: 'network',
        },
      ]);

    // Ensure language is set to entity language or ancestor language (or default to english)
    data.language =
      data?.language || getEntityAncestorAttribute(data, 'language', 'en');

    data.languages = getEntityLocales(data.toObject ? data.toObject() : data);

    // Set current entity in request object
    req.entity = data;
  } catch (error) {
    // If there is an error, log it and continue
    Logger.error('Error trying to get current entity!', error);
  }

  // Continue to next middleware
  next();
};

export const loadUser = async (req, res, next) => {
  // Extract JWT token or email and pass from Authorization header
  const { token, email, pass } = extractTokensFromReq(req);

  if (token) {
    try {
      // Token verification
      const decoded = await verifyToken(token);

      // Check if user is valid
      const user = await User.findOne({
        _id: decoded.id,
        enabled: true,
        deleted: false,
      }).populate('groups');

      if (!user) return next();

      // Check if user changed password after the JWT was issued
      if (user.changedPasswordAfter(decoded.iat)) return next();

      // Everything passed, add user to req object
      req.user = user;
    } catch (error) {
      Logger.warning('Error trying to verify logged in user!', error);
    }
  } else if (email && pass) {
    // Check if user is valid
    const user = await User.findOne({
      email,
      isAdmin: false, // For security reasons, this type of auth is disabled for admin accounts.
      twoFactorEnabled: false, // For security reasons, this type of auth is disabled for accounts with 2FA enabled
      deleted: false,
      enabled: true,
    })
      .select('+password +failedLoginCount +loginBlockedUntil')
      .populate('groups');

    if (!user) return next();

    // If the account has a current loginBlockedUntil, return
    if (
      user.loginBlockedUntil &&
      compare(user.loginBlockedUntil, Date.now()) > -1
    )
      return next();

    // If password is incorrect, increase failed login count and return
    if (!(await user.correctPassword(pass))) {
      await user.increaseFailedLogin();

      return next();
    }

    // Everything passed, add user to req object
    req.user = user;
  }

  const impersonationId = req.headers['x-impersonation-id'] || null;

  // Admin is required until we do the impersonation permission.
  if (req.user?.isAdmin && !_.isEmpty(impersonationId)) {
    const impersonateUser = await User.findOne({
      _id: impersonationId,
      deleted: false,
      enabled: true,
    }).populate('groups');

    // If the user is not found, continue
    if (!impersonateUser) {
      return next();
    }
    req.user.impersonateUser = impersonationId;

    // The api will use the impersonator's original data to log actions, but with the entity and groups of the impersonated user to check permissions.

    // Entity is used in checking which entities can be accessed. (We don't want the original entity to be used)
    req.user.entity = impersonateUser.entity;
    // Groups are used in checking permissions.
    req.user.groups = impersonateUser.groups;
    // We remove the admin flag to avoid the admin permissions to be applied. The backend will still have a button to stop impersonating.
    req.user.isAdmin = false;
  }

  next();
};

export const protect = (req, res, next) => {
  if (!req.user) next(errors.not_logged_in());

  next();
};

export const authorizeRequest = () => async (req, res, next) => {
  try {
    const clientToken = req.get('ClientToken');

    if (clientToken) {
      // Get client from DB, based on token, and should be enabled and not deleted
      const client = await Client.findOne({
        token: clientToken,
        deleted: false,
        enabled: true,
      });

      // Check if there is a client
      if (!client) {
        Logger.error('No client, or client disabled! Token:', clientToken);
        return next(errors.not_allowed());
      }

      // If the client has a list of allowed IPs, check that the IP from request is included
      if (client.allowedIPs.length > 0) {
        // Get IP from request
        const clientIp = getClientIp(req);

        // If not included, reject
        if (!client.allowedIPs.includes(clientIp))
          return next(errors.not_allowed());
      }

      // Always check if there is an entity in the request
      if (!req.entity) {
        Logger.error('No entity found!');
        return next(errors.entity_not_found());
      }

      const { hasAccess: hasAccessToCurrentEntity, error: accessError } =
        await hasAccessToEntity({
          targetEntityId: req.entity._id.toString(),
          user: req.user,
          currentEntity: req.entity,
          permission: 'read',
        });

      // If user doesn't have access to the current entity, try with user's own entity

      const { hasAccess: hasAccessToUserEntity } = await hasAccessToEntity({
        targetEntityId: req.user?.entity?.toString(),
        user: req.user,
        currentEntity: req.entity,
        permission: 'read',
      });

      // If current entity is not allowed but user's entity is, switch current entity to user's entity
      if (!hasAccessToCurrentEntity && hasAccessToUserEntity) {
        req.entity = await Entity.findById(req.user.entity);
      }

      const hasEntityAccess = hasAccessToCurrentEntity || hasAccessToUserEntity;

      if (
        req.user &&
        !req.user?.isAdmin && // Admins are always allowed
        !hasEntityAccess // Check if user has access to the current entity
      ) {
        Logger.warning('Entity not allowed!');
        return next(accessError);
      }

      // Prevent changing crucial fields
      if (req.body) {
        delete req.body.deleted; // deleted field is only modified through delete endpoints
        delete req.body.enabled; // enabled field is only modified through enable/disable endpoints
        delete req.body.createdAt; // createdAt field is automatically set on creation endpoints
        delete req.body.updatedAt; // updatedAt field is automatically set on update endpoints
      }

      next();
    } else {
      Logger.error('No client token provided!');
      return next(errors.not_allowed());
    }
  } catch (error) {
    Logger.error('Error trying to authorize request!', error);
    next(errors.internal_error());
  }
};

export const restrictTo =
  ({
    module = '',
    permissions = [], // i.e. ['read', 'update', 'delete']
    paramId = '',
    getPermissions = null,
  } = {}) =>
  async (req, res, next) => {
    // Admin is always allowed
    if (req.user.isAdmin) return next();

    // Get recordId from request:
    // - If its a translation, get the source recordId from the request (sourceRecordId needs to be provided by a previous middleware)
    // - Otherwise, get the recordId from the request params
    const recordId = req.sourceRecordId || req.params[paramId];

    // If getPemissions is a function, get the permissions from it
    if (typeof getPermissions === 'function') {
      permissions = _.uniq([
        ...permissions,
        ...(await getPermissions({ id: recordId })),
      ]);
    }

    // Permissions
    if (!_.isEmpty(module)) {
      for (const permission of permissions) {
        if (
          req.user.hasPermission({
            module, // Module to check
            permission, // Permission to check
            recordId, // Specific record to check permissions (if any)
          })
        ) {
          return next();
        }
      }
    }

    // Reject if no permissions matched
    next(errors.not_permitted(module, permissions, recordId));
  };

export const renewToken = async (req, res) => {
  const { user } = req;

  if (!user) throw errors.auth_failed();

  // All is good, return new pair of tokens
  await createSendToken(user, 200, req, res);
};

export const get2FASecretAndUrl = async (req, res) => {
  const secret = authenticator.generateSecret(32);
  const url = authenticator.keyuri(req.user.email, 'Hope Backend', secret);

  res.status(200).json({ secret, url });
};

export const enable2FA = async (req, res) => {
  const { secret, token, password } = req.body;

  // We need to get the user to obtain the current password
  const user = await User.findById(req.user._id).select('+password');

  if (!(await user.correctPassword(password))) {
    throw errors.auth_failed();
  }

  const isValid = authenticator.verify({
    secret,
    token: `${token}`,
  });

  // If secret and token are not valid, reject
  if (!isValid) throw errors.twofa_not_valid();

  // Generate 10 unique backup codes
  const twoFactorCodes = [];
  while (twoFactorCodes.length < 10) {
    const newCode = randomString({ chars: '0123456789', length: 6 });
    if (!twoFactorCodes.includes(newCode)) twoFactorCodes.push(newCode);
  }

  // Save secret, enable 2FA and return the backup codes
  user.twoFactorEnabled = true;
  user.twoFactorSecret = secret;
  user.twoFactorCodes = twoFactorCodes;
  await user.save();

  res.status(200).json({
    twoFactorCodes,
    user: _.omit(user.toObject(), [
      'password',
      'twoFactorSecret',
      'twoFactorCodes',
    ]),
  });
};

export const disable2FA = async (req, res) => {
  const { user } = req;

  // If 2FA is already disabled, just return
  if (!user.twoFactorEnabled) {
    return res.status(200).json(user);
  }

  const { password, token } = req.body;

  const isAuthorized = await checkPasswordAndToken(user._id, password, token);

  if (!isAuthorized) {
    throw errors.auth_failed();
  }

  // Remove secret, disable 2FA and return
  user.twoFactorEnabled = false;
  user.twoFactorSecret = undefined;
  user.twoFactorCodes = [];
  await user.save();

  res.status(200).json(user);
};

/**
 * Login user and send JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Promise<void>} -
 */
export const login = async (req, res) => {
  const { email, password, token } = req.body;

  // Check if email and password are sent
  if (!email || !password) throw errors.params();

  // Check if user exists and password is correct
  const user = await User.findOne({
    email,
    deleted: false,
    enabled: true,
  }).select(
    '+password +failedLoginCount +loginBlockedUntil +twoFactorSecret +twoFactorCodes'
  );

  if (!user) throw errors.auth_failed();

  // This is for the log middleware
  req.user = user;

  // If the account has a current loginBlockedUntil, return error
  if (
    user.loginBlockedUntil &&
    compare(user.loginBlockedUntil, Date.now()) > -1
  )
    throw errors.login_blocked();

  if (!(await user.correctPassword(password))) {
    await user.increaseFailedLogin();

    throw errors.auth_failed();
  }

  if (user.twoFactorEnabled) {
    // Require token
    if (!token) return res.status(200).json({ twoFactorRequired: true });

    // If the token is not valid, return error
    const isValid = authenticator.verify({
      secret: user.twoFactorSecret,
      token: `${token}`,
    });

    // If not, check if it matches one of the backup codes
    if (!isValid) {
      if (user.twoFactorCodes.includes(token)) {
        // It matches, so remove it from the list and allow to continue
        user.twoFactorCodes = user.twoFactorCodes.filter((c) => c !== token);
        await user.save();
      } else {
        await user.increaseFailedLogin();

        throw errors.auth_failed();
      }
    }
  }

  // Update the last login date and reset failed login count
  await user.setLastLogin();

  // Everything ok, send token to client
  await createSendToken(user, 200, req, res);
};

export const forgotPassword = async (req, res) => {
  const email = !_.isEmpty(req.body.email) ? req.body.email.toLowerCase() : '';
  const { entity } = req;

  try {
    // 1) Get user based on email
    const user = await User.findOne({
      email,
      // entity: entity._id,
      enabled: true,
      deleted: false,
    });

    if (user) {
      const token = await user.createPasswordResetToken();

      // Entity might not have a backendURL, so check it first, or get from the ancestors
      const backendURL = await getBackendURL(entity);

      const resetURL = `${backendURL}/${resetPath}/${token}`;

      // Define language for the email
      const language = getLanguage(user.preferences, entity);

      sendEmail({
        language,
        entity,
        to: email,
        subject: t(language, 'resetSubject'),
        templateName: 'passwordReset',
        templateValues: {
          title: t(language, 'resetTitle'),
          text1: t(language, 'resetText1'),
          text2: t(language, 'resetText2'),
          text3: t(language, 'resetText3'),
          text4: t(language, 'resetText4'),
          duration: t(language, 'resetDuration', {
            tokenDuration: 30,
          }),
          linkURL: resetURL,
          linkText: t(language, 'resetLinkText'),
          linkNotWorking: t(language, 'linkNotWorking'),
        },
      });
    }
  } catch (error) {
    Logger.error('Error trying to send password reset email!', error);
  }

  res.status(204).json({});
};

export const resetPassword = async (req, res) => {
  // Get user based on the token
  const hashedToken = crypto
    .createHash('sha256')
    .update(req.params.token)
    .digest('hex');

  const user = await User.findOne({
    passwordResetToken: hashedToken,
    passwordResetExpires: { $gt: Date.now() },
  });

  // If token has not expired, set the new password
  if (!user) throw errors.token_not_valid();

  user.password = req.body.password;
  user.passwordResetToken = undefined;
  user.passwordResetExpires = undefined;
  await user.save();

  // Send email to alert of the change
  // Do it after res is finished, so the response is sent quickly
  res.on('finish', async () => {
    // Only send alert if user has already logged in at least once
    if (user.lastLoginAt) {
      sendPasswordChangeAlert(user, req.entity);
    }

    // Update the last login date and reset failed login count
    await user.setLastLogin();
  });

  // Log the user in and send token
  await createSendToken(user, 200, req, res);
};

export const updatePassword = async (req, res) => {
  const { user } = req;
  const { currentPassword, newPassword, token } = req.body;

  const isAuthorized = await checkPasswordAndToken(
    user._id,
    currentPassword,
    token
  );

  if (!isAuthorized) {
    throw errors.auth_failed();
  }

  // If so, update the password
  user.password = newPassword;
  await user.save();

  // Send email to alert of the change
  // Do it after res is finished, so the response is sent quickly
  res.on('finish', () => {
    sendPasswordChangeAlert(user, req.entity);
  });

  // Log the user in and send token
  await createSendToken(user, 200, req, res);
};

export default {
  loadCurrentEntity,
  loadUser,
  protect,
  authorizeRequest,
  restrictTo,
  renewToken,
  get2FASecretAndUrl,
  enable2FA,
  disable2FA,
  login,
  forgotPassword,
  resetPassword,
  updatePassword,
};
