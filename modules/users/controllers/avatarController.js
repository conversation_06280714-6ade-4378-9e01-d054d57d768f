import _ from 'lodash';
import fs from 'fs';
import { extname } from 'path';

import {
  cropImage,
  fileTypes,
  getImageSizeData,
  resizeImage,
} from '#utils/files.js';
import { sendEvent } from '#utils/notifier.js';
import { getUpload, removeFile, uploadFile } from '#utils/storage.js';
import Logger from '#utils/logger.js';
import User from '../models/User.js';

const allowedContentTypes = [
  ...fileTypes.jpg,
  ...fileTypes.png,
  ...fileTypes.webp,
  ...fileTypes.tiff,
];
const bucketAvatars = process.env.BUCKET_AVATARS || 'hope-avatars';
const maxFileSize = process.env.MAX_AVATAR_FILE_SIZE || '5mb';

const upload = getUpload({
  folder: bucketAvatars,
  maxFileSize,
  allowedContentTypes,
});

export const uploadAvatar = upload.single('file');

export const cropAvatar = async (req, res, next) => {
  if (!req.file) return next();

  const { file } = req;
  let crop;

  if (req.body.crop) {
    try {
      crop = JSON.parse(req.body.crop);
    } catch (e) {
      Logger.error('Could not parse crop values:', e);
    }
  }

  if (crop) {
    // Get image width and height
    const { width, height } = await getImageSizeData(file.path);

    let x = _.toInteger(crop.x);
    let y = _.toInteger(crop.y);
    let cropWidth = _.toInteger(crop.width);

    // Make sure the crop values are within image values
    x = x > width || x < 0 ? 0 : x;
    y = y > height || y < 0 ? 0 : y;

    const maxWidth = Math.min(width - x, height - y);

    if (cropWidth > maxWidth) {
      cropWidth = maxWidth;
    } else if (cropWidth < 80) {
      // Make sure avatar is at least 80 pixels of width
      cropWidth = 80;
    }

    await cropImage(file.path, file.path, cropWidth, cropWidth, x, y);
  }

  // Get image width and height
  const { width, height } = await getImageSizeData(file.path);
  const resolution = width * height;
  // If image is bigger than 16.5 megapixels, resize it
  if (resolution > 16500000) {
    // Resizing to fit in 16 megapixels (5333x3000)
    await resizeImage(file.path, file.path, 5333, 3000);
  }

  next();
};

export const setAvatar = async (req, res) => {
  if (!req.file) {
    return res
      .status(400)
      .json({ status: 'error', message: 'No avatar file provided' });
  }

  const { id, avatar, impersonateUser } = req.user || {};
  const { path: filePath, mimetype } = req.file;

  // If user is impersonating, get the impersonated user
  const impersonatedUser = impersonateUser
    ? await User.findById(impersonateUser)
    : null;

  // Get the user id and avatar
  const userId = impersonateUser || id;
  const userAvatar = impersonatedUser ? impersonatedUser.avatar : avatar;

  // Create a unique file name based on the current date
  const fileName = `avatar-${+new Date()}`;

  // Upload the file
  await uploadFile(filePath, {
    bucket: bucketAvatars,
    key: userId,
    name: fileName,
    mime: mimetype,
  });

  // Delete the temp file
  try {
    fs.unlinkSync(filePath);
  } catch (error) {
    Logger.error('Error trying to delete temp file:', error);
  }

  // Delete previous avatar
  await removeFile(bucketAvatars, `${userId}/${userAvatar}`);

  // Update user's avatar field
  req.body = { avatar: `${fileName}${extname(filePath)}` };
  await User.updateOne({ _id: userId }, req.body);

  // Send socket event
  sendEvent({ to: userId, event: 'ACCOUNT_UPDATED' });

  res.status(200).json({ status: 'OK' });
};

export const setDefaultAvatar = async (user) => {
  try {
    const fileName = `avatar-${+new Date()}`;

    await uploadFile('assets/default-avatar.jpg', {
      bucket: bucketAvatars,
      key: user.id,
      name: fileName,
      mime: 'image/jpeg',
    });

    user.avatar = `${fileName}.jpg`;

    await user.save();
  } catch (error) {
    Logger.error('Error when trying to set default avatar:', error);
  }
};

export default {
  uploadAvatar,
  cropAvatar,
  setAvatar,
  setDefaultAvatar,
};
