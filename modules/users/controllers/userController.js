import _ from 'lodash';

import Group from '#modules/groups/models/Group.js';
import Log from '#modules/logs/models/Log.js';

import factory from '#utils/handlerFactory.js';
import { errorCodes, errors, generateError } from '#utils/appError.js';
import { areEqualIDs } from '#utils/helpers.js';
import { randomPassword } from '#utils/strings.js';
import User from '../models/User.js';
import { getBackendURL } from '../utils/ancestors.js';
import userServices from '../services/userServices.js';

const resetPath = process.env.PASSWORD_RESET_PATH;

export const setUserId = (req, res, next) => {
  req.params.id = req.user._id;

  next();
};

/**
 *  Middleware to filter properties that can't be set by the user
 */
export const filterProperties = (req, res, next) => {
  // These props can't be set by the user and must be handled only by the system
  const propsToOmit = [
    'password',
    'passwordChangedAt',
    'passwordResetToken',
    'passwordResetExpires',
    'lastLoginAt',
    'failedLoginCount',
    'loginBlockedUntil',
    'twoFactorEnabled',
    'twoFactorSecret',
    'twoFactorCodes',
    'avatar',
    'entity',
  ];

  //  Include isAdmin to the list if user is not admin or is editing himself
  if (!req.user.isAdmin || areEqualIDs(req.params.id, req.user._id)) {
    propsToOmit.push('isAdmin');
  }

  // Remove from body non-settable properties
  req.body = _.omit(req.body, propsToOmit);

  next();
};

export const createUser = async (req, res, next) => {
  const { entity } = req;
  req.body.entity = entity._id;
  req.body.password = randomPassword();

  const existingUser = await User.findOne({
    email: req.body.email,
    // Don't allow any duplicate users. Disabling the entity check for now.
    // entity: entity._id,
  }).populate('entity');

  // If user already exists, return error
  if (existingUser) {
    return next(
      generateError(
        `Email already in use: ${req.body.email} in ${existingUser.entity.name}`,
        errorCodes.USER_ALREADY_EXISTS,
        409
      )
    );
  }

  // Create user
  const user = await User.create(req.body);

  // If user couldn't be created, return error
  if (!user) {
    return next(
      generateError('Error creating user', errorCodes.USER_CREATION_ERROR, 404)
    );
  }

  // Set default avatar
  // await setDefaultAvatar(user); // TODO: Do we need this? Disabled for now.
  const backendURL = await getBackendURL(entity);
  // If no backend URL is defined, we don't send the email, we raise an error
  if (!backendURL) {
    return next(
      generateError(
        `No backend URL defined for ${entity.name} or its ancestors`,
        errorCodes.NO_BACKEND_URL,
        404
      )
    );
  }

  // Set up the new user, send the email
  await userServices.setUpNewUser(user, entity, backendURL, resetPath);

  res.status(201).json(user);
};

export const getAllUsers = async (req, res) => {
  const { excludedIds } = req.query;
  const users = await userServices.getUsers({
    ...req.query,
    entityId: req.entity._id,
    isAdmin: req.user.isAdmin,
    exclude: excludedIds,
  });

  res.status(200).json(users);
};

export const getOwnUser = async (req, res) => {
  if (_.isEmpty(req.user?.impersonateUser?.toString())) {
    return res.status(200).json(req.user);
  }

  // req.user.impersonateUser is the id of the user that is being impersonated.
  const impersonatedUser = await User.findById(req.user.impersonateUser).lean();

  if (!impersonatedUser) {
    return res.status(200).json(req.user);
  }
  impersonatedUser.groups = req.user.groups; //TODO: review if this is needed
  impersonatedUser.impersonated = true; // We tell the backend that this user is impersonated

  res.status(200).json(impersonatedUser);
};

export const getUser = async (req, res) => {
  // If the user needs to be impersonated, we get the user from the id in the req.user.impersonateUser.
  const data = await userServices.getUser({
    id: req.params.id,
    entityId: req.entity._id,
    filterByEntity: !areEqualIDs(req.params.id, req.user.id),
    filterByGroups:
      req.query.filterByGroups && !areEqualIDs(req.params.id, req.user.id),
  });

  let user = {};

  const canReadUsers = req.user.hasPermission({
    module: 'users',
    permission: 'read',
  });

  // For normal users, return only some basic fields
  if (
    !areEqualIDs(data._id, req.user._id) &&
    !req.user.isAdmin &&
    !canReadUsers
  ) {
    user = _.pick(data.toObject(), ['name', 'avatar']);
  } else {
    // const permissions = await data.getPermissions();

    const cantDisable2FA = !!data.groups.find((g) => g.requires2FA);

    user = {
      ...data.toObject(),
      // permissions, THIS IS DEPRECATED, but it could be repurposed for my refactor of permissions.
      cantDisable2FA,
    };
  }

  res.status(200).json(user);
};

export const getUserByEmail = async (req, res) => {
  const user = await userServices.getUserByEmail(req.query.email);
  res.status(!user ? 404 : 200).json(user);
};

export const updateUser = async (req, res) => {
  const filter = {};

  // Make sure non-admin users can't modify admins
  if (!req.user.isAdmin) {
    filter.isAdmin = false;
  }

  const data = await factory.updateOne(User, req, {
    filter,
    filterByEntity: !areEqualIDs(req.params.id, req.user.id),
  });

  res.status(200).json(data);
};

export const stopImpersonating = async (req, res) => {
  const user = userServices.stopImpersonating(req.user._id);

  res.status(200).json(user);
};

export const disableUser = async (req, res) => {
  const filter = {};

  // Make sure non-admin users can't modify admins
  if (!req.user.isAdmin) {
    filter.isAdmin = false;
  }

  const data = await factory.disableOne(User, req, {
    filter,
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const enableUser = async (req, res) => {
  const filter = {};

  // Make sure non-admin users can't modify admins
  if (!req.user.isAdmin) {
    filter.isAdmin = false;
  }

  const data = await factory.enableOne(User, req, {
    filter,
    filterByEntity: true,
  });

  res.status(200).json(data);
};

export const transferUser = async (req, res) => {
  const filter = {};

  // Make sure non-admin users can't modify admins
  if (!req.user.isAdmin) {
    filter.isAdmin = false;
  }

  const user = await factory.getOne(User, req, {
    filter,
    filterByEntity: true,
  });

  if (!user) {
    throw errors.not_found();
  }

  if (user.entity !== req.body.entityId) {
    user.entity = req.body.entityId;
    await user.save();
  }

  res.status(200).json(user);
};

export const deleteUser = async (req, res) => {
  const filter = {};

  // Make sure non-admin users can't modify admins
  if (!req.user.isAdmin) {
    filter.isAdmin = false;
  }

  const user = await factory.getOne(User, req, {
    filter,
    filterByEntity: true,
  });

  // First, remove user from all groups
  await user.removeFromAllGroups();

  // If there are logs of the user, we make a soft delete
  // otherwise, we fully delete it
  const logCount = await Log.find({ user }).countDocuments();

  if (logCount > 0) {
    // Add [DELETED] to name, a fake email, and set as deleted and disabled
    user.name = `[DELETED] - ${user.name}`;
    user.email = `${user.id.toString()}@hopemedia.de`;
    user.deleted = true;
    user.enabled = false;
    await user.save();
  } else {
    await User.deleteOne({ _id: user._id });
  }

  res.status(204).json({});
};

export const removeFromGroup = async (req, res) => {
  const group = await Group.findById(req.body.groupId);

  // Don't allow removing users from groups that don't belong to the current entity
  if (!group.entity.equals(req.entity._id)) {
    throw errors.not_allowed();
  }

  const user = await factory.getOne(User, req);

  if (group) {
    await group.removeUser(user.id);
  }

  res.status(200).json(user);
};

export default {
  setUserId,
  filterProperties,
  createUser,
  getAllUsers,
  getUser,
  getUserByEmail,
  updateUser,
  disableUser,
  enableUser,
  transferUser,
  deleteUser,
  removeFromGroup,
  stopImpersonating,
  getOwnUser,
};
