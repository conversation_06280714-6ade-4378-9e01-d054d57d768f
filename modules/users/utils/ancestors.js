import Entity from '#modules/entities/models/Entity.js';
import _ from 'lodash';

export async function getAncestorField(entity, field) {
  // We make a recursive function that will return the field of the first ancestor that has the field
  if (_.isEmpty(entity)) return null;

  const value = _.get(entity, field); // We get the value of the field, with get because it could be nested
  if (!_.isEmpty(value)) return value;

  const { parent: parentId } = entity.parent
    ? entity
    : await Entity.findById(entity._id, 'parent'); // If we don't have the parent, we get it from the database
  const parent = await Entity.findById(parentId, `parent, ${field}`);

  if (parent) return getAncestorField(parent, field);

  return null; // If no ancestor has the field, return null.
}
// Recursive function that inside every ancestor will check if in an array of objects, a condition is met, and if so returns the field of the object that meets the condition. E.g. getAncestorObjectByCondition(entity, 'config.backendDomains', {isPrimary: true}, 'domain');
export async function getAncestorObjectArrayValueByCondition(
  entity,
  field,
  objectCondition,
  objectField
) {
  if (_.isEmpty(entity)) return null;

  const array = _.get(entity, field);

  // For example this will match {isPrimary: true} with {isPrimary: true, domain: 'example.com'} and return 'example.com'
  if (!_.isEmpty(array)) {
    const object = array.find((obj) => _.isMatch(obj, objectCondition));
    if (!_.isEmpty(object)) return object[objectField];
  }

  const { parent: parentId } = entity.parent
    ? entity
    : await Entity.findById(entity._id, 'parent');

  const parent = await Entity.findById(parentId, `parent, ${field}`);

  if (parent)
    return getAncestorObjectArrayValueByCondition(
      parent,
      field,
      objectCondition,
      objectField
    );

  return null;
}

export async function getBackendURL(entity) {
  const backendURL = await getAncestorObjectArrayValueByCondition(
    entity,
    'config.backendDomains',
    { isPrimary: true },
    'domain'
  );

  if (!backendURL) return null;
  // We append the correct protocol depending on if it's localhost or not
  const protocol = backendURL?.includes('localhost') ? 'http' : 'https';
  return `${protocol}://${backendURL}`;
}

export async function getDefaultDesign(entity) {
  const defaultDesign = await getAncestorField(entity, 'config.defaultDesign');
  return defaultDesign;
}

export async function getEmailLogo(entity) {
  const logo = await getAncestorField(entity, 'config.email.appearance.logo');
  return logo;
}

export async function getEmailColors(entity) {
  const colors = await getAncestorField(
    entity,
    'config.email.appearance.colors'
  );
  return colors;
}
