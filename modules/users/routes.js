import express from 'express';
import rateLimit from 'express-rate-limit';

import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { validate } from '#utils/validationMiddleware.js';

import authController, { restrictTo } from './controllers/authController.js';
import avatarController from './controllers/avatarController.js';
import userController from './controllers/userController.js';
import userListSchema from './validation/userListSchema.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authController.authorizeRequest());

// Limit to 2 requests every 5 seconds for login
router.post(
  '/login',
  rateLimit({
    windowMs: 5 * 1000,
    max: 2,
  }),
  logRequest({ module: 'users', action: 'LOGIN' }),
  authController.login
);

// Limit to 1 request every 10 seconds for forgot-password
router.post(
  '/forgot-password',
  rateLimit({
    windowMs: 10 * 1000,
    max: 1,
  }),
  logRequest({ module: 'users', action: 'FORGOT_PASSWORD' }),
  authController.forgotPassword
);

router.patch(
  '/reset-password/:token',
  logRequest({ module: 'users', action: 'RESET_PASSWORD' }),
  authController.resetPassword
);

// Require user for all routes after this middleware
router.use(authController.protect);

router.post('/renew-token', authController.renewToken);
router.get('/me', userController.setUserId, userController.getOwnUser);
router.patch(
  '/update-password',
  logRequest({ module: 'users', action: 'UPDATE_PASSWORD' }),
  authController.updatePassword
);
router.get('/new-2fa', authController.get2FASecretAndUrl);
router.patch(
  '/enable-2fa',
  logRequest({ module: 'users', action: 'ENABLE_2FA' }),
  authController.enable2FA
);
router.patch(
  '/disable-2fa',
  logRequest({ module: 'users', action: 'DISABLE_2FA' }),
  authController.disable2FA
);
router.patch(
  '/stop-impersonating',
  logRequest({ module: 'users', action: 'STOP_IMPERSONATING' }),
  userController.stopImpersonating
);
router.patch(
  '/update-me',
  logRequest({ module: 'users', action: 'UPDATE_ME' }),
  userController.setUserId,
  userController.filterProperties,
  userController.updateUser
);
router.post(
  '/upload-avatar',
  logRequest({ module: 'users', action: 'UPLOAD_AVATAR' }),
  userController.setUserId,
  avatarController.uploadAvatar,
  avatarController.cropAvatar,
  avatarController.setAvatar
);

router.route('/by-email').get(
  restrictTo({
    module: 'users',
    permissions: ['read'],
  }),
  userController.getUserByEmail
);

// Allow any authenticated user to get a user
// but props returned will be filtered in the controller
router.route('/:id').get(userController.getUser);

// Restrict to user manager
router.use(
  restrictTo({
    module: 'users',
    permissions: ['read'],
  })
);

router
  .route('/')
  .get(validate(userListSchema, 'query'), userController.getAllUsers)
  .post(
    restrictTo({ module: 'users', permissions: ['create'] }),
    logRequest({ module: 'users', action: 'CREATE_USER' }),
    userController.filterProperties,
    userController.createUser
  );

router
  .route('/:id')
  .patch(
    restrictTo({ module: 'users', permissions: ['update'] }),
    userController.filterProperties,
    userController.updateUser
  )
  .delete(
    restrictTo({ module: 'users', permissions: ['delete'] }),
    logRequest({ module: 'users', action: 'DELETE_USER' }),
    userController.deleteUser
  );

router
  .route('/:id/disable')
  .patch(
    restrictTo({ module: 'users', permissions: ['update'] }),
    logRequest({ module: 'users', action: 'DISABLE_USER' }),
    userController.disableUser
  );

router
  .route('/:id/enable')
  .patch(
    restrictTo({ module: 'users', permissions: ['update'] }),
    logRequest({ module: 'users', action: 'ENABLE_USER' }),
    userController.enableUser
  );

router
  .route('/:id/transfer')
  .patch(
    restrictTo({ module: 'users', permissions: ['transfer'] }),
    logRequest({ module: 'users', action: 'TRANSFER_USER' }),
    userController.transferUser
  );

router
  .route('/:id/remove-from-group')
  .post(
    restrictTo({ module: 'groups', permissions: ['update'] }),
    logRequest({ module: 'users', action: 'REMOVE_USER_FROM_GROUP' }),
    userController.removeFromGroup
  );

export default router;
