import isEmpty from 'lodash/isEmpty.js';
import mongoose from 'mongoose';

import getEntityAncestorAttribute from '#modules/entities/helpers/getEntityAncestorAttribute.js';
import siteServices from '#modules/web/services/siteServices.js';
import { getProjection } from '#utils/aggregations.js';
import getCollationFilter from '#utils/api/collation/filter.js';
import getListFilters from '#utils/api/list/filters.js';
import { isValidObjectId } from '#utils/api/mongoose/id.js';

import Category from '../models/Category.js';

// List of supported category types
const categoryTypes = ['global', 'network', 'entity'];

/**
 * Get category types.
 * @returns {array} - Category types
 */
function getCategoryTypes() {
  return categoryTypes;
}

/**
 * Get all categories
 * @param {Object} params - The params object
 * @param {String[]} params.types - List of category types to filter
 * @param {Object} params.entity - Current entity object
 * @param {String[]} params.statuses - List of statuses to filter
 * @param {String[]} params.ids - List of category ids to filter
 * @param {String[]} params.excludedIds - List of category ids to exclude
 * @param {String} params.search - Search string
 * @param {String} params.siteId - Site ID to filter categories from entities of site
 * @param {Number} params.limit - Number of items to return
 * @param {Number} params.page - Page number
 * @param {String} params.sort - Sort field
 * @param {Number} params.skip - Number of items to skip
 * @param {String} params.language - Collation language
 * @returns {Promise<object>} - List of categories and total count
 */
export async function getList({
  entity = null,
  excludedIds = [],
  fields = [],
  ids = [],
  language = 'en',
  limit = 25,
  page = 1,
  search = '',
  siteId = null,
  skip = 0,
  sort = 'title.en',
  statuses = [],
  types = [],
}) {
  // TODO: Escape special characters in search string if they are still present and not escaped

  // Get list filters
  const filters = getListFilters({
    statuses,
    limit,
    page,
    skip,
    sort,
    sortFields: ['title', 'type'],
    search,
    searchFields: [`title.${language}`, 'name', `description.${language}`],
  });

  const matchFilters = {
    $and: [filters.statuses],
  };

  if (!isEmpty(filters.search)) {
    matchFilters.$and.push(filters.search);
  }

  // Search by types in the types list
  if (types?.length) {
    matchFilters.type = { $in: types };
  }

  const validIds = ids
    .filter((id) => isValidObjectId(id))
    .map((id) => new mongoose.Types.ObjectId(id));

  const validExcludedIds = excludedIds
    .filter((id) => isValidObjectId(id))
    .map((id) => new mongoose.Types.ObjectId(id));

  // Only return categories in the ids list
  if (validIds?.length) {
    matchFilters._id = {
      $in: validIds,
    };
  } else if (validExcludedIds?.length) {
    // Or exclude categories by id
    matchFilters._id = {
      $nin: validExcludedIds,
    };
  }

  if (entity || siteId) {
    let filterEntity = entity;

    // Override entity filter if siteId is defined
    if (siteId) {
      // Get entity ids from site
      const { site } = await siteServices.getSiteById(siteId);

      // If site entities are found, add them to the entity filter
      if (site) {
        filterEntity = site.entity;
      }
    }

    // If entity is defined, return global, network and entity categories
    matchFilters.$or = [
      // Global categories:
      { type: 'global' },
      // Network categories (if entity has a network):
      ...(filterEntity?.network
        ? [
            {
              type: 'network',
              network: filterEntity?.network?._id || entity?.network,
            },
          ]
        : []),
      // Entity (local) categories:
      { type: 'entity', entity: filterEntity?._id },
    ];
  } else {
    // If entity is not defined, return global categories only
    matchFilters.entity = { $eq: null };
    matchFilters.network = { $eq: null };
  }

  const collationFilters = getCollationFilter({ language });
  const projection = getProjection(fields);

  const itemsAggregate = Category.aggregate().match(matchFilters);

  if (projection) {
    itemsAggregate.project(projection);
  }

  // Add __idsOrder field to sort by the order of the ids list
  if (filters.sort.__idsOrder) {
    itemsAggregate.addFields({
      __idsOrder: {
        $indexOfArray: [validIds, '$_id'],
      },
    });
  }

  itemsAggregate
    .sort(filters.sort)
    .addFields({
      id: '$_id',
      __idsOrder: '$$REMOVE', // Remove __idsOrder field since it's only for sorting
    })
    .skip(filters.pagination.skip)
    .limit(filters.pagination.limit) // NOTE: limit must be after skip!
    .lookup({
      from: 'categories',
      let: { ancestors: '$ancestors' },
      as: 'ancestors',
      pipeline: [
        {
          $match: {
            $expr: {
              $in: [
                '$_id',
                {
                  $cond: [{ $isArray: '$$ancestors' }, '$$ancestors', []],
                },
              ],
            },
          },
        },
        {
          $addFields: {
            id: '$_id',
          },
        },
        {
          $project: { id: 1, title: 1, description: 1, type: 1 },
        },
      ],
    })
    .collation(collationFilters);

  const items = await itemsAggregate;
  const count = await Category.find(matchFilters).countDocuments();

  return { items, count };
}

/**
 * Add a new category.
 * @param {Object} data - Category data
 * @param {Object} entity - Entity object
 * @param {Object} user - User object
 * @returns {Object} - New category
 */
export async function addCategory(data = {}, entity = null, user = null) {
  if (!entity) {
    return {
      error: {
        method: 'addCategory',
        code: 'entity_required',
        message: 'Entity is required',
      },
    };
  }

  // Set type to 'entity' if user is not an admin. Only admins can create 'global' and 'network' categories.
  if (!user.isAdmin) {
    data.type = 'entity';
  }

  // Calculate ancestors list based on the parent category
  const ancestors = await getCategoryAncestorsIds(data.parent);
  const newData = { ...data, ancestors }; // Clone data
  const { type } = data; // Get category type

  // If type is defined and is not valid, return error
  if (type && !categoryTypes.includes(type)) {
    return {
      error: {
        method: 'addCategory',
        code: 'category_type_invalid',
        message: 'Category type is invalid',
      },
    };
  }

  // Depending on the category type, we need to set the entity or network id (or none)
  switch (type) {
    case 'network': {
      // Get network id from entity (directly or from ancestors)
      const networkId =
        entity.network || (await getEntityAncestorAttribute(entity, 'network'));

      // Set network id
      newData.network = networkId;
      // and clear entity id (if exists)
      newData.entity = null;
      break;
    }
    case 'entity': {
      // Set entity id
      newData.entity = entity._id;
      //and clear network id (if exists)
      newData.network = null;
      break;
    }
    default: {
      // If type is global (on not set),
      // we need to clear both entity and network ids
      newData.entity = null;
      newData.network = null;
      break;
    }
  }

  // Create category
  const newCategory = await Category.create(newData);

  // Return new category
  return { data: newCategory };
}

/**
 * Update category.
 * @param {String} id - Category ID
 * @param {Object} data - Category data
 * @param {Object} entity - Entity object
 * @param {Object} user - User object
 * @returns {Object} - Updated category
 */
async function updateCategory(id, data = {}, entity = null, user = null) {
  if (!id) {
    return {
      error: {
        method: 'updateCategory',
        code: 'category_id_required',
        message: 'A Category ID is required',
      },
    };
  }

  if (!entity) {
    return {
      error: {
        method: 'updateCategory',
        code: 'entity_required',
        message: 'Entity is required',
      },
    };
  }

  const category = await Category.findById(id);

  if (!category) {
    return {
      error: {
        method: 'updateCategory',
        code: 'category_not_found',
        message: 'Category not found',
      },
    };
  }

  // Calculate ancestors list based on the parent category
  const ancestors = await getCategoryAncestorsIds(
    !data.parent && category.parent ? undefined : data.parent || category.parent
  );

  const newData = { ...data, ancestors }; // Clone data
  const { type } = data; // Get category type

  // If type is defined and is not valid, return error
  if (type && !categoryTypes.includes(type)) {
    return {
      error: {
        method: 'updateCategory',
        code: 'category_type_invalid',
        message: 'Category type is invalid',
      },
    };
  }

  // Check if user can manage categories of the current category type or the new type
  if (
    !userCanManageCategory(user, category) ||
    !userCanManageCategory(user, category, type)
  ) {
    return {
      error: {
        method: 'updateCategory',
        code: 'category_type_not_allowed',
        message: 'User is not allowed to manage this category',
      },
    };
  }

  // Depending on the category type, we need to set the entity or network id
  switch (type) {
    case 'network': {
      // Get network id from entity
      const networkId =
        entity.network ||
        (await getEntityAncestorAttribute(entity.a, 'network'));

      // Set network id
      newData.network = networkId;
      // and clear entity id (if exists)
      newData.entity = null;
      break;
    }
    case 'entity': {
      // Set entity id
      newData.entity = entity._id;
      //and clear network id (if exists)
      newData.network = null;
      break;
    }
    default: {
      // If type is global (on not set),
      // we need to clear both entity and network ids
      newData.entity = null;
      newData.network = null;
      break;
    }
  }

  // Update category
  const updatedCategory = await Category.findByIdAndUpdate(id, newData, {
    new: true,
  });

  // Return updated category
  return { data: updatedCategory };
}

/**
 * Check if user can manage category.
 * - If an user is admin, he can manage any type of category (global, network, entity).
 * - If an user is not an admin, he can manage only categories of type entity.
 * @param {*} user - User object
 * @param {*} category - Category object
 * @param {*} type - Category type (global, network or entity)
 * @returns `boolean` - True if user can manage category
 */
function userCanManageCategory(user, category, type = null) {
  // If user is not defined, return false
  if (!user) return false;

  // If user is ADMIN, return true
  if (user.isAdmin) return true;

  // NON-ADMIN user checks:
  //  1) If category is not defined, return false
  if (!category) return false;

  // 2) If category type is not defined, return false
  const typeToCheck = type || category?.type;
  if (!typeToCheck) return false;

  // 3) If is trying to manage a 'global' or 'network'category, return false
  if (typeToCheck !== 'entity') return false;

  // 4) If category is not from the same entity as the user, return false
  if (category.entity !== user.entity) return false;

  // 5) If user can't update categories, return false
  const canUpdateCategory = user.hasPermission({
    module: 'categories',
    permission: 'update',
  });
  if (!canUpdateCategory) return false;

  // Finally, return true at this point because all checks passed
  return true;
}

/**
 * Get category ancestors based on the parent category, from closest ancestor to the top level (a root, with no parent).
 * @param {Object} parentId Parent category ID
 * @param {Object[]} ancestors List of ancestors to append to
 * @returns {Object[]} List of category ancestors
 */
async function getCategoryAncestorsIds(parentId, ancestors = []) {
  // If parent category is not defined or is not a valid ObjectId, return ancestors list as is.
  if (!parentId || !mongoose.Types.ObjectId.isValid(parentId)) return ancestors;

  // Get parent category
  const parentCategory = await Category.findById(parentId).select('id parent');

  // If parent category exists,
  if (parentCategory) {
    // Add parent to ancestors list
    ancestors.push(parentCategory._id);

    // And get recursively parent of parent (if exists)
    await getCategoryAncestorsIds(parentCategory.parent, ancestors);
  }

  // Return ancestors list
  return ancestors;
}

export default {
  getCategoryTypes,
  getList,
  addCategory,
  updateCategory,
  userCanManageCategory,
};
