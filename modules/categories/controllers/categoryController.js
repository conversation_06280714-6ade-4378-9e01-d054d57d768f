import factory from '#utils/handlerFactory.js';

import Category from '../models/Category.js';
import categoryServices from '../services/categoriesSevices.js';

export const getCategories = async (req, res) => {
  const { query, entity } = req;
  const {
    excludedIds,
    fields,
    ids,
    language,
    limit,
    page,
    search,
    siteId,
    sort,
    statuses,
    types,
  } = query;

  const data = await categoryServices.getList({
    entity,
    excludedIds,
    fields,
    ids,
    language,
    limit,
    page,
    search,
    siteId,
    sort,
    statuses,
    types,
  });

  res.status(200).json(data);
};

export const categoriesTypes = async (req, res) => {
  const items = categoryServices.getCategoryTypes();

  res.status(200).json({ items, count: items.length });
};

export const createCategory = async (req, res) => {
  const { body, entity, user } = req;

  const { data, error } = await categoryServices.addCategory(
    body,
    entity,
    user
  );

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json(data);
};

export const getCategory = async (req, res) => {
  const data = await factory.getOne(Category, req, {
    paramId: 'id',
    filter: { $or: [{ entity: { $eq: null } }, { entity: req.entity._id }] },
    populate: [
      {
        path: 'ancestors',
        select: 'id name title',
      },
    ],
  });

  res.status(200).json(data);
};

export const updateCategory = async (req, res) => {
  const { body, params, entity, user } = req;
  const { id } = params;

  const { data, error } = await categoryServices.updateCategory(
    id,
    body,
    entity,
    user
  );

  if (error) {
    return res.status(400).json({ error });
  }

  res.status(200).json(data);
};

export const disableCategory = async (req, res) => {
  const data = await factory.disableOne(Category, req);

  res.status(200).json(data);
};

export const enableCategory = async (req, res) => {
  const data = await factory.enableOne(Category, req);

  res.status(200).json(data);
};

export const deleteCategory = async (req, res) => {
  const data = await factory.deleteOne(Category, req);

  res.status(200).json(data);
};

export const restoreCategory = async (req, res) => {
  const data = await factory.restoreOne(Category, req);

  res.status(200).json(data);
};

export default {
  createCategory,
  getCategories,
  categoriesTypes,
  getCategory,
  updateCategory,
  disableCategory,
  enableCategory,
  deleteCategory,
  restoreCategory,
};
