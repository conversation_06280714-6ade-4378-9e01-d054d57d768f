import express from 'express';

import {
  authorizeRequest,
  protect,
  restrictTo,
} from '#modules/users/controllers/authController.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import categoryController from './controllers/categoryController.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

router
  .route('/')
  .get(categoryController.getCategories)
  .post(
    restrictTo({ module: 'categories', permissions: ['create'] }),
    logRequest({ module: 'categories', action: 'CREATE_CATEGORY' }),
    categoryController.createCategory
  );

router.route('/types').get(categoryController.categoriesTypes);

router
  .route('/:id')
  .get(categoryController.getCategory)
  .patch(
    restrictTo({ module: 'categories', permissions: ['update'] }),
    logRequest({ module: 'categories', action: 'UPDATE_CATEGORY' }),
    categoryController.updateCategory
  )
  .delete(
    restrictTo({ module: 'categories', permissions: ['delete'] }),
    logRequest({ module: 'categories', action: 'DELETE_CATEGORY' }),
    categoryController.deleteCategory
  );

router
  .route('/:id/restore')
  .patch(
    restrictTo({ module: 'categories', permissions: ['delete'] }),
    logRequest({ module: 'categories', action: 'RESTORE_CATEGORY' }),
    categoryController.restoreCategory
  );

router
  .route('/:id/disable')
  .patch(
    restrictTo({ module: 'categories', permissions: ['update'] }),
    logRequest({ module: 'categories', action: 'DISABLE_CATEGORY' }),
    categoryController.disableCategory
  );

router
  .route('/:id/enable')
  .patch(
    restrictTo({ module: 'categories', permissions: ['update'] }),
    logRequest({ module: 'categories', action: 'ENABLE_CATEGORY' }),
    categoryController.enableCategory
  );

export default router;
