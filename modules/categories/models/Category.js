import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const categorySchema = SchemaFactory({
  title: {
    type: mongoose.SchemaTypes.Mixed,
    default: {
      en: '',
    },
  },
  description: {
    type: mongoose.SchemaTypes.Mixed,
  },
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: ['global', 'network', 'entity'],
    required: true,
  },
  parent: {
    type: mongoose.Types.ObjectId,
    ref: 'Category',
    default: null,
  },
  ancestors: {
    type: [mongoose.Types.ObjectId],
    ref: 'Category',
    default: [],
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    default: null,
  },
  network: {
    type: mongoose.Types.ObjectId,
    ref: 'Network',
    default: null,
  },
  importIDs: {
    type: [mongoose.SchemaTypes.Mixed],
  },

  // @deprecated: old typo3 imports
  oldImportIDs: {
    type: [String],
    default: [],
  },
});

categorySchema.index({ title: 1, name: 1 });
categorySchema.index({ type: 1 });
categorySchema.index({ parent: 1 });
categorySchema.index({ entity: 1 });
categorySchema.index({ network: 1 });

export default mongoose.model('Category', categorySchema);
