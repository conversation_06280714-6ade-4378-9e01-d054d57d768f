const db = {};
const ObjectId = (id) => id;

// After having added more granular permissions to the sites some people can't access features they had access before
// This script grants the following permissions to all groups that have the update permission for a site
const newPermissions = [
  'flags',
  'notifications',
  'regions',
  'appearance',
  'template',
  'scripts',
  'analytics',
  'search',
  'newsletter',
  'rss',
  'emailConfig',
  'cacheControl',
  'cookies',
];

// Only get the groups that are assigned to subentities of the FiD and have permissions related to sites
db.groups
  .aggregate([
    {
      $lookup: {
        from: 'entities',
        localField: 'entity',
        foreignField: '_id',
        as: 'entitydetail',
      },
    },
    {
      $unwind: {
        path: '$entitydetail',
      },
    },
    {
      $match: {
        'entitydetail.ancestors': {
          // Filter based on the FiD ancestor ID
          $in: [ObjectId('63d0fd450665185d40296f12')],
        },
      },
    },
    {
      $match: {
        'permissions.sites': {
          $exists: true,
        },
      },
    },
  ])
  .forEach((group) => {
    if (group?.permissions?.sites?.update) {
      db.groups.updateOne(
        {
          _id: group._id,
        },
        // Set the defined values to true if the permission was not already set before
        {
          $set: Object.fromEntries(
            newPermissions.map((key) => [
              `permissions.sites.${key}`,
              group.permissions.sites?.[key] === undefined
                ? true
                : group.permissions.sites[key],
            ])
          ),
        }
      );
    }
  });
