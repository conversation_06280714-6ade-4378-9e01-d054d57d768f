import _ from 'lodash';

import factory from '#utils/handlerFactory.js';

import Group from '../models/Group.js';
import groupServices from '../services/groupServices.js';

export const filterProperties = (req, res, next) => {
  if (!req.user.isAdmin) {
    // Remove from body non-settable properties
    req.body = _.omit(req.body, ['users', 'entity']);
  }

  next();
};

export const createGroup = async (req, res) => {
  const data = await Group.create({
    ...req.body,
    entity: req.entity._id,
  });

  res.status(200).json(data);
};

export const getGroups = async (req, res) => {
  const data = await factory.getAll(Group, req, {
    filterByEntity: true,
    populate: [
      {
        path: 'users',
        match: { enabled: true, deleted: false },
        select: 'name email avatar',
      },
    ],
  });

  res.status(200).json(data);
};

export const getGroup = async (req, res) => {
  const data = await factory.getOne(Group, req, {
    filterByEntity: true,
    populate: [
      {
        path: 'users',
        match: { enabled: true, deleted: false },
        select: 'name email avatar',
        options: { sort: { name: 1 } },
      },
    ],
  });

  res.status(200).json(data);
};

export const updateGroup = async (req, res) => {
  const data = await factory.updateOne(Group, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const deleteGroup = async (req, res) => {
  const group = await factory.getOne(Group, req, {
    filterByEntity: true,
  });

  await Group.deleteOne({ _id: group._id });

  res.status(204).json({});
};

export const disableGroup = async (req, res) => {
  const data = await factory.disableOne(Group, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const enableGroup = async (req, res) => {
  const data = await factory.enableOne(Group, req, { filterByEntity: true });

  res.status(200).json(data);
};

export const createUsers = async (req, res) => {
  const { users, supportedLanguages } = req.body || {};

  const group = await groupServices.createUsersToGroup({
    users,
    entityId: req.entity._id,
    groupId: req.params.id,
    supportedLanguages,
  });

  res.status(200).json(group);
};

export const addUsers = async (req, res) => {
  const { userIds } = req.body;

  const group = await groupServices.addUsersToGroup({
    groupId: req.params.id,
    userIds,
  });

  res.status(200).json(group);
};

export const removeUsers = async (req, res) => {
  const { userIds } = req.body;

  const group = await factory.getOne(Group, req, { filterByEntity: true });

  const ids = userIds || [];

  group.users = group.users.filter((id) => !ids.includes(id.toString()));
  await group.save();

  res.status(200).json(group);
};

export const candidates = async (req, res) => {
  const { excludedIds } = req.query;

  const users = await groupServices.getGroupCandidates({
    groupId: req.params.id, // Can be null
    search: req.query.search,
    entityId: req.entity._id,
    excludedIds,
  });

  res.status(200).json(users);
};

export default {
  filterProperties,
  createGroup,
  getGroups,
  getGroup,
  updateGroup,
  deleteGroup,
  disableGroup,
  enableGroup,
  createUsers,
  addUsers,
  removeUsers,
  candidates,
};
