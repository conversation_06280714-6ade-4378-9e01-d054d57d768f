import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';
import { areEqualIDs } from '#utils/helpers.js';

const groupSchema = SchemaFactory({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  requires2FA: {
    type: Boolean,
    default: false,
  },
  permissions: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
  users: [
    {
      type: mongoose.Types.ObjectId,
      ref: 'User',
    },
  ],
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
});

groupSchema.index({ name: 1 });
groupSchema.index({ entity: 1 });

groupSchema.methods.removeUser = async function (userId) {
  this.users = this.users.filter((id) => !areEqualIDs(id, userId));
  await this.save();
};

export default mongoose.model('Group', groupSchema);
