{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Node.js",
      "skipFiles": ["<node_internals>/**"],
      "outputCapture": "std",
      "program": "${workspaceFolder}/server.js",
      "envFile": "${workspaceFolder}/.env"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Node.js - Prod",
      "skipFiles": ["<node_internals>/**"],
      "outputCapture": "std",
      "program": "${workspaceFolder}/server.js",
      "envFile": "${workspaceFolder}/.env.prod"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Node.js - Local Staging",
      "skipFiles": ["<node_internals>/**"],
      "outputCapture": "std",
      "program": "${workspaceFolder}/server.js",
      "envFile": "${workspaceFolder}/.env.local-staging"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Node.js - Local Prod",
      "skipFiles": ["<node_internals>/**"],
      "outputCapture": "std",
      "program": "${workspaceFolder}/server.js",
      "envFile": "${workspaceFolder}/.env.local-prod"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Node.js - Local Prod Scalingo",
      "skipFiles": ["<node_internals>/**"],
      "outputCapture": "std",
      "program": "${workspaceFolder}/server.js",
      "envFile": "${workspaceFolder}/.env.local-prod-scalingo"
    }
  ]
}
