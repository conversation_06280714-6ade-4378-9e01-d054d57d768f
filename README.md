# AWE Core API

Main API for the AWE platform.

---

## Logging requests in the Database

To save a log of an important request in the DB, simply add the `logRequest` middleware located in `#modules/logs/middlewares/logMiddleware.js` to that request, specifying the module and the action.

For example: `logRequest({ module: 'users', action: 'CREATE_USER' })`. Use `UPPER_CASE` for `action` values.

These logs are tied to the Entity, Client and User models.

You can see examples in the `routes` of the `users`, and `web` modules.
