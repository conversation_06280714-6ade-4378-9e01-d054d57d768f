import 'dotenv/config.js';

import fs from 'fs';
import mongoose from 'mongoose';
import path from 'path';
import url from 'url';

import app from '#app';
import featureFlagService from '#modules/feature-flags/services/featureFlagService.js';
import searchIndexTaskService from '#modules/search/services/searchIndexTaskService.js';
import registerTasks from '#scripts/tasks.js';
import { toLogDate } from '#utils/dates.js';
import { logError, logInfo, logSuccess, logWarning } from '#utils/logger.js';
import { notifyAdmins } from '#utils/notifier.js';
import initSocket from '#utils/socket.js';

const mongoSSLOptions = {};

// If DB_CERT_REQUIRED is set, we need to use SSL for MongoDB connection, and use the provided CA certificate if available
if (process.env.DB_CERT_REQUIRED) {
  const __dirname = url.fileURLToPath(new URL('.', import.meta.url));
  const mongoCertPath = path.join(__dirname, './ca-certificate.crt');

  if (process.env.DB_CA_CERT) {
    fs.writeFileSync(mongoCertPath, process.env.DB_CA_CERT);
  }

  mongoSSLOptions.ssl = true;
  // Replace deprecated SSL options with new TLS options
  mongoSSLOptions.tlsAllowInvalidCertificates = false; // was sslValidate: true
  mongoSSLOptions.tlsCAFile = mongoCertPath; // was sslCA
}

let dbUrl = process.env.DATABASE;

// if PLATFORM_RELATIONSHIPS is set, parse it to get the MongoDB connection credentials
if (process.env.PLATFORM_RELATIONSHIPS) {
  try {
    const dbCredentials = JSON.parse(
      Buffer.from(process.env.PLATFORM_RELATIONSHIPS, 'base64')
    ).mongodb[0];
    dbUrl = `mongodb://${dbCredentials.username}:${dbCredentials.password}@${dbCredentials.host}:${dbCredentials.port}/${dbCredentials.path}`;
  } catch (error) {
    logError('Error parsing PLATFORM_RELATIONSHIPS:', error);
  }
}

/**
 * Log and send an email notification for DB connection errors
 * @param {Error} err The error object
 * @returns {void}
 */
function sendDBConnectionError(err) {
  logError('DB connection error!', err);

  notifyAdmins({
    subject: 'API - DB connection error!',
    templateName: 'requestError',
    templateValues: {
      status: 500,
      data: {
        'Date': toLogDate(new Date()),
        'DB Connection Error': JSON.stringify(err),
      },
    },
  });
}

/**
 * Gracefully shutdown the server
 * @param {Object} options Options for shutdown
 * @param {string} [options.message] Optional message to log
 * @param {Object} options.server The Express server instance
 * @param {Error} [options.error] Optional error to log
 * @returns {void}
 */
function shutdown({ message = '', server, error }) {
  // Stop all search index tasks before shutting down
  searchIndexTaskService
    .stopAllSearchIndexTasks()
    .then(() => {
      // If there's an error, log it and notify the admins
      if (error) {
        if (message) logError('Message:', message);
        logError('Error:', error.name, error.message);
        logError(error.stack);
        logError('💥 Shutting down...');

        notifyAdmins({
          subject: `API - ${message}`,
          templateName: 'requestError',
          templateValues: {
            status: 500,
            data: {
              'Date': toLogDate(new Date()),
              'Error Name': error.name,
              'Error Message': error.message,
              'Error Stack': error.stack,
              'Error': error,
            },
          },
        });
      } else {
        // Otherwise, log the messages
        if (message) logInfo(message);
        logInfo('👋 Shutting down gracefully...');
      }

      // Close the server and the DB connection
      server.close(async () => {
        await mongoose.connection.close();
        logInfo('💥 Process terminated');
        // eslint-disable-next-line n/no-process-exit
        process.exit(error ? 1 : 0);
      });
    })
    .catch((e) => {
      logError('Error during shutdown:', e);
      // eslint-disable-next-line n/no-process-exit
      process.exit(1);
    });
}

// Connect to the MongoDB database
// - Note: the express app is not started until the DB connection is successful. And if the DB connection fails or errors out, the app will not start or will shut down gracefully.
logInfo('Connecting to DB...');
mongoose
  .connect(dbUrl, mongoSSLOptions)
  .then(async () => {
    logSuccess('DB connection successful!');

    const port = process.env.PORT || 3000;

    // Initialize feature flags
    await featureFlagService.initializeFeatureFlags();

    // Start Express app
    const server = app.listen(port, (error) => {
      if (error) {
        throw error; // e.g. EADDRINUSE
      }
      logInfo(`App running on port ${port}...`);
    });

    // Start socket.io server and attach it to the Express app
    // TODO: We should start using this for notifications and live updates.
    initSocket(server, app);

    // Register scheduled tasks
    await registerTasks();

    process.on('uncaughtException', (error) => {
      shutdown({ message: 'UNCAUGHT EXCEPTION!', error, server });
    });

    process.on('unhandledRejection', (error) => {
      shutdown({ message: 'UNHANDLED REJECTION!', error, server });
    });

    // Soft kill triggered by the OS
    process.on('SIGTERM', () =>
      shutdown({ message: 'SIGTERM RECEIVED.', server })
    );

    // Usually triggered by control-c
    process.on('SIGINT', () =>
      shutdown({ message: 'SIGINT RECEIVED.', server })
    );
  })
  .catch((err) => sendDBConnectionError(err));

// Handle MongoDB connection errors
mongoose.connection.on('error', (err) => sendDBConnectionError(err));

// Handle MongoDB disconnection
mongoose.connection.on('disconnected', () => {
  logWarning('DB disconnected');
});
