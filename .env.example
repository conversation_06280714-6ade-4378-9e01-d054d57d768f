NODE_ENV=development
PORT=3999

DATABASE=
DB_CERT_REQUIRED=

RUN_CRON_JOBS=

JWT_SECRET=
JWT_EXPIRES_IN=

#EMAIL_TEST=
EMAIL_USER=
EMAIL_PASS=
EMAIL_HOST=
EMAIL_PORT=

EMAIL_ADDRESS=

DEFAULT_LOGO_URL=

# Backend URL to the User Password Reset page
PASSWORD_RESET_PATH=auth/reset-password

SYS_TOKEN=

# Generic encryption
ENCRYPTION_SECRET_KEY=

# Storage
IMAGES_CDN=
FILES_CDN=
BUCKET_ENDPOINT=
BUCKET_AVATARS=
BUCKET_IMAGES=
BUCKET_DOCUMENTS=
BUCKET_SECRET=
BUCKET_KEY_ID=
ZAFIR_DOCUMENTS=
ZAFIR_FONTS=
TEMP_FOLDER=

# Files, in readable format (i.e. 10mb)
MAX_AVATAR_FILE_SIZE='5mb'
MAX_IMAGE_FILE_SIZE='15mb'
MAX_DOCUMENT_FILE_SIZE='20mb'

# JSON payload size
JSON_PAYLOAD_SIZE='1mb'

# YouTube Data API v3
YOUTUBE_API_KEY=

# Vimeo API
VIMEO_CLIENT_ID=
VIMEO_CLIENT_SECRET=
VIMEO_ACCESS_TOKEN=

# Jetstream GraphQL API
JETSTREAM_GRAPHQL_API_URL=
JETSTREAM_GRAPHQL_ACCESS_TOKEN=

# Jetstream GraphQL API (staging)
JETSTREAM_GRAPHQL_API_URL_STAGING=
JETSTREAM_GRAPHQL_ACCESS_TOKEN_STAGING=

# Jetstream Analytics GraphQL API
JETSTREAM_ANALYTICS_GRAPHQL_API_URL=
JETSTREAM_ANALYTICS_GRAPHQL_ACCESS_TOKEN=

# waveware import
WAVEWARE_API_URL=
WAVEWARE_API_SECRET=

# churchfinder import
CHURCHFINDER_API_URL=
CHURCHFINDER_API_SECRET=

# courses import
COURSES_IMPORT_API_URL=
COURSES_IMPORT_USERNAME=
COURSES_IMPORT_PASSWORD=
COURSES_IMPORT_CLIENT_TOKEN=
COURSES_IMPORT_ORIGIN=

# OrgMast API import
ORGMAST_API_URL=

# ACMS Import
ACMS_API_URL=
ACMS_API_KEY=

# Telegram bot
BOT_TOKEN=
CHAT_ID=

# Hosting domain registration
REGISTER_DOMAINS=
DEFAULT_HOSTING_PROVIDER=

# Enable/disable cron jobs: 'yes' or 'no-way-jose'
RUN_CRON_JOBS=no-way-jose


# Scalingo
SCALINGO_API_TOKEN=

# Vercel
VERCEL_API_TOKEN=
VERCEL_TEAM_ID=

# DigitalOcean
DIGITALOCEAN_API_TOKEN=

# Names of the frontend apps. Add new ones here to allow them to be selected in the backend when creating/updating sites. Separate them with a space.
# Variables can be just the design when on scalingo (e.g. "adventist adventisten" because there are no regions in scalingo) or the design and the region (e.g. "adventist-eu-central hope-eu-central")
FRONTEND_APPS=

# Dns hosting (Cloudflare for now)
DNS_REGISTER=
DNS_PROVIDER=
DNS_PROVIDER_TOKEN=

# Task registration (boolean)
TASK_REGISTRATION=true

# FTP config for schedule import
FTP_HOST=
FTP_USER=
FTP_PASS=