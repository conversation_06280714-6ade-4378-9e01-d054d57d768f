# This workflow will do a clean installation of node dependencies, cache/restore them, run tests and linting
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: <PERSON><PERSON> and test

on:
  push:
    branches: ['main', 'staging']
  pull_request:
    branches: ['main', 'staging']

jobs:
  lint-and-test:
    # Skip push events that are part of a pull request
    if: github.event_name == 'pull_request' || (github.event_name == 'push' && github.event.pull_request == null)
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [22.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - run: npm install
      - run: npm run lint
      - run: npm run test
