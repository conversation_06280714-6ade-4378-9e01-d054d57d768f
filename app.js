import compression from 'compression';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import express from 'express';
import mongoSanitize from '@exortek/express-mongo-sanitize';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import logger from 'morgan';
import path from 'path';
import url from 'url';

import globalErrorHandler from '#modules/errors/controllers/errorController.js';
import authController from '#modules/users/controllers/authController.js';

import { errors } from '#utils/appError.js';
import reqlogger from '#utils/reqlogger.js';

// Modules
import ai from '#modules/ai/register.js';
import articles from '#modules/articles/register.js';
import lexicon from '#modules/lexicon/register.js';
import categories from '#modules/categories/register.js';
import clients from '#modules/clients/register.js';
import countries from '#modules/countries/register.js';
import courses from '#modules/courses/register.js';
import dataImport from '#modules/data-import/register.js';
import documents from '#modules/documents/register.js';
import entities from '#modules/entities/register.js';
import events from '#modules/events/register.js';
import featureFlags from '#modules/feature-flags/register.js';
import groups from '#modules/groups/register.js';
import images from '#modules/images/register.js';
import instances from '#modules/instances/register.js';
import languages from '#modules/languages/register.js';
import logs from '#modules/logs/register.js';
import mediaLibrary from '#modules/media-library/register.js';
import payments from '#modules/payments/register.js';
import persons from '#modules/persons/register.js';
import publications from '#modules/publications/register.js';
import search from '#modules/search/register.js';
import sermons from '#modules/sermons/register.js';
import tasks from '#modules/tasks/register.js';
import translations from '#modules/translations/register.js';
import users from '#modules/users/register.js';
import web from '#modules/web/register.js';
import webAuth from '#modules/web-auth/register.js';

import registerModules from '#modules/registerModules.js';

const __dirname = url.fileURLToPath(new URL('.', import.meta.url));

const app = express();

const JSON_PAYLOAD_SIZE = process.env.JSON_PAYLOAD_SIZE || '1mb';

// GLOBAL MIDDLEWARE

// CORS
app.use(cors());
app.options('*path', cors()); // For PATCH/DELETE methods

// Serving static files
app.use(express.static(path.join(__dirname, 'public')));

// Query parser
app.set('query parser', 'extended');

// Body parser, reading data from body into req.body: Extends payload limits
app.use(express.json({ limit: JSON_PAYLOAD_SIZE }));
app.use(express.urlencoded({ limit: JSON_PAYLOAD_SIZE, extended: true })); // Fede has extended: false

// Set security HTTP Headers
app.use(helmet({ contentSecurityPolicy: false }));

// Log
app.use(logger(reqlogger));

// Cookie parser
app.use(cookieParser());

// Data sanitization against NoSQL query injection
app.use(mongoSanitize());

// Compression
app.use(compression());

// https://express-rate-limit.mintlify.app/guides/troubleshooting-proxy-issues
app.set('trust proxy', 1 /* number of proxies between user and server */);

// Heartbeat route for the API's health checks
app.get(
  '/v1/heartbeat',
  // Rate limit to 1 request per 10 seconds
  rateLimit({
    windowMs: 10 * 1000, // 10 seconds window for each request
    max: 1, // Start blocking after 1 requests
  }),
  (req, res) => res.status(200).json({ status: 'ok' })
);

// Load Entity and User
app.use(authController.loadCurrentEntity);
app.use(authController.loadUser);

// Register modules
registerModules(app, [
  ai,
  articles,
  lexicon,
  categories,
  clients,
  countries,
  courses,
  dataImport,
  documents,
  entities,
  events,
  featureFlags,
  groups,
  images,
  instances,
  languages,
  logs,
  mediaLibrary,
  payments,
  persons,
  publications,
  search,
  sermons,
  tasks,
  translations,
  users,
  web,
  webAuth,
]);

// Middleware for any non-existing route
app.all('*splat', (req, res, next) => {
  next(errors.not_found());
});

// Global error handler
app.use(globalErrorHandler);

export default app;
