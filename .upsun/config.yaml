# Complete list of all available properties: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html
applications:
  awe-core-api:
    # Application source code directory
    source:
      root: '/'

    # The runtime the application uses.
    # Complete list of available runtimes: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#types
    type: 'nodejs:20'

    # The relationships of the application with services or other applications.
    # The left-hand side is the name of the relationship as it will be exposed
    # to the application in the PLATFORM_RELATIONSHIPS variable. The right-hand
    # side is in the form `<service name>:<endpoint name>`.
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#relationships

    relationships:
      mongodb:

    # Mounts define directories that are writable after the build is complete.
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#mounts
    mounts:
      'temp':
        source: instance
        source_path: temp

    # The web key configures the web server running in front of your app.
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#web
    web:
      # Commands are run once after deployment to start the application process.
      # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#web-commands
      commands:
        # The command to launch your app. If it terminates, it’s restarted immediately.
        # You can use the $PORT or the $SOCKET environment variable depending on the socket family of your upstream
        start: 'npm run start'
      # You can listen to a UNIX socket (unix) or a TCP port (tcp, default).
      # Whether your app should speak to the webserver via TCP or Unix socket. Defaults to tcp
      # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#where-to-listen
      upstream:
        # Whether your app should speak to the webserver via TCP or Unix socket. Defaults to tcp
        # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#where-to-listen
        socket_family: tcp
      # Each key in locations is a path on your site with a leading /.
      # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#locations
      locations:
        '/':
          passthru: true

    # Alternate copies of the application to run as background processes.
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#workers
    # workers:

    # The timezone for crons to run. Format: a TZ database name. Defaults to UTC, which is the timezone used for all logs
    # no matter the value here. More information: https://docs.upsun.com/create-apps/timezone.html
    # timezone: <time-zone>

    # Access control for roles accessing app environments.
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#access
    # access:

    # Variables to control the environment. More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#variables
    # variables:
    #   env:
    #     # Add environment variables here that are static.
    #     NODE_ENV: development

    # Outbound firewall rules for the application. More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#firewall
    # firewall:

    # Specifies a default set of build tasks to run. Flavors are language-specific.
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#build
    build:
      flavor: none

    # Installs global dependencies as part of the build process. They’re independent of your app’s dependencies and
    # are available in the PATH during the build process and in the runtime environment. They’re installed before
    # the build hook runs using a package manager for the language.
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#dependencies
    # dependencies:
    #   nodejs: # Specify one NPM package per line.
    #     pm2: '^4.5.0'

    # Hooks allow you to customize your code/environment as the project moves through the build and deploy stages
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#hooks
    hooks:
      # The build hook is run after any build flavor.
      # More information: https://docs.upsun.com/create-apps/hooks/hooks-comparison.html#build-hook
      build: |
        set -eux
        npm ci

      # The deploy hook is run after the app container has been started, but before it has started accepting requests.
      # More information: https://docs.upsun.com/create-apps/hooks/hooks-comparison.html#deploy-hook
      # deploy: |
      #   set -eux
      #   # echo 'Put your deploy command here'

      # The post_deploy hook is run after the app container has been started and after it has started accepting requests.
      # More information: https://docs.upsun.com/create-apps/hooks/hooks-comparison.html#deploy-hook
      # post_deploy: |

    # Scheduled tasks for the app.
    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#crons
    # crons:

    # Customizations to your PHP or Lisp runtime. More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#runtime
    # runtime:

    # More information: https://docs.upsun.com/create-apps/app-reference/single-runtime-image.html#additional-hosts
    # additional_hosts:

# The services of the project.
#
# Each service listed will be deployed
# to power your Upsun project.
# More information: https://docs.upsun.com/add-services.html
# Full list of available services: https://docs.upsun.com/add-services.html#available-services
services:
  mongodb:
    type: mongodb-enterprise:7.0

# The routes of the project.
#
# Each route describes how an incoming URL is going
# to be processed by Upsun.
# More information: https://docs.upsun.com/define-routes.html
routes:
  'https://{default}/':
    type: upstream
    upstream: 'awe-core-api:http'
    tls:
      #min_version: TLSv1.3
      strict_transport_security:
        enabled: true
        include_subdomains: true
        preload: true
