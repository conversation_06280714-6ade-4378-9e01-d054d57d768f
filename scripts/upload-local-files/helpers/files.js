import fs from 'fs';
import { logError, logInfo } from '#utils/logger.js';
import { sanitizeForContentDisposition, uploadFile } from '#utils/storage.js';
import mimeTypes from 'mime-types';
import path from 'path';

/**
 * Load the files map from the cache Folder
 * @param {Object} params The parameters object
 * @param {String} params.tempFolder The folder that contains the files cache file
 * @returns {ModxImportImageMap} The files map
 */
export function loadFilesMap({ tempFolder }) {
  const filesCacheFile = `${tempFolder}/files.json`;

  // Read the files cache file if it exists
  if (fs.existsSync(filesCacheFile)) {
    try {
      const filesCache = fs.readFileSync(filesCacheFile, 'utf8');
      return JSON.parse(filesCache);
    } catch (error) {
      logError('Failed to read files cache file', error);
    }
  }

  return {};
}

/**
 * Save the files in the cache folder, upload them to the CDN and update the files map with file objects.
 * @param {Object} params The parameters object
 * @param {Object} params.filesMap The map to save the data of the files (key: URL, value: file object)
 * @param {String} params.tempFolder The folder to save the files
 * @param {String} params.entityId The entity id to save the files to
 * @param {Boolean} params.dryRun Whether to skip saving the files or not
 * @param {Object} params.customS3Client The custom S3 client to use for uploading the files
 * @param {String} params.bucket The bucket to save the files to
 * @param {Boolean} params.verbose Whether to log the progress or not
 * @returns {Promise<void>}
 */
export async function saveFiles({
  filesMap,
  tempFolder,
  dryRun = false,
  customS3Client = null,
  bucket = 'hope-documents',
  verbose = false,
  entityId,
}) {
  const filesWithErrorsMap = {};

  if (!tempFolder) {
    logError('Missing tempFolder to save files');
    return { error: 'Missing tempFolder to save files' };
  }

  // Calculate the total files to save
  let totalFiles = Object.keys(filesMap || {}).length;

  // If there are no files to save, return an error
  if (totalFiles === 0) {
    logInfo('No files to save');
    return { error: 'No files to save' };
  }

  // Create the temp folder if it doesn't exist yet
  if (!fs.existsSync(tempFolder)) {
    fs.mkdirSync(tempFolder, { recursive: true });
  }

  // The path to save the files cache file
  const filesCacheFile = `${tempFolder}/files.json`;

  // The path to save the files cache file
  const tempFilesFolder = `${tempFolder}/files`;

  // Create the files folder if it doesn't exist yet
  if (!fs.existsSync(tempFilesFolder)) {
    fs.mkdirSync(tempFilesFolder, { recursive: true });
  }

  logInfo(`Saving ${totalFiles} files to ${tempFilesFolder}`);

  fs.writeFileSync(filesCacheFile, JSON.stringify(filesMap, null, 2));

  // Avoid saving the files if it's a dry run
  if (dryRun) {
    logInfo(`- Dry run: Skipping saving files.`);
  } else {
    // 1. Upload the files to the S3 bucket
    const fileKeys = Object.keys(filesMap);
    const batchSize = 10;

    for (let i = 0; i < fileKeys.length; i += batchSize) {
      const batchFiles = fileKeys.slice(i, i + batchSize);
      const batchPromises = batchFiles.map(async (key) => {
        // If the file is already saved (cached), skip it
        if (filesMap[key]?.file) {
          logInfo(`File already saved: ${key}`);
          return;
        }

        let fileFile = null;
        let fileError = null;

        try {
          // Save the file to the CDN from the filesystem
          // Files should be in the folder ./input/files
          const originalName = sanitizeForContentDisposition(
            filesMap[key]?.filename
          );

          // Try to get the mime-type from the file name
          const fileExtension = originalName.split('.').pop();
          const fileMime = mimeTypes.lookup(fileExtension);

          if (!fileMime) {
            logError(
              'Failed to get the mime type of the file',
              key,
              originalName
            );
          }

          const __dirname = path.resolve();
          const filePath = `${__dirname}/input/files/${filesMap[key]?.filename}`;

          // Check if the file exists
          const fileExists = fs.existsSync(filePath);

          const fileSize = fs.statSync(filePath).size;

          if (!fileExists) {
            logError('File not found', originalName, 'in', filePath);
            fileError = 'File not found';
          } else {
            const uploadInfo = await uploadFile(filePath, {
              customS3Client,
              bucket,
              acl: 'private',
              key: entityId,
              mime: fileMime,
              contentDisposition: `attachment; filename="${originalName}"`,
            });

            // Build the file object
            const remoteName = uploadInfo.Key.split('/').pop();
            const file = {
              containerId: entityId,
              extension: fileExtension,
              name: remoteName,
              acl: 'private',
              originalFilename: originalName
                .substring(0, originalName.lastIndexOf('.'))
                .trim(),
              size: fileSize,
              mime: fileMime,
            };

            logInfo('Uploaded file to DO Spaces', originalName);

            fileFile = file;
          }
        } catch (error) {
          fileError = error;
        }

        // If there is an error or the file is missing, log it
        if (fileError || !fileFile) {
          if (verbose) {
            logError(`Failed to save file ${key}`, {
              error: fileError,
              file: fileFile,
            });
          }

          const { filename } = filesMap[key];

          // Add the file to the files with errors map
          filesWithErrorsMap[key] = {
            error: fileError,
            filename,
          };
        } else {
          // Update the files map with the file object
          filesMap[key] = { ...filesMap[key], file: fileFile };
        }
      });

      await Promise.all(batchPromises);
    }
  }

  totalFiles = Object.keys(filesMap || {}).length;

  // 3. Save the files map to the cache folder as a JSON file for later use as index
  try {
    fs.writeFileSync(filesCacheFile, JSON.stringify(filesMap, null, 2));

    logInfo(`Files map saved to ${filesCacheFile}`);

    // 4. Log the files with errors if there are any
    const errorsCacheFile = `${tempFolder}/files-with-error.json`;

    // Save the files with errors to a separate file
    if (Object.keys(filesWithErrorsMap).length > 0) {
      fs.writeFileSync(
        errorsCacheFile,
        JSON.stringify(filesWithErrorsMap, null, 2)
      );

      logInfo(`Files with errors map saved to ${errorsCacheFile}`);
    }
  } catch (error) {
    logError('Failed to save files', error);
  }
}
