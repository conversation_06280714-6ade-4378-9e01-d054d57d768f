## Usage

```bash
cd scripts/upload-local-files
node uploadFiles.js
```

Remember to set the .env

## Obtaining the pages to be updated

Export from mongodb compass using a match like this:

```javascript
db.getCollection('pages').find(
  {
    _id: {
      $in: [
        ObjectId('679c99088487ab77d3d2c8d9'),
        ObjectId('679c991d8487ab77d3d2cad6'),
        ObjectId('6673fb2d04a3c2b7bfa6a6ae'),
        ObjectId('6673ff4904a3c2b7bfa73970'),
        ObjectId('6673fb5904a3c2b7bfa6abfe'),
        ObjectId('6673fb6604a3c2b7bfa6add3'),
        ObjectId('6673fb7304a3c2b7bfa6afb0'),
        ObjectId('6673fb9004a3c2b7bfa6b382'),
        ObjectId('6673fd7b04a3c2b7bfa6f3ed'),
        ObjectId('6673fe0a04a3c2b7bfa701eb'),
        ObjectId('6673fe1c04a3c2b7bfa7056a'),
        ObjectId('6673feab04a3c2b7bfa71bbf'),
        ObjectId('6673fe8504a3c2b7bfa71553'),
        ObjectId('6673fb3b04a3c2b7bfa6a86b'),
        ObjectId('6673ff7404a3c2b7bfa73fd7'),
        ObjectId('6673fb9e04a3c2b7bfa6b577'),
        ObjectId('679b97ba8487ab77d3cfdf07'),
        ObjectId('6673fbc304a3c2b7bfa6bb86'),
        ObjectId('6673fc6b04a3c2b7bfa6cf93'),
        ObjectId('6673fc5504a3c2b7bfa6c98d'),
        ObjectId('6673fcec04a3c2b7bfa6e2bf')
      ]
    }
  },
  { id: 1, content: 1, title: 1 }
);
```

## Updating the pages with the generated content

Get the content of each page generated in the pages.json file and use mongodb compass to replace the page content.
Its recommended to remove escape characters from the content before pasting it in the compass editor.
