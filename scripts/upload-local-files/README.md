# Migration scripts

Scripts here can be used to migrate data from other systems into AWE.

## Usage

```bash
cd scripts/migrations
node <script-name>.js
```

## Env variables

All environment variables should be set before running the script. Look at env.example for the required variables.
A script can use a .env file in the migrations directory if it shares the same environment variables with other scripts. If not, it should have its own .env file in the script's directory. (Remember to point to the .env path in the script)
