import omit from 'lodash/omit.js';
import fs from 'fs';
import path from 'path';

import { newMongoDBUUid, toJsonObjectId } from '#utils/api/mongoose/id.js';
import { toMongoDateObject } from '#utils/dates.js';
import { saveToJsonFile } from '#utils/json.js';
import { logError, logInfo } from '#utils/logger.js';
import { slugify } from '#utils/strings.js';

import { connectAuthors } from './authors.js';
import { getImagesFormTiptapNodes } from './images.js';
import { sanitize } from './strings.js';
import convertToTiptap from './tiptap/convertToTiptap.js';
import { adjustBibleReferences } from './bibleReferences.js';
import { convertTimestamp } from './dates.js';

/**
 * @typedef {import('#modules/persons/models/Person.js').PersonBioRole} PersonBioRole A PersonBioRole object
 */

/**
 * Parse an article object from MODX to AWE's Article schema
 * @param {Object} params The parameters object
 * @param {String} params.articleId The article id to use (generated)
 * @param {Object} params.rawArticle The raw article object from MODX
 * @param {Object} params.imagesMap The images map to save the assets
 * @param {Object} params.authorsMap The authors map to save the authors
 * @param {Object} params.debugCollectorMap The debug collector map to save the debug information
 * @param {Object} params.seriesMap The series map to save the series information
 * @param {import('../migrate.js').CategoriesMap} params.categoriesMap The categories map to reference the categories
 * @returns {Promise<Object>} The parsed article object
 */
export async function parseArticle({
  articleId,
  rawArticle,
  imagesMap,
  categoriesMap,
  localCategoriesMap,
  importType,
  entityId,
  authorsMap,
  debugCollectorMap,
  seriesMap,
  pdfFilesMap,
}) {
  const {
    id,
    lang,
    title,
    subtitle,
    abstract,
    bibleReferences,
    authors,
    categories: importedFlags,
    status, // 1 for ok, 2 for parent of multiple articles, 3 for failed to correctly import and needs manual fixes.
    content, // Equivalent to body
    topics, // TODO: Find out what to do with this
    binder, // The series
    publishedDate,
  } = rawArticle;

  const articleDate =
    publishedDate || convertTimestamp(1000 * id.split('|')[1]); // The timestamp is the second part of the id, or the publishedDate if it exists.

  if (!content) {
    logError(`Article with id ${id} has no content. Skipping...`);
    // Add the article to the debug collector map
    debugCollectorMap.articles[id] = {
      type: 'article',
      status: 'error',
      message: 'Article has no content',
      articleId,
    };
    return;
  }

  if (!title && status === 1) {
    logError(`Article with id ${id} has no title. Skipping...`);
    // Add the article to the debug collector map
    debugCollectorMap.articles[id] = {
      type: 'article',
      status: 'error',
      message: 'Article has no title',
      articleId,
    };
    return;
  }

  // Parse the content to Tiptap format (JSON)
  const body = content
    ? convertToTiptap({
        html: content,
        debugId: `article-body-${id}`,
        debugCollectorMap,
      })
    : undefined;

  if (!body || body?.content?.length === 0) {
    logError(`Article "${title}" (ID: ${id}) has no body. Skipping...`);
    // Ensure debugCollectorMap.articles is initialized
    if (!debugCollectorMap.articles) {
      debugCollectorMap.articles = {};
    }

    // Add the article to the debug collector map
    debugCollectorMap.articles[id] = {
      type: 'article',
      status: 'error',
      message: 'Article has no body',
      articleId,
    };
    return;
  }

  if (body?.content && imagesMap) {
    getImagesFormTiptapNodes({
      nodes: body.content,
      imagesMap,
      debugId: `article-${id}-body`,
    });
  }

  const sanitizedTitle =
    status === 3 ? `Revision needed (${id}) ` : sanitize(title);
  const slug = sanitize(slugify(sanitizedTitle)); // Use the uri or the slugified title
  const sanitizedSubtitle = sanitize(subtitle) || undefined;
  const sanitizedAbstract = sanitize(abstract) || undefined;

  // NOTE: Topics are AWE categories, the imported categories are AWE flags.
  const articleCategories = []; // Initialize the article categories array
  // Also add the topics to the article categories
  if (topics?.length > 0) {
    topics.forEach((topicName) => {
      if (categoriesMap[topicName]) {
        // If the topic exists in the map, use its ID
        articleCategories.push(categoriesMap[topicName]);
      } else {
        // If the topic does not exist, generate a new ID and add it to the map
        const newTopicId = newMongoDBUUid(); // Generate a new MongoDB ObjectId
        categoriesMap[topicName] = newTopicId;
        articleCategories.push(toJsonObjectId(newTopicId));
        localCategoriesMap.push({
          _id: toJsonObjectId(newTopicId),
          title: { en: topicName },
          name: slugify(topicName),
          type: 'entity',
          entity: toJsonObjectId(entityId),
          enabled: true,
          deleted: false,
          createdAt: toMongoDateObject(),
          updatedAt: toMongoDateObject(),
          importIDs: [{ type: importType }], // No record ID, this is meant to be run once, and at most deleted and ran again.
        });
      }
    });
  }

  // Handle the author if it exists. Adds the bios to the authorsMap, returns the authorId and bioId uuids that will be saved to the db.
  // Authors field looks like [{ person: ObjectId('1231413ads1234123'), bio: ObjectId('1231413ads1234123') }]
  const authorAndBioIds = connectAuthors(authors, authorsMap, articleDate, id);

  // Handle series

  const series = binder ? seriesMap[binder] : undefined;

  if (series) {
    // push to the seriesMap the article id
    seriesMap[binder].articles.push(toJsonObjectId(articleId));
  }

  // Adjusted bible references to the new format (book:’’, chapter: {number:[verses]}
  const articleBibleReferences = adjustBibleReferences(bibleReferences, id);

  // Handle pdf files, by adding them if they exist to the pdfFilesMap
  const pdfFilePath = `${id.split('|').slice(0, 2).join('|')}.pdf`; // The pdf file is made from the first and second parts of the id split by '|', or just the first part if only one '|' is present, and the pdf extension. e.g "123|456|1.pdf" the pdf file will be "123|456.pdf"
  const pdfFile = fs.existsSync(path.join('./pdfFiles', pdfFilePath)); // Check if the file exists

  if (pdfFile && !pdfFilesMap[pdfFilePath]) {
    // If the file exists, add it to the pdfFilesMap
    pdfFilesMap[pdfFilePath] = {
      debugId: `article-${id}-pdf`,
      title: series ? series.name : sanitizedTitle,
      uploaded: false,
    };

    logInfo(`PDF file added for article ID: ${articleId}`);
  }

  let files = []; // Initialize the files array
  if (pdfFilesMap[pdfFilePath]?.file) {
    // If the file exists, add it to the article object
    files = [
      {
        id: 'bripdf',
        name: 'PDF',
        file: pdfFilesMap[pdfFilePath].file,
      },
    ];
  }

  // Create the article object, mapping the fields to match AWE's Article schema
  // Some fields are set when saving.
  const article = {
    id: articleId,
    title: sanitizedTitle, // For failed articles, set the title to "Needs revision (Draft)"
    slug: slug, // For failed articles, set the slug to undefined, when adding
    subtitle: sanitizedSubtitle,
    abstract: sanitizedAbstract,
    body,
    files,
    importedFlags,
    authors: authorAndBioIds,
    createdAt: articleDate,
    updatedAt: articleDate,
    publishedAt: articleDate,
    bibleReferences: articleBibleReferences,
    categories: articleCategories,
    status: status === 1 ? 'approved' : 'draft', // 1 for published, 3 for failed to import
    language: lang,
    enabled: true,
    deleted: false,
    type: 'article',
    series: series ? series._id : undefined, // Add the series id if it exists (Already in ObjectId)
    importId: id, // Add the imported entry id for reference
  };

  // If the article has an image, and it is not yet in the images map,
  if (article.image && !imagesMap[article.image]) {
    // Add it to the images map to be saved later
    imagesMap[article.image] = {
      debugId: `article-${id}-image`,
    };
  }

  return article;
}

/**
 * Save the articles to a file
 * @param {Object} params The parameters object
 * @param {Object} params.articlesMap The articles map to save
 * @param {Object} params.categoriesMap The categories map to reference the categories
 * @param {Object} params.articleSites The article sites array to save the articles to
 * @param {Object} params.imagesMap The images map to reference the images
 * @param {Object} params.organizationsMap The organizations map to reference the organizations
 * @param {Object} params.canonicalSitePage The canonical site page to reference the articles
 * @param {String} params.siteId The site id where to publish the articles
 * @param {Object} params.flagsMap The flags map to reference the article categories
 * @param {String} params.entityId The entity id to save the articles to
 * @param {String} params.outputFolder The folder path to save the articles to
 * @returns {void}
 */
export function saveArticles({
  articlesMap,
  imagesMap,
  organizationsMap,
  categoriesMap,
  articleSites,
  flagsMap,
  siteId,
  canonicalSitePage,
  entityId,
  importType, // The import type (modx, bri-articles)
  outputFolder,
}) {
  if (!Array.isArray(articleSites)) {
    logError('Missing articleSites array');
  }

  // Convert the articles map to an array
  const articles = Object.values(articlesMap).map((article) => {
    const {
      id,
      publishedAt,
      body,
      importedFlags,
      createdAt,
      updatedAt,
      importId,
      ...rest
    } = article;

    // Update the body content to include the images file objects
    const bodyContentWithImages = body?.content?.map((node) => {
      if (node.type === 'image') {
        const imageFile = imagesMap[node.attrs.url]?.file;

        if (imageFile) {
          return {
            ...node,
            attrs: {
              ...node.attrs,
              url: undefined, // Remove the src attribute
              file: imageFile, // Add the file object
            },
          };
        }
      }

      if (node.type === 'imageGallery') {
        const images = node.attrs.images.map((item) => {
          const imageFile = imagesMap[item.image]?.file;

          if (imageFile) {
            return {
              ...item,
              image: imageFile,
            };
          }

          return item;
        });

        return {
          ...node,
          attrs: {
            ...node.attrs,
            images,
          },
        };
      }

      return node;
    });

    // Obtain the categories which are AWE flags.
    // Categories in the BRI are equivalent to flags in AWE
    // Example: articleFlags: {'books': true}

    const articleFlags = {};
    if (importedFlags && importedFlags.length > 0) {
      importedFlags.forEach((categoryName) => {
        if (flagsMap[categoryName]) {
          const { flagId } = flagsMap[categoryName];
          // If the category exists in the map, use its ID and set the value to true
          articleFlags[flagId] = true;
        } else {
          // If the category does not exist, generate a new ID and add it to the flags
          const newFlagId = slugify(categoryName);
          articleFlags[newFlagId] = true;

          // Add the new flag to the flags map
          flagsMap[newFlagId] = {
            name: categoryName,
            id: newFlagId,
            disabled: false,
            importIDs: [{ type: importType }], // No record ID, this is meant to be run once, and at most deleted and ran again.
          };
        }
      });
    }

    // Add an entry of this article to the articleSites array for publishing to the target site
    articleSites.push({
      _id: toJsonObjectId(newMongoDBUUid()),
      article: toJsonObjectId(id),
      site: toJsonObjectId(siteId),
      startsAt: toMongoDateObject(publishedAt || createdAt), // Use the publishedAt date or the current date
      createdAt: toMongoDateObject(publishedAt || createdAt), // Use the publishedAt date or the current date
      updatedAt: toMongoDateObject(publishedAt || createdAt), // Use the publishedAt date or the current date
      importIDs: [{ type: importType, recordID: importId }], // Add the importId to the importIDs array for reference (This is not in the model, but it is useful for debugging)
      enabled: true,
      flags: articleFlags,
      deleted: false,
    });

    // Get the organization id from the organizations map based on the organization name
    const organizationId =
      organizationsMap?.[article.organization]?.id || undefined;

    // Set the categories array

    const canonicalSitePageId = canonicalSitePage || '';

    // Create the article object, mapping the fields to match AWE's Article schema
    return {
      // Spread the rest fields (title, body, ...) first to avoid overriding the following fields
      ...omit(rest, 'author', 'organization'), // ignore the author and organization fields (they are handled separately)

      // Generate a new article id (as ObjectId)
      _id: toJsonObjectId(id),

      // Set the body, including the images at the bottom
      body: {
        ...body,
        content: bodyContentWithImages,
      },

      // Set the article ownwership to the local entity id
      entity: entityId ? toJsonObjectId(entityId) : undefined,

      // Get the organization id from the organizations map based on the organization name
      organizations: organizationId
        ? [toJsonObjectId(organizationId)]
        : undefined,

      // Filter tags, by removing organizations and categories from tags
      tags:
        article.tags?.length > 0
          ? article.tags
              ?.filter((tag) => !organizationsMap[tag]) // Remove organizations from tags
              .filter((tag) => !categoriesMap[tag]) // Remove categories from tags
          : undefined,

      // Set dates of creation and update
      createdAt: toMongoDateObject(createdAt),
      updatedAt: toMongoDateObject(updatedAt),

      // Set the canonical site page id, based on the main category's name (brand)
      canonicalSitePage: toJsonObjectId(canonicalSitePageId),

      // Add the importId to the importIDs array for reference (If using another import
      importIDs: [{ type: importType, recordID: importId }],
    };
  });

  // Uncomment when ready
  saveToJsonFile({
    data: articles,
    fileName: 'articles',
    folder: outputFolder,
  });
}

export function handleSeries(rawSeries, seriesMap, importType, entityId) {
  for (const series of rawSeries) {
    const { id, title, abstract, lang, chapters } = series;

    if (!chapters || chapters.length === 0) {
      logInfo(`Series "${title}" has no chapters. Skipping...`);
      continue;
    }

    // The description will have to be saved as a Tiptap object (For which description will have to be wrapped in a <p> tag)
    const description = abstract
      ? convertToTiptap({
          html: `<p>${abstract}</p>`,
          debugId: `series-body-${id}`,
        })
      : undefined;
    // Create the series object
    const newSeries = {
      _id: toJsonObjectId(newMongoDBUUid()),
      enabled: true,
      deleted: false,
      createdAt: toMongoDateObject(),
      updatedAt: toMongoDateObject(),
      name: title,
      description,
      chapters, // Chapters will not be saved, just used to connect the articles to the series
      importIDs: [{ type: importType, recordID: id }],
      language: lang,
      entity: toJsonObjectId(entityId),
      articles: [], // Articles will be added as they are parsed for the articlesMap
    };

    seriesMap[id] = newSeries;
  }
}
