// List of MODX instances to import from
export const instances = [
  {
    label: 'Instance #1',
    name: 'instance-1',
    enabled: true,
    baseDomain: 'www.adventistmission.org',
    contexts: {
      portal: 'adventistmission.org',
      web: 'am.adventistmission.org',
      gm: 'gm.adventistmission.org',
      missionplus: 'missionplus.org',
    },
  },
  {
    label: 'Instance #2',
    name: 'instance-2',
    enabled: false, // Disabled until next stage
    baseDomain: 'globalmissioncenters.org',
    contexts: {
      'web': 'globalmissioncenters.org',
      'cear': 'cabr.globalmissioncenters.org',
      'csar': 'csar.globalmissioncenters.org',
      'csar-es': 'csar-es.globalmissioncenters.org',
      'csar-pt': 'csar-pt.globalmissioncenters.org',
      'csps': 'csps.globalmissioncenters.org',
      'gcamr': 'camr.globalmissioncenters.org',
      'urban': 'urbanmissioncenters.org',
      'wjafc': 'cajr.globalmissioncenters.org',
    },
  },
  {
    label: 'Instance #3',
    name: 'instance-3',
    baseDomain: 'm360.tv',
    enabled: false, // Disabled because it doesn't have articles
    contexts: {
      m360: 'm360.tv', // ASK: Can we import something from this domain via the Modx API? or do we just import it directly from Zoho endpoints?
      city: 'iwantthiscity.org',
      web: 'adventistmission.tv',
    },
  },
  {
    label: 'Instance #4',
    name: 'instance-4',
    enabled: false, // Disabled until next stage
    baseDomain: 'urbancenters.org',
    contexts: {
      web: 'urbancenters.org',
    },
  },
  {
    label: 'Instance #5',
    name: 'instance-5',
    enabled: false, // Disabled until next stage
    baseDomain: 'missiontothecities.org',
    contexts: {
      web: 'missiontothecities.org',
    },
  },
];

export const enabledInstances = instances.filter(
  (instance) => instance.enabled
);
