export function sanitize(string = '') {
  if (typeof string !== 'string') return '';

  return string
    .trim() // Remove leading and trailing spaces
    .replaceAll(/​/g, '') // Remove zero-width spaces
    .replaceAll(/ /g, ' ') // Replace non-breaking spaces with regular spaces
    .replaceAll(/[\u2028\u2029]/g, '\n') // Replace line separators with new lines
    .replaceAll('\r\n', '\n'); // Replace Windows new lines with Unix new lines
}
