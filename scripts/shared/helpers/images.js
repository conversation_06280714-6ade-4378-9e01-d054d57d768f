import fs from 'fs';

import { logError, logInfo } from '#utils/logger.js';
import mimeTypes from 'mime-types';

import { uploadFile } from '#utils/storage.js';
import path, { extname } from 'path';
import { encodeImageToBlurhash, getImageSizeData } from '#utils/files.js';

/**
 * Looks for image nodes (like images) in a Tiptap document nodes
 * @param {Array} nodes The nodes array to look for assets
 * @param {Object} imagesMap The images map to save the assets
 * @param {String} domain The base/fallback domain of the images
 * @param {String} debugId The debug id to use for logging
 * @returns {Promise<void>}
 */
export function getImagesFormTiptapNodes({
  nodes = [],
  imagesMap = {},
  debugId = null,
}) {
  // Get the image nodes from the nodes array
  const imageNodes = nodes.filter((node) => node?.type === 'image');

  // For each image node, add it to the images map
  for (const node of imageNodes) {
    const { attrs } = node || {};

    // Skip the image if the URL is missing
    if (!attrs?.url) {
      continue;
    }

    // Fix the image URL
    const cleanedUrl = attrs?.url.replace(/^\//, ''); // Remove the leading slash e.g "/image.jpg" to "image.jpg"

    const url = cleanedUrl.startsWith('images/')
      ? cleanedUrl || ''
      : `images/${cleanedUrl || ''}`; // The url needs to have the images/ prefix to be found in the images folder

    // Check if the image is already in the images map
    const imageRecord = imagesMap[url];

    // If it is, skip it
    if (imageRecord) {
      continue;
    }

    // Otherwise add the image to the images map
    // NOTE: The value will be replaced with the image "file" object later
    imagesMap[url] = {
      debugId, // The debug id to use for logging possible errors with this image
      originalUrl: url, // This url is used to find the image in the images folder
    };

    // Do it recursively with its children
    if (Array.isArray(node.content)) {
      getImagesFormTiptapNodes({
        nodes: node.content,
        imagesMap,
        debugId,
      });
    }
  }
}
/**
 * Load the images map from the cache Folder
 * @param {Object} params The parameters object
 * @param {String} params.tempFolder The folder that contains the images cache file
 * @returns {ModxImportImageMap} The images map
 */
export function loadImagesMap({ tempFolder }) {
  const imagesCacheFile = `${tempFolder}/images.json`;

  // Read the images cache file if it exists
  if (fs.existsSync(imagesCacheFile)) {
    try {
      const imagesCache = fs.readFileSync(imagesCacheFile, 'utf8');
      return JSON.parse(imagesCache);
    } catch (error) {
      logError('Failed to read images cache file', error);
    }
  }

  return {};
}

/**
 * Save the images in the cache folder, upload them to the CDN and update the images map with file objects.
 * @param {Object} params The parameters object
 * @param {Object} params.imagesMap The map to save the data of the images (key: URL, value: file object)
 * @param {String} params.outputFolder The folder to save the images cache file
 * @param {String} params.entityId The entity id to save the images to
 * @param {Boolean} params.dryRun Whether to skip saving the images or not
 * @param {Object} params.customS3Client The custom S3 client to use for uploading the images
 * @param {String} params.bucket The bucket to save the images to
 * @param {Boolean} params.verbose Whether to log the progress or not
 * @returns {Promise<void>}
 */
export async function saveImages({
  imagesMap,
  outputFolder,
  entityId,
  dryRun = false,
  customS3Client = null,
  bucket = 'hope-images',
  verbose = false,
}) {
  const imagesWithErrorsMap = {};

  // Calculate the total images to save
  let totalImages = Object.keys(imagesMap || {}).length;

  // If there are no images to save, return an error
  if (totalImages === 0) {
    logInfo('No images to save');
    return { error: 'No images to save' };
  }

  if (!entityId) {
    logError('Missing entityId to save images');
    return { error: 'Missing entityId to save images' };
  }

  // The path to save the images cache file
  const imagesCacheFile = `${outputFolder}/images.json`;

  // Create the images folder if it doesn't exist yet
  if (!fs.existsSync(outputFolder)) {
    fs.mkdirSync(outputFolder, { recursive: true });
  }

  logInfo(`Saving ${totalImages} images to ${outputFolder}`);

  fs.writeFileSync(imagesCacheFile, JSON.stringify(imagesMap, null, 2));

  // Avoid saving the images if it's a dry run
  if (dryRun) {
    logInfo(`- Dry run: Skipping saving images.`);
  } else {
    // 1. Download the images to the cache folder and upload them to the CDN
    const imagePaths = Object.keys(imagesMap);
    const batchSize = 50;

    for (let i = 0; i < imagePaths.length; i += batchSize) {
      const batchUrls = imagePaths.slice(i, i + batchSize);
      const batchPromises = batchUrls.map(async (imagePath) => {
        // If the image is already saved (cached), skip it
        if (imagesMap[imagePath]?.file) {
          // logInfo(`Image ${imagePath} already saved`);
          // Skip the image if it's already saved
          return;
        }

        let imageFile = null;
        let imageError = null;

        try {
          // Get the file path from the images folder
          const filePath = path.join(`./${imagePath}`);

          if (!fs.existsSync(filePath)) {
            throw new Error(`File ${filePath} does not exist`);
          }

          // Image properties
          const { size } = fs.statSync(filePath);
          const { width, height } = await getImageSizeData(filePath);
          const blurhash = await encodeImageToBlurhash(filePath);

          // Get the mime type of the image
          const mimeType = mimeTypes.lookup(filePath);
          const mime = mimeType || 'application/octet-stream';

          // Save the image to the CDN
          const uploadInfo = await uploadFile(filePath, {
            bucket,
            key: entityId,
            customS3Client,
            mime,
          });

          const remoteName = uploadInfo.Key.split('/').pop();

          // Set the image file object (Same as the service and controllers generate)
          imageFile = {
            containerId: entityId,
            extension: extname(remoteName),
            mime,
            name: remoteName,
            size,
            originalFilename: path.basename(filePath, path.extname(filePath)),
            width,
            height,
            blurhash,
          };
        } catch (error) {
          // logError(error);
          imageError = error || 'Unknown error';
        }

        // If there is an error or the file is missing, log it
        if (imageError || !imageFile) {
          if (verbose) {
            logError(`Failed to save image ${imagePath}`, {
              error: imageError || 'Unknown error',
              file: imageFile,
            });
          }

          // Get the debugId from the images map for this image
          const { debugId } = imagesMap[imagePath];

          // Add the image to the images with errors map
          imagesWithErrorsMap[imagePath] = {
            error: imageError.message || imageError,
            debugId,
          };
        } else {
          // Update the images map with the file object (overrides the prev value including debugId with the file object as it is not required anymore)
          imagesMap[imagePath] = { file: imageFile };
        }
      });

      await Promise.all(batchPromises);
    }
  }

  totalImages = Object.keys(imagesMap || {}).length;

  // 3. Save the images map to the cache folder as a JSON file for later use as index
  try {
    fs.writeFileSync(imagesCacheFile, JSON.stringify(imagesMap, null, 2));

    logInfo(`Images map saved to ${imagesCacheFile}`);

    // 4. Log the images with errors if there are any
    const errorsCacheFile = `${outputFolder}/images-with-error.json`;

    // Save the images with errors to a separate file
    if (Object.keys(imagesWithErrorsMap).length > 0) {
      fs.writeFileSync(
        errorsCacheFile,
        JSON.stringify(imagesWithErrorsMap, null, 2)
      );

      logInfo(`Images with errors map saved to ${errorsCacheFile}`);
    }
  } catch (error) {
    logError('Failed to save images', error);
  }
  // 5. Log the total images saved
  logInfo(`Total images saved: ${totalImages}`);
  logInfo(
    `Total images with errors: ${Object.keys(imagesWithErrorsMap).length}`
  );
  // 6. Return the images map and the images with errors map
  return {
    imagesMap,
    imagesWithErrorsMap,
  };
}
