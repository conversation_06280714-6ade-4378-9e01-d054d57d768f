import fs from 'fs';
import { readFile } from 'fs/promises';

import Logger from '#utils/logger.js';

const defaultPagedResourcesParams = {
  page: 1,
  perPage: 100,
  baseDomain: 'www.adventistmission.org',
  context: 'web',
  type: 'modDocument',
};

/**
 * Get resources from the API
 * Read more: https://www.adventistmission.org/webengine/docs.html
 * @param {Object} options - The options object
 * @param {String} options.apiKey - The API key to use (required)
 * @param {String} options.baseDomain - The instance domain to get resources from (default: 'www.adventistmission.org')
 * @param {Number} options.page - The page number to get (default: 1)
 * @param {Number} options.perPage - The number of items per page (default: 100)
 * @param {String} options.context - The context to use (default: 'web'). Options: Instance 1: `web`, `portal`, ``, Instance 2: `web2`
 * @param {String} options.type - The type of resource to get (default: 'modDocument'). Options: `modDocument`, `modStaticResource`, or `modWeblink`
 * @returns {Promise<Object>} The response data
 */
export async function getModxPagedResources(
  options = defaultPagedResourcesParams
) {
  const { apiKey, baseDomain, page, perPage, context, type } = {
    ...defaultPagedResourcesParams,
    ...options,
  };

  if (!baseDomain) {
    throw new Error('getModxResources: Domain is required');
  }

  if (!apiKey) {
    throw new Error('getModxResources: API key is required');
  }

  const url = `https://${baseDomain}/webengine/resources.php`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        key: apiKey,
        type: type || 'modDocument',
        page: page || 1,
        perPage: perPage || 10,
        context: context || 'web',
      }),
    });

    const { totalResults, totalPages, currentPage, entries } =
      (await response.json()) ?? {};

    return {
      entries: entries ?? [],
      totalResults: totalResults ?? 0,
      totalPages: totalPages ?? 0,
      currentPage: currentPage ?? 0,
    };
  } catch (error) {
    return {
      error,
    };
  }
}

const defaultResourcesParams = {
  baseDomain: 'www.adventistmission.org',
  context: 'web',
  siteDomain: 'am.adventistmission.org',
  type: 'modDocument',
  useCache: false,
};

/**
 * Gets all resources from the API, paginating through all available pages and return the entries
 * Read more: https://www.adventistmission.org/webengine/docs.html
 * @param {Object} params The parameters object
 * @param {String} params.apiKey The API key to use (required)
 * @param {String} params.cacheFolder The cache folder to use for the resources (default: `./cache`)
 * @param {String} params.baseDomain The domain to get resources from (default: `www.adventistmission.org`)
 * @param {String} params.context The context to filter the content of the instance (default: `web`).
 * @param {String} params.siteDomain The site domain to get resources from (default: `am.adventistmission.org`)
 * @param {Boolean} params.useCache Whether to use the cache or not (default: false)
 * @param {String} params.type The type of resource to get (default: 'modDocument'). Options: `modDocument`, `modStaticResource`, or `modWeblink`
 * @returns {Promise<Array>} The response data
 */
export async function getAllModxResources(params = defaultResourcesParams) {
  const {
    apiKey,
    baseDomain,
    context,
    siteDomain,
    type,
    useCache,
    cacheFolder,
  } = {
    ...defaultResourcesParams,
    ...params,
  };

  const contextCacheFile = `${cacheFolder}/cache-${siteDomain}.json`;

  // Init the resources object and the raw entries array
  let resources = {}; // Will be updated with every page of resources fetched (totalResults, totalPages, currentPage, entries)
  let rawEntries = []; // The raw entries array to collect all the entries

  // In case we want to use the cache,
  if (useCache) {
    try {
      // Read resources from cache folder
      const cacheEntries = await readFile(contextCacheFile, 'utf8');

      // If there are cache entries there,
      if (cacheEntries) {
        // parse them to the rawEntries array
        rawEntries = JSON.parse(cacheEntries || '[]');
      }
    } catch {
      Logger.warning(
        `No cache file found for site "${siteDomain}". Fetch from the API...`
      );
    }
  }

  // Shared params for the resources requests
  const resourcesParams = {
    apiKey,
    domain: baseDomain,
    context,
    type,
  };

  // If we have entries in the cache, just return them
  if (rawEntries.length > 0) {
    Logger.info(`Using cache entries for ${siteDomain}.`);
    return rawEntries;
  }

  // Otherwise if at this point we still don't have any entries (i.e. the cache is empty or not used),
  // We read the api for the first time
  resources = await getModxPagedResources(resourcesParams);

  // If there's an error fetching the resources, log it and return an empty array
  if (resources.error) {
    Logger.error(
      `Error fetching resources for site "${siteDomain}"`,
      resources.error
    );
    return [];
  }

  // Set the entries from the first page
  rawEntries = resources.entries;

  // Loop through the pages until we reach the last one
  while (resources.currentPage < resources.totalPages) {
    resources = await getModxPagedResources({
      ...resourcesParams,
      page: resources.currentPage + 1, // Get the next page
    });

    // If there's an error fetching the resources, log it and break the loop
    if (resources.error) {
      Logger.error('Error fetching resources', resources.error);
      break;
    }

    // Add the new entries to the entries array
    rawEntries.push(...resources.entries);
  }

  if (useCache) {
    // Save/update the cache for the current domain with the rawEntries (articles)
    fs.writeFileSync(contextCacheFile, JSON.stringify(rawEntries, null, 2));
  }

  return rawEntries;
}
