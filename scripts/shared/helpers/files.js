import fs from 'fs';

import { logError, logInfo } from '#utils/logger.js';
import { uploadFile } from '#utils/storage.js';
import path, { extname } from 'path';

/**
 * Load the files map from the cache Folder
 * @param {Object} params The parameters object
 * @param {String} params.tempFolder The folder that contains the files cache file
 * @returns {ModxImportImageMap} The files map
 */
export function loadFilesMap({ tempFolder }) {
  const filesCacheFile = `${tempFolder}/files.json`;

  // Read the files cache file if it exists
  if (fs.existsSync(filesCacheFile)) {
    try {
      const filesCache = fs.readFileSync(filesCacheFile, 'utf8');
      return JSON.parse(filesCache);
    } catch (error) {
      logError('Failed to read files cache file', error);
    }
  }

  return {};
}

export async function saveFiles({
  filesMap,
  outputFolder,
  entityId,
  dryRun = false,
  customS3Client = null,
  bucket = 'hope-documents',
  verbose = false,
}) {
  const filesWithErrorsMap = {};

  // Calculate the total files to save
  let totalFiles = Object.keys(filesMap || {}).length;

  // If there are no files to save, return an error
  if (totalFiles === 0) {
    logInfo('No files to save');
    return { error: 'No files to save' };
  }

  if (!entityId) {
    logError('Missing entityId to save files');
    return { error: 'Missing entityId to save files' };
  }

  // The path to save the files cache file
  const filesCacheFile = `${outputFolder}/files.json`;

  // Create the files folder if it doesn't exist yet
  if (!fs.existsSync(outputFolder)) {
    fs.mkdirSync(outputFolder, { recursive: true });
  }

  logInfo(`Saving ${totalFiles} files to ${outputFolder}`);

  fs.writeFileSync(filesCacheFile, JSON.stringify(filesMap, null, 2));

  // Avoid saving the files if it's a dry run
  if (dryRun) {
    logInfo(`- Dry run: Skipping saving files.`);
  } else {
    // 1. Download the files to the cache folder and upload them to the CDN
    const filePaths = Object.keys(filesMap);
    const batchSize = 50;

    for (let i = 0; i < filePaths.length; i += batchSize) {
      const batchUrls = filePaths.slice(i, i + batchSize);
      const batchPromises = batchUrls.map(async (filePath) => {
        // If the file is already saved (cached), skip it
        if (filesMap[filePath]?.file) {
          // logInfo(`Image ${filePath} already saved`);
          // Skip the file if it's already saved
          return;
        }

        let fileFile = null;
        let fileError = null;

        try {
          // Get the file path from the pdf files folder
          const pdfPath = path.join('./pdfFiles', filePath);
          const originalName = filesMap[filePath]?.title;

          if (!fs.existsSync(pdfPath)) {
            throw new Error(`File ${pdfPath} does not exist`);
          }

          // Image properties
          const { size } = fs.statSync(pdfPath);
          // Get the mime type of the file

          const mime = 'application/pdf'; // Its only pdf

          const uriSafeName = encodeURIComponent(originalName);

          // Save the file to the CDN
          const uploadInfo = await uploadFile(pdfPath, {
            bucket,
            key: entityId,
            customS3Client,
            mime,
            contentDisposition: `attachment; filename="${uriSafeName}.pdf"`,
          });

          const remoteName = uploadInfo.Key.split('/').pop();

          // Set the file file object (Same as the service and controllers generate)
          // TODO: See if code
          fileFile = {
            containerId: entityId,
            extension: extname(remoteName),
            mime,
            name: remoteName,
            size,
            originalFilename:
              originalName || path.basename(pdfPath, path.extname(pdfPath)),
          };
        } catch (error) {
          // logError(error);
          fileError = error || 'Unknown error';
        }

        // If there is an error or the file is missing, log it
        if (fileError || !fileFile) {
          if (verbose) {
            logError(`Failed to save file ${filePath}`, {
              error: fileError || 'Unknown error',
              file: fileFile,
            });
          }

          // Get the debugId from the files map for this file
          const { debugId } = filesMap[filePath];

          // Add the file to the files with errors map
          filesWithErrorsMap[filePath] = {
            error: fileError.message || fileError,
            debugId,
          };
        } else {
          // Update the files map with the file object (overrides the prev value including debugId with the file object as it is not required anymore)
          filesMap[filePath] = { file: fileFile };
        }
      });

      await Promise.all(batchPromises);
    }
  }

  totalFiles = Object.keys(filesMap || {}).length;

  // 3. Save the files map to the cache folder as a JSON file for later use as index
  try {
    fs.writeFileSync(filesCacheFile, JSON.stringify(filesMap, null, 2));

    logInfo(`Files map saved to ${filesCacheFile}`);

    // 4. Log the files with errors if there are any
    const errorsCacheFile = `${outputFolder}/files-with-error.json`;

    // Save the files with errors to a separate file
    if (Object.keys(filesWithErrorsMap).length > 0) {
      fs.writeFileSync(
        errorsCacheFile,
        JSON.stringify(filesWithErrorsMap, null, 2)
      );

      logInfo(`Files with errors map saved to ${errorsCacheFile}`);
    }
  } catch (error) {
    logError('Failed to save files', error);
  }
  // 5. Log the total files saved
  logInfo(`Total files saved: ${totalFiles}`);
  logInfo(`Total files with errors: ${Object.keys(filesWithErrorsMap).length}`);
  // 6. Return the files map and the files with errors map
  return {
    filesMap,
    filesWithErrorsMap,
  };
}
