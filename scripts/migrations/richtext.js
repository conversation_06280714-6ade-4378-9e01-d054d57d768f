import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { logError, logInfo } from '#utils/logger.js';

const envPath = new URL('.env', import.meta.url);

dotenv.config({ path: envPath });

async function findAndReplaceDocumentsWithType({
  collectionName,
  fieldName,
  searchType,
  replaceType,
  replace = false, // Set to true to perform the replacement
}) {
  try {
    await mongoose.connect(process.env.DATABASE);
    logInfo('Connected to MongoDB');

    const collectionSchema = mongoose.connection.collection(collectionName);
    const collection = await collectionSchema.find({}).toArray();

    // count the number of articles
    const collectionCount = collection.length;
    logInfo('Number of items in the collection:', collectionCount);

    const results = collection
      .filter((doc) => {
        // Always resolve the field value using dot notation
        const fieldValue = fieldName
          .split('.')
          .reduce((obj, key) => obj?.[key], doc);

        if (typeof fieldValue === 'string') {
          // Match type":"footnote or type" : "footnote (with optional spaces)
          const regex = new RegExp(`type"\\s*:\\s*"${searchType}`, 'g');
          return fieldValue && regex.test(fieldValue);
        }

        // Recursively search for the type in objects/arrays
        const searchForType = (field) => {
          if (Array.isArray(field)) {
            return field.some(searchForType);
          }
          if (typeof field === 'object' && field !== null) {
            if (field.type === searchType) {
              return true;
            }
            return Object.values(field).some(searchForType);
          }
          return false;
        };
        return searchForType(fieldValue);
      })
      ?.map((doc) => {
        const fieldValue = fieldName
          .split('.')
          .reduce((obj, key) => obj?.[key], doc);
        if (typeof fieldValue === 'string') {
          return {
            id: doc._id,
            [fieldName]: fieldValue,
          };
        }
        return {
          id: doc._id,
          [fieldName]: fieldValue,
        };
      });
    // console.log('results', JSON.stringify(results, null, 2));
    logInfo(
      'Results:',
      results.map((result) => [result.id, typeof result[fieldName]])
    );
    logInfo(
      `Found ${results.length} document(s) containing the type "${searchType}" in the field "${fieldName}" in the collection "${collectionName}"`
    );

    // // Update the documents
    if (replace) {
      for (const result of results) {
        const updatedField =
          typeof result[fieldName] === 'string'
            ? result[fieldName].replace(
                // Replace type":"footnote or type" : "footnote (with optional spaces)
                new RegExp(`type"\\s*:\\s*"${searchType}`, 'g'),
                `type":"${replaceType}`
              )
            : (() => {
                const replaceTypeInField = (field) => {
                  if (typeof field === 'object' && field !== null) {
                    if (field.type === searchType) {
                      field.type = replaceType;
                    } else {
                      Object.values(field).forEach(replaceTypeInField);
                    }
                  }
                  if (Array.isArray(field)) {
                    field.forEach(replaceTypeInField);
                  }
                };
                replaceTypeInField(result[fieldName]);
                return result[fieldName];
              })();

        await collectionSchema.updateOne(
          { _id: result.id },
          { $set: { [fieldName]: updatedField } }
        );
      }

      logInfo(
        `Replaced "${searchType}" with "${replaceType}" in ${results.length} document(s).`
      );
    }
  } catch (error) {
    logError('Error:', error);
  } finally {
    await mongoose.disconnect();

    logInfo('Connection to MongoDB closed');
  }
}

// Example usage
const collectionName = 'courseslides'; // Replace with your collection name
const fieldName = 'content'; // Replace with the nested field path
const searchType = 'footnote'; // Replace with the word you are searching for
const replaceType = 'smallText'; // Replace with the word you want to replace with

findAndReplaceDocumentsWithType({
  collectionName,
  fieldName,
  searchType,
  replaceType,
  replace: true, // Set to true to perform the replacement
});

// Things to check

/**
 * pages content
 * articles body
 * Courses body
 * Courseslides content
 * coursequestionnaires questions
 */
