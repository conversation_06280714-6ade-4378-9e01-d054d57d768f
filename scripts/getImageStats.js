import 'dotenv/config.js';
import mongoose from 'mongoose';

import { formatBytes } from '#utils/bytes.js';
import { getFiles } from '#utils/storage.js';
import Logger from '#utils/logger.js';
import Entity from '#modules/entities/models/Entity.js';

const bucket = process.env.BUCKET_IMAGES || 'hope-images';

await mongoose.connect(process.env.DATABASE);

const entities = await Entity.find();

for (const entity of entities) {
  const files = await getFiles(bucket, { prefix: entity.id });

  const size = files.reduce((acc, f) => {
    acc += f.size;
    return acc;
  }, 0);

  Logger.info(
    `${entity.name} has ${files.length} ${
      files.length === 1 ? 'image' : 'images'
    } using a total of ${formatBytes(size)}.`
  );
}

await mongoose.disconnect();
