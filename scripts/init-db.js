import 'dotenv/config.js';
import mongoose from 'mongoose';

import Client from '#modules/clients/models/Client.js';
import Entity from '#modules/entities/models/Entity.js';
import User from '#modules/users/models/User.js';
import { setDefaultAvatar } from '#modules/users/controllers/avatarController.js';

import { sendEmail } from '#utils/notifier.js';
import { randomPassword, randomString } from '#utils/strings.js';
import { t } from '#utils/translator.js';
import Logger from '#utils/logger.js';
import { getBackendURL } from '#modules/users/utils/ancestors.js';

const resetPath = process.env.PASSWORD_RESET_PATH;

const initDB = async () => {
  try {
    // Recursive function to generate a unique token among all clients
    const generateUniqueToken = async () => {
      let token = randomString({ length: 32 });
      const count = await Client.countDocuments({ token });
      if (count > 0) {
        token = await generateUniqueToken();
      }

      return token;
    };

    let token = await generateUniqueToken();
    const backend = await Client.create({
      name: 'Backend',
      token,
    });

    Logger.success('Backend client created:', backend);

    token = await generateUniqueToken();
    const frontend = await Client.create({
      name: 'Frontend',
      token,
    });

    Logger.success('Frontend client created:', frontend);

    const entity = await Entity.create({
      name: 'Hope Software',
      language: 'en',
      config: {
        backendURL: 'http://localhost:3000',
        domains: ['localhost:3000'],
        backendDomains: [
          {
            domain: 'localhost:3000',
            isPrimary: true,
          },
        ],
      },
      location: {
        type: 'Point',
        coordinates: [0, 0], // Default location
      },
    });

    Logger.success('Entity created:', entity);

    const admin = await User.create({
      name: 'The Admin',
      email: '<EMAIL>',
      password: randomPassword(),
      isAdmin: true,
      entity: entity._id,
    });

    // Set default avatar
    await setDefaultAvatar(admin);

    Logger.success('Admin created:', admin);

    // 30 days
    const expiresIn = Date.now() + 30 * 24 * 60 * 60 * 1000;
    const userToken = await admin.createPasswordResetToken(expiresIn);
    const language = 'en';
    // Find the
    const backendURL = await getBackendURL(entity);
    const resetURL = `${backendURL}/${resetPath}/${userToken}`;

    sendEmail({
      language,
      entity,
      to: admin.email,
      subject: t(language, 'newAccountSubject'),
      templateName: 'newAccount',
      templateValues: {
        title: t(language, 'newAccountTitle'),
        entityName: entity.name,
        subtitle: t(language, 'newAccountSubtitle'),
        linkURL: resetURL,
        linkText: t(language, 'newAccountLinkText'),
        linkNotWorking: t(language, 'linkNotWorking'),
        subtext: t(language, 'newAccountSubtext'),
        subtext2: t(language, 'newAccountSubtext2'),
        duration: t(language, 'newAccountDuration', {
          tokenDuration: 30,
        }),
      },
    });
  } catch (error) {
    Logger.error('Error!', error);
  }
};

mongoose
  .connect(process.env.DATABASE)
  .then(async () => {
    Logger.success('DB connection successful!');
    await initDB();
  })
  .catch((error) => Logger.error('DB connection error!', error));
