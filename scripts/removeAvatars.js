import 'dotenv/config.js';
import mongoose from 'mongoose';

import { getFiles, removeFile } from '#utils/storage.js';
import Logger from '#utils/logger.js';
import User from '#modules/users/models/User.js';

const bucket = process.env.BUCKET_AVATARS || 'hope-avatars';

await mongoose.connect(process.env.DATABASE);

const users = await User.find();

const userIds = users.map((u) => u.id);

const files = await getFiles(bucket);

let count = 0;

for (const file of files) {
  const userId = file.name.split('/')[0];
  if (!userIds.includes(userId)) {
    await removeFile(bucket, file.name);
    count += 1;
  }
}

await mongoose.disconnect();

Logger.success(`Removed ${count} avatars.`);
