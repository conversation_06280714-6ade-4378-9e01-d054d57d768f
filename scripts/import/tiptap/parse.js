import { generateJSON } from '@tiptap/html';
import StarterKit from '@tiptap/starter-kit';

import {
  Table,
  TableCell,
  TableHeader,
  TableRow,
} from '@tiptap/extension-table';

import { logError } from '#utils/logger.js';

import Image from './plugins/Image/index.js';
import Link from './plugins/Link/index.js';
import Media from './plugins/Media/index.js';
import Chiastic from './plugins/Chiastic/index.js';
import FootnoteReference from './plugins/FootnoteReference/index.js';
import { extractFootnotes } from './plugins/FootnoteReference/helpers.js';

export function htmlToTiptap(html = '', customPlugins = []) {
  try {
    // Remove all empty paragraphs, replace all &nbsp; with spaces,
    // replace multiple spaces with one, un-escape tags
    // and replace two or more <br /> together as closing and opening a paragraph
    const purgedHTML = html
      .replace(/(<p>&nbsp;<\/p>)/gm, '')
      .replace(/(<p><\/p>)/gm, '')
      // replace only space paragraphs
      .replace(/(<p>[\s]*<\/p>)/gm, '')
      .replace(/(&nbsp;)+/gm, ' ')
      .replace(/(\s){2,}/gm, ' ')
      .replace(/(&lt;)/gm, '<')
      .replace(/(&gt;)/gm, '>')
      .replace(/(<br\s*[/]?>\s*){2,}/gm, '<br/>');

    // The footnotes are not handled by the tiptap parser, but rather extracted in the case of the notes, and only the references inside the content are left there. All the footnotes are collected in a collector object and added to the end of the TipTap content.
    const { footnotes, updatedHTML } = extractFootnotes(purgedHTML); // Mutates purgedHTML removing the footnotes
    // Deserialize the purgedHTML to the tiptap format and return it
    // console.log('Updated HTML: ', updatedHTML);
    // console.log('Footnotes: ', JSON.stringify(footnotes, null, 2));

    const generatedJson = generateJSON(updatedHTML, [
      FootnoteReference,
      StarterKit,
      Chiastic,
      Image,
      Link,
      Media,
      Table,
      TableCell,
      TableHeader,
      TableRow,
      ...(customPlugins || []),
    ]);

    // Add the footnotes if they exist
    if (footnotes) {
      generatedJson.content.push(footnotes);
    }

    // console.log(JSON.stringify(generatedJson, null, 2));
    return generatedJson;
  } catch (error) {
    logError('Error when trying to deserialize the html: ', error);
    return false;
  }
}
