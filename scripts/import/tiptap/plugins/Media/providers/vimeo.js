const viRegex = {
  tParam: /#t=([^&#?]+)/,
  h: /(\d+)h/,
  m: /(\d+)m/,
  s: /(\d+)s/,
};

/**
 * Get Vimeo id from a url
 * @param {String} url The Vimeo url to extract data from (i.e. https://vimeo.com/123456789)
 * @returns {String} The Vimeo id
 */
export function getVimeoId(url = '') {
  // Obtain the video ID: In Vimeo, the video ID is the last part of the URL
  // Here I also make sure to remove any parameters after the video ID
  return url.split('/').pop().split(/\?|#/).shift();
}

/**
 * Get the start time of a Vimeo video based on the url
 * @param {String} url The Vimeo url to extract data from (i.e. https://vimeo.com/123456789)
 * @returns {String} The start time in HH:MM:SS format
 */
export function getVimeoStartAt(url = '') {
  // Obtain the t parameter, if present, and transform its value to HH:MM:SS
  if (viRegex.tParam.test(url)) {
    // Get the parameter's value
    const t = url.match(viRegex.tParam).pop();

    // Divide it in hours, minutes and seconds
    let hours = viRegex.h.test(t) ? t.match(viRegex.h).pop() : '00';
    hours = `${hours.length < 2 ? '0' : ''}${hours}`;
    let minutes = viRegex.m.test(t) ? t.match(viRegex.m).pop() : '00';
    minutes = `${minutes.length < 2 ? '0' : ''}${minutes}`;
    let seconds = viRegex.s.test(t) ? t.match(viRegex.s).pop() : '00';
    seconds = `${seconds.length < 2 ? '0' : ''}${seconds}`;

    // Compose the final value
    return `${hours}:${minutes}:${seconds}`;
  }

  return undefined;
}
