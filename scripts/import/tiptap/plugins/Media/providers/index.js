import { getVimeoId, getVimeoStartAt } from './vimeo.js';
import { getYoutubeEndAt, getYoutubeId, getYoutubeStartAt } from './youtube.js';

const attributeGetterMap = {
  youtube: {
    id: getYoutubeId,
    startAt: getYoutubeStartAt,
    endAt: getYoutubeEndAt,
  },
  vimeo: {
    id: getVimeoId,
    startAt: getVimeoStartAt,
  },
};

/**
 * Get the attribute of a video based on its provider
 * @param {String} provider The provider of the video (i.e. youtube, vimeo)
 * @param {String} attribute The attribute to get (i.e. id, startAt)
 * @returns {Function} The function to get the attribute
 * @example
 * const youtubeId = getProviderAttribute('youtube', 'id');
 * const vimeoId = getProviderAttribute('vimeo', 'id');
 * const youtubeStartAt = getProviderAttribute('youtube', 'startAt');
 */
export function getProviderAttribute(provider, attribute) {
  const getter = attributeGetterMap[provider];

  if (!getter || !getter[attribute]) {
    return undefined;
  }

  return getter[attribute];
}

/**
 * Get the provider of a video based on its url
 * @param {String} url The url to extract data from (i.e. https://www.youtube.com/watch?v=123456789)
 * @returns {String} The provider of the video (i.e. youtube, vimeo)
 */
export function getMediaProvider(url = '') {
  if (/youtu\.?be/.test(url)) {
    return 'youtube';
  }
  if (/vimeo/.test(url)) {
    return 'vimeo';
  }
  return undefined;
}
