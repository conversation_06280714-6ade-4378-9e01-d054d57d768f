import { Node, mergeAttributes } from '@tiptap/core';

import { getVideoAttr } from './helpers.js';

export default Node.create({
  name: 'media',
  group: 'block',
  atom: true,
  draggable: true,

  addAttributes() {
    return {
      type: {
        default: 'video', // 'video' or 'audio' (At the moment, only video is supported)
      },
      caption: {
        default: '',
      },
      copyright: {
        default: '',
      },
      id: {
        default: '',
        parseHTML: getVideoAttr('id'),
      },
      provider: {
        default: '',
        parseHTML: getVideoAttr('provider'),
      },
      startAt: {
        default: null,
        parseHTML: getVideoAttr('startAt'),
      },
      endAt: {
        default: null,
        parseHTML: getVideoAttr('endAt'),
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'video[src]',
        // getAttrs: getVideoAttrs,
      },
      {
        tag: 'iframe[src]',
        // getAttrs: getVideoAttrs,
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['media', mergeAttributes(HTMLAttributes)];
  },
});
