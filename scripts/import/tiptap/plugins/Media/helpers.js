import { getMediaProvider, getProviderAttribute } from './providers/index.js';

/**
 * Convert seconds to HH:MM:SS format
 * @param {Number} seconds The number of seconds to convert
 * @returns {String} The time in HH:MM:SS format
 */
export function secondsToHours(seconds = 0) {
  return new Date(seconds * 1000).toISOString().substr(11, 8);
}

/**
 * Get the attributes of a video element based on its src attribute
 * If it's a youtube or vimeo url, it will extract the video ID and other parameters
 * @param {HTMLElement} element The video element to get the attributes from
 * @returns {Object|Boolean} The attributes object or false if not a video element
 */
export function getVideoAttr(attribute) {
  return function (element) {
    const src = element.getAttribute('src');

    if (!src) {
      return false;
    }

    const provider = getMediaProvider(src);

    if (!provider) {
      return false;
    }

    if (attribute === 'provider') {
      return provider;
    }

    const getAttribute = getProviderAttribute(provider, attribute);

    return getAttribute?.(src);
  };
}
