import { mergeAttributes, Node } from '@tiptap/core';

const FootnoteReference = Node.create({
  name: 'footnoteReference',

  group: 'inline',

  inline: true,

  atom: true,

  addAttributes() {
    return {
      'class': {
        default: 'footnote-ref',
        parseHTML: () => 'footnote-ref',
      },
      'data-id': {
        default: null,
      },
      'referenceNumber': {
        default: null,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'a', // Match all <a> tags
        getAttrs: (node) => {
          // Convert the attributes array into an object for easier access (This is needed because the attributes are not directly accessible)
          const attributes = node.attributes.reduce((acc, attr) => {
            acc[attr.name] = attr.value;
            return acc;
          }, {});

          // This is tailored to the data sent from BRI
          // Ensure the node has the "reflink" class and a "data-id" attribute
          if (attributes.class === 'reflink' && attributes['data-id']) {
            // Check if the first child is a <sup> tag
            const firstChild = node.children?.[0];
            if (firstChild?.tagName === 'SUP') {
              // Extract the content of the <sup> tag
              const referenceNumber = firstChild.children?.[0]?._text; // Access the value of the VTextNode
              if (referenceNumber) {
                const dataId = attributes['data-id']; // Extract the UUID from the `data-id` attribute
                return {
                  'class': 'footnote-ref',
                  'data-id': dataId,
                  referenceNumber,
                };
              }
            }
          }

          return false; // Skip this element if it doesn't meet the criteria
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['a', mergeAttributes({ class: 'footnote-ref' }, HTMLAttributes)];
  },
});

export default FootnoteReference;
