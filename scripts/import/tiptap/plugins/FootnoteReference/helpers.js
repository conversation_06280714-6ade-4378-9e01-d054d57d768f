import { generateJSON } from '@tiptap/html';
import StarterKit from '@tiptap/starter-kit';

import { parseDocument, DomUtils } from 'htmlparser2';
import serialize from 'dom-serializer';

import { randomUUID } from 'crypto';

/**
 * @description Extracts footnotes from the parsed HTML and mutates the input HTML to remove footnotes and update references.
 * @param {string} parsedHtml - The HTML string to process.
 * @returns {object} Returns the footnotes object that should be appended to a Tiptap object.
 */
export function extractFootnotes(parsedHtml) {
  // Parse the HTML into a DOM-like structure
  const dom = parseDocument(parsedHtml);

  const footnotes = [];
  const footnoteElements = DomUtils.findAll(
    (elem) => elem.name === 'li' && elem.attribs?.id?.startsWith('note'),
    dom.children
  );

  // Process each footnote element
  footnoteElements.forEach((footnoteElement) => {
    const { id } = footnoteElement.attribs;
    const referenceNumber = id.replace('note', ''); // Extract the number from the ID
    const dataId = randomUUID(); // Generate a UUID for the footnote

    // Serialize the content of the footnote back into HTML
    const footnoteContentHtml = serialize(
      DomUtils.getChildren(footnoteElement).filter(
        (node) =>
          !(node.name === 'a' && node.attribs?.class?.includes('reflink'))
      )
    );

    // Use generateJSON to parse the serialized HTML into TipTap JSON
    const footnoteContentJson = generateJSON(footnoteContentHtml, [StarterKit]);

    // Clean up nested paragraphs in the footnote content
    const flattenedContent = flattenParagraphs(footnoteContentJson.content);

    // Add the footnote to the array
    footnotes.push({
      type: 'footnote',
      attrs: {
        'id': `fn:${referenceNumber}`,
        'data-id': dataId,
      },
      content: flattenedContent,
    });

    // Remove the footnote element from the DOM
    DomUtils.removeElement(footnoteElement);
  });

  // Update the references in the HTML
  const referenceElements = DomUtils.findAll(
    (elem) => elem.name === 'a' && elem.attribs?.class?.includes('reflink'),
    dom.children
  );

  referenceElements.forEach((referenceElement) => {
    const referenceNumber = DomUtils.findOne(
      (child) => DomUtils.isTag(child) && child.name === 'sup',
      referenceElement.children
    )?.children?.[0]?.data;

    const dataId = footnotes.find(
      (footnote) => footnote.attrs.id === `fn:${referenceNumber}`
    )?.attrs['data-id'];

    if (dataId) {
      referenceElement.attribs['data-id'] = dataId;
      delete referenceElement.attribs.href;
      delete referenceElement.attribs.id;
    }
  });

  return {
    updatedHTML: serialize(dom), // Convert the DOM back to HTML with the dom-serializer
    footnotes: {
      type: 'footnotes',
      attrs: {
        class: 'footnotes',
      },
      content: footnotes,
    },
  };
}

/**
 * Flattens unnecessary nested paragraphs in the content.
 * If a paragraph contains another paragraph, it replaces the parent with the child content.
 * @param {Array} content The content array to process.
 * @returns {Array} The flattened content array.
 */
function flattenParagraphs(content) {
  if (!Array.isArray(content)) return content;

  return content.flatMap((node) => {
    if (node.type === 'paragraph' && Array.isArray(node.content)) {
      // If the paragraph contains another paragraph, replace it with its content
      return node.content.some((child) => child.type === 'paragraph')
        ? flattenParagraphs(node.content)
        : [node];
    }

    // Recursively process child nodes
    if (node.content) {
      node.content = flattenParagraphs(node.content);
    }

    return node;
  });
}
