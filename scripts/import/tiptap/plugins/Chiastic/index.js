import { Node, mergeAttributes } from '@tiptap/core';
import {
  detectChiasticStructure,
  determineNotationAndSubNotationFromElement,
} from './helpers.js';

export default Node.create({
  name: 'chiastic',
  group: 'block',
  atom: true,
  draggable: true,

  addAttributes() {
    return {
      items: {
        default: [], // Default to an empty array
        parseHTML: (element) => {
          const listItems = Array.from(element.querySelectorAll('li'));
          const structure = detectChiasticStructure(
            listItems,
            'c-marker',
            'level'
          );
          return structure ? structure.items : [];
        },
      },
      notation: {
        default: 'latin',
        parseHTML: (element) => {
          const { notation } =
            determineNotationAndSubNotationFromElement(element);
          return notation;
        },
      },
      subNotation: {
        default: 'apostrophe',
        parseHTML: (element) => {
          const { subNotation } =
            determineNotationAndSubNotationFromElement(element);
          return subNotation;
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'ul.chiastic', // Match the chiastic structure by its tag
        getAttrs: (element) => {
          const listItems = Array.from(element.querySelectorAll('li'));
          const structure = detectChiasticStructure(
            listItems,
            'c-marker',
            'level'
          );
          if (structure) {
            const { notation, subNotation } =
              determineNotationAndSubNotationFromElement(element);
            return {
              items: structure.items,
              notation,
              subNotation,
            };
          }
          return false; // Return false if no chiastic structure is detected
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['chiastic', mergeAttributes(HTMLAttributes)];
  },
});
