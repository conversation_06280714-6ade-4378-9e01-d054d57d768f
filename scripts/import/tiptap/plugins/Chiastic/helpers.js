/* eslint-disable no-console */
export function detectChiasticStructure(elements, markerClass, levelClass) {
  console.log('Input elements:', elements);

  if (!Array.isArray(elements) || elements.length < 3) {
    console.log('Not enough elements or invalid input.');
    return false;
  }

  const values = elements.map((el, index) => {
    const markerElement = el.querySelector(`.${markerClass}`);
    const marker = markerElement?.textContent.trim();

    // Extract the level class
    let level = Array.from(el.classList).find((cls) =>
      cls.startsWith(levelClass)
    );

    // Fallback: If classList is not working, parse the class attribute manually
    if (!level) {
      const classAttr = el.getAttribute('class') || '';
      level = classAttr.split(' ').find((cls) => cls.startsWith(levelClass));
    }

    // Assume level1 for the first and last items if no level is defined
    if (!level && (index === 0 || index === elements.length - 1)) {
      level = 'level1';
    }

    if (!marker) {
      console.log('Missing marker.');
      return null;
    }

    return {
      content: el.textContent.replace(marker, '').trim(), // Remove the marker from the content
    };
  });

  console.log('Parsed values:', values);

  if (values.some((val) => val === null)) {
    console.log('Invalid element detected.');
    return false;
  }

  // Group items into start and end pairs
  const items = [];
  const { length } = values;

  for (let i = 0; i < Math.floor(length / 2); i += 1) {
    items.push({
      start: values[i].content,
      end: values[length - 1 - i].content,
    });
  }

  // If there's a middle element (odd number of elements), include it as a standalone pair
  if (length % 2 !== 0) {
    items.push({
      start: values[Math.floor(length / 2)].content,
      end: null,
    });
  }

  console.log('Grouped items:', items);

  return {
    items,
  };
}

/**
 * Determines the global notation and subNotation directly from the HTML element.
 * @param {HTMLElement} element - The root HTML element of the chiastic structure.
 * @returns {Object} - An object containing `notation` and `subNotation`.
 */
export function determineNotationAndSubNotationFromElement(element) {
  if (!element) {
    console.log(
      'No element provided for determining notation and subNotation.'
    );
    return { notation: 'latin', subNotation: 'apostrophe' };
  }

  const listItems = Array.from(element.querySelectorAll('li'));
  if (listItems.length === 0) {
    console.log('No list items found in the element.');
    return { notation: 'latin', subNotation: 'apostrophe' };
  }

  // Analyze the last item's marker to determine notation and subNotation
  const lastItem = listItems[listItems.length - 1];
  const markerElement = lastItem.querySelector('.c-marker');
  const marker = markerElement?.textContent.trim() || '';

  const notation = /^[A-Z]$/i.test(marker[0]) ? 'latin' : 'greek';
  const subNotation = /\d/.test(marker) ? 'numbered' : 'apostrophe'; // Test for digits

  console.log('Determined notation and subNotation:', {
    notation,
    subNotation,
  });

  return { notation, subNotation };
}
