import 'dotenv/config.js';
import ky from 'ky';

import Sync from '#modules/data-import/models/Sync.js';
// import { sendEmail } from '#utils/notifier.js';
import Logger from '#utils/logger.js';

import { importArticles, importMediaLibrary } from './functions.js';

// Function to convert Date to unix date format (for typo3...)
const toUnixDate = (date) => Math.floor(new Date(date).getTime() / 1000);

export default async function ({ sync } = {}) {
  try {
    if (!sync) {
      throw new Error('Please provide a Sync instance!');
    }

    const lastSync = sync.lastSync ? toUnixDate(sync.lastSync) : 0;
    const currentSync = Date.now();
    const customKy = ky.create({
      prefixUrl: sync.baseURL,
      headers: { secret: sync.secret },
    });

    Logger.info(
      `Starting import for ${sync.entity.name}. Last sync date: ${
        lastSync === 0 ? 'Never' : new Date(lastSync * 1000)
      }`
    );

    if (sync.articles) {
      await importArticles(
        customKy,
        sync.entity,
        sync.site,
        sync.articlePage,
        sync.articleFlagMappings,
        lastSync
      );
    }

    if (sync.mediaLibrary) {
      await importMediaLibrary(customKy, sync.entity, lastSync);
    }

    // Save sync time
    await Sync.updateOne({ _id: sync.id }, { lastSync: currentSync });

    Logger.info('Finished import for', sync.entity.name);
  } catch (error) {
    Logger.error(
      `Error running import for ${sync.entity} with baseURL ${sync.baseURL}`,
      error
    );
  }
}
