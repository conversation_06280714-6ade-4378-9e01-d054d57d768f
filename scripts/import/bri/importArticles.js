// This script imports articles from JSON files in ./files, also images, pdf files
// in ./cache the authors and existing categories.
// Outputs json files in ./output

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { newMongoDBUUid } from '#utils/api/mongoose/id.js';
import { loadImagesMap, saveImages } from '#scripts/shared/helpers/images.js';
import { getS3Client } from '#utils/storage.js';
import {
  parseArticle,
  handleSeries,
  saveArticles,
} from '#scripts/shared/helpers/articles.js';
import { loadAuthorsMap } from '#scripts/shared/helpers/authors.js';

import { saveToJsonFile } from '#utils/json.js';
import { logError, logInfo } from '#utils/logger.js';
import { loadFilesMap, saveFiles } from '#scripts/shared/helpers/files.js';

import {
  // VERBOSE,
  // USE_CACHE,
  // IMPORT_IMAGES,
  CACHE_FOLDER,
  OUTPUT_FOLDER,
} from './constants.js';

const entityId = '65de02451beb6f51dee0d1e4'; // Production environment
const siteId = '6668426d76cb1257d770488e'; // Production
const canonicalSitePage = '680b3a8cebcda951187680eb'; // Production
const documentsBucket = 'hope-documents'; // The bucket to save the documents to
const imagesBucket = 'hope-images'; // The bucket to save the images to

// const entityId = '6819d528e6ad7c276484f767'; // Testing environment
// const siteId = '681a175e30b3f7b9cb61238d'; // Testing
// const canonicalSitePage = '681a176630b3f7b9cb6123be'; // Testing
// const documentsBucket = 'hope-documents-staging'; // The bucket to save the documents to
// const imagesBucket = 'hope-images-staging'; // The bucket to save the images to

const importType = 'bri-articles'; // The type of import

// Load environment variables
const envPath = new URL('.env', import.meta.url);

dotenv.config({ path: envPath });

const { BUCKET_ENDPOINT, BUCKET_SECRET, BUCKET_KEY_ID } = process.env;

// Initialize a custom S3 client to upload the images to the CDN
const customS3Client = getS3Client({
  endpoint: BUCKET_ENDPOINT,
  secretAccessKey: BUCKET_SECRET,
  accessKeyId: BUCKET_KEY_ID,
});

// Initialize maps and arrays to store the data during the migration process
// - the images map from the cache folder (if exists)
const imagesMap = loadImagesMap({ tempFolder: './output' });
// logInfo('loaded images map', JSON.stringify(imagesMap, null, 2));

// Files map (The pdf files)
const pdfFilesMap =
  loadFilesMap({
    tempFolder: './output',
  }) || [];

// - the authors map (persons)
const authorsMap = loadAuthorsMap({
  tempFolder: CACHE_FOLDER,
  imagesMap,
  entityId,
});

// The categories map is used to connect the articles to the categories, but the local categories is used to upload the missing categories as local categories to the entity.
const categoriesMap = readCategoriesMap();
logInfo(`Categories map: ${Object.keys(categoriesMap).length} categories`);
const localCategoriesMap = []; // Objects will be pushed to this map if they are not found in the categories map

// - the flags map (for the articleSites)
const flagsMap = [];

// - articles map
const articlesMap = {};
const seriesMap = {};

// - article sites array
const articleSites = [];

// - debug collector map (for debugging purposes)
const debugCollectorMap = {};

async function migrate({ inputFolder }) {
  const { status1, status2, status3 } = readJsonFiles(inputFolder); // Status 1 is the normal articles, 2 is the series, 3 are the failed to parse properly articles

  handleSeries(status2, seriesMap, 'bri-articles', entityId);

  const rawEntries = [...status1, ...status3]; // Bring status 1 for ok, 3 for failed to parse (Will have a different name and wont be published)

  // TESTING COMMENTS
  // Find articles of a specific author
  // const articlesByAuthor = rawEntries.filter((rawArticle) =>
  //   rawArticle.authors?.some((author) => author.id === 180)
  // );
  // logInfo(articlesByAuthor);

  // // How many languages are there?
  // const languages = new Set();
  // rawEntries.forEach((rawArticle) => {
  //   const { lang } = rawArticle || {};
  //   if (lang) {
  //     languages.add(lang);
  //   }
  // });
  // logInfo(`Languages: ${languages.size}`);
  // logInfo(`Languages: ${Array.from(languages).join(', ')}`);
  // logInfo(`Articles: ${rawEntries.length}`);

  // // Count articles with images
  // const articlesWithImages = rawEntries.filter(
  //   (rawArticle) => rawArticle.image
  // );
  // logInfo(`Articles with images: ${articlesWithImages.length}`);

  // // Count articles with chiastic in content
  // const articlesWithChiastic = rawEntries.filter((rawArticle) => {
  //   const { content } = rawArticle || {};
  //   return content && content.includes('chiastic');
  // });
  // // Log each id of the articles with chiastic
  // articlesWithChiastic.forEach((article) => {
  //   logInfo(article.id);
  // });
  // logInfo(`Articles with chiastic: ${articlesWithChiastic.length}`);

  //  Get an article to test based on its id (Comment out the loop if using this)
  // const testArticle = rawEntries.find(
  // (rawArticle) => rawArticle.id === '00145|1551848400' // chiastic
  // (rawArticle) => rawArticle.id === '00289|1528171200' // footnotes
  // (rawArticle) => rawArticle.id === '00065|1217390400' // images
  // (rawArticle) => rawArticle.id === '01620|1711944000|2' // author image
  // (rawArticle) => rawArticle.id === '00246|1361250000' // Footnotes with empty text
  // (rawArticle) => rawArticle.id === '00336|1194411600' // Footnotes with nested paragraphs
  // );

  // Loop through the rawEntries with a limit of 10 at a time
  // for (let i = 0; i < 10 && i < rawEntries.length; i += 1) {
  // const rawArticle = rawEntries[i];

  // Loop through all articles
  for (const rawArticle of rawEntries) {
    const articleId = newMongoDBUUid();

    const article = await parseArticle({
      articleId,
      rawArticle,
      // rawArticle: testArticle, // If testing with a specific article, uncomment this line
      imagesMap, // NOTE: The images map will be updated with the images found within the article
      categoriesMap,
      localCategoriesMap,
      importType,
      entityId,
      debugCollectorMap,
      authorsMap,
      seriesMap,
      pdfFilesMap,
    });

    // Skip the article if it is not valid
    if (!article) {
      continue;
    }

    // Add the article to the map
    articlesMap[articleId] = article;
  } // End of articles loop

  // Save the articles to the output folder
  saveArticles({
    articlesMap,
    articleSites,
    outputFolder: OUTPUT_FOLDER,
    entityId,
    siteId,
    flagsMap,
    canonicalSitePage,
    imagesMap,
    importType,
  });
}

await migrate({
  inputFolder: './files',
})
  .then(() => {
    logInfo('Migration completed');
  })
  .catch((error) => {
    logError('Error during migration:', error);
  });

// Save the local categories map to the output folder
saveToJsonFile({
  data: localCategoriesMap,
  fileName: 'local-categories',
  folder: OUTPUT_FOLDER,
});

// Save the flags map to the output folder (to be added to the site)
const flagsArray = Object.values(flagsMap).map((flag) => ({
  ...flag,
}));

saveToJsonFile({
  data: flagsArray,
  fileName: 'flags',
  folder: OUTPUT_FOLDER,
});

// Save the debug collector map
saveToJsonFile({
  data: debugCollectorMap,
  fileName: 'debug-collector',
  folder: OUTPUT_FOLDER,
});

// Save the authors, saving each object value to a new array
const authorsArray = Object.values(authorsMap).map((author) => ({
  ...author,
}));

saveToJsonFile({
  data: authorsArray,
  fileName: 'authors',
  folder: OUTPUT_FOLDER,
});

await saveImages({
  imagesMap,
  tempFolder: './images',
  outputFolder: './output',
  entityId,
  // dryRun: true,
  customS3Client,
  bucket: imagesBucket,
  verbose: true,
});

await saveFiles({
  filesMap: pdfFilesMap,
  tempFolder: './pdfFiles',
  outputFolder: './output',
  entityId,
  // dryRun: true,
  customS3Client,
  bucket: documentsBucket,
  verbose: true,
});

// Save the article sites
saveToJsonFile({
  data: articleSites,
  fileName: 'articlesites',
  folder: OUTPUT_FOLDER,
});

const articleSeries = Object.values(seriesMap).map((series) => ({
  ...series,
}));

// Save the article series
saveToJsonFile({
  data: articleSeries,
  fileName: 'articleseries',
  folder: OUTPUT_FOLDER,
});

// Read JSON files from the ./files directory
function readJsonFiles(directory) {
  const files = fs.readdirSync(directory);
  const jsonFiles = files.filter((file) => file.endsWith('.json'));
  const jsonData = [];

  jsonFiles.forEach((file) => {
    const filePath = path.join(directory, file);
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    jsonData.push(data);
  });

  const status1 = jsonData.filter((data) => data.status === 1);
  const status2 = jsonData.filter((data) => data.status === 2);
  const status3 = jsonData.filter((data) => data.status === 3);

  // count of each status
  logInfo(`Status 1: ${status1.length}`);
  logInfo(`Status 2: ${status2.length}`);
  logInfo(
    `Status 3: ${status3.length}`
    // JSON.stringify(status3[0], null, 2)
  );
  return {
    status1,
    status2,
    status3,
  };
}

function readCategoriesMap() {
  const categoriesCacheFile = `${CACHE_FOLDER}/categories.json`;

  // Read the categories cache file if it exists
  if (fs.existsSync(categoriesCacheFile)) {
    try {
      const categoriesCache = fs.readFileSync(categoriesCacheFile, 'utf8');
      return JSON.parse(categoriesCache);
    } catch (error) {
      logError('Failed to read article categories cache file', error);
    }
  }

  return {};
}

// Code to delete from the database: Do a query in the collections with the following
// {importIDs: {$elemMatch: { type: "bri-articles" }}}
// And delete the articles/articleseries/articlesites
