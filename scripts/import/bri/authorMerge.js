/* global use, db, ObjectId */

// This file will be used to merge the authors with the people collection in the database

/*
 * Testing: Recommended to be run locally with existing data loaded into the collection.
 * To use this file modify the data to the latest output,
 * and then test run the script in a terminal with
 * mongosh mongodb://localhost:27017/yourDatabase /absolute/path/to/authorMerge.js
 * replacing the path. You can use pwd to find it.
 */

use('hopeplatform'); // Change the name to match the desired database

// Copy the authors output
const data = [
  {
    _id: {
      $oid: '67f86cb2f72c3da5a0a4240d',
    },
    firstName: '<PERSON>',
    middleName: 'R.',
    lastName: 'Timm',
    fullName: '',
    importIDs: [
      {
        recordID: '1',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf03',
        },
        startDate: {
          $date: '2009-04-01T00:00:00.000+02:00',
        },
        bio: 'is the Rector of the Latin-American Adventist Theological Seminary and the Spirit of Prophecy Coordinator for the South American Division of Seventh-day Adventists',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf04',
        },
        startDate: {
          $date: '2019-05-28T00:00:00.000+02:00',
        },
        bio: 'Associate Dirctor, Ellen G. White Estate, Inc.',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf05',
        },
        startDate: {
          $date: '2002-06-05T00:00:00.000+02:00',
        },
        bio: 'Professor of Historical Theology, Brazil Adventist University College - Campus 2, Director of the Brazilian Ellen G. White Research Center',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf06',
        },
        startDate: {
          $date: '2014-04-01T00:00:00.000+02:00',
        },
        bio: 'is an associate director of the Ellen G. White Estate',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf07',
        },
        startDate: {
          $date: '2005-01-01T00:00:00.000+01:00',
        },
        bio: 'Brazil Adventist University College',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf08',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Ellen G. White Estate',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf09',
        },
        startDate: {
          $date: '2021-05-17T00:00:00.000+02:00',
        },
        bio: 'Associate Director, Ellen G. White Estate, Inc.',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf0a',
        },
        startDate: {
          $date: '2013-10-01T00:00:00.000+02:00',
        },
        bio: 'is Associate Director of the Ellen G. White Estate',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf0b',
        },
        startDate: {
          $date: '2023-04-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb7c4',
        },
        bio: 'é o Reitor do Seminário Adventista Latino-Americano de Teologia e Coordenador do Espírito de Profecia para a Divisão Sul Americana dos Adventistas do Sétimo Dia.',
        startDate: {
          $date: '2009-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bada2632747d67cb998',
        },
        bio: 'is the Rector of the Latin-American Adventist Theological Seminary and the Spirit of Prophecy Coordinator for the South American Division of Seventh-day Adventists',
        startDate: {
          $date: '2009-04-01T00:00:00.000+02:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf19',
    },
    fullName: 'Ángel Manuel Rodríguez',
    slug: 'angel-manuel-rodriguez',
    roles: ['author'],
    importIDs: [
      {
        recordID: '2',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf0c',
        },
        startDate: {
          $date: '2012-04-01T00:00:00.000+02:00',
        },
        bio: 'BRI (retired)',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf0d',
        },
        startDate: {
          $date: '2009-04-01T00:00:00.000+02:00',
        },
        bio: 'is director of the Biblical Research Institute of the General Conference of Seventh-day Adventists',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf0e',
        },
        startDate: {
          $date: '2014-08-01T00:00:00.000+02:00',
        },
        bio: 'aposentado',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf0f',
        },
        startDate: {
          $date: '2020-01-01T00:00:00.000+01:00',
        },
        bio: 'Director (retired) Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf10',
        },
        startDate: {
          $date: '1997-07-15T00:00:00.000+02:00',
        },
        bio: 'Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf11',
        },
        startDate: {
          $date: '2012-01-03T00:00:00.000+01:00',
        },
        bio: 'is the recently retired director of the Biblical Research Institute, Silver Spring, Maryland, United States.',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf12',
        },
        startDate: {
          $date: '2001-11-01T00:00:00.000+01:00',
        },
        bio: 'Biblical Research Institute, Silver Spring, MD',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf13',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Biblical Research Institute (retired)',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf14',
        },
        startDate: {
          $date: '2019-01-01T00:00:00.000+01:00',
        },
        bio: 'Director (ret.) Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf15',
        },
        startDate: {
          $date: '2012-01-01T00:00:00.000+01:00',
        },
        bio: 'recently-retired BRI director, continues to serve the Biblical Research Institute in a part-time capacity',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf16',
        },
        startDate: {
          $date: '2012-04-01T00:00:00.000+02:00',
        },
        bio: 'BRI (retired)',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf17',
        },
        startDate: {
          $date: '2010-01-01T00:00:00.000+01:00',
        },
        bio: 'is director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf18',
        },
        startDate: {
          $date: '2010-04-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249bada2632747d67cb915',
        },
        bio: 'é diretor do Instituto Bíblico de Pesquisa da Associação Geral dos Adventistas do Sétimo Dia.',
        startDate: {
          $date: '2009-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd544',
        },
        bio: 'is the director of the Biblical Research Institute',
        startDate: {
          $date: '2010-04-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'eWN1747131005941.jpg',
      size: 245879,
      originalFilename: 'Rodriguez-Angel-Manuel-2',
      width: 1446,
      height: 1914,
      blurhash: 'LDF}=tNbUH^j~VM{bvRjK*xZwINH',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.239+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.239+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a4b',
    },
    fullName: 'Ekkehardt Mueller',
    importIDs: [
      {
        recordID: '57QT',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '3',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf1a',
        },
        startDate: {
          $date: '2018-01-03T00:00:00.000+01:00',
        },
        bio: 'is Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf1b',
        },
        startDate: {
          $date: '2018-01-03T00:00:00.000+01:00',
        },
        bio: 'is Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf1c',
        },
        startDate: {
          $date: '2017-05-22T00:00:00.000+02:00',
        },
        bio: 'Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf1d',
        },
        startDate: {
          $date: '2016-10-01T00:00:00.000+02:00',
        },
        bio: 'es director asociado del Instituto de Investigaciones Bíblicas',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf1e',
        },
        startDate: {
          $date: '2014-01-01T00:00:00.000+01:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf1f',
        },
        startDate: {
          $date: '2020-10-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director, Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf20',
        },
        startDate: {
          $date: '2001-01-01T00:00:00.000+01:00',
        },
        bio: 'Instituto de Pesquisa Bíblica',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf21',
        },
        startDate: {
          $date: '2002-09-26T00:00:00.000+02:00',
        },
        bio: 'is an Associate Director of the Biblical Research Institute of the General Conference of Seventh-day Adventists. <EMAIL>',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf22',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Biblical research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf23',
        },
        startDate: {
          $date: '2014-01-01T00:00:00.000+01:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf24',
        },
        startDate: {
          $date: '2012-04-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf25',
        },
        startDate: {
          $date: '2011-07-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf26',
        },
        startDate: {
          $date: '2024-04-01T00:00:00.000+02:00',
        },
        bio: 'Retired, Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf27',
        },
        startDate: {
          $date: '2020-04-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb4c9',
        },
        bio: 'is Deputy Director of the Biblical Research Institute',
        startDate: {
          $date: '2015-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bada2632747d67cb908',
        },
        bio: 'is an Associate Director of the Biblical Research Institute',
        startDate: {
          $date: '2016-10-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bada2632747d67cbb6f',
        },
        bio: 'is an Associate Director of the Biblical Research Institute of the General Conference of Seventh-day Adventists. <EMAIL>',
        startDate: {
          $date: '2002-09-26T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bada2632747d67cbcb5',
        },
        bio: 'Associate Director of the Biblical Research Institute',
        startDate: {
          $date: '2018-01-03T00:00:00.000+01:00',
        },
      },
      {
        _id: {
          $oid: '68249baea2632747d67cbd44',
        },
        bio: 'é Diretor Associado do the Instituto Bíblico de Pesquisa.',
        startDate: {
          $date: '2017-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249baea2632747d67cbf5f',
        },
        bio: 'is deputy director of the Biblical Research Institute',
        startDate: {
          $date: '2012-01-01T00:00:00.000+01:00',
        },
      },
      {
        _id: {
          $oid: '68249bb3a2632747d67cd0ba',
        },
        bio: 'is Deputy Director of the Biblical Research Institute',
        startDate: {
          $date: '2015-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb3a2632747d67cd1cd',
        },
        bio: 'is Associate Director of the Biblical Research Institute',
        startDate: {
          $date: '2016-01-13T00:00:00.000+01:00',
        },
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd609',
        },
        bio: 'is the deputy director of the Biblical Research Institute.',
        startDate: {
          $date: '2011-07-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd60f',
        },
        bio: 'is the deputy director of the Biblical Research Institute',
        startDate: {
          $date: '2011-07-01T00:00:00.000+02:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf29',
    },
    fullName: 'Miroslav M. Kiš',
    slug: 'miroslav-m-kis',
    roles: ['author'],
    importIDs: [
      {
        recordID: '4',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf28',
        },
        startDate: {
          $date: '2009-07-01T00:00:00.000+02:00',
        },
        bio: 'is Professor of Ethics and Chair of the Theology and Christian Philosophy Department at the Seventh-day Adventist Theological Seminary, Andrews University',
      },
      {
        _id: {
          $oid: '68249bada2632747d67cb9d0',
        },
        bio: 's Professor of Ethics and Chair of the Theology and Christian Philosophy Department at the Seventh-day Adventist Theological Seminary, Andrews University',
        startDate: {
          $date: '2009-07-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'eY71747131004373.jpg',
      size: 49800,
      originalFilename: 'Kis-Miroslav-M',
      width: 740,
      height: 860,
      blurhash: 'LEAJE19t0e~C-6R+OFxD5RxZ-9I:',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.243+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.243+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf2e',
    },
    fullName: 'James L. Gibson',
    slug: 'james-l-gibson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '5',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf2a',
        },
        startDate: {
          $date: '2008-04-01T00:00:00.000+02:00',
        },
        bio: 'Geoscience Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf2b',
        },
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
        bio: 'Geoscience Research Institute Director',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf2c',
        },
        startDate: {
          $date: '2018-04-16T00:00:00.000+02:00',
        },
        bio: 'is Director of the Geoscience Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf2d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Geoscience Research Institute',
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb4c0',
        },
        bio: 'Geoscience Research Institute, Director',
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bada2632747d67cb941',
        },
        bio: 'Director, Geoscience Research Institute',
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb2a2632747d67ccda4',
        },
        bio: 'Director Emeritus of the Geo-Science Research Institute',
        startDate: {
          $date: '2021-03-22T00:00:00.000+01:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'be71747131005475.jpg',
      size: 79303,
      originalFilename: 'Gibson-James-L',
      width: 904,
      height: 1134,
      blurhash: 'LdKA?3xaOrof~WoLo~j]0LWBVrWB',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.245+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.245+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf33',
    },
    fullName: 'Timothy G. Standish',
    slug: 'timothy-g-standish',
    roles: ['author'],
    importIDs: [
      {
        recordID: '6',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf2f',
        },
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
        bio: 'is a research scientist at the Geoscience Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf30',
        },
        startDate: {
          $date: '2007-07-01T00:00:00.000+02:00',
        },
        bio: 'Geoscience Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf31',
        },
        startDate: {
          $date: '2006-01-01T00:00:00.000+01:00',
        },
        bio: 'GRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf32',
        },
        startDate: {
          $date: '2018-08-08T00:00:00.000+02:00',
        },
        bio: 'GC, Geoscience Research Institute',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'BHw1747131004106.jpg',
      size: 41398,
      originalFilename: 'Standish-Timothy',
      width: 506,
      height: 528,
      blurhash: 'LADlNFH@0eIX=?oMOGRj01-;}rad',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.246+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.246+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a61',
    },
    fullName: 'Clinton Wahlen',
    importIDs: [
      {
        recordID: 'I7RH',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '7',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf34',
        },
        startDate: {
          $date: '2016-07-29T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf35',
        },
        startDate: {
          $date: '2011-04-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf36',
        },
        startDate: {
          $date: '2011-10-01T00:00:00.000+02:00',
        },
        bio: 'is an associate director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf37',
        },
        startDate: {
          $date: '2020-10-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director, Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf38',
        },
        startDate: {
          $date: '2022-12-19T00:00:00.000+01:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf39',
        },
        startDate: {
          $date: '2022-12-19T00:00:00.000+01:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf3a',
        },
        startDate: {
          $date: '2022-12-19T00:00:00.000+01:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf3b',
        },
        startDate: {
          $date: '2022-12-19T00:00:00.000+01:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf3c',
        },
        startDate: {
          $date: '2022-12-19T00:00:00.000+01:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf3d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf3e',
        },
        startDate: {
          $date: '2011-07-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf3f',
        },
        startDate: {
          $date: '2015-04-01T00:00:00.000+02:00',
        },
        bio: 'is an Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf40',
        },
        startDate: {
          $date: '2024-04-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf41',
        },
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
        bio: 'is associate director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf42',
        },
        startDate: {
          $date: '2020-04-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baba2632747d67cb1e6',
        },
        bio: 'is an Associate Director of the Biblical Research Institute',
        startDate: {
          $date: '2011-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb3ec',
        },
        bio: 'is an associate director of the Biblical Research Institute and editor of Reflections',
        startDate: {
          $date: '2010-01-01T00:00:00.000+01:00',
        },
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb45a',
        },
        bio: 'is associate director of the Biblical Research Institute and editor of Reflections',
        startDate: {
          $date: '2010-01-01T00:00:00.000+01:00',
        },
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb832',
        },
        bio: 'is Associate Director of the Biblical Research Institute',
        startDate: {
          $date: '2018-04-16T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb0a2632747d67cc7f4',
        },
        bio: 'Biblical Research Institute',
        startDate: {
          $date: '2020-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd4b1',
        },
        bio: 'is associate director of the Biblical Research Institute and editor of Reflections',
        startDate: {
          $date: '2010-01-01T00:00:00.000+01:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a8f',
    },
    fullName: 'Gerhard Pfandl',
    importIDs: [
      {
        recordID: '57SR',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '8',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf43',
        },
        startDate: {
          $date: '2015-01-26T00:00:00.000+01:00',
        },
        bio: 'Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf44',
        },
        startDate: {
          $date: '2014-07-08T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf45',
        },
        startDate: {
          $date: '2011-10-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf46',
        },
        startDate: {
          $date: '2022-06-22T00:00:00.000+02:00',
        },
        bio: 'Associate Director (ret.) Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf47',
        },
        startDate: {
          $date: '2020-01-01T00:00:00.000+01:00',
        },
        bio: 'Associate Director (retired) Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf48',
        },
        startDate: {
          $date: '2022-06-22T00:00:00.000+02:00',
        },
        bio: 'Associate Director (ret.) Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf49',
        },
        startDate: {
          $date: '2015-01-26T00:00:00.000+01:00',
        },
        bio: 'Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf4a',
        },
        startDate: {
          $date: '2003-02-13T00:00:00.000+01:00',
        },
        bio: 'is an Associate Director of the Biblical Research Institute. He holds M.A. and Ph.D. degrees in Old Testament from Andrews University. A native of Austria, he has worked as a pastor in Austria and in the Southern California Conference. From 1977-1989 he was Professor of Religion at Bogenhofen Seminary in Austria. Prior to joining the Biblical Research Institute in 1999, he served for seven years as Field Secretary of the South Pacific Division in Sydney. He has published many articles for scholarly and popular journals in German and English and is the author of vol. 1 of the Adventist Theological Society Dissertation Series, The Time of the End in the Book of Daniel. <EMAIL>',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf4b',
        },
        startDate: {
          $date: '2013-07-01T00:00:00.000+02:00',
        },
        bio: 'is Retired Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf4c',
        },
        startDate: {
          $date: '2011-10-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf4d',
        },
        startDate: {
          $date: '2011-10-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf4e',
        },
        startDate: {
          $date: '2021-12-09T00:00:00.000+01:00',
        },
        bio: 'Former Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baba2632747d67cb28d',
        },
        bio: 'Biblical Research Institute, Silver Spring, MD',
        startDate: {
          $date: '1999-06-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bada2632747d67cb8f1',
        },
        bio: 'is an associate director of the Biblical Research Institute',
        startDate: {
          $date: '2010-07-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb3a2632747d67cd1bf',
        },
        bio: 'is Retired Associate Director of the Biblical Research Institute',
        startDate: {
          $date: '2016-01-13T00:00:00.000+01:00',
        },
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd5b1',
        },
        bio: 'is an associate director of the Biblical Research Institute.',
        startDate: {
          $date: '2011-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd61a',
        },
        bio: 'is an Associate Director of the Biblical Research Institute',
        startDate: {
          $date: '2011-07-01T00:00:00.000+02:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf58',
    },
    fullName: 'Kwabena Donkor',
    slug: 'kwabena-donkor',
    roles: ['author'],
    importIDs: [
      {
        recordID: '9',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf4f',
        },
        startDate: {
          $date: '2009-07-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf50',
        },
        startDate: {
          $date: '2009-07-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf51',
        },
        startDate: {
          $date: '2017-10-17T00:00:00.000+02:00',
        },
        bio: 'is an Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf52',
        },
        startDate: {
          $date: '2018-01-09T00:00:00.000+01:00',
        },
        bio: 'is Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf53',
        },
        startDate: {
          $date: '2020-04-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf54',
        },
        startDate: {
          $date: '2004-10-01T00:00:00.000+02:00',
        },
        bio: 'Ontario Conference',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf55',
        },
        startDate: {
          $date: '2018-01-03T00:00:00.000+01:00',
        },
        bio: 'is Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf56',
        },
        startDate: {
          $date: '2020-04-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf57',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baba2632747d67cb1fe',
        },
        bio: 'an associate director of the Biblical Research Institute in Silver Spring, Maryland',
        startDate: {
          $date: '2007-01-01T00:00:00.000+01:00',
        },
      },
      {
        _id: {
          $oid: '68249baea2632747d67cc09a',
        },
        bio: 'Associate Director, Biblical Research Institute',
        startDate: {
          $date: '2020-04-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'tWL1747131006210.jpg',
      size: 127551,
      originalFilename: 'Donkor-Kwabena',
      width: 806,
      height: 1132,
      blurhash: 'LIGbbiofcuxvtTo2yENF0eM{VDR*',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.252+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.252+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf60',
    },
    fullName: 'Elias Brasil de Souza',
    slug: 'elias-brasil-de-souza',
    roles: ['author'],
    importIDs: [
      {
        recordID: '10',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf59',
        },
        startDate: {
          $date: '2017-04-12T00:00:00.000+02:00',
        },
        bio: 'is Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf5a',
        },
        startDate: {
          $date: '2017-04-01T00:00:00.000+02:00',
        },
        bio: 'is Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf5b',
        },
        startDate: {
          $date: '2015-10-27T00:00:00.000+01:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf5c',
        },
        startDate: {
          $date: '2020-04-01T00:00:00.000+02:00',
        },
        bio: 'Director, Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf5d',
        },
        startDate: {
          $date: '2015-10-01T00:00:00.000+02:00',
        },
        bio: 'is an associate director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf5e',
        },
        startDate: {
          $date: '2015-10-01T00:00:00.000+02:00',
        },
        bio: 'is an associate director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf5f',
        },
        startDate: {
          $date: '2013-04-01T00:00:00.000+02:00',
        },
        bio: 'is an Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb759',
        },
        bio: 'de Souza é diretor do Biblical Research Institute',
        startDate: {
          $date: '2014-04-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bada2632747d67cbc1c',
        },
        bio: 'é diretor do Biblical Research Institute.',
        startDate: {
          $date: '2017-04-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'K8Y1747131006617.jpg',
      size: 97923,
      originalFilename: 'Brasil-de-Souza-Elias',
      width: 974,
      height: 1204,
      blurhash: 'L7G8TP#R0;Tf^Orq*00L00E2*w%M',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.253+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.253+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf63',
    },
    fullName: 'Raúl Esperante',
    slug: 'raul-esperante',
    roles: ['author'],
    importIDs: [
      {
        recordID: '11',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf61',
        },
        startDate: {
          $date: '2004-01-01T00:00:00.000+01:00',
        },
        bio: 'Geoscience Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf62',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Geoscience Research Institute',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.253+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.253+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf66',
    },
    fullName: 'Elaine G. Kennedy',
    slug: 'elaine-g-kennedy',
    roles: ['author'],
    importIDs: [
      {
        recordID: '12',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf64',
        },
        startDate: {
          $date: '2004-10-01T00:00:00.000+02:00',
        },
        bio: 'Geoscience Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf65',
        },
        startDate: {
          $date: '2006-01-01T00:00:00.000+01:00',
        },
        bio: 'formerly at GRI',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.254+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.254+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a72',
    },
    fullName: 'Michael G. Hasel',
    importIDs: [
      {
        recordID: '87RY',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '13',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf67',
        },
        startDate: {
          $date: '2017-04-10T00:00:00.000+02:00',
        },
        bio: 'Director, Institute of Archaeology, Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf68',
        },
        startDate: {
          $date: '2011-10-01T00:00:00.000+02:00',
        },
        bio: 'is Professor of Near Eastern Studies and Archaeology in the School of Religion, Director of the Institute of Archaeology, and Curator of the Lynn H. Wood Archaeological Museum at Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf69',
        },
        startDate: {
          $date: '2005-04-01T00:00:00.000+02:00',
        },
        bio: 'Southern Adventist University and Lynn H. Wood Museum',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf6a',
        },
        startDate: {
          $date: '2003-07-01T00:00:00.000+02:00',
        },
        bio: 'Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf6b',
        },
        startDate: {
          $date: '2006-04-01T00:00:00.000+02:00',
        },
        bio: 'Southern Adventist University and Lynn H. Wood Archaeological Museum',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf6c',
        },
        startDate: {
          $date: '2007-10-01T00:00:00.000+02:00',
        },
        bio: 'Southern Adventist University and Curator of the Lynn Wood Museum',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf6d',
        },
        startDate: {
          $date: '2022-12-19T00:00:00.000+01:00',
        },
        bio: 'Professor of Near Eastern Studies and Archaeology, Director of the Institute of Archaeology at Southern Adventsit University',
      },
      {
        _id: {
          $oid: '68249baea2632747d67cbd50',
        },
        bio: 'is Professor of Near Eastern Studies and Archaeology in the School of Religion, Director of the Institute of Archaeology, and Curator of the Lynn H. Wood Archaeological Museum at Southern Adventist University',
        startDate: {
          $date: '2011-10-01T00:00:00.000+02:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf72',
    },
    fullName: 'Gerald A. Klingbeil',
    slug: 'gerald-a-klingbeil',
    roles: ['author'],
    importIDs: [
      {
        recordID: '14',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf6e',
        },
        startDate: {
          $date: '2017-07-27T00:00:00.000+02:00',
        },
        bio: 'Associate Editor, Adventist Review; Research Professor, Old Testament and Ancient Near Eastern Studies, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf6f',
        },
        startDate: {
          $date: '2005-01-01T00:00:00.000+01:00',
        },
        bio: 'River Plate Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf70',
        },
        startDate: {
          $date: '2006-01-01T00:00:00.000+01:00',
        },
        bio: 'AIIAS',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf71',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Ellen G. White Estate; Adventist Review/Adventist World',
      },
      {
        _id: {
          $oid: '68249bb3a2632747d67cd1bd',
        },
        bio: 'Associate Editor, Adventist Review; Research Professor, Old Testament and Ancient Near Eastern Studies, Andrews University',
        startDate: {
          $date: '2017-07-01T00:00:00.000+02:00',
        },
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.255+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.255+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf74',
    },
    fullName: 'Fernando Canale',
    slug: 'fernando-canale',
    roles: ['author'],
    importIDs: [
      {
        recordID: '15',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf73',
        },
        startDate: {
          $date: '2005-10-01T00:00:00.000+02:00',
        },
        bio: 'Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.255+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.255+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf78',
    },
    fullName: 'Merlin D. Burt',
    slug: 'merlin-d-burt',
    roles: ['author'],
    importIDs: [
      {
        recordID: '16',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf75',
        },
        startDate: {
          $date: '2008-04-01T00:00:00.000+02:00',
        },
        bio: 'E. G. White Estate Branch Office, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf76',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf77',
        },
        startDate: {
          $date: '2008-01-01T00:00:00.000+01:00',
        },
        bio: 'Ellen G. White Estate, Branch Office Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.255+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.255+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf7b',
    },
    fullName: 'Frank B. Holbrook',
    slug: 'frank-b-holbrook',
    roles: ['author'],
    importIDs: [
      {
        recordID: '17',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf79',
        },
        startDate: {
          $date: '2013-02-19T00:00:00.000+01:00',
        },
        bio: 'Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf7a',
        },
        startDate: {
          $date: '1988-03-03T00:00:00.000+01:00',
        },
        bio: 'is associate director of the Biblical Research Institute at the General Conference.',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.256+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.256+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a30',
    },
    fullName: 'Jon Paulien',
    importIDs: [
      {
        recordID: '77Q2',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '18',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf7c',
        },
        startDate: {
          $date: '2006-01-01T00:00:00.000+01:00',
        },
        bio: 'Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf7d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Loma Linda University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf82',
    },
    fullName: 'George W. Reid',
    slug: 'george-w-reid',
    roles: ['author'],
    importIDs: [
      {
        recordID: '19',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf7e',
        },
        startDate: {
          $date: '1999-10-18T00:00:00.000+02:00',
        },
        bio: 'Former Director Biblical Research Institute, General Conference',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf7f',
        },
        startDate: {
          $date: '2007-01-01T00:00:00.000+01:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf80',
        },
        startDate: {
          $date: '2007-01-01T00:00:00.000+01:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf81',
        },
        startDate: {
          $date: '2008-07-01T00:00:00.000+02:00',
        },
        bio: 'Former Director of BRI',
      },
      {
        _id: {
          $oid: '68249bb0a2632747d67cc739',
        },
        bio: 'former director BRI',
        startDate: {
          $date: '2007-01-01T00:00:00.000+01:00',
        },
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.256+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.256+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf84',
    },
    fullName: 'Johannes Kovar',
    slug: 'johannes-kovar',
    roles: ['author'],
    importIDs: [
      {
        recordID: '20',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf83',
        },
        startDate: {
          $date: '2008-01-01T00:00:00.000+01:00',
        },
        bio: 'Seminar Bogenhofen',
      },
      {
        _id: {
          $oid: '68249baba2632747d67cb232',
        },
        bio: 'Seminário Bogenhofen',
        startDate: {
          $date: '2008-01-01T00:00:00.000+01:00',
        },
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.256+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.256+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf87',
    },
    fullName: 'Chad Stuart',
    slug: 'chad-stuart',
    roles: ['author'],
    importIDs: [
      {
        recordID: '21',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf85',
        },
        startDate: {
          $date: '2017-01-04T00:00:00.000+01:00',
        },
        bio: 'is Senior Pastor of the Spencerville Seventh-day Adventist Church',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf86',
        },
        startDate: {
          $date: '2017-01-03T00:00:00.000+01:00',
        },
        bio: 'is Senior Pastor of the Spencerville Seventh-day Adventist Church',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'nhY1747131007069.jpg',
      size: 119825,
      originalFilename: 'Stuart-Chad',
      width: 964,
      height: 1132,
      blurhash: 'LBH-x^-p0000@EES^$s;0uXSM+M{',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.256+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.256+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf8b',
    },
    fullName: 'Lincoln E. Steed',
    slug: 'lincoln-e-steed',
    roles: ['author'],
    importIDs: [
      {
        recordID: '22',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf88',
        },
        startDate: {
          $date: '2016-04-01T00:00:00.000+02:00',
        },
        bio: 'is Liberty magazine editor and Associate Director of the Public Affairs and Religious Liberty Department for the North American Division',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf89',
        },
        startDate: {
          $date: '2007-01-01T00:00:00.000+01:00',
        },
        bio: 'Editor Liberty Magazine',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf8a',
        },
        startDate: {
          $date: '2008-07-01T00:00:00.000+02:00',
        },
        bio: 'Liberty Magazine',
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb828',
        },
        bio: 'is Liberty magazine editor and Associate Director of the Public Affairs',
        startDate: {
          $date: '2016-04-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'WIQ1747131007283.jpg',
      size: 100460,
      originalFilename: 'Steed-Lincoln-E',
      width: 976,
      height: 1130,
      blurhash: 'LKFq^2~W0L0eMcIUNdx]AtODwcr?',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.257+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.257+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf90',
    },
    fullName: 'John Graz',
    slug: 'john-graz',
    roles: ['author'],
    importIDs: [
      {
        recordID: '23',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf8c',
        },
        startDate: {
          $date: '2007-07-01T00:00:00.000+02:00',
        },
        bio: 'Public Affairs and Religious Liberty',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf8d',
        },
        startDate: {
          $date: '2012-04-01T00:00:00.000+02:00',
        },
        bio: 'PARL and IRLA',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf8e',
        },
        startDate: {
          $date: '2015-07-21T00:00:00.000+02:00',
        },
        bio: 'Director of Public Affairs and Religious Liberty Department of the General Conference of Seventh-day Adventists',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf8f',
        },
        startDate: {
          $date: '2015-07-01T00:00:00.000+02:00',
        },
        bio: 'Director of Public Affairs and Religious Liberty Department of the General Conference of Seventh-day Adventists',
      },
      {
        _id: {
          $oid: '68249bb2a2632747d67ccc76',
        },
        bio: 'diretor do Departamento de Relações Públicas e Liberdade Religiosa da Conferência Geral dos Adventistas do Sétimo Dia',
        startDate: {
          $date: '2015-07-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'srW1747131006854.jpg',
      size: 20512,
      originalFilename: 'Graz-John',
      width: 336,
      height: 412,
      blurhash: 'L7FrbM}@0000^cxCyrE10LNb{d-;',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.257+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.257+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a62',
    },
    fullName: 'Roy E. Gane',
    importIDs: [
      {
        recordID: 'A7RI',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '24',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf91',
        },
        startDate: {
          $date: '2014-07-22T00:00:00.000+02:00',
        },
        bio: 'is Professor of Hebrew Bible and Ancient Near Eastern Languages at the Seventh-day Adventist Theological Seminary',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf92',
        },
        startDate: {
          $date: '2007-08-06T00:00:00.000+02:00',
        },
        bio: 'Prof. of Hebrew Bible and Ancient Near Eastern Languages Director, Ph.D./Th.D. and M.Th. Programs, Seventh-day Adventist Theological Seminary, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf93',
        },
        startDate: {
          $date: '2003-01-01T00:00:00.000+01:00',
        },
        bio: 'Andrews University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf95',
    },
    fullName: 'Jud Lake',
    slug: 'jud-lake',
    roles: ['author'],
    importIDs: [
      {
        recordID: '25',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf94',
        },
        startDate: {
          $date: '2005-10-01T00:00:00.000+02:00',
        },
        bio: 'Southern Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.257+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.257+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf97',
    },
    fullName: 'Leonard R. Brand',
    slug: 'leonard-r-brand',
    roles: ['author'],
    importIDs: [
      {
        recordID: '26',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf96',
        },
        startDate: {
          $date: '2007-04-01T00:00:00.000+02:00',
        },
        bio: 'Loma Linda University',
      },
      {
        _id: {
          $oid: '68249bafa2632747d67cc1dc',
        },
        bio: 'Universidade de Loma Linda',
        startDate: {
          $date: '2007-04-01T00:00:00.000+02:00',
        },
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.257+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.257+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf9a',
    },
    fullName: 'Woodrow W. Whidden',
    slug: 'woodrow-w-whidden',
    roles: ['author'],
    importIDs: [
      {
        recordID: '27',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf98',
        },
        startDate: {
          $date: '2008-04-01T00:00:00.000+02:00',
        },
        bio: 'AIIAS',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caf99',
        },
        startDate: {
          $date: '2005-04-18T00:00:00.000+02:00',
        },
        bio: 'Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf9b',
    },
    fullName: 'Ellen G. White',
    slug: 'ellen-g-white',
    roles: ['author'],
    importIDs: [
      {
        recordID: '28',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf9c',
    },
    fullName: 'G. Engel',
    slug: 'g-engel',
    roles: ['author'],
    importIDs: [
      {
        recordID: '29',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caf9e',
    },
    fullName: 'Harold G. Coffin',
    slug: 'harold-g-coffin',
    roles: ['author'],
    importIDs: [
      {
        recordID: '30',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf9d',
        },
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
        bio: 'a senior research scientist at the Geoscience Research Institute for 27 years, writes from Calhoun, Georgia.',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafa3',
    },
    fullName: 'Richard M. Davidson',
    slug: 'richard-m-davidson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '31',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caf9f',
        },
        startDate: {
          $date: '2009-07-01T00:00:00.000+02:00',
        },
        bio: 'is J. N. Andrews Professor of Old Testament Interpretation and Chair of the Old Testament Department at the Seventh-day Adventist Theological Seminary, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafa0',
        },
        startDate: {
          $date: '2003-05-20T00:00:00.000+02:00',
        },
        bio: 'Seventh-day Adventist Theological Seminary, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafa1',
        },
        startDate: {
          $date: '2011-10-01T00:00:00.000+02:00',
        },
        bio: 'is J. N. Andrews Professor of Old Testament Interpretation at the Seventh-day Adventist Theological Seminary, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafa2',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'S891747131007485.jpg',
      size: 76617,
      originalFilename: 'Davidson-Richard-M',
      width: 742,
      height: 820,
      blurhash: 'LDD[62AC00XS|qNxX9bG0}xG%Len',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafa5',
    },
    fullName: 'JoAnn Davidson',
    slug: 'jo-ann-davidson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '32',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafa4',
        },
        startDate: {
          $date: '2017-01-04T00:00:00.000+01:00',
        },
        bio: 'is Professor of Theology at Andrews University',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: '7gS1747131008085.jpg',
      size: 147747,
      originalFilename: 'Davidson-JoAnn',
      width: 974,
      height: 1128,
      blurhash: 'LRD[?_9b0#~AQ;j[NHt6B5xa-UE2',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.258+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafa7',
    },
    fullName: 'William H. Shea',
    slug: 'william-h-shea',
    roles: ['author'],
    importIDs: [
      {
        recordID: '33',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafa6',
        },
        startDate: {
          $date: '1988-12-01T00:00:00.000+01:00',
        },
        bio: 'Former Associate Director, Biblical Research Institute',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.259+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.259+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafaa',
    },
    fullName: 'Efrain Velasquez',
    slug: 'efrain-velasquez',
    roles: ['author'],
    importIDs: [
      {
        recordID: '34',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafa8',
        },
        startDate: {
          $date: '2008-01-01T00:00:00.000+01:00',
        },
        bio: 'Adventist University of Antilles',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafa9',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Inter-American Adventist Theological Seminary',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.259+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.259+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafac',
    },
    fullName: 'George R. Knight',
    slug: 'george-r-knight',
    roles: ['author'],
    importIDs: [
      {
        recordID: '35',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafab',
        },
        startDate: {
          $date: '2016-04-18T00:00:00.000+02:00',
        },
        bio: 'is a retired professor of church history at the theological seminary at Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.259+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.259+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafaf',
    },
    fullName: 'Merling Alomía',
    slug: 'merling-alomia',
    roles: ['author'],
    importIDs: [
      {
        recordID: '36',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafad',
        },
        startDate: {
          $date: '2016-10-01T00:00:00.000+02:00',
        },
        bio: 'Peruvian Union University, Lima, Peru',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafae',
        },
        startDate: {
          $date: '2015-04-01T00:00:00.000+02:00',
        },
        bio: 'Peruvian Union University',
      },
      {
        _id: {
          $oid: '68249baca2632747d67cb8b2',
        },
        bio: 'Peruvian Union University, Lima, Peru',
        startDate: {
          $date: '2016-10-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd4ae',
        },
        bio: 'Peruvian Union University Lima, Peru',
        startDate: {
          $date: '2016-10-01T00:00:00.000+02:00',
        },
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.259+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.259+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a89',
    },
    fullName: 'Frank M. Hasel',
    importIDs: [
      {
        recordID: 'I7SL',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '37',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafb0',
        },
        startDate: {
          $date: '2020-04-01T00:00:00.000+02:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafb1',
        },
        startDate: {
          $date: '2024-01-01T00:00:00.000+01:00',
        },
        bio: 'Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafb2',
        },
        startDate: {
          $date: '2019-05-28T00:00:00.000+02:00',
        },
        bio: 'is Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafb3',
        },
        startDate: {
          $date: '2008-07-01T00:00:00.000+02:00',
        },
        bio: 'Seminar Bogenhofen',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafb4',
        },
        startDate: {
          $date: '2022-06-22T00:00:00.000+02:00',
        },
        bio: 'Associate Director Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249bada2632747d67cb8ff',
        },
        bio: 'Associate Director, Biblical Research Institute',
        startDate: {
          $date: '2018-01-03T00:00:00.000+01:00',
        },
      },
      {
        _id: {
          $oid: '68249bb3a2632747d67cd1f5',
        },
        bio: 'Associate Director of the Biblical Research Institute',
        startDate: {
          $date: '2019-07-01T00:00:00.000+02:00',
        },
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd666',
        },
        bio: 'Associate Director at the Biblical Research Institute',
        startDate: {
          $date: '2021-12-09T00:00:00.000+01:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '67f682992428e63c4e994cca',
    },
    firstName: 'Michael',
    middleName: 'W.',
    lastName: 'Campbell',
    fullName: '',
    importIDs: [
      {
        recordID: '38',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafb5',
        },
        startDate: {
          $date: '2017-10-16T00:00:00.000+02:00',
        },
        bio: 'is a professor at Adventist International Institute for Advanced Studies (AIIAS) in Silang, Cavite, Philippines',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafb6',
        },
        startDate: {
          $date: '2014-01-01T00:00:00.000+01:00',
        },
        bio: 'is Assistant Professor of Historical/Theological Studies at the Adventist International Institute for Advanced Studies (AIIAS) in Silang, Cavite, Philippines',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafb7',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafb8',
        },
        startDate: {
          $date: '2007-04-01T00:00:00.000+02:00',
        },
        bio: 'Loma Linda University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafb9',
        },
        startDate: {
          $date: '2016-07-01T00:00:00.000+02:00',
        },
        bio: 'is a Professor at Adventist International Institute for Advanced Studies (AIIAS) in Silang, Cavite, Philippines',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafba',
        },
        startDate: {
          $date: '2012-07-01T00:00:00.000+02:00',
        },
        bio: 'SDA Pastor in Kansas',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafbb',
        },
        startDate: {
          $date: '2014-10-01T00:00:00.000+02:00',
        },
        bio: 'is Assistant Professor of Historical/Theological Studies at the Adventist International Institute for Advanced Studies (AIIAS) in Silang, Cavite, Philippines',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafbc',
        },
        startDate: {
          $date: '2009-07-01T00:00:00.000+02:00',
        },
        bio: 'Rocky Mountain Conference',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafbe',
    },
    fullName: 'Clifford Goldstein',
    slug: 'clifford-goldstein',
    roles: ['author'],
    importIDs: [
      {
        recordID: '39',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafbd',
        },
        startDate: {
          $date: '2018-04-16T00:00:00.000+02:00',
        },
        bio: 'Editor Sabbath School Quarterly',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.260+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.260+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafc3',
    },
    fullName: 'Stephen Bauer',
    slug: 'stephen-bauer',
    roles: ['author'],
    importIDs: [
      {
        recordID: '40',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafbf',
        },
        startDate: {
          $date: '2008-10-01T00:00:00.000+02:00',
        },
        bio: 'is Associate Professor of Theology and Ethics at Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafc0',
        },
        startDate: {
          $date: '2015-01-08T00:00:00.000+01:00',
        },
        bio: 'is Professor of Theology and Ethics at Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafc1',
        },
        startDate: {
          $date: '2015-01-26T00:00:00.000+01:00',
        },
        bio: 'is Professor of Theology and Ethics at Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafc2',
        },
        startDate: {
          $date: '2011-04-01T00:00:00.000+02:00',
        },
        bio: 'is Professor of Theology and Ethics at Southern Adventist University.',
      },
      {
        _id: {
          $oid: '68249bada2632747d67cbb99',
        },
        bio: 'Southern Adventist University',
        startDate: {
          $date: '2008-10-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'V361747131008464.jpg',
      size: 25872,
      originalFilename: 'Bauer-Stephen',
      width: 446,
      height: 482,
      blurhash: 'L7H0R.%L025R025S~U^O1i-U;1EM',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a3c',
    },
    fullName: 'Ivan Milanov',
    importIDs: [
      {
        recordID: '17QE',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '41',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafc4',
        },
        startDate: {
          $date: '2018-06-05T00:00:00.000+02:00',
        },
        bio: 'TED, Newbold College of Higher Education',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafc5',
    },
    fullName: 'Edward Heppenstall',
    slug: 'edward-heppenstall',
    roles: ['author'],
    importIDs: [
      {
        recordID: '42',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafc8',
    },
    fullName: 'Randall W. Younker',
    slug: 'randall-w-younker',
    roles: ['author'],
    importIDs: [
      {
        recordID: '43',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafc6',
        },
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
        bio: 'is Professor of Old Testament and Biblical Archaeology at the Seventh-day Adventist Theological Seminary and Director of the Institute of Archaeology, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafc7',
        },
        startDate: {
          $date: '2007-07-01T00:00:00.000+02:00',
        },
        bio: 'Institute of Archaeology Andrews University',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: '3PQ1747131008665.jpg',
      size: 42469,
      originalFilename: 'Younker-Randall-W',
      width: 578,
      height: 644,
      blurhash: 'LFCYwDo}BB}[%1t7xvs.EgxGrqEf',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafca',
    },
    fullName: 'Mark A. Finley',
    slug: 'mark-a-finley',
    roles: ['author'],
    importIDs: [
      {
        recordID: '44',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafc9',
        },
        startDate: {
          $date: '2017-07-27T00:00:00.000+02:00',
        },
        bio: 'Assistant to the President for Evangelism, General Conference of Seventh-day Adventists',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafce',
    },
    fullName: 'P. Gerard Damsteegt',
    slug: 'p-gerard-damsteegt',
    roles: ['author'],
    importIDs: [
      {
        recordID: '45',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafcb',
        },
        startDate: {
          $date: '2018-07-01T00:00:00.000+02:00',
        },
        bio: 'Church History Professor (ret.), Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafcc',
        },
        startDate: {
          $date: '2004-10-01T00:00:00.000+02:00',
        },
        bio: 'Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafcd',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University (retired)',
      },
      {
        _id: {
          $oid: '68249baea2632747d67cbe2a',
        },
        bio: 'Church History Professor (ret.), Andrews University',
        startDate: {
          $date: '2018-07-01T00:00:00.000+02:00',
        },
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafd2',
    },
    fullName: 'Greg A. King',
    slug: 'greg-a-king',
    roles: ['author'],
    importIDs: [
      {
        recordID: '46',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafcf',
        },
        startDate: {
          $date: '2018-07-01T00:00:00.000+02:00',
        },
        bio: 'is the Dean of the School of Religion and Professor of Biblical Studies at Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafd0',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafd1',
        },
        startDate: {
          $date: '2004-01-01T00:00:00.000+01:00',
        },
        bio: 'Pacific Union College',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'Bw21747131008851.jpg',
      size: 98103,
      originalFilename: 'King-Greg-A',
      width: 974,
      height: 980,
      blurhash: 'L8EfEO$c1aKR{akZE-RN0[Ri,:xt',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.261+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafd4',
    },
    fullName: 'Leandro J. Velardo',
    slug: 'leandro-j-velardo',
    roles: ['author'],
    importIDs: [
      {
        recordID: '47',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafd3',
        },
        startDate: {
          $date: '2020-10-01T00:00:00.000+02:00',
        },
        bio: 'Professor of New Testament Studies at the Faculty of Theology, Universidad Adventista del Plata, Argentina.',
      },
      {
        _id: {
          $oid: '68249bb3a2632747d67cd18a',
        },
        bio: 'Professor of New Testament Studies at the Faculty of Theology, Universidad Adventista del Plata, Argentina.',
        startDate: {
          $date: '2020-10-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'LFS1747131008995.jpg',
      size: 101280,
      originalFilename: 'Velardo-Leandro-J',
      width: 844,
      height: 918,
      blurhash: 'LuLg-8az?cof~qof-;ayR*axMxay',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafd6',
    },
    fullName: 'Tim Aka',
    slug: 'tim-aka',
    roles: ['author'],
    importIDs: [
      {
        recordID: '49',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafd5',
        },
        startDate: {
          $date: '2019-07-01T00:00:00.000+02:00',
        },
        bio: 'CFA, MBA Associate Treasurer General Conference of Seventh-day Adventist',
      },
      {
        _id: {
          $oid: '68249bb3a2632747d67cd218',
        },
        bio: 'Associate Treasurer General Conference of Seventh-day Adventist',
        startDate: {
          $date: '2019-07-01T00:00:00.000+02:00',
        },
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a57',
    },
    fullName: 'Dragoslava Santrac',
    importIDs: [
      {
        recordID: '77R5',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '50',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafd7',
        },
        startDate: {
          $date: '2020-09-17T00:00:00.000+02:00',
        },
        bio: 'managing editor of the Encyclopedia of Seventh-day Adventists',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafd9',
    },
    fullName: 'Audrey Andersson',
    slug: 'audrey-andersson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '51',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafd8',
        },
        startDate: {
          $date: '2024-01-01T00:00:00.000+01:00',
        },
        bio: 'General Conference Vice President, Human Sexuality Taskforce Chair',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'qWC1747131009202.jpg',
      size: 26575,
      originalFilename: 'Andersson-Audrey',
      width: 520,
      height: 594,
      blurhash: 'LrM7ZQr=t.XS~qjExut7IUofROWB',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafdb',
    },
    fullName: 'Mario Veloso',
    slug: 'mario-veloso',
    roles: ['author'],
    importIDs: [
      {
        recordID: '52',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafda',
        },
        startDate: {
          $date: '2020-10-01T00:00:00.000+02:00',
        },
        bio: 'Former Associate Secretary of the General Conference',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'FXT1747131009574.jpg',
      size: 56479,
      originalFilename: 'Veloso-Mario',
      width: 632,
      height: 734,
      blurhash: 'LAF=OHM_00-;+D-q9t9Z1*oJ}XNG',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafdd',
    },
    fullName: 'William Fagal',
    slug: 'william-fagal',
    roles: ['author'],
    importIDs: [
      {
        recordID: '53',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafdc',
        },
        startDate: {
          $date: '2020-07-01T00:00:00.000+02:00',
        },
        bio: 'retired Editor and Associate Director for the Ellen G. White Estate. He continues to work part-time for the White Estate.',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'r4I1747131009979.jpg',
      size: 59120,
      originalFilename: 'Fagal-William',
      width: 850,
      height: 988,
      blurhash: 'LDEVBL=|0e0e}]xGpINw9]S2VXxG',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a2f',
    },
    fullName: 'Laszlo Gallusz',
    importIDs: [
      {
        recordID: 'G7Q1',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '54',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafde',
        },
        startDate: {
          $date: '2020-07-01T00:00:00.000+02:00',
        },
        bio: 'Senior Lecturer in New Testament Studies at Newbold College, England',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafdf',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'TED, South East European Union Conference',
      },
      {
        _id: {
          $oid: '68249bb1a2632747d67cc86d',
        },
        bio: 'Senior Lecturer in New Testament Studies at Newbold College, England',
        startDate: {
          $date: '2020-07-01T00:00:00.000+02:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafe0',
    },
    fullName: 'Kenneth Gage',
    slug: 'kenneth-gage',
    roles: ['author'],
    importIDs: [
      {
        recordID: '55',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafe1',
    },
    fullName: 'Jan Paulsen',
    slug: 'jan-paulsen',
    roles: ['author'],
    importIDs: [
      {
        recordID: '56',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafe2',
    },
    fullName: 'Tim Crosby',
    slug: 'tim-crosby',
    roles: ['author'],
    importIDs: [
      {
        recordID: '57',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.262+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a29',
    },
    fullName: 'Denis Kaiser',
    importIDs: [
      {
        recordID: '77PU',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '58',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafe3',
        },
        startDate: {
          $date: '2017-10-17T00:00:00.000+02:00',
        },
        bio: 'is Assistant Professor of Church History at the Seventh-day Adventist Theological Seminary at Andrews University and the Annotation Project Editor for the Ellen G. White Estate.',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafe4',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafe6',
    },
    fullName: 'Arthur L. White',
    slug: 'arthur-l-white',
    roles: ['author'],
    importIDs: [
      {
        recordID: '59',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafe5',
        },
        startDate: {
          $date: '1990-01-01T00:00:00.000+01:00',
        },
        bio: 'Ellen G. White Estate Washington, D. C.',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafe9',
    },
    fullName: 'Ann Gibson',
    slug: 'ann-gibson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '60',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafe7',
        },
        startDate: {
          $date: '2015-10-27T00:00:00.000+01:00',
        },
        bio: 'holds a PhD in Accounting, is an Emerita Professor of Accounting at Andrews University and former dean of the AU School of Business. Presently she is Assistant to the General Conference Treasurer for Treasurer Training.',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cafe8',
        },
        startDate: {
          $date: '2021-09-01T00:00:00.000+02:00',
        },
        bio: 'Vice President of Finance Adventist Development and Relief Agency (ADRA), Professor Emerita School of Business Administration, Andrews University',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: '8Mm1747131010693.jpg',
      size: 53329,
      originalFilename: 'Gibson-Ann',
      width: 564,
      height: 766,
      blurhash: 'LGI4tw?w0f4o01wbniXRKgxZ=yt7',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafeb',
    },
    fullName: 'Hyunsok John Doh',
    slug: 'hyunsok-john-doh',
    roles: ['author'],
    importIDs: [
      {
        recordID: '61',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafea',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Southern Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafed',
    },
    fullName: 'Patrick Etoughe Anani',
    slug: 'patrick-etoughe-anani',
    roles: ['author'],
    importIDs: [
      {
        recordID: '62',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafec',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'WAD, Adventist University Cosendai',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cafef',
    },
    fullName: 'Segundo Azo Salazar',
    slug: 'segundo-azo-salazar',
    roles: ['author'],
    importIDs: [
      {
        recordID: '63',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafee',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Peruvian Union University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.263+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caff1',
    },
    fullName: 'Feliks Ponyatovskiy',
    slug: 'feliks-ponyatovskiy',
    roles: ['author'],
    importIDs: [
      {
        recordID: '64',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caff0',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'ESD, Ukranian Adventist Center of Higher Education',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a47',
    },
    fullName: 'Denis Fortin',
    importIDs: [
      {
        recordID: '77QP',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '65',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caff2',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67caff3',
        },
        startDate: {
          $date: '2007-08-07T00:00:00.000+02:00',
        },
        bio: 'Dean and Professor of Theology Seventh-day Adventist Theological Seminary, Andrews University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caff5',
    },
    fullName: 'Peter Korave',
    slug: 'peter-korave',
    roles: ['author'],
    importIDs: [
      {
        recordID: '66',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caff4',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SPD, Pacific Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caff7',
    },
    fullName: 'Thomas Davai',
    slug: 'thomas-davai',
    roles: ['author'],
    importIDs: [
      {
        recordID: '67',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caff6',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SPD, Pacific Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caff9',
    },
    fullName: 'Joel Ricardo Turpo Chaparro',
    slug: 'joel-ricardo-turpo-chaparro',
    roles: ['author'],
    importIDs: [
      {
        recordID: '68',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caff8',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Peruvian Union University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caffa',
    },
    fullName: 'Benjamin Rand',
    slug: 'benjamin-rand',
    roles: ['author'],
    importIDs: [
      {
        recordID: '69',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.264+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caffc',
    },
    fullName: 'Miguel Luna',
    slug: 'miguel-luna',
    roles: ['author'],
    importIDs: [
      {
        recordID: '70',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caffb',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Peruvian Union University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67caffe',
    },
    fullName: 'Heraldo Vander Lopes',
    slug: 'heraldo-vander-lopes',
    roles: ['author'],
    importIDs: [
      {
        recordID: '71',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67caffd',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SID, Beria Seminary',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a28',
    },
    fullName: 'Sampson M. Nwaomah',
    importIDs: [
      {
        recordID: 'G7PT',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '72',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cafff',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist University of Africa',
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a41',
    },
    fullName: 'Jiří Moskala',
    importIDs: [
      {
        recordID: '17QJ',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '73',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb000',
        },
        startDate: {
          $date: '2019-07-01T00:00:00.000+02:00',
        },
        bio: 'Dean of the Theological Seminar at Andrews University and Professor of Old Testament Exegesis and Theology',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb001',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb002',
        },
        startDate: {
          $date: '2007-10-01T00:00:00.000+02:00',
        },
        bio: 'Andrews University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb004',
    },
    fullName: 'Kayle B. de Waal',
    slug: 'kayle-b-de-waal',
    roles: ['author'],
    importIDs: [
      {
        recordID: '74',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb003',
        },
        startDate: {
          $date: '2019-10-01T00:00:00.000+02:00',
        },
        bio: 'Ph.D. Postgraduate Course Convenor Avondale University College in New South Wales, Australia.',
      },
      {
        _id: {
          $oid: '68249bb1a2632747d67cc871',
        },
        bio: 'Postgraduate Course Convenor Avondale University College in New South Wales, Australia.',
        startDate: {
          $date: '2019-10-01T00:00:00.000+02:00',
        },
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'PqG1747131010936.jpg',
      size: 90458,
      originalFilename: 'de-Waal-Kayle-B',
      width: 734,
      height: 1022,
      blurhash: 'L+L;a9M{.8t7~qofxut7Rjt7M{WB',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a8d',
    },
    fullName: 'Zdravko Stefanovic',
    importIDs: [
      {
        recordID: 'G7SP',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '75',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb005',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Adventist University of Health Sciences',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb007',
    },
    fullName: 'Vincent L. Ramik',
    slug: 'vincent-l-ramik',
    roles: ['author'],
    importIDs: [
      {
        recordID: '76',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb006',
        },
        startDate: {
          $date: '1981-09-17T00:00:00.000+02:00',
        },
        bio: 'senior partner of Diller, Ramik & Wight, Ltd., specialists in patent, trademark, and copyright cases, Washington, D.C.',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb009',
    },
    fullName: 'Warren L. Johns',
    slug: 'warren-l-johns',
    roles: ['author'],
    importIDs: [
      {
        recordID: '77',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb008',
        },
        startDate: {
          $date: '1981-09-17T00:00:00.000+02:00',
        },
        bio: 'chief counsel of the Office of General Counsel, General Conference of SDA.',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb00b',
    },
    fullName: 'May-Ellen Colón',
    slug: 'may-ellen-colon',
    roles: ['author'],
    importIDs: [
      {
        recordID: '78',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb00a',
        },
        startDate: {
          $date: '2005-04-01T00:00:00.000+02:00',
        },
        bio: 'Sabbath School/Personal Ministries',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.265+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb010',
    },
    fullName: 'James D. Standish',
    slug: 'james-d-standish',
    roles: ['author'],
    importIDs: [
      {
        recordID: '79',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb00c',
        },
        startDate: {
          $date: '2005-07-01T00:00:00.000+02:00',
        },
        bio: 'Public Relations and Religions Liberty',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb00d',
        },
        startDate: {
          $date: '2004-07-01T00:00:00.000+02:00',
        },
        bio: 'Public Affairs and Religious Liberty',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb00e',
        },
        startDate: {
          $date: '2006-07-01T00:00:00.000+02:00',
        },
        bio: 'Public Relations and Religious Liberty',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb00f',
        },
        startDate: {
          $date: '2008-07-01T00:00:00.000+02:00',
        },
        bio: 'Public Affairs & Religious Liberty, General Conference',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb012',
    },
    fullName: 'Wolfgang Stefani',
    slug: 'wolfgang-stefani',
    roles: ['author'],
    importIDs: [
      {
        recordID: '80',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb011',
        },
        startDate: {
          $date: '2004-01-01T00:00:00.000+01:00',
        },
        bio: 'South Australian Conference',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb015',
    },
    fullName: 'Raoul Dederen',
    slug: 'raoul-dederen',
    roles: ['author'],
    importIDs: [
      {
        recordID: '81',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb013',
        },
        startDate: {
          $date: '2000-05-18T00:00:00.000+02:00',
        },
        bio: 'Andrews University, Berrien Springs, Michigan',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb014',
        },
        startDate: {
          $date: '1995-05-01T00:00:00.000+02:00',
        },
        bio: 'Dr.es-Sc. Mor. Professor emeritus and former dean Seventh-day Adventist Theological Seminary Andrews University, Berrien Springs, Michigan',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb016',
    },
    fullName: 'Peter M. van Bemmelen',
    slug: 'peter-m-van-bemmelen',
    roles: ['author'],
    importIDs: [
      {
        recordID: '82',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb017',
    },
    fullName: 'Bert B. Beach',
    slug: 'bert-b-beach',
    roles: ['author'],
    importIDs: [
      {
        recordID: '83',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb018',
    },
    fullName: 'Larry J. Kane',
    slug: 'larry-j-kane',
    roles: ['author'],
    importIDs: [
      {
        recordID: '84',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb019',
    },
    fullName: 'Arthur Ferch',
    slug: 'arthur-ferch',
    roles: ['author'],
    importIDs: [
      {
        recordID: '85',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb01b',
    },
    fullName: 'Gerhard F. Hasel',
    slug: 'gerhard-f-hasel',
    roles: ['author'],
    importIDs: [
      {
        recordID: '86',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb01a',
        },
        startDate: {
          $date: '1984-06-01T00:00:00.000+02:00',
        },
        bio: 'Formerly of Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb01c',
    },
    fullName: 'Johann Heinz',
    slug: 'johann-heinz',
    roles: ['author'],
    importIDs: [
      {
        recordID: '87',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb01d',
    },
    fullName: 'Andrew G. Mustard',
    slug: 'andrew-g-mustard',
    roles: ['author'],
    importIDs: [
      {
        recordID: '88',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.266+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb01e',
    },
    fullName: 'Calvin B. Rock',
    slug: 'calvin-b-rock',
    roles: ['author'],
    importIDs: [
      {
        recordID: '89',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb020',
    },
    fullName: 'Kelvin Onongha',
    slug: 'kelvin-onongha',
    roles: ['author'],
    importIDs: [
      {
        recordID: '90',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb01f',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist University of Africa',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb022',
    },
    fullName: 'Silvia Torreblanca',
    slug: 'silvia-torreblanca',
    roles: ['author'],
    importIDs: [
      {
        recordID: '91',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb021',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Antillean Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb024',
    },
    fullName: 'Rico Javien',
    slug: 'rico-javien',
    roles: ['author'],
    importIDs: [
      {
        recordID: '92',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb023',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SSD, Adventist University of the Philippines',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb026',
    },
    fullName: 'Zane Yi',
    slug: 'zane-yi',
    roles: ['author'],
    importIDs: [
      {
        recordID: '93',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb025',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Loma Linda University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb029',
    },
    fullName: 'Erick Mendieta',
    slug: 'erick-mendieta',
    roles: ['author'],
    importIDs: [
      {
        recordID: '94',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb027',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Antillan Adventist University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb028',
        },
        startDate: {
          $date: '2022-12-19T00:00:00.000+01:00',
        },
        bio: 'PhD Candidate Chair of the Religion Department at Antillean Adventist University',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: '9ak1747131011140.jpg',
      size: 27695,
      originalFilename: 'Mendieta-Erick',
      width: 424,
      height: 494,
      blurhash: 'LKDSk89a03~AZ#kDpIV@5q$%wGIq',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb02b',
    },
    fullName: 'Passmore Hachalinga',
    slug: 'passmore-hachalinga',
    roles: ['author'],
    importIDs: [
      {
        recordID: '95',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb02a',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SID, Ellen G. White Institute',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a49',
    },
    fullName: 'Ranko Stefanovic',
    importIDs: [
      {
        recordID: '97QR',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '96',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb02c',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb02e',
    },
    fullName: 'Abner P. Dizon',
    slug: 'abner-p-dizon',
    roles: ['author'],
    importIDs: [
      {
        recordID: '97',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb02d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb030',
    },
    fullName: 'Remwil Tornalejo',
    slug: 'remwil-tornalejo',
    roles: ['author'],
    importIDs: [
      {
        recordID: '98',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb02f',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.267+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb032',
    },
    fullName: 'Warren C. Trenchard',
    slug: 'warren-c-trenchard',
    roles: ['author'],
    importIDs: [
      {
        recordID: '99',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb031',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, La Sierra University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.268+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.268+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67f72eca2428e63c4e9d12a9',
    },
    firstName: 'Richard',
    middleName: '',
    lastName: 'Rice',
    fullName: '',
    importIDs: [
      {
        recordID: '100',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb033',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Loma Linda University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb035',
    },
    fullName: 'Marcio Costa',
    slug: 'marcio-costa',
    roles: ['author'],
    importIDs: [
      {
        recordID: '101',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb034',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Parana Adventist Academy',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.268+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.268+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb037',
    },
    fullName: 'Joseph Kidder',
    slug: 'joseph-kidder',
    roles: ['author'],
    importIDs: [
      {
        recordID: '102',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb036',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.268+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.268+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb039',
    },
    fullName: 'Thomas Shepherd',
    slug: 'thomas-shepherd',
    roles: ['author'],
    importIDs: [
      {
        recordID: '103',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb038',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb03b',
    },
    fullName: 'Chantal Klingbeil',
    slug: 'chantal-klingbeil',
    roles: ['author'],
    importIDs: [
      {
        recordID: '104',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb03a',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Ellen G. White Estate; Adventist Review/Adventist World',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb03d',
    },
    fullName: 'Sang-Hoon Jee',
    slug: 'sang-hoon-jee',
    roles: ['author'],
    importIDs: [
      {
        recordID: '105',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb03c',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SSD, Asia-Pacific International University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb03f',
    },
    fullName: 'Robert Osei-Bonsu',
    slug: 'robert-osei-bonsu',
    roles: ['author'],
    importIDs: [
      {
        recordID: '106',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb03e',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'WAD, Valley View University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb041',
    },
    fullName: 'Solomon Appiah',
    slug: 'solomon-appiah',
    roles: ['author'],
    importIDs: [
      {
        recordID: '107',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb040',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'WAD, Valley View University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb043',
    },
    fullName: 'Jorge Torreblanca',
    slug: 'jorge-torreblanca',
    roles: ['author'],
    importIDs: [
      {
        recordID: '108',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb042',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Inter-American Adventist Theological',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.270+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb045',
    },
    fullName: 'Richard Sabuin',
    slug: 'richard-sabuin',
    roles: ['author'],
    importIDs: [
      {
        recordID: '109',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb044',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NSD, Education/Sabbath School and Personal Ministries',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.271+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.271+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb047',
    },
    fullName: 'Donny Chrissutianto',
    slug: 'donny-chrissutianto',
    roles: ['author'],
    importIDs: [
      {
        recordID: '110',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb046',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.271+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.271+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375aed',
    },
    fullName: 'Vanderlei Dorneles',
    importIDs: [
      {
        recordID: 'F7VD',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '111',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb048',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Brazil Adventist University',
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a31',
    },
    fullName: 'Edwin Reynolds',
    importIDs: [
      {
        recordID: 'I7Q3',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '112',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb049',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Southern Adventist University',
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd72a',
        },
        bio: 'retired professor of New Testament studies, is a research professor for Southern vAdventist University, School of Religion, Collegedale, Tennessee, United States.',
        startDate: {
          $date: '2024-10-01T00:00:00.000+02:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a91',
    },
    fullName: 'Sergio Becerra',
    importIDs: [
      {
        recordID: '57ST',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '113',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb04a',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, River Plate Adventist University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb04c',
    },
    fullName: 'John Skrzypaszek',
    slug: 'john-skrzypaszek',
    roles: ['author'],
    importIDs: [
      {
        recordID: '114',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb04b',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SPD, Avondale College',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.271+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.271+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375ad7',
    },
    fullName: 'Daniel Dei',
    importIDs: [
      {
        recordID: 'G7UR',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '115',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb04d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'WAD, Valley View University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb04f',
    },
    fullName: 'Kuk Heon Lee',
    slug: 'kuk-heon-lee',
    roles: ['author'],
    importIDs: [
      {
        recordID: '116',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb04e',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NSD, Sahmyook University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.272+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.272+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb051',
    },
    fullName: 'Oldřich Svoboda',
    slug: 'oldrich-svoboda',
    roles: ['author'],
    importIDs: [
      {
        recordID: '117',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb050',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'EUD, Czech-Slovakian Union Adventist Theological Institute',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.272+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.272+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb053',
    },
    fullName: 'Jacques Ratsimbason',
    slug: 'jacques-ratsimbason',
    roles: ['author'],
    importIDs: [
      {
        recordID: '118',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb052',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SID, Adventist University Zurcher',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.272+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.272+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb054',
    },
    fullName: 'Walter F. Specht',
    slug: 'walter-f-specht',
    roles: ['author'],
    importIDs: [
      {
        recordID: '119',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.272+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.272+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb056',
    },
    fullName: 'William G. Johnsson',
    slug: 'william-g-johnsson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '120',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb055',
        },
        startDate: {
          $date: '2006-07-01T00:00:00.000+02:00',
        },
        bio: 'Review and Herald',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375aa3',
    },
    fullName: 'Richard Elofer',
    importIDs: [
      {
        recordID: 'C7TB',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '121',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb057',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Global Mission Study Centers',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb059',
    },
    fullName: 'Ivan T. Blazen',
    slug: 'ivan-t-blazen',
    roles: ['author'],
    importIDs: [
      {
        recordID: '122',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb058',
        },
        startDate: {
          $date: '2010-07-13T00:00:00.000+02:00',
        },
        bio: 'Loma Linda University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375aea',
    },
    fullName: 'Eugene Zaitsev',
    importIDs: [
      {
        recordID: '97VA',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '123',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb05a',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'ESD, Zaoksky Theological Seminary',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb05c',
    },
    fullName: 'Nicholas Miller',
    slug: 'nicholas-miller',
    roles: ['author'],
    importIDs: [
      {
        recordID: '124',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb05b',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb05e',
    },
    fullName: 'Davidson Razafiarivony',
    slug: 'davidson-razafiarivony',
    roles: ['author'],
    importIDs: [
      {
        recordID: '125',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb05d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist University of Africa',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb060',
    },
    fullName: 'Emmer Chacón Parra',
    slug: 'emmer-chacon-parra',
    roles: ['author'],
    importIDs: [
      {
        recordID: '126',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb05f',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Montemorelos University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.273+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb062',
    },
    fullName: 'Marco Terreros',
    slug: 'marco-terreros',
    roles: ['author'],
    importIDs: [
      {
        recordID: '127',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb061',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Inter-American Adventist Theological Seminary',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb064',
    },
    fullName: 'Skip Bell',
    slug: 'skip-bell',
    roles: ['author'],
    importIDs: [
      {
        recordID: '128',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb063',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NSD, Sahmyook University; GC, Andrews University Seminary',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb066',
    },
    fullName: 'Gyeong Chun Choi',
    slug: 'gyeong-chun-choi',
    roles: ['author'],
    importIDs: [
      {
        recordID: '129',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb065',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NSD, Sahmyook University; GC, Andrews University Seminary',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb068',
    },
    fullName: 'Stan Patterson',
    slug: 'stan-patterson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '130',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb067',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NSD, Sahmyook University; GC, Andrews University Seminary',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb06a',
    },
    fullName: 'David Penno',
    slug: 'david-penno',
    roles: ['author'],
    importIDs: [
      {
        recordID: '131',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb069',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NSD, Sahmyook University; GC, Andrews University Seminary',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a82',
    },
    fullName: 'Sergey Davidoglu',
    importIDs: [
      {
        recordID: 'D7SE',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '132',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb06b',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'ESD, Zaoksky Theological Seminary',
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a97',
    },
    fullName: 'Adriani Milli Rodrigues',
    importIDs: [
      {
        recordID: 'I7SZ',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '133',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb06c',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Brazil Adventist University',
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a45',
    },
    fullName: 'Diogo Cavalcanti',
    importIDs: [
      {
        recordID: '57QN',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '134',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb06d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Brazil Publishing House',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb06f',
    },
    fullName: 'Anthony MacPerson',
    slug: 'anthony-mac-person',
    roles: ['author'],
    importIDs: [
      {
        recordID: '135',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb06e',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SPD, Fulton College',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb072',
    },
    fullName: 'Hans Heinz',
    slug: 'hans-heinz',
    roles: ['author'],
    importIDs: [
      {
        recordID: '136',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb070',
        },
        startDate: {
          $date: '2018-10-01T00:00:00.000+02:00',
        },
        bio: 'is a veteran theology teacher who taught for 40 years at Adventist schools in Austria and Germany',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb071',
        },
        startDate: {
          $date: '2018-10-01T00:00:00.000+02:00',
        },
        bio: 'is a veteran theology teacher who taught for 40 years at Adventist schools in Austria and Germany',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'iLt1747131011529.jpg',
      size: 57253,
      originalFilename: 'Heinz-Hans',
      width: 848,
      height: 1126,
      blurhash: 'LNEL~bE0X.f6~pIox^WBJBayiwj]',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.274+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375aab',
    },
    fullName: 'Paul Petersen',
    importIDs: [
      {
        recordID: 'I7TJ',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '137',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb073',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'TED, Danish Union',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb074',
        },
        startDate: {
          $date: '2008-07-01T00:00:00.000+02:00',
        },
        bio: 'South Pacific Division',
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375ae7',
    },
    fullName: 'Alexander Bolotnikov',
    importIDs: [
      {
        recordID: '97V7',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '138',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb075',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Shalom Learning Center',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb077',
    },
    fullName: 'Joses Imona',
    slug: 'joses-imona',
    roles: ['author'],
    importIDs: [
      {
        recordID: '139',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb076',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SPD, Sonoma Adventist College',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb079',
    },
    fullName: 'Marcelo Dias',
    slug: 'marcelo-dias',
    roles: ['author'],
    importIDs: [
      {
        recordID: '140',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb078',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Brazil Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a7c',
    },
    fullName: 'Eriks Galenieks',
    importIDs: [
      {
        recordID: '97S8',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '141',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb07a',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist University of Africa',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb07c',
    },
    fullName: 'Cristian Cardozo',
    slug: 'cristian-cardozo',
    roles: ['author'],
    importIDs: [
      {
        recordID: '142',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb07b',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Colombia Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb07e',
    },
    fullName: 'Felix Cortez',
    slug: 'felix-cortez',
    roles: ['author'],
    importIDs: [
      {
        recordID: '143',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb07d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67f867a0f72c3da5a0a40a25',
    },
    firstName: 'Richard',
    middleName: '',
    lastName: 'Choi',
    fullName: '',
    importIDs: [
      {
        recordID: '144',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb07f',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb082',
    },
    fullName: 'Ted N. C. Wilson',
    slug: 'ted-n-c-wilson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '145',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb080',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, President',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb081',
        },
        startDate: {
          $date: '2004-07-01T00:00:00.000+02:00',
        },
        bio: 'General Conference',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb084',
    },
    fullName: 'Ross E. Winkle',
    slug: 'ross-e-winkle',
    roles: ['author'],
    importIDs: [
      {
        recordID: '146',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb083',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Pacific Union College',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb086',
    },
    fullName: 'Miguel Gutierrez',
    slug: 'miguel-gutierrez',
    roles: ['author'],
    importIDs: [
      {
        recordID: '147',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb085',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'EUD, Italian Adventist University, Villa Aurora',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375ac4',
    },
    fullName: 'Abner F. Hernandez',
    importIDs: [
      {
        recordID: 'B7U8',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '148',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb087',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Montemorelos University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb089',
    },
    fullName: 'Gary Land',
    slug: 'gary-land',
    roles: ['author'],
    importIDs: [
      {
        recordID: '149',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb088',
        },
        startDate: {
          $date: '2011-01-01T00:00:00.000+01:00',
        },
        bio: 'is professor and chair of the history and political science department, Andrews University.',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.275+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb08c',
    },
    fullName: 'Jacques Benjamin Doukhan',
    slug: 'jacques-benjamin-doukhan',
    roles: ['author'],
    importIDs: [
      {
        recordID: '150',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb08a',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb08b',
        },
        startDate: {
          $date: '2022-09-29T00:00:00.000+02:00',
        },
        bio: 'Professor of Hebrew and Old Testament Exegesis, Emeritus',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'YHR1747131011289.jpg',
      size: 97190,
      originalFilename: 'Doukhan-Jacques-Benjamin',
      width: 850,
      height: 988,
      blurhash: 'LEEeDCEM01^i}?Ip0#-o5msn=wS4',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.276+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.276+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375aa7',
    },
    fullName: 'Jan Barna',
    importIDs: [
      {
        recordID: '27TF',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '151',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb08d',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'TED, Newbold College of Higher Education',
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a98',
    },
    fullName: 'Marcos Blanco',
    importIDs: [
      {
        recordID: 'G7T0',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '152',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb08e',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, South American Spanish Publishing House',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb090',
    },
    fullName: 'Ronald Nalin',
    slug: 'ronald-nalin',
    roles: ['author'],
    importIDs: [
      {
        recordID: '153',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb08f',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Geoscience Research Institute',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.276+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.276+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67f6c2612428e63c4e9b97ab',
    },
    firstName: 'Carlos',
    middleName: '',
    lastName: 'Olivares',
    fullName: '',
    importIDs: [
      {
        recordID: '154',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb091',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Brazil Adventist University',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb093',
    },
    fullName: 'Lael Caesar',
    slug: 'lael-caesar',
    roles: ['author'],
    importIDs: [
      {
        recordID: '155',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb092',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist Review/Adventist World',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a6c',
    },
    fullName: 'David Tasker',
    importIDs: [
      {
        recordID: '47RS',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '156',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb094',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SPD - Avondale College',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb096',
    },
    fullName: 'Teófilo Correa',
    slug: 'teofilo-correa',
    roles: ['author'],
    importIDs: [
      {
        recordID: '157',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb095',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb097',
    },
    fullName: 'Nancy Jean Vyhmeister',
    slug: 'nancy-jean-vyhmeister',
    roles: ['author'],
    importIDs: [
      {
        recordID: '158',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb099',
    },
    fullName: 'Gilbert Ojwang',
    slug: 'gilbert-ojwang',
    roles: ['author'],
    importIDs: [
      {
        recordID: '159',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb098',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Oakwood University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb09b',
    },
    fullName: 'Helmut Kramer',
    slug: 'helmut-kramer',
    roles: ['author'],
    importIDs: [
      {
        recordID: '160',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb09a',
        },
        startDate: {
          $date: '2003-07-01T00:00:00.000+02:00',
        },
        bio: 'Upper Columbia Conference',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb09d',
    },
    fullName: 'Robert E. Lemon',
    slug: 'robert-e-lemon',
    roles: ['author'],
    importIDs: [
      {
        recordID: '161',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb09c',
        },
        startDate: {
          $date: '2009-01-01T00:00:00.000+01:00',
        },
        bio: 'is the treasurer of the General Conference of Seventh-day Adventists',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb09f',
    },
    fullName: 'G. Edward Reid',
    slug: 'g-edward-reid',
    roles: ['author'],
    importIDs: [
      {
        recordID: '162',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb09e',
        },
        startDate: {
          $date: '2009-01-01T00:00:00.000+01:00',
        },
        bio: 'is director of the Stewardship department of the North American Division of Seventh-day Adventists',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'fgr1747131011787.jpg',
      size: 45323,
      originalFilename: 'Reid-G-Edward',
      width: 514,
      height: 780,
      blurhash: 'LHHB3#^k010L~B%2XUNH0LEL^O=|',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0a1',
    },
    fullName: 'John W. Peters',
    slug: 'john-w-peters',
    roles: ['author'],
    importIDs: [
      {
        recordID: '163',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0a0',
        },
        startDate: {
          $date: '2007-10-01T00:00:00.000+02:00',
        },
        bio: 'Formerly President of the 1888 Message Study Committee',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0a6',
    },
    fullName: 'Artur Stele',
    slug: 'artur-stele',
    roles: ['author'],
    importIDs: [
      {
        recordID: '164',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0a2',
        },
        startDate: {
          $date: '2015-07-01T00:00:00.000+02:00',
        },
        bio: 'Director of BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb0a3',
        },
        startDate: {
          $date: '2012-01-01T00:00:00.000+01:00',
        },
        bio: 'is director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb0a4',
        },
        startDate: {
          $date: '2012-04-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb0a5',
        },
        startDate: {
          $date: '2011-10-01T00:00:00.000+02:00',
        },
        bio: 'Director of BRI, and Members of the FBRC',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'zRy1747131012185.jpg',
      size: 36968,
      originalFilename: 'Stele-Artur',
      width: 600,
      height: 714,
      blurhash: 'L7FFHe=X0UBDE1RO%NNH00EO{b-U',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.277+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0a8',
    },
    fullName: 'Carmelo Martines',
    slug: 'carmelo-martines',
    roles: ['author'],
    importIDs: [
      {
        recordID: '165',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0a7',
        },
        startDate: {
          $date: '2015-07-01T00:00:00.000+02:00',
        },
        bio: 'River Plate Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0aa',
    },
    fullName: 'Warren Simatele',
    slug: 'warren-simatele',
    roles: ['author'],
    importIDs: [
      {
        recordID: '166',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0a9',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SID, Rusangu University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67f431ee2428e63c4e8e6c24',
    },
    firstName: 'A. Rahel',
    middleName: '',
    lastName: 'Wells',
    fullName: '',
    importIDs: [
      {
        recordID: '167',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ab',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a2b',
    },
    fullName: 'Jan A. Sigvartsen',
    importIDs: [
      {
        recordID: 'B7PX',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '168',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ac',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'EUD, Friedensau Adventist University',
      },
    ],
  },
  {
    _id: {
      $oid: '67f585852428e63c4e94ab96',
    },
    firstName: 'Leonardo',
    middleName: 'Godinho',
    lastName: 'Nunes',
    fullName: '',
    importIDs: [
      {
        recordID: '169',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ad',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Northeast Brazil Academy',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0af',
    },
    fullName: 'Willian Wenceslau de Oliveira',
    slug: 'willian-wenceslau-de-oliveira',
    roles: ['author'],
    importIDs: [
      {
        recordID: '170',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ae',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Northeast Brazil Academy',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0b1',
    },
    fullName: 'Anna Galeniece',
    slug: 'anna-galeniece',
    roles: ['author'],
    importIDs: [
      {
        recordID: '171',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0b0',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist University of Africa',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67f9230ef72c3da5a0a7b515',
    },
    firstName: 'Carlos',
    middleName: 'Elias',
    lastName: 'Mora',
    fullName: '',
    importIDs: [
      {
        recordID: '172',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0b2',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0b4',
    },
    fullName: 'Joel Iparraguirre',
    slug: 'joel-iparraguirre',
    roles: ['author'],
    importIDs: [
      {
        recordID: '173',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0b3',
        },
        startDate: {
          $date: '2015-04-01T00:00:00.000+02:00',
        },
        bio: 'Peruvian Union University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0b6',
    },
    fullName: 'Dojcin Zivadinovic',
    slug: 'dojcin-zivadinovic',
    roles: ['author'],
    importIDs: [
      {
        recordID: '174',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0b5',
        },
        startDate: {
          $date: '2021-09-01T00:00:00.000+02:00',
        },
        bio: 'Professor of Religion, Weimar University',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'lWL1747131011970.jpg',
      size: 55496,
      originalFilename: 'Zivadinovic-Dojcin',
      width: 850,
      height: 988,
      blurhash: 'L89??@Ip00?Gz,f*KljF0:xa}lIo',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0b8',
    },
    fullName: 'Glauber S. Araújo',
    slug: 'glauber-s-araujo',
    roles: ['author'],
    importIDs: [
      {
        recordID: '175',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0b7',
        },
        startDate: {
          $date: '2021-09-01T00:00:00.000+02:00',
        },
        bio: 'Editor, CPB – Casa Publicadora Brasileira',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'InS1747131012388.jpg',
      size: 77630,
      originalFilename: 'Araujo-Glauber-S',
      width: 850,
      height: 988,
      blurhash: 'LBGkUd000#}@D$%M%2t700s.=vE2',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0ba',
    },
    fullName: 'Wilson Parochi',
    slug: 'wilson-parochi',
    roles: ['author'],
    importIDs: [
      {
        recordID: '176',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0b9',
        },
        startDate: {
          $date: '2014-10-01T00:00:00.000+02:00',
        },
        bio: 'is Professor of New Testament Interpretation at the Latin American Adventist Theological Seminary in Engenheiro Coelho, SP, Brazil',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'VbP1747131012768.jpg',
      size: 76840,
      originalFilename: 'Parochi-Wilson',
      width: 722,
      height: 848,
      blurhash: 'L7G*$pQk1600H;EN_Nxa0jE,:}=|',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375acf',
    },
    fullName: 'Martin Pröbstle',
    importIDs: [
      {
        recordID: 'I7UJ',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '177',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0bb',
        },
        startDate: {
          $date: '2022-01-01T00:00:00.000+01:00',
        },
        bio: 'Professor of Old Testament and dean of the Department of Theology, Seminar Schloss Bogenhofen, Austria',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0bd',
    },
    fullName: 'Karel Nowak',
    slug: 'karel-nowak',
    roles: ['author'],
    importIDs: [
      {
        recordID: '178',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0bc',
        },
        startDate: {
          $date: '2009-04-01T00:00:00.000+02:00',
        },
        bio: 'is the director of Religious Liberty and Communication for the Euro-Africa Division',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0bf',
    },
    fullName: 'William E. Timm',
    slug: 'william-e-timm',
    roles: ['author'],
    importIDs: [
      {
        recordID: '179',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0be',
        },
        startDate: {
          $date: '2023-07-01T00:00:00.000+02:00',
        },
        bio: 'Novo Tempo Communication Network',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'QcP1747131012989.jpg',
      size: 37076,
      originalFilename: 'Timm-William-E',
      width: 518,
      height: 594,
      blurhash: 'LPH2G@IUX,x]~UxutlR*D*WBMxof',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.278+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0c1',
    },
    fullName: 'Gina Wahlen',
    slug: 'gina-wahlen',
    roles: ['author'],
    importIDs: [
      {
        recordID: '180',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0c0',
        },
        startDate: {
          $date: '2024-01-01T00:00:00.000+01:00',
        },
        bio: 'Editor, humansexuality.org',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'ZKW1747131013191.jpg',
      size: 41068,
      originalFilename: 'Wahlen-Gina',
      width: 520,
      height: 596,
      blurhash: 'LBHnjG0L_MK4]f4n-:I:CmD%$+-W',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67dadf21f4d4a26a76736f04',
    },
    firstName: 'Daniel',
    middleName: '',
    lastName: 'Bediako',
    fullName: '',
    importIDs: [
      {
        recordID: '181',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0c2',
        },
        startDate: {
          $date: '2013-10-01T00:00:00.000+02:00',
        },
        bio: 'Valley View University, Ghana',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb0c3',
        },
        startDate: {
          $date: '2023-01-01T00:00:00.000+01:00',
        },
        bio: 'Associate Director of the Biblical Research Institute',
      },
      {
        _id: {
          $oid: '68249bb4a2632747d67cd70a',
        },
        bio: 'Associate Director, Biblical Research Institute',
        startDate: {
          $date: '2024-10-01T00:00:00.000+02:00',
        },
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0c5',
    },
    fullName: 'Gheorghe Razmerita',
    slug: 'gheorghe-razmerita',
    roles: ['author'],
    importIDs: [
      {
        recordID: '182',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0c4',
        },
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
        bio: 'is Assistant Professor of Church History and Systematic Theology, Adventist University of Africa',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'A7J1747131013796.jpg',
      size: 52641,
      originalFilename: 'Razmerita-Gheorghe',
      width: 722,
      height: 734,
      blurhash: 'LhHe;8RO%zW._NV?%io#JCtSemay',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0c7',
    },
    fullName: 'George T. Javor',
    slug: 'george-t-javor',
    roles: ['author'],
    importIDs: [
      {
        recordID: '183',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0c6',
        },
        startDate: {
          $date: '2009-10-01T00:00:00.000+02:00',
        },
        bio: 'is Professor Emeritus of Biochemistry and Microbiology, Loma Linda University School of Medicine',
      },
    ],
    image: {
      containerId: '65de02451beb6f51dee0d1e4',
      extension: '.jpg',
      mime: 'image/jpeg',
      name: 'xJL1747131014179.jpg',
      size: 40875,
      originalFilename: 'Javor-George-T',
      width: 718,
      height: 694,
      blurhash: 'LIDS5c9u01~A^iI:EO-UE#o0$3W;',
    },
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0c9',
    },
    fullName: 'Rex Edwards',
    slug: 'rex-edwards',
    roles: ['author'],
    importIDs: [
      {
        recordID: '184',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0c8',
        },
        startDate: {
          $date: '2011-04-01T00:00:00.000+02:00',
        },
        bio: 'BRI',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0cb',
    },
    fullName: 'Hans K. La Rondelle',
    slug: 'hans-k-la-rondelle',
    roles: ['author'],
    importIDs: [
      {
        recordID: '185',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ca',
        },
        startDate: {
          $date: '1989-07-20T00:00:00.000+02:00',
        },
        bio: 'Adventist Review',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0cc',
    },
    fullName: 'W.H. Johns',
    slug: 'w-h-johns',
    roles: ['author'],
    importIDs: [
      {
        recordID: '186',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0cd',
    },
    fullName: 'Harry W. Lowe',
    slug: 'harry-w-lowe',
    roles: ['author'],
    importIDs: [
      {
        recordID: '187',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0cf',
    },
    fullName: 'Cristian Dumitrescu',
    slug: 'cristian-dumitrescu',
    roles: ['author'],
    importIDs: [
      {
        recordID: '188',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ce',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0d2',
    },
    fullName: 'Roger W. Coon',
    slug: 'roger-w-coon',
    roles: ['author'],
    importIDs: [
      {
        recordID: '189',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0d0',
        },
        startDate: {
          $date: '1998-07-23T00:00:00.000+02:00',
        },
        bio: 'At the time of this writing Roger W. Coon, Ph.D., served jointly as Associate Secretary, Ellen G. White Estate, General Conference of Seventh-day Adventists, Washington, DC, and Adjunct Professor of Prophetic Guidance, Seventh-day Adventist Theological Seminary, Andrews University, Berrien Springs, Michigan.',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb0d1',
        },
        startDate: {
          $date: '1987-12-10T00:00:00.000+01:00',
        },
        bio: 'Ellen G. White Estate Washington, D.C.',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0d3',
    },
    fullName: 'Robert W. Olson',
    slug: 'robert-w-olson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '190',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67f931a1f72c3da5a0a83dfa',
    },
    firstName: 'Dan-Adrian',
    middleName: '',
    lastName: 'Petre',
    fullName: '',
    importIDs: [
      {
        recordID: '191',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0d4',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'EUD, Romanian Adventist Theological Institute',
      },
      {
        _id: {
          $oid: '68249baaa2632747d67cb0d5',
        },
        startDate: {
          $date: '2023-10-01T00:00:00.000+02:00',
        },
        bio: 'Assistant Professor of Systematic Theology and Adventist Studies Editor-in-Chief, TheoRhēma Adventus University of Cernica, Romania',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0d7',
    },
    fullName: 'Sergio Celis',
    slug: 'sergio-celis',
    roles: ['author'],
    importIDs: [
      {
        recordID: '192',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0d6',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD, Chile Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.279+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0d8',
    },
    fullName: 'Jean R. Zurcher',
    slug: 'jean-r-zurcher',
    roles: ['author'],
    importIDs: [
      {
        recordID: '193',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0d9',
    },
    fullName: 'Herbert E. Douglass',
    slug: 'herbert-e-douglass',
    roles: ['author'],
    importIDs: [
      {
        recordID: '194',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0db',
    },
    fullName: 'Roger Ruiz',
    slug: 'roger-ruiz',
    roles: ['author'],
    importIDs: [
      {
        recordID: '195',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0da',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Central America Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0dd',
    },
    fullName: 'David Macario Flores',
    slug: 'david-macario-flores',
    roles: ['author'],
    importIDs: [
      {
        recordID: '196',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0dc',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'IAD, Central American Adventist University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0df',
    },
    fullName: 'Clodoaldo Tavares dos Santos',
    slug: 'clodoaldo-tavares-dos-santos',
    roles: ['author'],
    importIDs: [
      {
        recordID: '197',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0de',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'SAD – Amazonia Adventist Academy',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0e1',
    },
    fullName: 'Paolo Benini',
    slug: 'paolo-benini',
    roles: ['author'],
    importIDs: [
      {
        recordID: '198',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0e0',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0e3',
    },
    fullName: 'Frank Hardy',
    slug: 'frank-hardy',
    roles: ['author'],
    importIDs: [
      {
        recordID: '199',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0e2',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Retired',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0e5',
    },
    fullName: 'Ben Holdsworth',
    slug: 'ben-holdsworth',
    roles: ['author'],
    importIDs: [
      {
        recordID: '200',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0e4',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Union College',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.280+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67c9b6c2fa46feb307375a56',
    },
    fullName: 'Eike Mueller',
    importIDs: [
      {
        recordID: '17R4',
        type: 'adventist-dictionary-api',
      },
      {
        recordID: '201',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0e6',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist International Institute of Advanced Studies',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0e8',
    },
    fullName: 'Ron du Preez',
    slug: 'ron-du-preez',
    roles: ['author'],
    importIDs: [
      {
        recordID: '202',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0e7',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'NAD, Arizona Conference',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '67f587192428e63c4e94b159',
    },
    firstName: 'Darius',
    middleName: '',
    lastName: 'Jankiewicz',
    fullName: '',
    importIDs: [
      {
        recordID: '203',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0e9',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University Seminary',
      },
    ],
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0eb',
    },
    fullName: 'Oliver Glanz',
    slug: 'oliver-glanz',
    roles: ['author'],
    importIDs: [
      {
        recordID: '204',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ea',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Andrews University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0ed',
    },
    fullName: 'Delbert W. Baker',
    slug: 'delbert-w-baker',
    roles: ['author'],
    importIDs: [
      {
        recordID: '205',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ec',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'GC, Adventist University of Africa',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0ef',
    },
    fullName: 'Larry Lichtenwalter',
    slug: 'larry-lichtenwalter',
    roles: ['author'],
    importIDs: [
      {
        recordID: '206',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0ee',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'MENA, Middle East University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f0',
    },
    fullName: 'Gordon M. Hyde',
    slug: 'gordon-m-hyde',
    roles: ['author'],
    importIDs: [
      {
        recordID: '207',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.281+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f1',
    },
    fullName: 'Kenneth L. Vine',
    slug: 'kenneth-l-vine',
    roles: ['author'],
    importIDs: [
      {
        recordID: '208',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f2',
    },
    fullName: 'Jerry A. Gladson',
    slug: 'jerry-a-gladson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '209',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f3',
    },
    fullName: 'Julia Neuffer',
    slug: 'julia-neuffer',
    roles: ['author'],
    importIDs: [
      {
        recordID: '210',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f4',
    },
    fullName: 'Sakae Kubo',
    slug: 'sakae-kubo',
    roles: ['author'],
    importIDs: [
      {
        recordID: '211',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f5',
    },
    fullName: 'E. Marcella Anderson',
    slug: 'e-marcella-anderson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '212',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f6',
    },
    fullName: 'LaVone Neff',
    slug: 'la-vone-neff',
    roles: ['author'],
    importIDs: [
      {
        recordID: '213',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f7',
    },
    fullName: 'Betty Stirling',
    slug: 'betty-stirling',
    roles: ['author'],
    importIDs: [
      {
        recordID: '214',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0f8',
    },
    fullName: 'Fritz Guy',
    slug: 'fritz-guy',
    roles: ['author'],
    importIDs: [
      {
        recordID: '215',
        type: 'bri-articles',
      },
    ],
    bios: [],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0fa',
    },
    fullName: 'Ademola S. Tayo',
    slug: 'ademola-s-tayo',
    roles: ['author'],
    importIDs: [
      {
        recordID: '218',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0f9',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'WAD, Babcock University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0fc',
    },
    fullName: 'Efe M. Ehioghae',
    slug: 'efe-m-ehioghae',
    roles: ['author'],
    importIDs: [
      {
        recordID: '219',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0fb',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'WAD, Babcock University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
  {
    _id: {
      $oid: '68249baaa2632747d67cb0fe',
    },
    fullName: 'Theodore U. Dickson',
    slug: 'theodore-u-dickson',
    roles: ['author'],
    importIDs: [
      {
        recordID: '220',
        type: 'bri-articles',
      },
    ],
    bios: [
      {
        _id: {
          $oid: '68249baaa2632747d67cb0fd',
        },
        startDate: {
          $date: '2018-06-11T00:00:00.000+02:00',
        },
        bio: 'WAD, Babcock University',
      },
    ],
    createdAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    updatedAt: {
      $date: '2025-05-14T15:33:30.282+02:00',
    },
    enabled: true,
    deleted: false,
    entity: {
      $oid: '65de02451beb6f51dee0d1e4',
    },
  },
];

// Recursive function to transform $oid and $date
const transformFields = (obj) => {
  if (Array.isArray(obj)) {
    return obj.map(transformFields);
  }

  if (obj !== null && typeof obj === 'object') {
    for (const key of Object.keys(obj)) {
      if (key === '$oid') {
        return ObjectId(obj[key]);
      }
      if (key === '$date') {
        return new Date(obj[key]);
      }
      obj[key] = transformFields(obj[key]);
    }
  }
  return obj;
};

const transformedData = data.map(transformFields);

const bulkOps = transformedData.map((doc) => ({
  updateOne: {
    filter: { _id: doc._id },
    update: { $set: doc },
    upsert: true,
  },
}));

db.people.bulkWrite(bulkOps);
console.log('Bulk write operation completed successfully.'); // eslint-disable-line no-console
