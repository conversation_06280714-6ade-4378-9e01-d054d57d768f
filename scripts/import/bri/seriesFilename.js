/* global use, db, print, ObjectId */

// This script is run in the context of a MongoDB shell
// for example: mongosh <connection-string> <absolute-path-to-script>

// // <PERSON>ript to update files of articles of series with the filename of the series name.
// use('hopeplatform');

// const seriesCursor = db.articleseries.find();

// while (seriesCursor.hasNext()) {
//   const series = seriesCursor.next();
//   const seriesName = series.name;

//   const result = db.articles.updateMany(
//     { series: series._id },
//     {
//       $set: {
//         'files.0.file.originalFilename': `${seriesName}`,
//       },
//     }
//   );

//   print(`Updated ${result.modifiedCount} articles for series: ${seriesName}`);
// }

// print('Updated all series filenames');

// Script to change all originalFilenames that have .pdf
use('hopeplatform'); // Replace with your database name if different

const entityId = ObjectId('65de02451beb6f51dee0d1e4'); // The target entity ID

// Find all articles that belong to the specified entity
const articlesCursor = db.articles.find({ entity: entityId });

let totalUpdated = 0;

while (articlesCursor.hasNext()) {
  const article = articlesCursor.next();

  // Check if the article has a file with an originalFilename ending in ".pdf"
  if (
    article.files &&
    article.files.length > 0 &&
    article.files[0].file &&
    article.files[0].file.originalFilename &&
    article.files[0].file.originalFilename.endsWith('.pdf')
  ) {
    const { originalFilename } = article.files[0].file;

    // Remove the ".pdf" suffix
    const updatedFilename = originalFilename.replace(/\.pdf$/i, '');

    // Update the article in the database
    const result = db.articles.updateOne(
      { _id: article._id },
      {
        $set: {
          'files.0.file.originalFilename': updatedFilename,
        },
      }
    );

    if (result.modifiedCount > 0) {
      totalUpdated += 1;
    }
  }
}

print(`Total articles updated: ${totalUpdated}`);
