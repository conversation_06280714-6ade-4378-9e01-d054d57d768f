import { <PERSON><PERSON> } from 'croner';

import app from '#app';
import Sync from '#modules/data-import/models/Sync.js';
import Channel from '#modules/media-library/models/Channel.js';
import { ftpScheduleImport } from '#modules/media-library/scripts/scheduleImport.js';
import tasksService from '#modules/tasks/services/tasksService.js';
import { scheduleToChron } from '#modules/tasks/utils/cronUtils.js';
import Logger from '#utils/logger.js';

import typo3Import from './import/typo3-import.js';

async function getCronTasks() {
  try {
    const scheduledTasks = await tasksService.getScheduledTasks();
    return scheduledTasks.map((task) => {
      const { id, settings } = task;
      return {
        id,
        schedule: scheduleToChron(settings.schedule),
        execute: () => tasksService.executeTask(id),
      };
    });
  } catch (err) {
    Logger.info('Error setting up scheduled tasks.', err);
  }
}

// C<PERSON>
// ┌──────────────── (optional) second (0 - 59)
// | ┌────────────── minute (0 - 59)
// │ │ ┌──────────── hour (0 - 23)
// │ │ │ ┌────────── day of month (1 - 31)
// │ │ │ │ ┌──────── month (1 - 12, JAN-DEC)
// │ │ │ │ │ ┌────── day of week (0 - 6, SUN-Mon)
// │ │ │ │ │ │       (0 to 6 are Sunday to Saturday; 7 is Sunday, the same as 0)
// │ │ │ │ │ │
// * * * * * *
// For more info: https://github.com/Hexagon/croner#documentation
export default async function registerTasks() {
  if (process.env.RUN_CRON_JOBS?.toLowerCase() === 'yes') {
    await registerScheduledTasks();
    await registerTYPO3Syncs();
    await registerFTPScheduleImport();
  }
}

async function registerScheduledTasks() {
  let registeredTasks = {};

  Logger.info('Setting up scheduled tasks.');

  // Checks for task updates every 5 minutes
  const mainJob = new Cron('*/5 * * * *', async () => {
    const scheduledTasks = await getCronTasks();

    for (const task of scheduledTasks) {
      const { execute, schedule, id } = task;

      if (!execute || !schedule) {
        continue;
      }

      const existingJob = registeredTasks[id];

      if (!existingJob) {
        try {
          registeredTasks[id] = new Cron(schedule, execute);
          Logger.info(
            `Registered task "${id}" with schedule "${schedule}". Next run: ${registeredTasks[
              id
            ].nextRun()}`
          );
        } catch {
          Logger.error(
            `Registering task "${id}" failed with schedule "${schedule}"`
          );
        }
      }

      // Updates existing job if schedule updated
      if (existingJob && existingJob.getPattern() !== schedule) {
        existingJob.stop();
        delete registeredTasks[id];
        try {
          registeredTasks[id] = new Cron(schedule, execute);
          Logger.info(`Updated task "${id}" with schedule "${schedule}".`);
        } catch {
          Logger.error(
            `Updating task "${id}" failed with schedule "${schedule}"`
          );
        }
      }
    }

    // Clean up tasks that are no longer found in the scheduled tasks
    registeredTasks = Object.entries(registeredTasks).reduce(
      (acc, [taskId, task]) => {
        const foundTask = scheduledTasks.find(
          (scheduleTask) => scheduleTask.id === taskId
        );
        if (foundTask) {
          return {
            ...acc,
            [taskId]: task,
          };
        }
        Logger.info(`Removing task "${taskId}". Task no longer scheduled."`);
        return acc;
      },
      {}
    );

    app.set('registeredTasks', registeredTasks);
  });

  // Initialise tasks on start
  mainJob.trigger();
}

async function registerTYPO3Syncs() {
  const registeredSyncs = {};
  Logger.info('Setting up TYPO3 sync task.');

  // Checks for sync task updates every hour
  const mainJob = new Cron('0 * * * *', async () => {
    const syncs = await Sync.find({}).populate('entity site');
    for (const sync of syncs) {
      const { id, settings } = sync;
      const existingSync = registeredSyncs[id];

      const schedule =
        settings?.scheduled && settings?.schedule
          ? scheduleToChron(settings.schedule)
          : null;

      // Unregister sync tasks
      if (!schedule && existingSync) {
        existingSync.stop();
        delete registeredSyncs[id];
        continue;
      }

      // Create or update sync task
      if (!existingSync || existingSync.getPattern() !== schedule) {
        try {
          registeredSyncs[id] = new Cron(schedule, async () => {
            Logger.info('Running TYPO3 import');
            await typo3Import({ sync });
          });
        } catch {
          Logger.error(
            `TYPO3 sync task "${id}" failed with schedule "${schedule}"`
          );
        }
      }
    }
  });

  // Initialize syncs on start
  mainJob.trigger();
}

async function registerFTPScheduleImport() {
  // FTP schedule import every hour
  // eslint-disable-next-line no-unused-vars
  const mainJob = new Cron('0 */1 * * *', async () => {
    Logger.info('Running job FTP schedule import.');

    const channels = await Channel.find({
      enabled: true,
      deleted: false,
    }).populate('entity');

    for (let index = 0; index < channels.length; index += 1) {
      const channel = channels[index];

      await ftpScheduleImport({ channel });
    }
  });
}
